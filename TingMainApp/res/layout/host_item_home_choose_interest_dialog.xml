<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/host_item_pager_index_bg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_home_choose_interest_corner_4"
    android:paddingHorizontal="10dp"
    android:paddingVertical="10dp">

    <TextView
        android:id="@+id/host_item_pager_index_tv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="11111"
        android:textColor="@color/host_color_cc2c2c3c_dcdcdc"
        android:textSize="14sp" />
</FrameLayout>