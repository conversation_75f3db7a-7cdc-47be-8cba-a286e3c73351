<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:importantForAccessibility="no">

    <View
        android:id="@+id/host_view_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#4d000000" />

    <!--动画容器-->
    <FrameLayout
        android:id="@+id/host_fl_animal_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:importantForAccessibility="no"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <com.ximalaya.ting.android.alphamovie.AlphaMovieView
            android:id="@+id/host_alpha_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:scaleType="center_crop" />

        <ImageView
            android:id="@+id/iv_flash_move"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="invisible" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/host_ll_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/host_top_10corner_ffffff_131313"
        android:clickable="true"
        android:importantForAccessibility="no"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/host_ll_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/host_tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="18dp"
                android:ellipsize="end"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:importantForAccessibility="yes"
                android:singleLine="true"
                android:text="选择兴趣定制你的专属首页"
                android:textColor="@color/host_color_2c2c3c_ffffff"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/host_iv_close"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/host_tv_sub_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:importantForAccessibility="yes"
                android:singleLine="true"
                android:text="选择理由，获得更精准的推送"
                android:textColor="@color/host_color_662c2c3c_8d8d91"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="@+id/host_tv_title"
                app:layout_constraintRight_toRightOf="@+id/host_tv_title"
                app:layout_constraintTop_toBottomOf="@+id/host_tv_title" />

            <ImageView
                android:id="@+id/host_iv_close"
                android:layout_width="52dp"
                android:layout_height="wrap_content"
                android:contentDescription="关闭"
                android:importantForAccessibility="yes"
                android:paddingHorizontal="16dp"
                android:paddingVertical="10dp"
                android:src="@drawable/host_ic_home_dislike_close_new"
                app:layout_constraintBottom_toBottomOf="@+id/host_tv_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/host_tv_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/host_dialog_real_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="24dp"
            android:importantForAccessibility="yes"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/host_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_max="400dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/host_tv_submit"
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="28dp"
            android:background="@drawable/bg_dialog_home_choose_interest_select_normal_shape"
            android:gravity="center"
            android:importantForAccessibility="yes"
            android:paddingVertical="12dp"
            android:text="定制我的首页"
            android:textColor="@color/host_color_ffffff"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>