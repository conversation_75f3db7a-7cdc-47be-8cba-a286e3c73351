package com.ximalaya.ting.android.ad.model.thirdad.RewardVideo;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

public class CsjFullScreenVideoAd extends AbstractRewardVideoAd<TTFullScreenVideoAd>{

    public CsjFullScreenVideoAd(@Nullable Advertis advertis, TTFullScreenVideoAd ttFullScreenVideoAd, String dspPositionId) {
        super(advertis, ttFullScreenVideoAd, dspPositionId);
    }

    @Override
    public int getType() {
        return THIRD_AD_CSJ_FULL_SCREEN_VIDEO;
    }

    @Override
    public void showRewardVideoAd(@NonNull Activity activity, @NonNull RewardExtraParams extraParams, @NonNull IVideoAdStatueCallBack adStatueCallBack) {
        if (getAdData() == null) {
            return;
        }
        if(!ToolUtil.activityIsValid(activity)) {
            if (adStatueCallBack != null) {
                adStatueCallBack.onAdLoadError(
                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                        "Activity 已不能展示广告");
            }
            return;
        }
        if(adStatueCallBack != null) {
            adStatueCallBack.onAdLoad(this);
        }
        getAdData().setFullScreenVideoAdInteractionListener(new TTFullScreenVideoAd.FullScreenVideoAdInteractionListener() {
            @Override
            public void onAdShow() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayStart();
                }

                AdLogger.log("CSJFullScreenVideoAdManager : onAdShow ");
            }

            @Override
            public void onAdVideoBarClick() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdVideoClick(false, 0);
                }
                AdLogger.log("CSJFullScreenVideoAdManager : onAdVideoBarClick ");
            }

            @Override
            public void onAdClose() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdClose(false);
                }
                AdLogger.log("CSJFullScreenVideoAdManager : onAdClose ");
            }

            @Override
            public void onVideoComplete() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayComplete();
                }

                AdLogger.log("CSJFullScreenVideoAdManager : onVideoComplete ");
            }

            @Override
            public void onSkippedVideo() {
                AdLogger.log("CSJFullScreenVideoAdManager : onSkippedVideo ");
            }
        });
        getAdData().showFullScreenVideoAd(activity);
    }

    @Override
    public double getRtbPrice() {
        try{
            if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
                Logger.i("------msg_rtb", " ------- csj 激励视频广告， 不是实时竞价物料，使用固价 0000 - getPrice --》 " + getAdvertis().getPrice());
                return getAdvertis().getPrice();
            }
            if (getAdData() != null && getAdData().getMediaExtraInfo() != null) {
                String price = getAdData().getMediaExtraInfo().get("price") + "";
                return  Double.parseDouble(price) / 100d;
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return -1;
    }
}
