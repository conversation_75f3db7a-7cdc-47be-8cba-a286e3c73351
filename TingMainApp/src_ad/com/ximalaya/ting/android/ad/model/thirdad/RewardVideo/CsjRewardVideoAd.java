package com.ximalaya.ting.android.ad.model.thirdad.RewardVideo;

import android.app.Activity;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.sdk.openadsdk.TTRewardVideoAd;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBackExt;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

public class CsjRewardVideoAd extends AbstractRewardVideoAd<TTRewardVideoAd>{
    public CsjRewardVideoAd (@Nullable Advertis advertis, TTRewardVideoAd rewardVideoAD, String dspPositionId) {
        super(advertis, rewardVideoAD, dspPositionId);
    }

    @Override
    public int getType() {
        return THIRD_AD_CSJ_REWARD;
    }

    @Override
    public void showRewardVideoAd(@NonNull Activity activity, RewardExtraParams extraParams, IVideoAdStatueCallBack adStatueCallBack) {
        if (getAdData() == null) {
            return;
        }
        if(!ToolUtil.activityIsValid(activity)) {
            if (adStatueCallBack != null) {
                adStatueCallBack.onAdLoadError(
                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                        "Activity 已不能展示广告");
            }
            return;
        }
        if(adStatueCallBack != null) {
            adStatueCallBack.onAdLoad(this);
        }
        getAdData().setRewardAdInteractionListener(new TTRewardVideoAd.RewardAdInteractionListener() {

            @Override
            public void onAdShow() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayStart();
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdShow ");
            }

            @Override
            public void onAdVideoBarClick() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdVideoClick(false, 0);
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdVideoBarClick ");
            }

            @Override
            public void onAdClose() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdClose(false);
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdClose ");
            }

            //视频播放完成回调
            @Override
            public void onVideoComplete() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayComplete();
                }

                AdLogger.log("CSJExcitationVideoAdManager : onVideoComplete ");
            }

            @Override
            public void onVideoError() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_VIDEO_PLAY_ERROR ,"视频播放失败");
                }
                AdLogger.log("CSJExcitationVideoAdManager : onVideoError ");

            }
            //视频播放完成后，奖励验证回调，rewardVerify：是否有效，rewardAmount：奖励梳理，rewardName：奖励名称
            @Override
            public void onRewardVerify(boolean rewardVerify, int rewardAmount, String rewardName, int i, String s) {
                AdLogger.log("CSJExcitationVideoAdManager : onRewardVerify rewardVerify =" + rewardVerify);
                if (rewardVerify && adStatueCallBack != null && adStatueCallBack instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) adStatueCallBack).onRewardVerify();
                } else if (!rewardVerify) {
                    AdLogger.log("CSJExcitationVideoAdManager : onRewardVerify fail code =" + i + " msg =" + s);
                }
            }

            @Override
            public void onRewardArrived(boolean isRewardValid, int i, Bundle bundle) {
                AdLogger.log("CSJExcitationVideoAdManager : onRewardArrived isRewardValid =" + isRewardValid);
                if (isRewardValid && adStatueCallBack != null && adStatueCallBack instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) adStatueCallBack).onRewardVerify();
                } else if (!isRewardValid && bundle != null) {
                    Object errorCode = bundle.get("reward_extra_key_error_code");
                    Object errorMsg = bundle.get("reward_extra_key_error_msg");
                    AdLogger.log("CSJExcitationVideoAdManager : onRewardArrived fail code =" + errorCode + " msg =" + errorMsg);
                }
            }

            @Override
            public void onSkippedVideo() {
                AdLogger.log("CSJExcitationVideoAdManager : onSkippedVideo ");
            }
        });
        getAdData().showRewardVideoAd(activity);
    }

    @Override
    public double getRtbPrice() {
        try{
            if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
                Logger.i("------msg_rtb", " ------- csj 激励视频广告， 不是实时竞价物料，使用固价 0000 - getPrice --》 " + getAdvertis().getPrice());
                return getAdvertis().getPrice();
            }
            if (getAdData() != null && getAdData().getMediaExtraInfo() != null) {
                String price = getAdData().getMediaExtraInfo().get("price") + "";
                return  Double.parseDouble(price) / 100d;
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return -1;
    }
}
