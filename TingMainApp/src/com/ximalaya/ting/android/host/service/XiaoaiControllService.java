package com.ximalaya.ting.android.host.service;

import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_ADD_TO_FAVOURITE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_ADD_TRACK_COLLECT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_AUTO_STOP_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_CURRENT_SONG;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_CURRENT_SONG_SYNC;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_CURRENT_SPEED_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_CURRENT_TIME;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_CURRENT_TIME_SYNC;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_LAST_PLAY_TRACK_IN_ALBUM;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_PLAY_LIST_BY_PAGE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_PLAY_LIST_CUR_PAGE_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_PLAY_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_PLAY_SERVICE_ACTIVE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_SONG_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_SPEED_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_SUBSCRIBE_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_TOTAL_TIME;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_TOTAL_TIME_SYNC;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_GET_TRACK_LIST_BY_ALBUM_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_HAS_NEXT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_HAS_PRE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_IS_PLAYING;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PAUSE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_BY_ALBUMID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_HISTORY;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_LIKE_SOUND;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_NEXT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_PRE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_SONG_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_PLAY_TRACK_BY_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_QUERY_TRACK_COLLECT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_REMOVE_FAVOURITE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_REMOVE_SOUND_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_REMOVE_TRACK_COLLECT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SEARCH_PLAY_HISTORY;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SEEK_TO;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SET_AUTO_STOP_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SET_PLAY_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SET_SET_MOBILE_PLAY_ENABLE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_SET_SPEED_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_START_OTHER_ACTIVITY;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_STOP;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.ACTION_TO_PLAY_INDEX;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_GET_CUR_PAGE_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_AUTO_STOP_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_AUTO_STOP_MODE_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_CURRENT_SPEED_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_CURRENT_TIME;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_GET_CURRENT_SONG;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_HAS_NEXT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_HAS_PRE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_IS_PLAYING;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_LAST_PLAY_TRACK;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_LOAD_HISTORY_PAGE_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_LOAD_NEXT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_MOBILE_PLAY_ENABLE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PAGE_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PAGE_SIZE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PLAY_ALBUMID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PLAY_INDEX;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PLAY_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_PLAY_SERVICE_ACTIVE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_POSITION;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_REMOVE_INDEX;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SDK_VERSION;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SONG_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SOUND_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SPEED_MODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SPEED_MODE_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_START_OTHER_ACTIVITY_URI;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_START_POSITION;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_SUBSCRIBE_LIST;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_TOTAL_TIME;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_TO_PLAY_INDEX;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_TRACK_ID;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_TRACK_IS_COLLECT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_TRACK_IS_LIKED;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_VERIFY_RESULT;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.DATA_TYPE_XM_APP_VERSION;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.RESULT_CODE;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.SDK_VERSION;
import static com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants.XM_API_EVENT_PLAY_LIST_CHANGED;

import android.app.Notification;
import android.app.NotificationManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.net.Uri;
import android.os.*;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.AlbumOrderManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.manager.history.HistoryManagerForPlay;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.TrackCollectManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage;
import com.ximalaya.ting.android.host.manager.xmlog.XmLogManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.mylisten.WoTingAlbumItem;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.service.xmcontrolapi.Album;
import com.ximalaya.ting.android.host.service.xmcontrolapi.ErrorCodes;
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmApiCallback;
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmControlApi;
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmEventChangCallBack;
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmPlayStatusChangeCallBack;
import com.ximalaya.ting.android.host.service.xmcontrolapi.Song;
import com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.im.base.utils.MD5;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.history.HistoryModel;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.appnotification.NotificationChannelUtils;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.constants.PlayerConstants;
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlay;
import com.ximalaya.ting.android.opensdk.player.manager.PlayProgressManager;
import com.ximalaya.ting.android.opensdk.player.receive.NotificationLikeManager;
import com.ximalaya.ting.android.opensdk.player.service.IPlayListLoadStateCallback;
import com.ximalaya.ting.android.opensdk.player.service.IServiceLifeCallBack;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.MyRemoteCallbackList;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayListControl;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.ubt.TraceModel;
import com.ximalaya.ting.android.opensdk.player.ubt.UbtSourceProvider;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.PlayListMMKVUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForPlay;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.utils.AppUtils;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * Created by le.xin on 2019/1/14.
 * 小米小爱语音控制app (运行在播放进程)
 * <AUTHOR>
 * @email <EMAIL>
 */
public class XiaoaiControllService extends Service {

    private final int notificationId = 135451;

    private final RemoteCallbackList<IXmApiCallback> mXmApiCallback = new MyRemoteCallbackList<>();

    private final RemoteCallbackList<IXmEventChangCallBack> mEventCallBack = new MyRemoteCallbackList<>();

    private final RemoteCallbackList<IXmPlayStatusChangeCallBack> mPlayStatusChangeCallBack = new MyRemoteCallbackList<>();

    private final Set<String> mRegsiterEventSet = new HashSet<>();

    private XmControlImpl mXmControl;

    private static XiaoaiControllService mService;

    private XiaoaiControlManagerUsePlayer mXiaoaiControlManager;

    private final String[] mTempoStr = {"0.5", "0.75", "1.0", "1.25", "1.5", "1.75", "2.0", "2.5", "3.0"};
    private final Integer[] mTimeOffArr = {-1, 1, 2, 3, 10, 20, 30, 60, 90};

    public static boolean supportExtParam = false;
    private String timeLine;


    private static final Map<String ,String> verifyMaps = new HashMap<String, String>() {
        {
            put("com.miui.voiceassist", "8ddb342f2da5408402d7568af21e29f9,701478a1e3b4b7e3978ea69469410f13");
            put("com.xiaomi.xiaoailite", "546006caac910e821303ae86334d00a4");
            put("com.coloros.speechassist", "14b6b7f88e294c5bdd4ffd16425525ec");
            put("com.heytap.speechassist", "b3fdf7b7c8301469dbc8c9a96983179a");
            put("com.huawei.vassistant", "745a9c4d499d675c28400fa0c22f5de7");
            put("com.vivo.musicwidgetmix", "cb3817d94474ee58ab37d0825bd25f69,17f7c3978600f0d56aed0f32a1276942");
            put("com.zte.halo.app", "a2e44d1795185b8e3281444007effb7f,8ddb342f2da5408402d7568af21e29f9,6b93380d592ab0c9bd5607a8a5c70d6b,48284211ec100fbb5d22c2e9e91ed057,155b909774b201ac24bc6d62ef4a0b4f,0eef9489c079b72cacbdb4b02c791958,eb178df4852f1a13125f48e95e6d38f1,55b1c1de945ac7f54a5a1dcaad762e3d,efbfab461eee5f680df35896bf7c3375,57894ebfa7db3c4572c76d14585641d2,75e20ec287eb55751836f3eb1a2043ed");
            put("com.ximalaya.ting.harmony", "933861ef93d37f28fecbdd13c3d75cb0,bc0911b2e16061882117998491268c68");
            put("com.oplus.ai.assistant", "b3fdf7b7c8301469dbc8c9a96983179a");
            put("com.coloros.assistantscreen", "d5f2d71470028a27f040234dbe7b62b0");
            put("com.hihonor.magicvoice", "8aba517d67414279358f1ba607a967fb,6b4c3b09f3f29723450f5e06a6d7cfba");
            put("com.xiaomi.superhexa", "35f48ea2adc9f6e723719e1d5dc8a2bf");
            put("com.superhexa.supervision", "c756f5460ac7745bd562c5ea19457889");
        }
    };

    public static XiaoaiControllService getXiaoaiService() {
        return mService;
    }

    private HandlerThread mHandlerThread = null;
    private Handler mHandler = null;

    @Override
    public void onCreate() {
        super.onCreate();
        mService = this;

        if (mHandlerThread == null || !mHandlerThread.isAlive()) {
            mHandlerThread = new HandlerThread("XiaoaiControlThread");
            mHandlerThread.start();
            mHandler = new Handler(mHandlerThread.getLooper());
        }

        mXmControl = new XmControlImpl();
        NotificationCompat.Builder builder = NotificationChannelUtils.newNotificationBuilder(getApplication())
                .setSmallIcon(com.ximalaya.ting.android.framework.R.drawable.ting)
                .setContentTitle("正在使用喜马拉雅");
        builder.setGroupSummary(false).setGroup("后台运行服务");
        Notification notification = builder.build();
        startForeground(notificationId, notification);
        NotificationManager manager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if(manager != null){
            manager.notify(notificationId ,notification);
        }

        mXiaoaiControlManager = new XiaoaiControlManagerUsePlayer();
        mXiaoaiControlManager.init();

        XmPlayerService.setPlayModeChangeListener(mOnPlayModeChangeListener);

        registerReceiver(mLikeStateChangeBroadcastReceiver, new IntentFilter(DTransferConstants.ACTION_LIKE_STATE_CHANGE));

        // sdk 拉起播放器  其他事件
//        TraceModel trace = new TraceModel();
//        trace.metaId = 49624;
//        trace.serviceId = "others";
//        UbtSourceProvider.getInstance().sendTrace(trace);
        XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
        if (playerService != null) {
            sendEventChange(XmControlConstants.XM_API_EVENT_PLAY_SERVICE_CONNECT);
        } else {
            XmPlayerService.addServiceLife(new IServiceLifeCallBack() {
                @Override
                public void onServiceCreate() {
                    XmPlayerService.removeServiceLife(this);
                    sendEventChange(XmControlConstants.XM_API_EVENT_PLAY_SERVICE_CONNECT);
                }

                @Override
                public void onServiceDestory() {
                    XmPlayerService.removeServiceLife(this);
                }
            });
        }

        Logger.logToFile("XiaoaiControllService : onCreate");

        XmLogger.log(XmLogger.Builder.buildLog("apm", "xiaoaicontrol"));
    }

    private final BroadcastReceiver mLikeStateChangeBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && DTransferConstants.ACTION_LIKE_STATE_CHANGE.equals(intent.getAction())) {
                Bundle bundle = new Bundle();
                bundle.putLong(DATA_TYPE_TRACK_ID, intent.getLongExtra(DTransferConstants.PARAM_LIKE_TRACK_ID, 0));
                bundle.putBoolean(DATA_TYPE_TRACK_IS_LIKED, intent.getBooleanExtra(DTransferConstants.PARAM_LIKE_TRACK_STATE, false));
                XiaoaiControllService.sendEventChangeHasBundle(XmControlConstants.XM_API_EVENT_LIKE_STATE_CHANGED, bundle);
            }
        }
    };

    private final XmPlayerService.IOnPlayModeChangeListener mOnPlayModeChangeListener = new XmPlayerService.IOnPlayModeChangeListener() {
        @Override
        public void onPlayModeChange() {
            XiaoaiControllService.sendEventChange(XmControlConstants.XM_API_EVENT_PLAY_MODE_CHANGED);
        }
    };

    private final IPlayListLoadStateCallback mPlayListLoadStateCallback = new IPlayListLoadStateCallback() {
        @Override
        public void onLoadSuccess(boolean hasNextPage) {
            sendEventChange(XM_API_EVENT_PLAY_LIST_CHANGED);
        }

        @Override
        public void onLoadFail() {

        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        if (intent.getIntExtra(DATA_TYPE_SDK_VERSION, 0) >= SDK_VERSION) {
            supportExtParam = true;
        }
        Logger.logToFile("XiaoaiControllService : supportExtParam " + supportExtParam);
        return mXmControl;
    }

    class XmControlImpl extends IXmControlApi.Stub {

        private boolean isChecked = false;
        private String connectPackageName;

        private Bundle getVerifyBundleInfo() {
            Bundle resultBundle = new Bundle();
            resultBundle.putInt(DATA_TYPE_XM_APP_VERSION,  AppUtils.getAppVersionCode());
            resultBundle.putBoolean(DATA_TYPE_VERIFY_RESULT ,isChecked);
            if (isChecked) {
                if (XmPlayerService.getPlayerSrvice() == null) {
                    if ("com.coloros.assistantscreen".equals(connectPackageName)) {
                        initPlayerService(getApplicationContext());
                    }
                }
            }
            return resultBundle;
        }

        @Override
        public Bundle init() throws RemoteException {
            isChecked = false;

            Logger.logToFile("XiaoaiControllService == init");

            // 没有同意隐私策略
            if(!MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).
                    getBooleanCompat(PreferenceConstantsInOpenSdk.TINGMAIN_KEY_PRIVACY_POLICY_AGREED_NEW)) {
                Bundle resultBundle = new Bundle();
                if (!MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(
                        PreferenceConstantsInOpenSdk.KEY_IS_IN_BASE_MODE)) {
                    resultBundle.putInt(DATA_TYPE_XM_APP_VERSION, AppUtils.getAppVersionCode());
                    resultBundle.putInt(RESULT_CODE, ErrorCodes.ERROR_NO_POLICY_AGREED);
                }
                return resultBundle;
            }

            Logger.logToFile("XiaoaiControllService == init  1");

            String[] packages = getPackageManager().getPackagesForUid(getCallingUid());
            if(packages != null && packages.length > 0) {
                JSONObject json = null;
                try {
                    json = new JSONObject(MMKVUtil.getInstance().getString(CConstants.Group_android.ITEM_SPEECHCONTROLVERIFY));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                Map<String ,String> maps = new HashMap<>();

                if(json != null && json.length() > 0) {
                    Iterator<String> keys = null;
                    try {
                        keys = json.keys();

                        while(keys.hasNext()){
                            String key = keys.next();
                            maps.put(key ,json.getString(key));
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }

                if(ToolUtil.isEmptyMap(maps)) {
                    maps.putAll(verifyMaps);
                }

                for (int i = 0; i < packages.length; i++) {
                    String packageName = packages[i];

                    // 在debug环境下,如果包名中含有test直接通过
                    if(ConstantsOpenSdk.isDebug && packageName.contains("test")) {
                        isChecked = true;
                        connectPackageName = packageName;

//                        Bundle resultBundle = new Bundle();
//                        resultBundle.putInt(DATA_TYPE_XM_APP_VERSION,  AppUtils.getAppVersionCode());
//                        resultBundle.putBoolean(DATA_TYPE_VERIFY_RESULT ,isChecked);
                        return getVerifyBundleInfo();
                    }

                    String signature = maps.get(packageName);
                    if(TextUtils.isEmpty(signature)) {
                        continue;
                    }

                    PackageInfo packageInfo = null;
                    try {
                        packageInfo = getPackageManager().getPackageInfo(packageName, PackageManager.GET_SIGNATURES);
                        Signature[] signatures = packageInfo.signatures;
                        connectPackageName = packageName;

                        isChecked = containAppSignature(signatures, signature);

//                        Bundle resultBundle = new Bundle();
//                        resultBundle.putInt(DATA_TYPE_XM_APP_VERSION,  AppUtils.getAppVersionCode());
//                        resultBundle.putBoolean(DATA_TYPE_VERIFY_RESULT ,isChecked);
                        return getVerifyBundleInfo();
                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                }
            }

//            Bundle resultBundle = new Bundle();
//            resultBundle.putInt(DATA_TYPE_XM_APP_VERSION,  AppUtils.getAppVersionCode());
//            resultBundle.putBoolean(DATA_TYPE_VERIFY_RESULT ,isChecked);

            trace(connectPackageName, "init", null);

            return getVerifyBundleInfo();
        }

        @Override
        public Bundle execute(String action, Bundle bundle) throws RemoteException {
            Logger.logToFile("XiaoaiControllService == execute" + action + "   bundle=" + bundle);

            XmPlayerService playerService = XmPlayerService.getPlayerSrvice();

            if(!isChecked) {
                Bundle resultBundle = new Bundle();
                resultBundle.putInt(RESULT_CODE ,ErrorCodes.ERROR_VERIFY_ERROR);
                return resultBundle;
            }

            if (bundle != null) {
                // 不能删除,主要是进行一个 unparcel 的操作
                bundle.get("null");
            }

            trace(connectPackageName, action, bundle != null ? bundle.toString() : null);

            switch (action) {
                case ACTION_IS_PLAYING: {
                    Bundle resultBundle = new Bundle();
                    resultBundle.putBoolean(DATA_TYPE_IS_PLAYING,
                            playerService != null && playerService.isPlaying());
                    return resultBundle;
                }
                case ACTION_PLAY:
                    if (playerService != null) {
                        playerService.startPlay();
                    }
                    break;
                case ACTION_PAUSE:
                    if (playerService != null) {
                        playerService.pausePlay(false, PauseReason.Common.XIAOAI_ACTION_PAUSE);
                    }
                    break;
                case ACTION_PLAY_NEXT:
                    if (playerService != null) {
                        playerService.playNext();
                    }
                    break;
                case ACTION_HAS_NEXT: {
                    Bundle resultBundle = new Bundle();
                    boolean hasNext = false;

                    if (playerService != null
                            && (playerService.getPlayMode() == XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP || playerService.getPlayMode() == XmPlayListControl.PlayMode.PLAY_MODEL_LIST)
                            && playerService.hasNextSound()) {
                        hasNext = true;
                    }

                    resultBundle.putBoolean(DATA_TYPE_HAS_NEXT, hasNext);
                    return resultBundle;
                }
                case ACTION_PLAY_PRE:
                    if (playerService != null) {
                        playerService.playPre();
                    }
                    break;
                case ACTION_HAS_PRE: {
                    Bundle resultBundle = new Bundle();
                    boolean hasPre = false;
                    if (playerService != null
                            && (playerService.getPlayMode() == XmPlayListControl.PlayMode.PLAY_MODEL_LIST_LOOP || playerService.getPlayMode() == XmPlayListControl.PlayMode.PLAY_MODEL_LIST)
                            && playerService.hasPreSound()) {
                        hasPre = true;
                    }
                    resultBundle.putBoolean(DATA_TYPE_HAS_PRE, hasPre);
                    return resultBundle;
                }
                case ACTION_STOP:
                    if (playerService != null) {
                        playerService.stopPlay(PauseReason.StopBusiness.XiaoAiControl);
                    }
                    break;
                case ACTION_GET_CURRENT_SONG: {
                    Song song = null;
                    if (playerService != null) {
                        PlayableModel currSound = playerService.getCurrPlayModel();
                        if (currSound instanceof Track && PlayableModel.KIND_TRACK.equals(currSound.getKind())) {
                            song = Song.fromTrack((Track) currSound);
                        }
                    }

                    Bundle resultBundle = new Bundle();
                    resultBundle.putParcelable(DATA_TYPE_GET_CURRENT_SONG, song);
                    return resultBundle;
                }
                case ACTION_REMOVE_SOUND_LIST:
                    if (playerService != null) {
                        playerService.removeListByIndex(bundle.getInt(DATA_TYPE_REMOVE_INDEX));
                    }
                    break;
                case ACTION_GET_TOTAL_TIME: {
                    Bundle resultBundle = new Bundle();
                    resultBundle.putLong(DATA_TYPE_TOTAL_TIME,
                            playerService != null ? playerService.getDuration() : 0);
                    return resultBundle;
                }
                case ACTION_GET_CURRENT_TIME: {
                    Bundle resultBundle = new Bundle();
                    resultBundle.putLong(DATA_TYPE_CURRENT_TIME, playerService != null ? playerService.getPlayCurrPosition() : 0);
                    return resultBundle;
                }
                case ACTION_GET_PLAY_MODE: {
                    Bundle resultBundle = new Bundle();
                    resultBundle.putInt(DATA_TYPE_PLAY_MODE,
                            playerService != null ? playerService.getXmPlayMode().ordinal() : 0);
                    return resultBundle;
                }
                case ACTION_SET_PLAY_MODE:
                    int playModel = bundle.getInt(DATA_TYPE_PLAY_MODE);
                    if (playerService != null) {
                        playerService.setPlayMode(XmPlayListControl.PlayMode.getIndex(playModel));
                    }
                    break;
                case ACTION_SET_SET_MOBILE_PLAY_ENABLE:
                    int mobilePlayEnable = bundle.getInt(DATA_TYPE_MOBILE_PLAY_ENABLE);
                    if (mobilePlayEnable == 0) {
                        NetworkUtils.isAllowUse3G = false;
                    } else if (mobilePlayEnable == 1) {
                        NetworkUtils.isAllowUse3G = true;
                    } else if (mobilePlayEnable == 2) {
                        MmkvCommonUtil
                                .getInstance(
                                        MainApplication.getMyApplicationContext())
                                .saveBoolean(PreferenceConstantsInHost.TINGMAIN_KEY_WITHOUT_WIFI,
                                        true);
                    }
                    break;
                case ACTION_TO_PLAY_INDEX:
                    int anInt = bundle.getInt(DATA_TYPE_TO_PLAY_INDEX);

                    if (playerService != null) {
                        playerService.play(anInt);
                    }
                    break;
                case ACTION_START_OTHER_ACTIVITY:
                    String uri = bundle.getString(DATA_TYPE_START_OTHER_ACTIVITY_URI);
                    final Intent intent = new Intent(Intent.ACTION_VIEW);
                    intent.setData(Uri.parse(uri));
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    ComponentName componentName = intent.resolveActivity(getPackageManager());
                    if (componentName != null && !TextUtils.isEmpty(connectPackageName)
                            && connectPackageName.equals(componentName.getPackageName())) {
                        startActivity(intent);
                    }
                    break;
                case ACTION_SEEK_TO:
                    int position = bundle.getInt(DATA_TYPE_POSITION);
                    if (playerService != null) {
                        playerService.seekTo(position);
                    }
                    break;
                case ACTION_GET_PLAY_LIST_BY_PAGE:
                    XmPlayerService playerService1 = XmPlayerService.getPlayerSrvice();
                    if (playerService1 != null && playerService1.getPlayListControl() != null) {
                        XmPlayListControl playListControl = playerService1.getPlayListControl();
                        if (playListControl.getPlayListLoadStateListener() == null) {
                            playListControl.setPlayListLoadStateListener(mPlayListLoadStateCallback);
                        }

                        if (bundle == null || bundle.getBoolean(DATA_TYPE_LOAD_NEXT, true)) {
                            playListControl.getNextPlayList(false);
                        } else {
                            playListControl.getPrePlayList(false);
                        }
                    }
                    break;
                case ACTION_GET_SPEED_MODE:
                    ArrayList<String> strings = new ArrayList<>(Arrays.asList(mTempoStr));
                    Bundle speedBundle = new Bundle();
                    speedBundle.putStringArrayList(DATA_TYPE_SPEED_MODE_LIST,strings);
                    return speedBundle;
                case ACTION_SET_SPEED_MODE:
                    float tempo = bundle.getFloat(DATA_TYPE_SPEED_MODE);
                    Bundle speedResultBundle = new Bundle();
                    if (playerService!=null){
                        playerService.setTempo(tempo);
                        speedResultBundle.putInt(RESULT_CODE , ErrorCodes.ERROR_OK);
                    } else  {
                        speedResultBundle.putInt(RESULT_CODE , ErrorCodes.ERROR_PLAY_UNKNOWN);
                    }
                    return speedResultBundle;
                case ACTION_GET_CURRENT_SPEED_MODE:
                    //当前倍速
                    Bundle curSpeedBundle = new Bundle();
                    if (playerService != null && playerService.mPlayerControl != null) {
                        float curTempo = playerService.mPlayerControl.getTempo();
                        curSpeedBundle.putFloat(DATA_TYPE_CURRENT_SPEED_MODE,curTempo);
                    }
                    return curSpeedBundle;
                case ACTION_GET_AUTO_STOP_MODE:
                    ArrayList<Integer> timeOffList = new ArrayList<>(Arrays.asList(mTimeOffArr));
                    Bundle timeOffBundle = new Bundle();
                    timeOffBundle.putIntegerArrayList(DATA_TYPE_AUTO_STOP_MODE_LIST, timeOffList);
                    return timeOffBundle;
                case ACTION_SET_AUTO_STOP_MODE:
                    int mode = bundle.getInt(DATA_TYPE_AUTO_STOP_MODE);
                    PlanTerminateManagerForPlay.getInstance().startTimerForPlay(mode);
                    break;
                case ACTION_GET_PLAY_LIST_CUR_PAGE_ID:
                    Bundle playListPageBundle = new Bundle();
                    if (playerService != null && playerService.mPlayerControl != null) {
                        XmPlayListControl playListControl = playerService.getPlayListControl();
                        int nextPageId = playListControl.getNextPageId();
                        playListPageBundle.putInt(DATA_GET_CUR_PAGE_ID,nextPageId);
                    }
                    return playListPageBundle;
                case ACTION_GET_PLAY_SERVICE_ACTIVE:
                    Bundle resultBundleService = new Bundle();
                    if (playerService == null) {
                        resultBundleService.putBoolean(DATA_TYPE_PLAY_SERVICE_ACTIVE, false);
                    } else {
                        resultBundleService.putBoolean(DATA_TYPE_PLAY_SERVICE_ACTIVE, true);
                    }
                    return resultBundleService;
                case ACTION_GET_LAST_PLAY_TRACK_IN_ALBUM:
                    long albumId = 0;
                    if (bundle != null) {
                        albumId = bundle.getLong(XmControlConstants.DATA_TYPE_PLAY_ALBUMID, 0);
                    }
                    Song song = null;
                    if (playerService != null) {
                        Track lastPlayTrackInAlbum = playerService.getLastPlayTrackInAlbum(albumId);
                        if (lastPlayTrackInAlbum != null) {
                            song = Song.fromTrack(lastPlayTrackInAlbum);
                        }
                    }
                    Bundle lastPlayTrackBundle = new Bundle();
                    lastPlayTrackBundle.putParcelable(DATA_TYPE_LAST_PLAY_TRACK, song);
                    return lastPlayTrackBundle;
                default: {
                    Bundle resultBundle = new Bundle();
                    resultBundle.putInt(RESULT_CODE, ErrorCodes.ERROR_API_UNSUPPORTED_ACTION);
                    return resultBundle;
                }
            }

            return null;
        }

        @Override
        public void executeAsync(String action, Bundle bundle, IXmApiCallback callback) throws RemoteException {
            Logger.logToFile("XiaoaiControllService == executeAsync" + action + "   bundle=" + bundle);
            if (!isChecked) {
                onReturnErrorCode(callback, ErrorCodes.ERROR_VERIFY_ERROR);
            }

            trace(connectPackageName, action, bundle != null ? bundle.toString() : null);

            switch (action) {
                case ACTION_PLAY_SONG_LIST: {
                    if (bundle == null) {
                        return;
                    }
                    Map<String, String> params = new HashMap<>();
                    String soundList = bundle.getString(DATA_TYPE_SOUND_LIST);
                    params.put("trackIds", soundList);
                    int position = bundle.getInt(DATA_TYPE_PLAY_INDEX);
                    traceOPPO(soundList, position);
                    CommonRequestM.getTrackInfoListDetailV2(params, new IDataCallBack<List<Track>>() {
                        @Override
                        public void onSuccess(@Nullable List<Track> object) {
                            if (ToolUtil.isEmptyCollects(object)) {
                                onReturnErrorCode(callback,
                                        ErrorCodes.ERROR_FAILED_TO_QUERY_FROM_SONG_ID);
                            } else {
                                playTrackList(object, position, true, null, callback);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_GET_DATA_NETWORK);
                        }
                    });
                    break;
                }
                case ACTION_PLAY_LIKE_SOUND: {
                    int playIndex = 0;
                    if (bundle != null) {
                        playIndex = bundle.getInt(DATA_TYPE_START_POSITION, 0);
                    }

                    Map<String, String> params = new HashMap<>();
                    if (!UserInfoMannage.hasLogined()) {
                        onReturnErrorCode(callback, ErrorCodes.ERROR_NEED_USER_AUTHENTICATION);
                        return;
                    } else {
                        params.put("toUid", UserInfoMannage.getUid() + "");
                    }

                    params.put(HttpParamsConstants.PARAM_PAGE_ID, "" + 1);
                    params.put(HttpParamsConstants.PARAM_PAGE_SIZE, "" + 200);
                    params.put(HttpParamsConstants.PARAM_DEVICE, "android");
                    int finalPlayIndex = playIndex;
                    CommonRequestM.getUserFavorTrack(params,
                            new IDataCallBack<ListModeBase<TrackM>>() {
                        @Override
                        public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                            if (object == null || ToolUtil.isEmptyCollects(object.getList())) {
                                onReturnErrorCode(callback, ErrorCodes.ERROR_NO_HAVE_LIKE_SONG);
                            } else {
                                List<TrackM> list = object.getList();

                                for (TrackM trackM : list) {
                                    trackM.setLike(true);
                                }

                                List<Track> trackList = new ArrayList<>(list);
                                playTrackList(trackList, finalPlayIndex, true, object.getParams(), callback);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_GET_DATA_NETWORK);
                        }
                    });
                    break;
                }
                case ACTION_PLAY_BY_ALBUMID: {
                    long albumId = 0;
                    if (bundle != null) {
                        albumId = bundle.getLong(XmControlConstants.DATA_TYPE_PLAY_ALBUMID, 0);
                    }

                    boolean useHistory = false;
                    if (bundle != null) {
                        useHistory =
                                bundle.getBoolean(XmControlConstants.DATA_TYPE_PLAY_USE_HISTORY,
                                        true);
                    }

                    Context context = getApplicationContext();

                    long finalAlbumId = albumId;
                    boolean finalUseHistory = useHistory;
                    checkConnectState(context, new IServiceState() {
                        @Override
                        public void onServiceConnect() {
                            // 播放历史
                            playByAlbumById(finalAlbumId, finalUseHistory, callback);
                        }

                        @Override
                        public void onServiceTimeout() {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_PLAY_UNKNOWN);
                        }
                    });
                    break;
                }
                case XmControlConstants.ACTION_GET_ALBUM_LIST:
                    //获取专辑列表
                    if (!UserInfoMannage.hasLogined()) {
                        onReturnErrorCode(callback, ErrorCodes.ERROR_NEED_USER_AUTHENTICATION);
                        return;
                    }
                    getRecentAlbumList(callback);
                    break;
                case ACTION_GET_CURRENT_TIME_SYNC:
                    XmPlayerService services = XmPlayerService.getPlayerSrvice();
                    if (services != null) {
                        int duration = services.getPlayCurrPosition();
                        if (duration > 0) {
                            Bundle resultBundle = new Bundle();
                            resultBundle.putLong(DATA_TYPE_CURRENT_TIME, duration);
                            onReturn(callback,resultBundle);
                        } else {
                            int index = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getIntCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_INDEX, 0);
                            new MyAsyncTask<Void, Void, CommonTrackList<Track>>(){
                                @Override
                                protected CommonTrackList<Track> doInBackground(Void... params) {
                                    String playListStr = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getStringCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_LIST);
                                    if (!android.text.TextUtils.isEmpty(playListStr)) {
                                        try {

                                            Type objectType = new TypeToken<CommonTrackList<Track>>() {
                                            }.getType();
                                            CommonTrackList<Track> commonTrackList = new Gson().fromJson(
                                                    playListStr, objectType);
                                            return commonTrackList;
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    return null;
                                }

                                @Override
                                protected void onPostExecute(CommonTrackList<Track> commonTrackList) {
                                    if (commonTrackList != null) {
                                        List<Track> tracks = commonTrackList.getTracks();

                                        if (tracks != null) {
                                            if (index >= 0 && index < tracks.size()) {
                                                Track track = tracks.get(index);
                                                if (track != null) {
                                                    int soundHistoryPos = PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).
                                                            getSoundHistoryPos(track.getDataId());
                                                    Bundle resultBundle = new Bundle();
                                                    resultBundle.putLong(DATA_TYPE_CURRENT_TIME, soundHistoryPos);
                                                    onReturn(callback,resultBundle);
                                                }
                                            }
                                        }
                                    }
                                }
                            }.myexec();
                        }
                    }
                    break;
                case ACTION_GET_TOTAL_TIME_SYNC:
                    XmPlayerService service = XmPlayerService.getPlayerSrvice();
                    if (service != null) {
                        int duration = service.getDuration();
                        if (duration > 0) {
                            Bundle resultBundle = new Bundle();
                            resultBundle.putLong(DATA_TYPE_TOTAL_TIME, duration);
                            onReturn(callback,resultBundle);
                        } else {
                            int index = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getIntCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_INDEX, 0);
                            new MyAsyncTask<Void, Void, CommonTrackList<Track>>(){
                                @Override
                                protected CommonTrackList<Track> doInBackground(Void... params) {
                                    String playListStr = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getStringCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_LIST);
                                    if (!android.text.TextUtils.isEmpty(playListStr)) {
                                        try {

                                            Type objectType = new TypeToken<CommonTrackList<Track>>() {
                                            }.getType();
                                            CommonTrackList<Track> commonTrackList = new Gson().fromJson(
                                                    playListStr, objectType);
                                            return commonTrackList;
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    return null;
                                }

                                @Override
                                protected void onPostExecute(CommonTrackList<Track> commonTrackList) {
                                    if (commonTrackList != null) {
                                        List<Track> tracks = commonTrackList.getTracks();

                                        if (tracks != null) {
                                            if (index >= 0 && index < tracks.size()) {
                                                Track track = tracks.get(index);
                                                if (track != null) {
                                                    Bundle resultBundle = new Bundle();
                                                    resultBundle.putLong(DATA_TYPE_TOTAL_TIME, track.getDuration() * 1000);
                                                    onReturn(callback,resultBundle);
                                                }
                                            }
                                        }
                                    }
                                }
                            }.myexec();
                        }
                    }
                    break;
                case ACTION_GET_CURRENT_SONG_SYNC: {
                    Song song = null;
                    XmPlayerService playerService = XmPlayerService.getPlayerSrvice();
                    if (playerService != null) {
                        PlayableModel currSound = playerService.getCurrPlayModel();
                        if (currSound != null && currSound instanceof Track && PlayableModel.KIND_TRACK.equals(currSound.getKind())) {
                            song = Song.fromTrack((Track) currSound);
                            Bundle resultBundle = new Bundle();
                            resultBundle.setClassLoader(Song.class.getClassLoader());
                            resultBundle.putParcelable(DATA_TYPE_GET_CURRENT_SONG, song);
                            onReturn(callback,resultBundle);
                        } else {
                            int index = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getIntCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_INDEX, 0);
                            new MyAsyncTask<Void, Void, CommonTrackList<Track>>(){
                                @Override
                                protected CommonTrackList<Track> doInBackground(Void... params) {
                                    String playListStr = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getStringCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_LIST);
                                    if (!android.text.TextUtils.isEmpty(playListStr)) {
                                        try {

                                            Type objectType = new TypeToken<CommonTrackList<Track>>() {
                                            }.getType();
                                            CommonTrackList<Track> commonTrackList = new Gson().fromJson(
                                                    playListStr, objectType);
                                            return commonTrackList;
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    return null;
                                }

                                @Override
                                protected void onPostExecute(CommonTrackList<Track> commonTrackList) {
                                    if (commonTrackList != null) {
                                        List<Track> tracks = commonTrackList.getTracks();

                                        ArrayList<Song> songList = new ArrayList<>();
                                        if (tracks != null) {
                                            for (Track track : tracks) {
                                                songList.add(Song.fromTrack(track));
                                            }
                                        }
                                        if (index >= 0 && index < songList.size()) {
                                            Song song1 = songList.get(index);
                                            Bundle resultBundle = new Bundle();
                                            resultBundle.setClassLoader(Song.class.getClassLoader());
                                            resultBundle.putParcelable(DATA_TYPE_GET_CURRENT_SONG, song1);
                                            onReturn(callback,resultBundle);
                                        }
                                    }
                                }
                            }.myexec();
                        }
                    }
                    break;
                }
                case ACTION_GET_SONG_LIST:
                    List<Track> playList = null;

                    XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                    if (playerSrvice != null && playerSrvice.getPlayListControl() != null) {
                        playList = playerSrvice.getPlayListControl().getPlayList();
                        ArrayList<Song> songList = new ArrayList<>();
                        if (playList != null) {
                            for (Track track : playList) {
                                songList.add(Song.fromTrack(track));
                            }
                        }

                        Bundle resultBundle = new Bundle();
                        resultBundle.putParcelableArrayList(DATA_TYPE_SONG_LIST, songList);
                        onReturn(callback, resultBundle);
                    } else {
                        new MyAsyncTask<Void, Void, CommonTrackList<Track>>(){
                            @Override
                            protected CommonTrackList<Track> doInBackground(Void... params) {
                                String playListStr = PlayListMMKVUtil.getInstance(ToolUtil.getCtx()).getStringCompat(PreferenceConstantsInOpenSdk.XFramework_KEY_HISTORY_PLAY_LIST);
                                if (!android.text.TextUtils.isEmpty(playListStr)) {
                                    try {

                                        Type objectType = new TypeToken<CommonTrackList<Track>>() {
                                        }.getType();
                                        CommonTrackList<Track> commonTrackList = new Gson().fromJson(
                                                playListStr, objectType);
                                        return commonTrackList;
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                return null;
                            }

                            @Override
                            protected void onPostExecute(CommonTrackList<Track> commonTrackList) {
                                if (commonTrackList != null) {
                                    List<Track> tracks = commonTrackList.getTracks();

                                    ArrayList<Song> songList = new ArrayList<>();
                                    if (tracks != null) {
                                        for (Track track : tracks) {
                                            songList.add(Song.fromTrack(track));
                                        }
                                    }

                                    Bundle resultBundle = new Bundle();
                                    resultBundle.putParcelableArrayList(DATA_TYPE_SONG_LIST, songList);
                                    onReturn(callback, resultBundle);
                                }
                            }
                        }.myexec();
                    }
                    break;
                case ACTION_ADD_TO_FAVOURITE:
                case ACTION_REMOVE_FAVOURITE:
                    if (UserInfoMannage.hasLogined()) {
                        if (bundle == null) {
                            return;
                        }
                        long trackId = bundle.getLong(DATA_TYPE_TRACK_ID);
                        LikeTrackManage.toLikeOrUnLike(trackId,
                                ACTION_ADD_TO_FAVOURITE.equals(action),
                                new IDataCallBack<Boolean>() {
                                    @Override
                                    public void onSuccess(@Nullable Boolean object) {
                                        if (object != null && object) {
                                            List<Track> trackList = null;

                                            XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                                            if (playerSrvice != null && playerSrvice.getPlayListControl() != null) {
                                                trackList = playerSrvice.getPlayListControl().getPlayList();
                                            }

                                            if (trackList != null) {
                                                for (int i = 0; i < trackList.size(); i++) {
                                                    Track historyModel = trackList.get(i);
                                                    if (historyModel != null
                                                            && historyModel.getDataId() == trackId) {
                                                        historyModel.setLike(ACTION_ADD_TO_FAVOURITE.equals(action));
                                                        playerSrvice.getPlayListControl().updateTrackInPlayList(i, historyModel);
                                                        NotificationLikeManager.INSTANCE.onTrackLikeStateChange();
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        onReturnErrorCode(callback, ErrorCodes.ERROR_OK);
                                    }

                                    @Override
                                    public void onError(int code, String message) {
                                        onReturnErrorCode(callback,
                                                ErrorCodes.ERROR_GET_DATA_NETWORK);
                                    }
                                });
                    } else {
                        onReturnErrorCode(callback, ErrorCodes.ERROR_NEED_USER_AUTHENTICATION);
                    }
                    break;
                case ACTION_ADD_TRACK_COLLECT:
                case ACTION_REMOVE_TRACK_COLLECT:
                    if (UserInfoMannage.hasLogined()) {
                        if (bundle == null) {
                            return;
                        }
                        long trackId = bundle.getLong(DATA_TYPE_TRACK_ID);
                        TrackCollectManager.getInstance().requestCollectOrUnCollect(ACTION_REMOVE_TRACK_COLLECT.equals(action), trackId, new IDataCallBack<Boolean>() {
                            @Override
                            public void onSuccess(@Nullable Boolean data) {
                                onReturnErrorCode(callback, ErrorCodes.ERROR_OK);
                            }

                            @Override
                            public void onError(int code, String message) {
                                onReturnErrorCode(callback,
                                        ErrorCodes.ERROR_GET_DATA_NETWORK);
                            }
                        });

                    }  else {
                        onReturnErrorCode(callback, ErrorCodes.ERROR_NEED_USER_AUTHENTICATION);
                    }
                    break;
                case ACTION_QUERY_TRACK_COLLECT:
                    if (UserInfoMannage.hasLogined()) {
                        if (bundle == null) {
                            return;
                        }
                        long trackId = bundle.getLong(DATA_TYPE_TRACK_ID);
                        HashMap<String, String> params = new HashMap<>();
                        params.put("device", "android");
                        params.put("trackId", String.valueOf(trackId));
                        CommonRequestM.getPlayPageInfoNew(trackId, params, new IDataCallBack<PlayingSoundInfo>() {
                            @Override
                            public void onSuccess(@Nullable PlayingSoundInfo data) {
                                if (data != null && data.otherInfo != null) {
                                    Bundle resultBundleNew = new Bundle();
                                    resultBundleNew.putBoolean(DATA_TYPE_TRACK_IS_COLLECT, data.otherInfo.isCollect);
                                    onReturn(callback, resultBundleNew);
                                } else {
                                    onReturnErrorCode(callback,
                                            ErrorCodes.ERROR_GET_DATA_NETWORK);
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                onReturnErrorCode(callback,
                                        ErrorCodes.ERROR_GET_DATA_NETWORK);
                            }
                        });
                    }  else {
                        Bundle resultBundleNew = new Bundle();
                        resultBundleNew.putBoolean(DATA_TYPE_TRACK_IS_COLLECT, false);
                        onReturn(callback, resultBundleNew);
                    }
                    break;
                case ACTION_GET_SUBSCRIBE_LIST:
                    //最新订阅列表
                    if (UserInfoMannage.hasLogined()) {
                        if (bundle == null) {
                            return;
                        }
                        int pageId = bundle.getInt(DATA_TYPE_PAGE_ID);
                        loadSubscribeListByRecent(pageId,callback);
                    } else {
                        onReturnErrorCode(callback, ErrorCodes.ERROR_NEED_USER_AUTHENTICATION);
                    }
                    break;
                case ACTION_SEARCH_PLAY_HISTORY:
                case ACTION_PLAY_HISTORY: {
                    boolean playHistory = ACTION_PLAY_HISTORY.equals(action);

                    checkConnectState(XiaoaiControllService.this, new IServiceState() {
                        @Override
                        public void onServiceConnect() {
                            loadHistory(bundle, callback, playHistory);
                        }

                        @Override
                        public void onServiceTimeout() {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_PLAY_UNKNOWN);
                        }
                    });

                    break;
                }
                case ACTION_PLAY_TRACK_BY_ID: {
                    if (bundle == null) {
                        return;
                    }
                    long trackId = bundle.getLong(DATA_TYPE_TRACK_ID);
                    if(trackId > 0) {
                        checkConnectState(XiaoaiControllService.this, new XiaoaiControllService.IServiceState() {
                            @Override
                            public void onServiceConnect() {
                                XiaoaiControllUtil.playTrackById(trackId, new IDataCallBack<TrackM>() {
                                    @Override
                                    public void onSuccess(@Nullable TrackM data) {

                                    }

                                    @Override
                                    public void onError(int code, String message) {
                                        onReturnErrorCode(callback, ErrorCodes.ERROR_GET_DATA_NETWORK);
                                    }
                                });
                            }

                            @Override
                            public void onServiceTimeout() {
                                onReturnErrorCode(callback, ErrorCodes.ERROR_PLAY_UNKNOWN);
                            }
                        });
                    }
                    break;
                }
                case ACTION_GET_TRACK_LIST_BY_ALBUM_ID: {
                    if (bundle == null) {
                        return;
                    }

                    int pageSize = bundle.getInt(DATA_TYPE_PAGE_SIZE);
                    long albumId = bundle.getLong(DATA_TYPE_PLAY_ALBUMID);
                    boolean loadHistoryPage = bundle.getBoolean(DATA_TYPE_LOAD_HISTORY_PAGE_ID);
                    getAlbumInfo(albumId, pageSize, loadHistoryPage, new IDataCallBack<AlbumM>() {
                        @Override
                        public void onSuccess(@Nullable AlbumM data) {
                            ArrayList<Song> songList = new ArrayList<>();
                            if (data != null && data.getCommonTrackList() != null
                                    && data.getCommonTrackList().getTracks() != null
                                    && !data.getCommonTrackList().getTracks().isEmpty()) {
                                List<TrackM> tracks = data.getCommonTrackList().getTracks();
                                for (Track track : tracks) {
                                    songList.add(Song.fromTrack(track));
                                }
                            }

                            Bundle resultBundle = new Bundle();
                            resultBundle.putParcelableArrayList(DATA_TYPE_SONG_LIST, songList);
                            onReturn(callback, resultBundle);
                        }

                        @Override
                        public void onError(int code, String message) {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_GET_DATA_NETWORK);
                        }
                    });
                    break;
                }
                default:
                    onReturnErrorCode(callback, ErrorCodes.ERROR_API_UNSUPPORTED_ACTION);
                    break;
            }
        }

        private void loadHistory(Bundle bundle, IXmApiCallback callback, boolean playHistory) {
            Bundle resultBundle = new Bundle();

            if(!playHistory) {
                long[] albumIds = null;
                boolean searchedToPlay = false;
                if(bundle != null) {
                    albumIds = bundle.getLongArray(XmControlConstants.DATA_TYPE_ALBUM_IDS);
                    searchedToPlay = bundle.getBoolean(XmControlConstants.DATA_TYPE_HAS_SEARCH_RESULT_TO_PLAY ,false);
                }

                if(albumIds == null || albumIds.length <= 0) {
                    resultBundle.putLongArray(XmControlConstants.DATA_TYPE_SEARCH_HISTORY_RESULT ,null);
                    onReturn(callback , resultBundle);
                    return;
                }

                long[] finalAlbumIds = albumIds;
                boolean finalSearchedToPlay = searchedToPlay;

                new LoadHistoryTask(){
                    @Override
                    protected void onPostExecute(List<HistoryModel> historyModels) {
                        if(ToolUtil.isEmptyCollects(historyModels)) {
                            resultBundle.putLongArray(XmControlConstants.DATA_TYPE_SEARCH_HISTORY_RESULT ,null);
                            onReturn(callback , resultBundle);
                            return;
                        }

                        Set<Long> same = new HashSet<Long>();
                        Set<Long> temp = new HashSet<Long>();
                        for (int i = 0; i < finalAlbumIds.length; i++) {
                            temp.add(finalAlbumIds[i]);
                        }

                        boolean isFirstSearched = false;
                        for (HistoryModel history : historyModels) {
                            if(history != null && !temp.add(history.getAlbumId())) {
                                if(!isFirstSearched) {
                                    isFirstSearched = true;
                                    if(finalSearchedToPlay) {
                                        //设置声音的播放点
                                        final Track t = history.getTrack();
                                        playTrackByHistory(t, true, new IPlayHistoryCallBack() {
                                            @Override
                                            public void onError() {

                                            }

                                            @Override
                                            public void onSuccess() {

                                            }
                                        });
                                    }
                                }
                                same.add(history.getAlbumId());
                            }
                        }

                        long[] arr = new long[same.size()];

                        Iterator<Long> iterator = same.iterator();

                        int index = 0;
                        while (iterator.hasNext()) {
                            Long next = iterator.next();
                            if(next != null) {
                                arr[index] = next;
                            }
                            index ++;
                        }

                        resultBundle.putLongArray(XmControlConstants.DATA_TYPE_SEARCH_HISTORY_RESULT ,arr);
                        onReturn(callback , resultBundle);

                        super.onPostExecute(historyModels);
                    }
                }.myexec();
            } else {
                new LoadHistoryTask(){
                    @Override
                    protected void onPostExecute(List<HistoryModel> historyModels) {
                        if(ToolUtil.isEmptyCollects(historyModels)) {
                            onReturnErrorCode(callback, ErrorCodes.ERROR_NO_HISTORY);
                            return;
                        }

                        for (HistoryModel history : historyModels) {
                            if(history != null) {
                                final Track t = history.getTrack();
                                if(t == null) {
                                    continue;
                                }

                                boolean autoPlay = true;

                                if (bundle != null) {
                                    autoPlay = bundle.getBoolean(XmControlConstants.DATA_TYPE_AUTO_PLAY, true);
                                }

                                playTrackByHistory(t, autoPlay, new IPlayHistoryCallBack() {
                                    @Override
                                    public void onError() {
                                        onReturnErrorCode(callback , ErrorCodes.ERROR_PLAY_UNKNOWN);
                                    }

                                    @Override
                                    public void onSuccess() {
                                        resultBundle.putInt(RESULT_CODE, ErrorCodes.ERROR_OK);
                                        onReturnErrorCode(callback , ErrorCodes.ERROR_OK);
                                    }
                                });
                                return;
                            }
                        }
                        super.onPostExecute(historyModels);
                    }
                }.myexec();
            }
        }

        private int registerStatueSize = 0;
        @Override
        public void registerPlayStatueChangeListener(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
            if (!isChecked) {
                return;
            }

            try {
                mPlayStatusChangeCallBack.register(callBack);
            } catch (Exception e) {
                e.printStackTrace();
            }
            registerStatueSize ++;
            if (registerStatueSize > 0) {
                XmPlayerService.addPlayerStatusListenerOnPlayProcees(mPlayerStatusListener);
            }
        }

        @Override
        public void unRegisterPlayStatuChangeListener(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
            if (!isChecked) {
                return;
            }

            try {
                mPlayStatusChangeCallBack.unregister(callBack);
            } catch (Exception e) {
                e.printStackTrace();
            }

            registerStatueSize--;
            if (registerStatueSize == 0) {
                XmPlayerService.removePlayerStatusListenerOnPlayProcess(mPlayerStatusListener);
            }
        }

        @Override
        public void registerEventListener(List<String> events, IXmEventChangCallBack listener) throws RemoteException {
            if(!isChecked) {
                return;
            }

            mEventCallBack.register(listener);
            mRegsiterEventSet.addAll(events);
            onEventChange(mRegsiterEventSet);
        }

        @Override
        public void unregisterEventListener(List<String> events, IXmEventChangCallBack listener) throws RemoteException {
            if(!isChecked) {
                return;
            }

            mEventCallBack.unregister(listener);
            mRegsiterEventSet.removeAll(events);
            onEventChange(mRegsiterEventSet);
        }

        private void traceOPPO(String trackList, int position) {
            try {
                if (!"com.coloros.speechassist".equals(connectPackageName)) {
                    return;
                }
                String[] trackIds = trackList.split(",");
                String trackId = trackIds[position];
                // 语音助手启动播放  其他事件
                HashMap<String, String> props = new HashMap<>();
                props.put("trackId", trackId);
                props.put("prodId", "S_PROD32_2035");
                props.put("sceneName", "3");
                TraceModel traceModel = new TraceModel();
                traceModel.serviceId = "others";
                traceModel.metaId = 43370;
                traceModel.props = props;
                UbtSourceProvider.getInstance().sendTrace(traceModel);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取最近的专辑列表
     * @param callback
     */
    private void getRecentAlbumList(IXmApiCallback callback) {
        HistoryManagerForPlay historyManagerForPlay= (HistoryManagerForPlay) RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
        if (historyManagerForPlay == null) {
            debugLog("getRecentAlbumList s1, historyManagerForPlay is null ");
            return;
        }

        List<HistoryModel> trackList = historyManagerForPlay.getTrackList();
        if (trackList == null || trackList.isEmpty()) {
            debugLog("getRecentAlbumList s2, trackList is null or empty");
            return;
        }

        //找出所属专辑
        HashSet<Album> albumSet = new HashSet<>();
        for (HistoryModel historyModel : trackList) {
            Track track = historyModel.getTrack();
            if (track != null && track.getAlbum() != null) {
                SubordinatedAlbum trackAlbum = track.getAlbum();

                Album album = new Album();
                album.setId(trackAlbum.getAlbumId());
                album.setTitle(trackAlbum.getAlbumTitle());
                album.setCoverUri(trackAlbum.getCoverUrlMiddle());

                albumSet.add(album);
            }
        }

        debugLog("getRecentAlbumList >>> albumSet size: " + albumSet.size() + ", track size: " + trackList.size());

        Bundle resultBundle = new Bundle();
        resultBundle.putInt(RESULT_CODE ,ErrorCodes.ERROR_OK);
        resultBundle.putParcelableArrayList(XmControlConstants.DATA_TYPE_ALBUM_LIST, new ArrayList<>(albumSet));

        debugLog("getRecentAlbumList >>> test get: " + resultBundle.getParcelableArrayList(XmControlConstants.DATA_TYPE_ALBUM_LIST));

        onReturn(callback, resultBundle);
    }

    private void debugLog(String s) {
        if (ConstantsOpenSdk.isDebug) {
            Log.d("OppoScreen", s);
        }
    }

    private boolean eventPlayListChange;

    private void onEventChange(Set<String> eventSet) {
        if (eventSet != null) {
            eventPlayListChange = eventSet.contains(XM_API_EVENT_PLAY_LIST_CHANGED);
        }
    }

    private final XmPlayerManager.IOnPlayListChange playListChange = new XmPlayerManager.IOnPlayListChange() {
        @Override
        public void onPlayListChange() {
            if (!eventPlayListChange) {
                return;
            }
            sendEventChange(XM_API_EVENT_PLAY_LIST_CHANGED ,null);
        }
    };

    private boolean processOnHandler() {
        return MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.ITEMS_XIAOAI_SERVICE_PROCESS_ON_HANDLER, false);
    }

    private final IXmPlayerStatusListener mPlayerStatusListener = new IXmPlayerStatusListener() {
        @Override
        public void onPlayStart() {
            NotificationManager manager =
                    (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if(manager == null){
                return;
            }
            try {
                manager.cancel(notificationId);
            } catch (Exception e) {
                e.printStackTrace();
            }

            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onPlayStart();
                }
            });
        }

        @Override
        public void onPlayPause() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onPlayPause();
                }
            });
        }

        @Override
        public void onPlayStop() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onPlayStop();
                }
            });
        }

        @Override
        public void onSoundPlayComplete() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onSoundPlayComplete();
                }
            });
        }

        @Override
        public void onSoundPrepared() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onSoundPrepared();
                }
            });
        }

        @Override
        public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onSoundSwitch(lastModel instanceof Track ? Song.fromTrack((Track) lastModel) : null
                            ,curModel instanceof Track ? Song.fromTrack((Track) curModel) : null);
                }
            });
        }

        @Override
        public void onBufferingStart() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onBufferingStart();
                }
            });
        }

        @Override
        public void onBufferingStop() {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onBufferingStop();
                }
            });
        }

        @Override
        public void onBufferProgress(int percent) {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onBufferProgress(percent);
                }
            });
        }

        @Override
        public void onPlayProgress(int currPos, int duration) {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onPlayProgress(currPos ,duration);
                }
            });
        }

        @Override
        public boolean onError(XmPlayerException exception) {
            onPlayStatusChange(new IHandleOkCallBack() {
                @Override
                public void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException {
                    callBack.onError();
                }
            });
            return false;
        }
    };

    interface IHandleOkCallBack {
        void onReady(IXmPlayStatusChangeCallBack callBack) throws RemoteException;
    }

    private void onPlayStatusChange(IHandleOkCallBack handleOk) {
        if (processOnHandler() && mHandler != null) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    onPlayStatusChangeInner(handleOk);
                }
            });
        } else {
            onPlayStatusChangeInner(handleOk);
        }
    }

    private void onPlayStatusChangeInner(IHandleOkCallBack handleOk) {
        synchronized (mLock) {
            int N = mPlayStatusChangeCallBack.beginBroadcast();
            for (int i = 0; i < N; i++) {
                IXmPlayStatusChangeCallBack l = mPlayStatusChangeCallBack.getBroadcastItem(i);
                try {
                    handleOk.onReady(l);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            mPlayStatusChangeCallBack.finishBroadcast();
        }
    }

    private byte[] mLock = new byte[0];

    private void sendEventChange(String event, Bundle bundle) {
        synchronized (mLock) {
            int N = mEventCallBack.beginBroadcast();
            for (int i = 0; i < N; i++) {
                IXmEventChangCallBack l = mEventCallBack
                        .getBroadcastItem(i);
                try {
                    l.onEvnet(event, bundle);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            mEventCallBack.finishBroadcast();
        }
    }

    private void onReturn(IXmApiCallback callback , Bundle bundle) {
        if(callback == null) {
            return;
        }

        synchronized (mLock) {
            mXmApiCallback.register(callback);
            int N = mXmApiCallback.beginBroadcast();
            for (int i = 0; i < N; i++) {
                IXmApiCallback l = mXmApiCallback
                        .getBroadcastItem(i);
                try {
                    l.onReturn(bundle);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            mXmApiCallback.finishBroadcast();
            mXmApiCallback.unregister(callback);
        }
    }

    private void onReturnErrorCode(IXmApiCallback callback , int errorCode) {
        if(callback == null) {
            return;
        }

        synchronized (mLock) {
            mXmApiCallback.register(callback);
            int N = mXmApiCallback.beginBroadcast();
            for (int i = 0; i < N; i++) {
                IXmApiCallback l = mXmApiCallback
                        .getBroadcastItem(i);
                try {
                    Bundle bundle = new Bundle();
                    bundle.putInt(RESULT_CODE ,errorCode);
                    l.onReturn(bundle);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
            mXmApiCallback.finishBroadcast();
            mXmApiCallback.unregister(callback);
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        new UserTracking()
                .setId("5494")
                .setSrcChannel("mi")
                .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_VOICE_WAKEUP);

        XiaoaiControllUtil.getInstance().onStartCommand(intent ,this);
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        mService = null;
        unregisterReceiver(mLikeStateChangeBroadcastReceiver);

        stopForeground(true);

        NotificationManager manager =
                (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        if(manager == null){
            return;
        }
        try {
            manager.cancel(notificationId);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (mXiaoaiControlManager != null) {
            mXiaoaiControlManager.release();
            mXiaoaiControlManager = null;
        }
    }

    public static void sendEventChange(String event) {
        if(mService != null && mService.mXmControl != null) {
            mService.sendEventChange(event, null);
        }
    }

    public static void sendEventChangeHasBundle(String event, Bundle bundle) {
        if(mService != null && mService.mXmControl != null) {
            mService.sendEventChange(event, bundle);
        }
    }


    public static boolean containAppSignature(Signature[] signatures, String appSignature) {
        if (signatures != null && appSignature != null) {
            int tempSignaturesLength = signatures.length;

            for (int i = 0; i < tempSignaturesLength; ++i) {
                Signature signature = signatures[i];
                String s = MD5.hexdigest(signature.toByteArray());
                Logger.logToFile("XiaoaiControllService : containAppSignature " + s  + "   " + appSignature);
                if (appSignature.toLowerCase(Locale.getDefault()).contains(s.toLowerCase(Locale.getDefault()))) {
                    return true;
                }
            }

            return false;
        }

        return false;
    }

    private void playByAlbumById(long albumId ,boolean useHistory ,IXmApiCallback dataCallBack) {
        if (useHistory) {
            XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
            Track trackByHistory = null;
            if (playerSrvice != null) {
                trackByHistory = playerSrvice.getTrackByHistory(albumId);
                if (trackByHistory != null) {
                    trackByHistory = playerSrvice.getLastPlayTrackInAlbum(albumId);
                }
            }

            if (trackByHistory != null) {
                playTrackByHistory(trackByHistory, true, new IPlayHistoryCallBack() {
                    @Override
                    public void onError() {
                        onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_GET_DATA_NETWORK);
                    }

                    @Override
                    public void onSuccess() {
                        onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_OK);
                    }
                });
            } else {
                playByAlbumIdForNet(albumId, dataCallBack);
            }
        } else {
            playByAlbumIdForNet(albumId, dataCallBack);
        }
    }

    private void loadSubscribeListByRecent(int pageId, IXmApiCallback dataCallBack) {
        Bundle resultBundle = new Bundle();
        Map<String, String> params = new HashMap<>();
        params.put("pageId", String.valueOf(pageId));
        params.put("pageSize", "30");
        CommonRequestM.getMySubscribeListByUpdateXiaoAi(params, new IDataCallBack<WoTingAlbumItem>() {
            @Override
            public void onSuccess(final WoTingAlbumItem object) {

                if (object != null) {
                    LinkedHashSet<Album> albumSet = new LinkedHashSet<>();
                    if (object.getData() != null) {
                        List<com.ximalaya.ting.android.opensdk.model.album.Album> albumsList = object.getData().createAlbums();
                        for (com.ximalaya.ting.android.opensdk.model.album.Album album: albumsList) {
                            if (album != null) {
                                Album resuleAlbum = new Album();
                                resuleAlbum.setId(album.getId());
                                resuleAlbum.setTitle(album.getAlbumTitle());
                                resuleAlbum.setCoverUri(album.getCoverUrlMiddle());
                                albumSet.add(resuleAlbum);
                            }
                        }
                    }
                    resultBundle.putInt(RESULT_CODE ,ErrorCodes.ERROR_OK);
                    resultBundle.putParcelableArrayList(DATA_TYPE_SUBSCRIBE_LIST, new ArrayList<>(albumSet));
                    onReturn(dataCallBack,resultBundle);
                } else {
                    onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_GET_DATA_NETWORK);
                }
            }

            @Override
            public void onError(int code, String message) {
                onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_GET_DATA_NETWORK);
            }
        });
    }

    private void playByAlbumIdForNet(long albumId, IXmApiCallback dataCallBack) {
        HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_PAGE_ID, "1");
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, DTransferConstants.DEFAULT_PAGE_SIZE + "");
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        params.put(HttpParamsConstants.PARAM_DEVICE, "android");
        CommonRequestM.getNewsTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
            @Override
            public void onSuccess(@Nullable ListModeBase<TrackM> object) {
                if (object != null) {
                    List<Track> trackList = new ArrayList<Track>(object.getList());
                    playTrackList(trackList, 0, true, object.getParams(), dataCallBack);
                } else {
                    onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_GET_DATA_NETWORK);
                }
            }

            @Override
            public void onError(int code, String message) {
                onReturnErrorCode(dataCallBack, ErrorCodes.ERROR_GET_DATA_NETWORK);
            }
        });
    }

    private static class LoadHistoryTask extends MyAsyncTask<Void ,Void ,List<HistoryModel>> {
        @Override
        protected List<HistoryModel> doInBackground(Void... voids) {
            List<HistoryModel> historyModels = new ArrayList<>();

            IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
            if (historyManager != null) {
                historyModels = historyManager.getTrackList();
            }
            if (historyModels == null) {
                return null;
            }

            // 一键听历史过滤
            HistoryModel onekeyHis = null;
            int oneKeyIndex = -1;
            int size = historyModels.size();
            for (int i = size - 1; i >= 0; i--) {
                HistoryModel historyModel = historyModels.get(i);
                if (historyModel != null &&
                        ((historyModel.getTrack() != null
                        && historyModel.getTrack().getPlaySource() == ConstantsOpenSdk.PLAY_FROM_ONE_KEY_PLAY) ||
                                historyModel.isRadio)) {

                    if (onekeyHis == null) {
                        onekeyHis = historyModel;
                        oneKeyIndex = i;
                    } else if (onekeyHis.getEndedAt() < historyModel.getEndedAt()) {
                        if(historyModels.size() > oneKeyIndex) {
                            historyModels.remove(oneKeyIndex);
                        }
                        onekeyHis = historyModel;
                        oneKeyIndex = i;
                    } else {
                        historyModels.remove(i);
                    }
                }
            }

            // 助眠声音过滤
            HistoryModel sleepTrackHis = null;
            int sleepTrackIndex = -1;
            size = historyModels.size();
            for (int i = size - 1; i >= 0; i--) {
                HistoryModel historyModel = historyModels.get(i);
                if (historyModel != null && historyModel.getTrack() != null
                        && !android.text.TextUtils.isEmpty(historyModel.getTrack().getKind())
                        && historyModel.getTrack().getKind().equals(PlayableModel.KIND_MODE_SLEEP)) {
                    if (sleepTrackHis == null) {
                        sleepTrackHis = historyModel;
                        sleepTrackIndex = i;
                    } else if (sleepTrackHis.getEndedAt() < historyModel.getEndedAt()) {
                        historyModels.remove(sleepTrackIndex);
                        sleepTrackHis = historyModel;
                        sleepTrackIndex = i;
                    } else {
                        historyModels.remove(i);
                    }
                }
            }

            return historyModels;
        }
    }

    @Override
    public boolean onUnbind(Intent intent) {
        XiaoaiControllUtil.getInstance().setHasXiaoaiApp(false);
        return super.onUnbind(intent);
    }

    private void initPlayerService(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(XmPlayerService.getIntent(context, true));
            } else {
                context.startService(XmPlayerService.getIntent(context, false));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void playTrackList(List<Track> object, int position, boolean autoPlay, Map<String, String> params, IXmApiCallback callback) {
        checkConnectState(this, new IServiceState() {
            @Override
            public void onServiceConnect() {
                XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                if (playerSrvice != null) {
                    playerSrvice.setPlayList(params, object);
                    if (playListChange != null) {
                        playListChange.onPlayListChange();
                    }
                    if (autoPlay) {
                        playerSrvice.play(position);
                    } else {
                        playerSrvice.play(position, false);
                    }
                    onReturnErrorCode(callback, ErrorCodes.ERROR_OK);
                }
            }

            @Override
            public void onServiceTimeout() {
                onReturnErrorCode(callback,
                        ErrorCodes.ERROR_PLAY_UNKNOWN);
            }
        });

    }

    interface IServiceState {
        void onServiceConnect();

        void onServiceTimeout();
    }

    private void checkConnectState(Context context, IServiceState callback) {
        XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
        if (playerSrvice != null) {
            if (callback != null) {
                callback.onServiceConnect();
            }
        } else {
            long mLastHandleTrackTime = System.currentTimeMillis();

            XmPlayerService.addServiceLife(new IServiceLifeCallBack() {
                @Override
                public void onServiceCreate() {
                    XmPlayerService.removeServiceLife(this);

                    //超过10s放弃以前的操作
                    if (System.currentTimeMillis() - mLastHandleTrackTime > 10_000) {
                        callback.onServiceTimeout();
                        return;
                    }

                    if (callback != null) {
                        callback.onServiceConnect();
                    }
                }

                @Override
                public void onServiceDestory() {
                    XmPlayerService.removeServiceLife(this);
                }
            });

            initPlayerService(context);
        }
    }

    interface IPlayHistoryCallBack {
        void onError();

        void onSuccess();
    }

    public void playTrackByHistory(Track track, boolean autoPlay, IPlayHistoryCallBack callBack) {
        final HashMap<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, track.getAlbum() != null ? track.getAlbum()
                .getAlbumId() + "" : "0");
        params.put(HttpParamsConstants.PARAM_TRACK_ID, track.getDataId() + "");
        boolean isAsc;
        if (BaseUtil.isPlayerProcess(ToolUtil.getCtx())) {
            IHistoryManagerForPlay historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForPlay.class);
            int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(
                    track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0) : IHistoryManagerForMain.ASC_ORDER;
            //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
            isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        } else {
            IHistoryManagerForMain historyManager = RouterServiceManager.getInstance().getService(IHistoryManagerForMain.class);
            int order = historyManager != null ? historyManager.getAlbumSortByAlbumId(
                    track.getAlbum() != null ? track.getAlbum().getAlbumId() : 0) : IHistoryManagerForMain.ASC_ORDER;
            //asc为true时貌似时倒序，和示意有点不符,服务端默认是倒序
            isAsc = (order == IHistoryManagerForMain.ASC_ORDER);
        }

        params.put(HttpParamsConstants.PARAM_ASC, String.valueOf(isAsc));
        CommonRequestM.getPlayHistory(params, new IDataCallBack<ListModeBase<TrackM>>() {
            @Override
            public void onSuccess(ListModeBase<TrackM> object) {
                if (object != null && object.getList() != null) {
                    if (!object.getList().contains(track)) {
                        if (callBack != null) {
                            callBack.onError();
                        }
                        return;
                    }

                    int pageId = object.getPageId();
                    int maxPageId = object.getMaxPageId();
                    int totalCount = object.getTotalCount();
                    params.put(DTransferConstants.PAGE, pageId + "");
                    params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(maxPageId));
                    params.put(DTransferConstants.PRE_PAGE, String.valueOf(pageId - 1));

                    if (object.getList() != null) {
                        if (callBack != null) {
                            callBack.onSuccess();
                        }
                        XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();

                        if (object.getParams() != null
                                && playerSrvice != null
                                && playerSrvice.getPlayListControl() != null) {
                            List<Track> playList = playerSrvice.getPlayListControl().getPlayList();
                            if (!ToolUtil.isEmptyCollects(playList) && playList.contains(track)) {
                                boolean playListOrder =
                                        playerSrvice.getPlayListControl().getPlayListOrder();
                                Logger.logToFile("XiaoaiControllService : isAsc=" + playListOrder);
                                object.getParams().put(DTransferConstants.LOCAL_IS_ASC, String.valueOf(playListOrder));
                                object.getParams().put(XmPlayListControl.POSITIVE_SEQ, String.valueOf(playerSrvice.getPlayListControl().getPositiveSeq()));
                            }
                        } else {
                            Logger.logToFile("XiaoaiControllService : NO success isAsc");
                        }

                        int playIndex = object.getList().indexOf(track);
                        if (playIndex + 1 < object.getList().size()) {
                            int lastPos = PlayerConstants.PLAY_NO_HISTORY;
                            if (playerSrvice != null) {
                                lastPos = PlayProgressManager.getInstance(XmPlayerService.getPlayerSrvice()).
                                        getSoundHistoryPos(track.getDataId());
                            }
                            boolean trackPlayComplete = ToolUtil.isTrackPlayComplete(lastPos, track.getDuration());
                            if (trackPlayComplete) {
                                playIndex += 1;
                            }
                        }

                        List<Track> trackList = new ArrayList<>(object.getList());
                        playTrackList(trackList, playIndex, autoPlay, object.getParams(), null);
                    } else {
                        if (callBack != null) {
                            callBack.onError();
                        }
                    }
                } else {
                    if (callBack != null) {
                        callBack.onError();
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if(callBack != null) {
                    callBack.onError();
                }
            }
        });
    }

    // 获取订阅列表
    private void getAlbumInfo(long albumId, int pageSize, boolean loadHistoryPage, @NonNull IDataCallBack<AlbumM> callBack) {
        Map<String, String> params = new HashMap<>();
        params.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId + "");
        params.put(HttpParamsConstants.PARAM_PAGE_SIZE, ((pageSize > 0) ? pageSize : 30) + "");

        boolean isAsc = AlbumOrderManager.isAlbumSortAsc(albumId);
//        boolean isAsc = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext())
//                .getBooleanCompat(PreferenceConstantsInHost.KEY_IS_ASC + albumId, true);
        params.put(HttpParamsConstants.PARAM_IS_ASC, isAsc + "");

        if (loadHistoryPage) {
            long lastPlayTrackByAlbumId = getLastPlayTrackByAlbumId(albumId);
            if (lastPlayTrackByAlbumId > 0) {
                params.put(HttpParamsConstants.PARAM_TRACK_ID, lastPlayTrackByAlbumId + "");
            }
        }

        CommonRequestM.getAlbumInfo(params, callBack);
    }

    private long getLastPlayTrackByAlbumId(long albumId) {
        long trackId = 0;
        if (XmPlayerService.getPlayerSrvice() != null) {
            Track track = XmPlayerService.getPlayerSrvice().getTrackByHistory(albumId);

            if (track != null) {
                trackId = track.getDataId();
            }
        }

        if (trackId <= 0) {
            try {
                SharedPreferences mPlayLastPlayTrackInAlbum = getSharedPreferences(PreferenceConstantsInOpenSdk.OPENSDK_FILENAME_PLAY_TRACK_HISTORY_RECORD,
                        Context.MODE_PRIVATE);
                String trackStr = mPlayLastPlayTrackInAlbum.getString(albumId + "", null);
                if (!TextUtils.isEmpty(trackStr)) {
                    trackId = new Gson().fromJson(trackStr.split("__xm__")[0], Track.class).getDataId();
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        return trackId;
    }

    public static void trace(String appName, String action, @Nullable String description) {
        if (ACTION_GET_CURRENT_SONG.equals(action)
                || ACTION_IS_PLAYING.equals(action)
                || ACTION_GET_CURRENT_TIME.equals(action)
                || ACTION_GET_TOTAL_TIME.equals(action)
                || ACTION_GET_PLAY_LIST_CUR_PAGE_ID.equals(action)
                || ACTION_GET_PLAY_MODE.equals(action)) {
            // 过滤掉一些没啥价值的action,这个vivo原子听的上报太多了
            return;
        }
        
        Logger.logToFile("XiaoaiControllService : trace " + appName + "  action=" + action + " hasPlayed=" + XmPlayerService.hasPlayed);

        // 远程控制 sdk 触发  其他事件
        XMTraceApi.Trace trace = new XMTraceApi.Trace()
                .setMetaId(54928)
                .setServiceId("others")
                .put("Text", appName) // 传对应的包名
                .put("keyWord", action) // 传对应的远程控制 app 的指令
                .put("isFirstPlay", XmPlayerService.hasPlayed ? "false" : "true") // 传对应的远程控制 app 的指令
                .put("description", description); // 传对应的远程控制 app 的指令内容
        if (BaseUtil.isPlayerProcess(ToolUtil.getCtx())) {
            XmLogManager.sendToXlog(trace);
        } else {
            trace.createTrace();
        }
    }
}
