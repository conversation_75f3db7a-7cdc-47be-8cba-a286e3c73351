package com.ximalaya.ting.android.host.hybrid.providerSdk.busi;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.google.gson.Gson;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardGiveUpHintDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.ad.AdCollectDataRewardVideo;
import com.ximalaya.ting.android.host.model.ad.JssdkFuliSuperCommonModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.provider.BaseAction;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

public class ListenEarnRewardCoinAction extends BaseAction {
    //激励视频播放完成
    private static int CLIENT_CODE_AD_VIDEO_PLAY_COMPLETE = 10003;

    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args, AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        if (args == null) {
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }
        Logger.log("ListenEarnRewardCoinAction :  " + args);

        //福利页通用激励视频
        String superCommonAdVideoShow = args.optString("showCommonVideoAd", "");
        if (!TextUtils.isEmpty(superCommonAdVideoShow)) {
            dealWithSupperCommonAdVideoShow(superCommonAdVideoShow, callback);
            return;
        }

        //可以增加字段，增加福利页其他处理
        if (callback != null) {
            callback.callback(NativeResponse.fail());
        }
    }

    /**
     * 通用激励视频
     */
    private void dealWithSupperCommonAdVideoShow(String superCommon, AsyncCallback callback) {
        if (TextUtils.isEmpty(superCommon)) {
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }
        JssdkFuliSuperCommonModel superCommonModel = null;
        try {
            Gson gson = new Gson();
            superCommonModel = gson.fromJson(superCommon, JssdkFuliSuperCommonModel.class);
        } catch (Exception ignored) {

        }

        if (superCommonModel == null) {
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }
        //positionName: 'name', // 业务名称 （产品给）
        //slot_id: '945063602' // 广告位id（产品给）
        //数据校验
        if (TextUtils.isEmpty(superCommonModel.slot_id) || TextUtils.isEmpty(superCommonModel.positionName)) {
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }

        if (!NetworkUtils.isNetworkAvaliable(BaseApplication.getMyApplicationContext())) {
            CustomToast.showFailToast("目前网络差，请稍后操作～");
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }

        Activity topActivity = BaseApplication.getTopActivity();
        if (!(topActivity instanceof FragmentActivity)) {
            if (callback != null) {
                callback.callback(NativeResponse.fail());
            }
            return;
        }

        RewardExtraParams rewardExtraParams = new RewardExtraParams();

        JSONObject json =
                ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_FORWARD_VIDEO_CONFIG);
        boolean watchVideoClosenable = true;

        int watchVideoTime = RewardExtraParams.DEFAULT_CLOSE_TIME;
        if(json != null) {
            watchVideoClosenable = json.optBoolean("watchVideoClosenable" ,true);
            watchVideoTime = json.optInt("watchVideoTime" ,RewardExtraParams.DEFAULT_CLOSE_TIME);
        }
        rewardExtraParams.setCloseable(watchVideoClosenable);
        rewardExtraParams.setCanCloseTime(watchVideoTime);

        JssdkFuliSuperCommonModel finalSuperCommonModel = superCommonModel;
        final int adSource = Advertis.AD_SOURCE_CSJ;
        RewardVideoAdManager.getInstance().loadRewardAd(topActivity, superCommonModel.slot_id,
                adSource, rewardExtraParams , new IVideoAdStatueCallBack() {
            boolean isPlaying;
            Dialog mDialog;
            boolean isComplete = false;
            boolean isClosed = false;
            @Override
            public void onAdLoad(AbstractThirdAd thirdAd) {
                isPlaying = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).isPlaying();

                if(isPlaying) {
                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).pause(PauseReason.Business.ListenEarnRewardCoinAction);
                }

                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                adCollectDataShowTime.setPositionName(finalSuperCommonModel.positionName);
                adCollectDataShowTime.setDspPositionId(finalSuperCommonModel.slot_id);
                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                adCollectDataShowTime.setObType("1");
                CommonRequestM.statOnlineAd(adCollectDataShowTime);
            }

            @Override
            public void onAdLoadError(int code, String message) {
                if (callback != null) {
                    callback.callback(NativeResponse.fail());
                }
            }

            @Override
            public void onAdPlayStart() {

            }

            @Override
            public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                adCollectDataShowTime.setPositionName(finalSuperCommonModel.positionName);
                adCollectDataShowTime.setDspPositionId(finalSuperCommonModel.slot_id);
                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                adCollectDataShowTime.setObType("3");
                CommonRequestM.statOnlineAd(adCollectDataShowTime);
            }

            @Override
            public void onAdClose(boolean isCustomCloseBtn) {
                if(isPlaying) {
                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).play();
                }

                if(isCustomCloseBtn) {
                    if (callback != null) {
                        callback.callback(NativeResponse.fail());
                    }
                } else if(isComplete) {
                    isClosed = true;

                    JSONObject successData = new JSONObject();
                    try {
                        successData.put("clientCode", CLIENT_CODE_AD_VIDEO_PLAY_COMPLETE);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    if (callback != null) {
                        callback.callback(NativeResponse.success(successData));
                    }
                }
            }

            @Override
            public void onAdPlayComplete() {
                isComplete = true;

                if(mDialog != null) {
                    mDialog.dismiss();
                    mDialog = null;
                }

                AdCollectDataRewardVideo adCollectDataShowTime = new AdCollectDataRewardVideo();
                adCollectDataShowTime.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                adCollectDataShowTime.setPositionName(finalSuperCommonModel.positionName);
                adCollectDataShowTime.setDspPositionId(finalSuperCommonModel.slot_id);
                adCollectDataShowTime.setSdkType(AdManager.getSDKType(adSource) + "");
                adCollectDataShowTime.setObType("2");
                CommonRequestM.statOnlineAd(adCollectDataShowTime);
            }

            @Override
            public void onAdPlayError(int code, String message) {
                if (callback != null) {
                    callback.callback(NativeResponse.fail());
                }
            }

            @Override
            public View.OnClickListener getCloseClickListener(Activity myActivity) {
                return new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!ToolUtil.activityIsValid(myActivity)) {
                            return;
                        }

                        RewardGiveUpHintDialog rewardGiveUpHintDialog = new RewardGiveUpHintDialog(myActivity);
                        rewardGiveUpHintDialog.setShowStyle(RewardGiveUpHintDialog.SHOW_STYLE_TASK_CENTER);
                        rewardGiveUpHintDialog.setCancleHandle(new IHandleOk() {
                            @Override
                            public void onReady() {
                                onAdClose(true);

                                myActivity.finish();
                            }
                        });
                        rewardGiveUpHintDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                            @Override
                            public void onDismiss(DialogInterface dialog) {
                                mDialog = null;
                            }
                        });
                        rewardGiveUpHintDialog.show();
                        mDialog = rewardGiveUpHintDialog;
                    }
                };
            }
        });
    }

}
