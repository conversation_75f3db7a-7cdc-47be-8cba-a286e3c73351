package com.ximalaya.ting.android.host.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.VideoView;

import com.bytedance.sdk.openadsdk.TTDrawFeedAd;
import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.bytedance.sdk.openadsdk.TTNativeAd;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjDrawTemplateVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjDrawVideoAd;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

/**
 * 穿山甲drawAd,并行请求框架下使用
 */
public class CSJDrawAdActivityParallel extends BaseFragmentActivity2 {
    public static final String REQUEST_KEY = "requestKey";
    public static final String IS_TEMPLATE = "isTemplate";
    private long requestKey;
    private boolean isTemplate;

    private RelativeLayout mRootLay;
    private View mVideoView;

    private int videoCompleteTime = 0;
    private static AbstractRewardVideoAd rewardVideoAd;

    public static void startCSJDrawAdActivity(AbstractRewardVideoAd videoAd, Context context, long requestKey, boolean isTemplate) {
        Intent intent = new Intent(context, CSJDrawAdActivityParallel.class);
        intent.putExtra(REQUEST_KEY, requestKey);
        intent.putExtra(IS_TEMPLATE, isTemplate);
        ToolUtil.checkIntentAndStartActivity(context, intent);
        rewardVideoAd = videoAd;
    }

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);
        requestKey = getIntent().getLongExtra(REQUEST_KEY, 0);
        isTemplate = getIntent().getBooleanExtra(IS_TEMPLATE, false);
        mRootLay = findViewById(R.id.host_csj_draw_ad_root_lay);
        if(requestKey == 0) {
            finish();
            return;
        }
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                callBack -> callBack.onAdLoad(rewardVideoAd));

        if(isTemplate) {
            showTemplateDrawAd(rewardVideoAd);
        } else {
            showNoTemplateDrawAd(rewardVideoAd);
        }
    }

    private void showTemplateDrawAd(AbstractRewardVideoAd rewardVideoAd) {
        if (rewardVideoAd.getAdData() == null || !(rewardVideoAd instanceof CsjDrawTemplateVideoAd)) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                    callback->callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_VIDEO_PLAY_ERROR, "数据为空或者类型不匹配"));
            finish();
            return;
        }
        TTNativeExpressAd ad = (TTNativeExpressAd) rewardVideoAd.getAdData();
        ad.setCanInterruptVideoPlay(false);
        ad.setVideoAdListener(new TTNativeExpressAd.ExpressVideoAdListener() {
            @Override
            public void onVideoLoad() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }

            @Override
            public void onVideoError(int errorCode, int extraCode) {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }

            @Override
            public void onVideoAdStartPlay() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");

                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        IVideoAdStatueCallBack::onAdPlayStart);
            }

            @Override
            public void onVideoAdPaused() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }

            @Override
            public void onVideoAdContinuePlay() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }

            @Override
            public void onProgressUpdate(long current, long duration) {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }

            @Override
            public void onVideoAdComplete() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                videoCompleteTime++;
                // draw广告会自动播放两遍，仅在播放第二遍的时候认为结束
                if (videoCompleteTime == 2) {
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                            IVideoAdStatueCallBack::onAdPlayComplete);
                }
            }

            @Override
            public void onClickRetry() {
                AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
            }
        });
        ad.setCanInterruptVideoPlay(false);
        ad.setExpressInteractionListener(new TTNativeExpressAd.ExpressAdInteractionListener() {
            @Override
            public void onAdClicked(View view, int type) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdVideoClick(false,0));
            }

            @Override
            public void onAdShow(View view, int type) {

            }

            @Override
            public void onRenderFail(View view, String msg, int code) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdPlayError(code, msg));
            }

            @Override
            public void onRenderSuccess(View view, float width, float height) {
                mRootLay.removeAllViews();
                View expressAdView = ad.getExpressAdView();
                mVideoView = expressAdView;
                mRootLay.addView(expressAdView);
            }
        });
        ad.render();
    }

    private void showNoTemplateDrawAd(AbstractRewardVideoAd rewardVideoAd) {
        if (rewardVideoAd.getAdData() == null || !(rewardVideoAd instanceof CsjDrawVideoAd)) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                    callback->callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_VIDEO_PLAY_ERROR, "数据为空或者类型不匹配"));
            finish();
            return;
        }
        TTDrawFeedAd ad = (TTDrawFeedAd) rewardVideoAd.getAdData();
        ad.setActivityForDownloadApp(CSJDrawAdActivityParallel.this);
        //点击监听器必须在getAdView之前调
        ad.setDrawVideoListener(new TTDrawFeedAd.DrawVideoListener() {
            @Override
            public void onClickRetry() {
            }

            @Override
            public void onClick() {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdVideoClick(false, 0));

            }
        });
        ad.setCanInterruptVideoPlay(false);
        ad.setVideoAdListener(new TTFeedAd.VideoAdListener() {
            @Override
            public void onVideoLoad(TTFeedAd ttFeedAd) {

            }

            @Override
            public void onVideoError(int i, int i1) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdPlayError(i, "播放失败"));
            }

            @Override
            public void onVideoAdStartPlay(TTFeedAd ttFeedAd) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        IVideoAdStatueCallBack::onAdPlayStart);
            }

            @Override
            public void onVideoAdPaused(TTFeedAd ttFeedAd) {

            }

            @Override
            public void onVideoAdContinuePlay(TTFeedAd ttFeedAd) {

            }

            @Override
            public void onProgressUpdate(long l, long l1) {

            }

            @Override
            public void onVideoAdComplete(TTFeedAd ttFeedAd) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        IVideoAdStatueCallBack::onAdPlayComplete);
            }
        });
        View adView = ad.getAdView();
        mVideoView = adView;
        mRootLay.addView(adView);
        ad.registerViewForInteraction(mRootLay, adView, new TTNativeAd.AdInteractionListener() {
            @Override
            public void onAdClicked(View view, TTNativeAd ttNativeAd) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdVideoClick(false, 0));
            }

            @Override
            public void onAdCreativeClick(View view, TTNativeAd ttNativeAd) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callback->callback.onAdVideoClick(false, 0));
            }

            @Override
            public void onAdShow(TTNativeAd ttNativeAd) {

            }
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_csj_draw_ad_lay;
    }

    @Override
    public void onBackPressed() {
        // 屏蔽返回键
//        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        if(mVideoView instanceof VideoView) {
            ((VideoView) mVideoView).stopPlayback();
        }
        super.onDestroy();
    }
}
