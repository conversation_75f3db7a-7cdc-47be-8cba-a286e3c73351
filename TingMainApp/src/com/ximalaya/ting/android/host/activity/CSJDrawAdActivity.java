package com.ximalaya.ting.android.host.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.VideoView;

import androidx.annotation.NonNull;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTDrawFeedAd;
import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.bytedance.sdk.openadsdk.TTNativeAd;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJDrawThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJExpressDrawThirdAd;
import com.ximalaya.ting.android.adsdk.external.IBaseLoadListener;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.CSJAdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockVipTrackManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.preciseye.OriginalAdParams;
import com.ximalaya.ting.android.preciseye.csj.CSJPrecisEyeListenerUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by le.xin on 2020/8/19.
 * 穿山甲drawAd
 * <AUTHOR>
 * @email <EMAIL>
 */
public class CSJDrawAdActivity extends BaseFragmentActivity2 {

    public static final String REQUEST_KEY = "requestKey";
    public static final String IS_TEMPLATE = "isTemplate";
    public static final String DSP_POSITION_ID = "dspPositionId";
    public static final String ADVERTIS = "advertis";
    public static final String REWARD_COUNT_DOWN_STYLE = "countDownStyle";

    private long requestKey;
    private boolean isTemplate;
    private String dspPositionId;
    private int rewardCountDownStyle;
    private RelativeLayout mRootLay;
    private Advertis mAdvertis;

    private View mVideoView;

    private int videoCompleteTime = 0;

    private boolean isAdLoadOverTime;

    private CountDownTimer countDownTimer;

    public static void startCSJDrawAdActivity(Context context, long requestKey, boolean isTemplate, String dspPositionId, @NonNull RewardExtraParams extraParams) {
        Intent intent = new Intent(context, CSJDrawAdActivity.class);
        intent.putExtra(REQUEST_KEY, requestKey);
        intent.putExtra(IS_TEMPLATE, isTemplate);
        intent.putExtra(DSP_POSITION_ID, dspPositionId);
        intent.putExtra(ADVERTIS, extraParams.getAdvertis());
        intent.putExtra(REWARD_COUNT_DOWN_STYLE, extraParams.getRewardCountDownStyle());
        ToolUtil.checkIntentAndStartActivity(context, intent);
    }

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);

        requestKey = getIntent().getLongExtra(REQUEST_KEY, 0);
        isTemplate = getIntent().getBooleanExtra(IS_TEMPLATE, false);
        dspPositionId = getIntent().getStringExtra(DSP_POSITION_ID);
        mAdvertis = getIntent().getParcelableExtra(ADVERTIS);
        rewardCountDownStyle = getIntent().getIntExtra(REWARD_COUNT_DOWN_STYLE, RewardExtraParams.REWARD_COUNT_DOWN_STYLE);

        mRootLay = findViewById(R.id.host_csj_draw_ad_root_lay);
        isAdLoadOverTime = false;
        countDownTimer = null;

        if(requestKey == 0) {
            finish();
            return;
        }

        if(isTemplate) {
            requestTemplateDrawAd();
        } else {
            requestNoTemplateDrawAd();
        }
    }

    private void requestTemplateDrawAd() {
        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }

                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(code, message));
            }})) {
            return;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(dspPositionId)
                .setSupportDeepLink(true)
                .setExpressViewAcceptedSize(BaseUtil.px2dip(getContext(), screenWidth), BaseUtil.px2dip(getContext(), screenHeight)) //期望模板广告view的size,单位dp
                .setAdCount(1);
        //添加实时竞价参数
        if (mAdvertis != null && mAdvertis.isSlotRealBid() && !TextUtils.isEmpty(mAdvertis.getSlotAdm())) {
            builder.withBid(mAdvertis.getSlotAdm());
            CustomToast.showToast("参与竞价 3 + " + mAdvertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价 3 + " + mAdvertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if(mAdvertis != null) {
            originalAdParams = new OriginalAdParams(mAdvertis.getAdPositionId(), mAdvertis.getAdid(), mAdvertis.getResponseId());
        }

        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(rewardCountDownStyle), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未开始播放，认为此次广告加载失败
                isAdLoadOverTime = true;
                AdLogger.log("CSJDrawAd : adLoadOverTime ");
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                                "广告加载超时"));
                finish();
            }
        }.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(rewardCountDownStyle));

        TTAdSdk.getAdManager()
                .createAdNative(context).loadExpressDrawFeedAd(adSlot, CSJPrecisEyeListenerUtil.getNativeExpressAdExtends(originalAdParams, new TTAdNative.NativeExpressAdListener() {
            @Override
            public void onError(int code, String message) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(code, message));
            }

            @Override
            public void onNativeExpressAdLoad(List<TTNativeExpressAd> ads) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                if (ads == null || ads.isEmpty()) {
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                            callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));

                    return;
                }

                TTNativeExpressAd ad = ads.get(0);

                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoad(new CSJExpressDrawThirdAd(ad, dspPositionId)));
                ad.setCanInterruptVideoPlay(false);
                ad.setVideoAdListener(new TTNativeExpressAd.ExpressVideoAdListener() {
                    @Override
                    public void onVideoLoad() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }

                    @Override
                    public void onVideoError(int errorCode, int extraCode) {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }

                    @Override
                    public void onVideoAdStartPlay() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");

                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                IVideoAdStatueCallBack::onAdPlayStart);
                    }

                    @Override
                    public void onVideoAdPaused() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }

                    @Override
                    public void onVideoAdContinuePlay() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }

                    @Override
                    public void onProgressUpdate(long current, long duration) {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }

                    @Override
                    public void onVideoAdComplete() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                        videoCompleteTime++;
                        // draw广告会自动播放两遍，仅在播放第二遍的时候认为结束
                        if (videoCompleteTime == 2) {
                            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                    IVideoAdStatueCallBack::onAdPlayComplete);
                        }
                    }

                    @Override
                    public void onClickRetry() {
                        AdLogger.log("loadExpressDrawFeedAd  onVideoLoad");
                    }
                });
                ad.setCanInterruptVideoPlay(false);
                ad.setExpressInteractionListener(new TTNativeExpressAd.ExpressAdInteractionListener() {
                    @Override
                    public void onAdClicked(View view, int type) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdVideoClick(false,0));
                    }

                    @Override
                    public void onAdShow(View view, int type) {

                    }

                    @Override
                    public void onRenderFail(View view, String msg, int code) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdPlayError(code, msg));
                    }

                    @Override
                    public void onRenderSuccess(View view, float width, float height) {
                        mRootLay.removeAllViews();
                        View expressAdView = ad.getExpressAdView();
                        mVideoView = expressAdView;
                        mRootLay.addView(expressAdView);
                    }
                });
                ad.render();
            }
        }));
    }

    private void requestNoTemplateDrawAd() {
        CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(code, message));
            }
        });

        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(dspPositionId)
                .setSupportDeepLink(true)
                .setImageAcceptedSize(screenWidth, screenHeight)
                .setAdCount(1);//请求广告数量为1到3条

        //添加实时竞价参数
        if (mAdvertis != null && mAdvertis.isSlotRealBid() && !TextUtils.isEmpty(mAdvertis.getSlotAdm())) {
            builder.withBid(mAdvertis.getSlotAdm());
            CustomToast.showToast("参与竞价 4 + " + mAdvertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价 4 + " + mAdvertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();

        //step4:请求广告,对请求回调的广告作渲染处理
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(rewardCountDownStyle), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未开始播放，认为此次广告加载失败
                isAdLoadOverTime = true;
                AdLogger.log("CSJDrawAd : adLoadOverTime ");
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                                "广告加载超时"));
                finish();

            }
        }.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(rewardCountDownStyle));

        TTAdSdk.getAdManager()
                .createAdNative(context).loadDrawFeedAd(adSlot, new TTAdNative.DrawFeedAdListener() {
            @Override
            public void onError(int i, String s) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoadError(i, s));
            }

            @Override
            public void onDrawFeedAdLoad(List<TTDrawFeedAd> list) {
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                if (ToolUtil.isEmptyCollects(list)) {
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                            callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));
                    return;
                }

                TTDrawFeedAd ad = list.get(0);

                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                        callBack -> callBack.onAdLoad(new CSJDrawThirdAd(ad, dspPositionId)));

                ad.setActivityForDownloadApp(CSJDrawAdActivity.this);
                //点击监听器必须在getAdView之前调
                ad.setDrawVideoListener(new TTDrawFeedAd.DrawVideoListener() {
                    @Override
                    public void onClickRetry() {
                    }

                    @Override
                    public void onClick() {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdVideoClick(false, 0));

                    }
                });

                ad.setCanInterruptVideoPlay(false);
                ad.setVideoAdListener(new TTFeedAd.VideoAdListener() {
                    @Override
                    public void onVideoLoad(TTFeedAd ttFeedAd) {

                    }

                    @Override
                    public void onVideoError(int i, int i1) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdPlayError(i, "播放失败"));
                    }

                    @Override
                    public void onVideoAdStartPlay(TTFeedAd ttFeedAd) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                IVideoAdStatueCallBack::onAdPlayStart);
                    }

                    @Override
                    public void onVideoAdPaused(TTFeedAd ttFeedAd) {

                    }

                    @Override
                    public void onVideoAdContinuePlay(TTFeedAd ttFeedAd) {

                    }

                    @Override
                    public void onProgressUpdate(long l, long l1) {

                    }

                    @Override
                    public void onVideoAdComplete(TTFeedAd ttFeedAd) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                IVideoAdStatueCallBack::onAdPlayComplete);
                    }
                });
                View adView = ad.getAdView();
                mVideoView = adView;
                mRootLay.addView(adView);
                ad.registerViewForInteraction(mRootLay, adView, new TTNativeAd.AdInteractionListener() {
                    @Override
                    public void onAdClicked(View view, TTNativeAd ttNativeAd) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdVideoClick(false, 0));
                    }

                    @Override
                    public void onAdCreativeClick(View view, TTNativeAd ttNativeAd) {
                        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(requestKey,
                                callback->callback.onAdVideoClick(false, 0));
                    }

                    @Override
                    public void onAdShow(TTNativeAd ttNativeAd) {

                    }
                });
            }
        });
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_csj_draw_ad_lay;
    }

    @Override
    public void onBackPressed() {
        // 屏蔽返回键
//        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        if(mVideoView instanceof VideoView) {
            ((VideoView) mVideoView).stopPlayback();
        }

        super.onDestroy();
    }
}
