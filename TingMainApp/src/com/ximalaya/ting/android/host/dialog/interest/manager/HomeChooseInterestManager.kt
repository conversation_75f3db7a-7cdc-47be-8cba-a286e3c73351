package com.ximalaya.ting.android.host.dialog.interest.manager

import android.app.Activity
import com.google.gson.Gson
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.service.DownloadService
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.FileUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2
import com.ximalaya.ting.android.host.dialog.interest.HomeChooseInterestDialog
import com.ximalaya.ting.android.host.dialog.interest.inter.IChooseInterestCallback
import com.ximalaya.ting.android.host.dialog.interest.model.ChooseInterestItemModel
import com.ximalaya.ting.android.host.dialog.interest.model.ChooseInterestModel
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.record.BaseDownloadTask
import com.ximalaya.ting.android.host.manager.record.DownloadManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.read.utils.LogUtils
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.read.widgets.localReader.util.GSON
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import org.json.JSONObject
import java.io.File
import java.lang.ref.SoftReference

object HomeChooseInterestManager {

    private const val TAG = "HomeChooseInterestManager"

    private const val VIDEO_URL =
        "https://aod.cos.tx.xmcdn.com/storages/f3c8-audiofreehighqps/6E/D7/GAqhp50MQsG1ABAAAAPc7Cl_.mp4"

    private const val DARK_VIDEO_URL =
        "https://aod.cos.tx.xmcdn.com/storages/5cc1-audiofreehighqps/98/90/GAqhfD0MRom2ABAAAAPfc996.mp4"

    var LOCAL_VIDEO_URL: String? = null

    const val FLASH_URL =
        "https://imagev2.xmcdn.com/storages/ecbc-audiofreehighqps/EC/45/GAqhp50MOY8_AACzOgPWwhcU.webp"

    // 滑动闪光动画
    var isAllowAnimal = false

    private const val KEY_HOME_CHOOSE_DIALOG_SHOW_TIME = "key_home_choose_dialog_show_time"

    private var mChooseDialog: SoftReference<HomeChooseInterestDialog>? = null

    @JvmStatic
    fun getVideoUrl(): String {
        return if (BaseFragmentActivity2.sIsDarkMode) {
            DARK_VIDEO_URL
        } else {
            VIDEO_URL
        }
    }

    @JvmStatic
    fun checkShowDialog() {
        val activity = BaseApplication.getMainActivity()
        if (activity !is MainActivity) {
            return
        }

        if (BaseUtil.isFirstInstallApp(activity)) {
            return
        }

        downloadVideo(object : IDataCallBack<String> {
            override fun onSuccess(data: String?) {
                LOCAL_VIDEO_URL = data
            }

            override fun onError(code: Int, message: String?) {

            }
        })

        HandlerManager.postOnUIThreadDelay({
            requestDialogData(activity)
        }, 5000)
    }

    private fun downloadVideo(callBack: IDataCallBack<String>?) {
        val hash = getVideoUrl().hashCode()
        val fileName = "$hash.mp4"
        val dirPath = DownloadService.getDiskCachePath(ToolUtil.getCtx()) + "/home"
        val file = File(dirPath + File.separator + fileName)
        if (file.exists()) {
            callBack?.onSuccess(file.absolutePath)
            return
        }

        val result = FileUtil.getAvailableExternalMemorySize()
        if (result < 10 * 1024 * 1024) {
            callBack?.onError(-2, "存储空间不足")
            return
        }

        val dir = File(dirPath)
        if (!dir.exists()) {
            dir.mkdirs() // 更健壮，递归创建多级目录
        }

        // BaseDownloadTask 已自动实现：
        // 1. 下载到临时文件（.temp）
        // 2. 下载完成后重命名为目标文件名
        // 3. 只有重命名成功才回调 handleCompleteDownload
        // 4. 下载出错时自动清理临时文件
        val task = object : BaseDownloadTask() {
            override fun getDownloadUrl(): String {
                return getVideoUrl()
            }

            override fun getLocalPath(): String {
                return dirPath
            }

            override fun getLocalName(): String {
                return fileName
            }

            override fun isRefresh(): Boolean {
                return false
            }

            override fun handleStartDownload() {
                // LogUtils.d(TAG, "handleStartDownload")
            }

            override fun handleStopDownload() {
            }

            override fun handleUpdateDownload(curr: Long, total: Long) {
                // LogUtils.d(TAG, "handleUpdateDownload curr:$curr total:$total")
            }

            override fun handleCompleteDownload() {
                // LogUtils.d(TAG, "handleCompleteDownload")
                callBack?.onSuccess(file.absolutePath)
            }

            override fun handleDownloadError(e: Exception?, what: Int, extra: Int) {
                LogUtils.d(TAG, "handleDownloadError:${e?.message}")
                callBack?.onError(-2, "${e?.message} what:$what extra:$extra")
            }
        }
        DownloadManager.getInstance().download(task, true)
    }

    private fun isAllowShowDialog(activity: Activity): Boolean {
        if (BaseUtil.isFirstInstallApp(activity)) {
            return false
        }

        if (activity !is MainActivity) {
            return false
        }

        if (!activity.checkActivity()) {
            return false
        }

        if (ViewUtil.haveDialogIsShowing(activity)) {
            return false
        }

        if (!activity.isHomePageFragmentOnTop()) {
            return false
        }

        return true
    }

    fun getContentTopHeight(): Int {
        val activity = BaseApplication.getMainActivity()
        if (activity !is MainActivity) {
            return 0
        }

        if (!activity.checkActivity()) {
            return 0
        }

        return activity.contentTopHeight
    }

    @JvmStatic
    fun requestDialogData(activity: MainActivity) {
        val map = mutableMapOf("source" to "IndexFeed")
        map["exposeTime"] = MmkvCommonUtil.getInstance(activity)
            .getString(KEY_HOME_CHOOSE_DIALOG_SHOW_TIME, "")

        CommonRequestM.baseGetRequest<ChooseInterestModel>(UrlConstants.getInstanse()
            .queryHomeCategoryUrl(),
            map, object : IDataCallBack<ChooseInterestModel?> {
                override fun onSuccess(data: ChooseInterestModel?) {
                    if (data == null || data.categories.isNullOrEmpty()) {
                        LogUtils.d(TAG, "onSuccess categories :${data?.categories}")
                    } else {
                        data.xmRequestId = XmRequestIdManager.getInstance(activity).requestId
                        showDialog(data, activity)
                    }
                }

                override fun onError(code: Int, message: String) {
                    LogUtils.d(TAG, "onError: $code, $message")
                }
            },
            CommonRequestM.IRequestCallBack { content ->
                val jsonObject = JSONObject(content)
                val data = jsonObject.optJSONObject("data")
                if (jsonObject.optInt("ret") != 0 || data == null) {
                    return@IRequestCallBack null
                }
                return@IRequestCallBack GSON.fromJson(
                    data.toString(),
                    ChooseInterestModel::class.java
                )
            })

    }

    @JvmStatic
    fun submitSelectData(
        type: String?,
        list: MutableList<ChooseInterestItemModel>,
        callBack: IDataCallBack<Boolean>
    ) {
        val map = mutableMapOf<String, Any>("source" to "IndexFeed")
        map["type"] = type ?: ""
        map["categories"] = list

        CommonRequestM.basePostRequestJsonStr<Boolean>(UrlConstants.getInstanse()
            .submitHomeSelectCategoryUrl(), Gson().toJson(map),
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(data: Boolean?) {
                    callBack.onSuccess(data)
                }

                override fun onError(code: Int, message: String) {
                    callBack.onError(code, message)
                }
            },
            CommonRequestM.IRequestCallBack { content ->
                val jsonObject = JSONObject(content)
                return@IRequestCallBack jsonObject.optInt("ret") == 0
            })

    }

    @JvmStatic
    private fun showDialog(data: ChooseInterestModel, activity: MainActivity) {
        if (!isAllowShowDialog(activity)) {
            return
        }

        val dialog = HomeChooseInterestDialog.newInstance()
        dialog.dialogData = data
        dialog.mDialogListener = object : IChooseInterestCallback {

            override fun onDialogShow() {
                MmkvCommonUtil.getInstance(activity)
                    .saveString(KEY_HOME_CHOOSE_DIALOG_SHOW_TIME, "${System.currentTimeMillis()}")
            }

            override fun onDialogDismiss() {
                mChooseDialog = null
            }

            override fun onRefreshHomeData() {
                try {
                    Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction?.refreshHomepageRecommendFragmentOnly()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        dialog.show(activity.supportFragmentManager, "HomeChooseInterestDialog")
        mChooseDialog = SoftReference(dialog)

        ImageManager.from(BaseApplication.getMyApplicationContext())
            .downLoadBitmap(FLASH_URL)
    }

    fun loadHomeDataFinish() {
        if (!isAllowAnimal) {
            return
        }
        val dialog = mChooseDialog?.get()
        if (dialog?.isVisible == true && dialog.isAdded && dialog.isResumed) {
            dialog.showFlashMoveAnimal()
        }
    }

}