package com.ximalaya.ting.android.host.dialog.interest.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.dialog.interest.model.ChooseInterestItemModel

class HomeChooserInterestAdapter(
    val list: List<ChooseInterestItemModel>,
    val listener: IOnChooseInterestItemClickListener
) :
    RecyclerView.Adapter<HomeChooserInterestViewHolder>() {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): HomeChooserInterestViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(
            R.layout.host_item_home_choose_interest_dialog,
            parent,
            false
        )
        return HomeChooserInterestViewHolder(view)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: HomeChooserInterestViewHolder, position: Int) {
        val context = holder.itemView.context

        val model = list[position]
        val text: String = model.name ?: ""
        holder.adapterTv?.text = text

        if (model.isSelect) {
            holder.adapterTv?.setTextColor(
                ContextCompat.getColor(context, R.color.host_color_ff4444)
            )
            holder.rootView?.setBackgroundResource(R.drawable.host_bg_home_choose_interest_select_corner_4)
        } else {
            holder.adapterTv?.setTextColor(
                ContextCompat.getColor(context, R.color.host_color_cc2c2c3c_dcdcdc)
            )
            holder.rootView?.setBackgroundResource(R.drawable.host_bg_home_choose_interest_corner_4)
        }

        holder.itemView.setOnClickListener {
            model.isSelect = !model.isSelect
            notifyItemChanged(position, "select")
            listener.onItemClick(model)
        }
    }

}

class HomeChooserInterestViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    val rootView: View? = itemView.findViewById(R.id.host_item_pager_index_bg)
    val adapterTv: TextView? = itemView.findViewById(R.id.host_item_pager_index_tv)
}

interface IOnChooseInterestItemClickListener {
    fun onItemClick(item: ChooseInterestItemModel)
}