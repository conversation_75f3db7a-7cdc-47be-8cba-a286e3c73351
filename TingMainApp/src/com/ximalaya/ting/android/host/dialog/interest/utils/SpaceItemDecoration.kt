package com.ximalaya.ting.android.host.dialog.interest.utils

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView

class SpaceItemDecoration(
    private val horizontalSpace: Int = 0, // 横向间隙（左右）
    private val verticalSpace: Int = 0     // 纵向间隙（上下）
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val layoutManager = parent.layoutManager ?: return

        if (layoutManager is androidx.recyclerview.widget.GridLayoutManager) {
            val spanCount = layoutManager.spanCount
            val position = parent.getChildAdapterPosition(view)
            val column = position % spanCount
            val itemCount = state.itemCount

            // 判断是否是第一行 & 最后一行
            val isFirstRow = position < spanCount
            val isLastRow = (position / spanCount) == ((itemCount - 1) / spanCount)

            // 左右间距逻辑：每列 item 平分 horizontalSpace
            when (column) {
                0 -> {
                    // 第一列：只有右边距
                    outRect.right = horizontalSpace / 2
                }

                spanCount - 1 -> {
                    // 最后一列：只有左边距
                    outRect.left = horizontalSpace / 2
                }

                else -> {
                    // 中间列：左右都有间距
                    outRect.left = horizontalSpace / 2
                    outRect.right = horizontalSpace / 2
                }
            }

            // 上下间距逻辑
            if (isFirstRow) {
                // 第一行：不设置顶部间距
                outRect.top = 0
            } else {
                outRect.top = verticalSpace / 2
            }

            if (isLastRow) {
                // 最后一行：不设置底部间距
                outRect.bottom = 0
            } else {
                outRect.bottom = verticalSpace / 2
            }
        } else {
            // 非 GridLayoutManager 的默认处理
            outRect.set(horizontalSpace/2, verticalSpace/2, horizontalSpace/2, verticalSpace/2)
        }
    }
}
