package com.ximalaya.ting.android.host.dialog.interest

import android.content.res.Configuration
import android.graphics.PorterDuff
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.alphamovie.AlphaMovieView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.dialog.interest.adapter.HomeChooserInterestAdapter
import com.ximalaya.ting.android.host.dialog.interest.adapter.IOnChooseInterestItemClickListener
import com.ximalaya.ting.android.host.dialog.interest.inter.IChooseInterestCallback
import com.ximalaya.ting.android.host.dialog.interest.manager.HomeChooseInterestManager
import com.ximalaya.ting.android.host.dialog.interest.model.ChooseInterestItemModel
import com.ximalaya.ting.android.host.dialog.interest.model.ChooseInterestModel
import com.ximalaya.ting.android.host.dialog.interest.utils.SpaceItemDecoration
import com.ximalaya.ting.android.host.fragment.other.BaseCustomDialogFragment
import com.ximalaya.ting.android.host.fragment.other.inter.IDialogBackListener
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager


/**
 * 首页兴趣表达弹窗
 */
class HomeChooseInterestDialog : BaseCustomDialogFragment() {

    companion object {
        fun newInstance(): HomeChooseInterestDialog {
            return HomeChooseInterestDialog()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        parentNeedBg = false
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.host_dialog_home_choose_interest_layout
    }

    private var mLlContainer: LinearLayout? = null
    private var mBgView: View? = null
    private var mFlAnimalContainer: ViewGroup? = null
    private var mRecyclerView: RecyclerView? = null
    private var mMovieView: AlphaMovieView? = null
    private var mIvFlashView: ImageView? = null
    private var mTvSubmit: TextView? = null

    var mDialogListener: IChooseInterestCallback? = null
    private var mSelectList = mutableListOf<ChooseInterestItemModel>()
    private var isSubmit: Boolean = false
    var dialogData: ChooseInterestModel? = null
    private var mSpanCount = 2

    private var mList = mutableListOf<ChooseInterestItemModel>()
    private var mAdapter: HomeChooserInterestAdapter? = null

    private var isShowLoadAnimal = false
    private var isShowFlashAnimal = false
    private var isMovieEnd = false
    private var isSubmitDataSuccess: Boolean = false
    private var isNeedPause = true
    private var isVideoError = false

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        mLlContainer = mContainerView.findViewById(R.id.host_ll_container)
        mBgView = mContainerView.findViewById(R.id.host_view_bg)
        mFlAnimalContainer = mContainerView.findViewById(R.id.host_fl_animal_container)
        mRecyclerView = mContainerView.findViewById(R.id.host_recycler_view)
        mTvSubmit = mContainerView.findViewById(R.id.host_tv_submit)
        mMovieView = mContainerView.findViewById(R.id.host_alpha_view)
        mIvFlashView = mContainerView.findViewById(R.id.iv_flash_move)

        mFlAnimalContainer?.run {
            val params = layoutParams as MarginLayoutParams
            params.topMargin = HomeChooseInterestManager.getContentTopHeight()
            if (params.topMargin <= 0) {
                params.topMargin = 74.dp + BaseUtil.mStatusBarHeight
            }
            layoutParams = params
        }

        mBgView?.setOnClickListener {
            dismiss()
        }

        val ivClose = mContainerView.findViewById<ImageView?>(R.id.host_iv_close)
        ivClose?.setOnClickListener {
            dismiss()

            // 新首页-小雅助手兴趣选择弹窗-关闭  弹框控件点击
            val trace = XMTraceApi.Trace()
                .setMetaId(69138)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("contentType", "homeChooseInterest") // 去重使用
                .put(XmRequestIdManager.XM_REQUEST_ID, dialogData?.xmRequestId) // 去重使用
                .put("contentId", "1") // 去重使用
                .put("currPage", "homePage")
                .put("source", dialogData?.type) // 区分标签的来源类型都是3.0还是4.0
                .put("tags", getAllTags()) // 传显示的所有标签值，用_拼接，比如：小说_出版社_相声评书

            addUbtV2Data(trace, dialogData?.ubtV2)
            trace.createTrace()
        }
        ivClose.setColorFilter(
            ContextCompat.getColor(
                mActivity, R.color.host_color_4d2c2c3c_8d8d91
            ), PorterDuff.Mode.SRC_IN
        )

        mTvSubmit?.setOnClickListener {
            if (mSelectList.isEmpty()) {
                return@setOnClickListener
            }

            if (isSubmit) {
                return@setOnClickListener
            }
            isSubmit = true

            // 新首页-小雅助手兴趣选择弹窗-提交  弹框控件点击
            val trace = XMTraceApi.Trace()
                .setMetaId(69137)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("currPage", "homePage")
                .put("contentType", "homeChooseInterest") // 去重使用
                .put(XmRequestIdManager.XM_REQUEST_ID, dialogData?.xmRequestId) // 去重使用
                .put("contentId", "1") // 去重使用
                .put("source", dialogData?.type) // 区分标签的来源类型都是3.0还是4.0
                .put("unSelectedTags", getUnSelectTags()) // 传未选中的所有标签值，用_拼接
                .put("selectedTags", getSelectTags()) // 传已选中的所有标签值，用_拼接

            addUbtV2Data(trace, dialogData?.ubtV2)
            trace.createTrace()

            HomeChooseInterestManager.submitSelectData(dialogData?.type, mSelectList,
                object : IDataCallBack<Boolean> {
                    override fun onSuccess(data: Boolean?) {
                        isSubmit = false
                        isSubmitDataSuccess = true
                        if (data == true) {
                            if (!isVideoError) {
                                showLoadingAnimal()
                            } else {
                                dismiss()
                            }
                            return
                        }

                        ToastManager.showToast("兴趣提交失败")
                    }

                    override fun onError(code: Int, message: String?) {
                        isSubmit = false
                        ToastManager.showToast(message)
                    }

                })
        }

        if (BaseUtil.isFoldScreen(mActivity)) {
            mSpanCount = 4
        }

        mList.clear()
        dialogData?.categories?.run {
            mList.addAll(this)
        }

        mRecyclerView?.run {
            layoutManager = GridLayoutManager(mActivity, mSpanCount)
            addItemDecoration(SpaceItemDecoration(8.dp, 8.dp))
            mAdapter =
                HomeChooserInterestAdapter(mList, object : IOnChooseInterestItemClickListener {
                    override fun onItemClick(item: ChooseInterestItemModel) {
                        if (item.isSelect) {
                            mSelectList.add(item)
                        } else {
                            mSelectList.remove(item)
                        }

                        if (mSelectList.isEmpty()) {
                            mTvSubmit?.setBackgroundResource(R.drawable.bg_dialog_home_choose_interest_select_normal_shape)
                        } else {
                            mTvSubmit?.setBackgroundResource(R.drawable.bg_dialog_home_choose_interest_select_submit_shape)
                        }
                    }
                })
            adapter = mAdapter
        }


        mDialogListener?.onDialogShow()

        // 初始化视频播放器
        initVideoView()

        // 新首页-小雅助手兴趣选择弹窗  弹框展示
        val trace = XMTraceApi.Trace()
            .setMetaId(69136)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("currPage", "homePage")
            .put("contentType", "homeChooseInterest") // 去重使用
            .put(XmRequestIdManager.XM_REQUEST_ID, dialogData?.xmRequestId) // 去重使用
            .put("contentId", "1") // 去重使用
            .put("source", dialogData?.type) // 区分标签的来源类型都是3.0还是4.0
            .put("tags", getAllTags()) // 传显示的所有标签值，用_拼接

        addUbtV2Data(trace, dialogData?.ubtV2)
        trace.createTrace()
    }

    private fun getSelectTags(): String {
        return mSelectList.joinToString("_") {
            it.name ?: ""
        }
    }

    private fun getUnSelectTags(): String {
        return mList.filter { !it.isSelect }.joinToString("_") {
            it.name ?: ""
        }
    }

    private fun getAllTags(): String {
        return mList.joinToString("_") {
            it.name ?: ""
        }
    }

    // 初始化视频播放相关
    private fun initVideoView() {
        mMovieView?.run {
            setOnErrorListener { _, _, _ ->
                isVideoError = true
                if (isSubmitDataSuccess) {
                    refreshHomeData()
                    dismiss()
                }
                false
            }
            setOnVideoEndedListener {
                isMovieEnd = true
                mMovieView?.visibility = View.GONE
                showFlashMoveAnimal()
            }
            setOnVideoStartedListener {
                if (isNeedPause) {
                    mMovieView?.visibility = View.GONE
                    isNeedPause = false
                    pause()
                }
            }

            if (HomeChooseInterestManager.LOCAL_VIDEO_URL.isNullOrEmpty()) {
                setVideoByUrl(HomeChooseInterestManager.getVideoUrl())
            } else {
                setVideoFromSD(HomeChooseInterestManager.LOCAL_VIDEO_URL)
            }
        }
    }

    private fun showLoadingAnimal() {
        mLlContainer?.let { container ->
            val animation = AnimationUtils.loadAnimation(
                container.context,
                R.anim.host_anim_home_choose_interest_slide_out_down
            )

            animation.setAnimationListener(object : Animation.AnimationListener {
                override fun onAnimationStart(animation: Animation?) {
                    isShowLoadAnimal = true
                }

                override fun onAnimationRepeat(animation: Animation?) {}

                override fun onAnimationEnd(animation: Animation?) {
                    if (!container.context.checkActivity()) {
                        return
                    }

                    container.visibility = View.GONE
                    mBgView?.visibility = View.GONE
                    showVideoAnimal()
                }
            })

            container.startAnimation(animation)
        }
    }

    private fun showVideoAnimal() {
        isNeedPause = false
        mMovieView?.visibility = View.VISIBLE
        mMovieView?.seekTo(0)
        mMovieView?.start()

        ImageManager.from(context)
            .displayImage(mIvFlashView, HomeChooseInterestManager.FLASH_URL, -1)
        mFlAnimalContainer?.let { container ->
            container.alpha = 0f
            container.visibility = View.VISIBLE
            container.animate()
                .alpha(1f)
                .setDuration(100)
                .withEndAction {
                    HomeChooseInterestManager.isAllowAnimal = true
                    HandlerManager.postOnUIThreadDelay({
                        refreshHomeData()
                    }, 500)
                }
                .start()
        }
    }

    private var isRefreshHomeData = true

    private fun refreshHomeData() {
        if (!isRefreshHomeData) {
            return
        }
        isRefreshHomeData = false
        mDialogListener?.onRefreshHomeData()
    }

    fun showFlashMoveAnimal() {
        if (!isMovieEnd) {
            return
        }
        if (isShowFlashAnimal) {
            return
        }
        isShowFlashAnimal = true

        mMovieView?.visibility = View.GONE
        val width = BaseUtil.getScreenWidth(context)
        mIvFlashView?.let { container ->
            container.translationX = width * -1f
            container.visibility = View.VISIBLE
            container.animate()
                .translationX(width.toFloat())
                .setDuration(1600)
                .withEndAction {
                    dismiss()
                }
                .start()
        }
    }

    override fun getCustomLayoutParams(): FragmentDialogParams? {
        val params = super.getCustomLayoutParams()
        params.width = WindowManager.LayoutParams.MATCH_PARENT
        params.height = WindowManager.LayoutParams.MATCH_PARENT
        params.canceledOnTouchOutside = true
        params.animationRes = R.style.host_popup_window_from_bottom_animation
        params.style = R.style.host_share_dialog
        params.gravity = Gravity.TOP
        params.dialogBackListener = object : IDialogBackListener {
            override fun interceptBackEvent(): Boolean {
                return isShowLoadAnimal
            }
        }
        return params
    }

    override fun onResume() {
        super.onResume()
        fitStatusBar()
        // 禁用背景变暗
        dialog?.window?.setDimAmount(0f)
    }

    private fun fitStatusBar() {
        if (dialog == null) {
            return
        }
        val window = dialog!!.window ?: return
        StatusBarManager.canChangeColor(window)
        StatusBarManager.transparencyBar(window)
    }

    override fun getNoContentView(): View? {
        return null
    }

    override fun getNetworkErrorView(): View? {
        return null
    }

    override fun getLoadingView(): View? {
        return null
    }

    override fun dismiss() {
        super.dismiss()
        execDismissListen()
    }

    private fun execDismissListen() {
        mDialogListener?.onDialogDismiss()
        mDialogListener = null
    }

    override fun loadData() {
    }

    fun addUbtV2Data(trace: XMTraceApi.Trace, map: Map<String, String>?) {
        if (map.isNullOrEmpty()) {
            return
        }
        for (entry in map.entries) {
            entry.let {
                trace.put(it.key, it.value)
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mMovieView?.release()
    }

    fun getContentTopHeight(): Int {
        mRecyclerView?.let { recyclerView ->
            val location = IntArray(2)
            recyclerView.getLocationOnScreen(location)
            return location[1]
        }
        return 0
    }

}