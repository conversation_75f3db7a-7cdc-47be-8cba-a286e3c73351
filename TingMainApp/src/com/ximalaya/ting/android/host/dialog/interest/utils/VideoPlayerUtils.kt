package com.ximalaya.ting.android.host.dialog.interest.utils

import android.graphics.SurfaceTexture
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Surface
import android.view.TextureView
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk

class VideoPlayerUtils(private val textureView: TextureView) : TextureView.SurfaceTextureListener {
    enum class VideoPlayerState {
        IDLE, PREPARING, PREPARED, PLAYING, PAUSED, COMPLETED, ERROR, RELEASED
    }

    private companion object {
        const val TAG = "VideoPlayerUtils"
        private val LOG_ENABLE = ConstantsOpenSdk.isDebug
    }

    private fun logD(msg: String) {
        if (LOG_ENABLE) Log.d(TAG, msg)
    }
    private fun logW(msg: String) {
        if (LOG_ENABLE) Log.w(TAG, msg)
    }
    private fun logE(msg: String, throwable: Throwable? = null) {
        if (LOG_ENABLE) {
            if (throwable != null) Log.e(TAG, msg, throwable) else Log.e(TAG, msg)
        }
    }

    private var mediaPlayer: MediaPlayer? = null
    private var surface: Surface? = null
    private var videoPath: String? = null
    private var currentPosition: Int = 0
    private var state: VideoPlayerState = VideoPlayerState.IDLE
    private var startAfterPrepared: Boolean = false
    private var listener: VideoPlayerListener? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    init {
        textureView.surfaceTextureListener = this
    }

    fun setVideoPath(path: String) {
        videoPath = path
        prepareMediaPlayer()
    }

    fun start() {
        when (state) {
            VideoPlayerState.RELEASED, VideoPlayerState.ERROR -> {
                logW("MediaPlayer is released or error, cannot start")
                startAfterPrepared = true
                prepareMediaPlayer()
            }
            VideoPlayerState.PREPARING -> {
                logD("MediaPlayer is preparing, will start after prepared")
                startAfterPrepared = true
            }
            VideoPlayerState.PREPARED, VideoPlayerState.PAUSED, VideoPlayerState.COMPLETED -> {
                mediaPlayer?.let {
                    it.seekTo(currentPosition)
                    it.start()
                    state = VideoPlayerState.PLAYING
                    listener?.onPlay()
                }
            }
            VideoPlayerState.PLAYING -> {
                // already playing
            }
            else -> {
                startAfterPrepared = true
                prepareMediaPlayer()
            }
        }
    }

    fun pause() {
        mediaPlayer?.let {
            if (it.isPlaying) {
                it.pause()
                currentPosition = it.currentPosition
                state = VideoPlayerState.PAUSED
                listener?.onPause()
            }
        }
        startAfterPrepared = false
    }

    fun stop() {
        mediaPlayer?.run {
            stop()
            state = VideoPlayerState.COMPLETED
            listener?.onStop()
        }
        startAfterPrepared = false
    }

    fun release() {
        if (state == VideoPlayerState.RELEASED) return
        mediaPlayer?.run {
            stop()
            reset()
            release()
        }
        mediaPlayer = null
        surface?.release()
        surface = null
        state = VideoPlayerState.RELEASED
        startAfterPrepared = false
        listener = null
        logD("MediaPlayer released")
    }

    fun isPlayable(): Boolean {
        return state == VideoPlayerState.PREPARED || state == VideoPlayerState.PAUSED || state == VideoPlayerState.COMPLETED
    }

    fun onTextureViewVisibilityChanged(isVisible: Boolean) {
        if (isVisible) {
            if (isPlayable()) {
                start()
            } else if (videoPath != null) {
                prepareMediaPlayer()
            }
        } else {
            pause()
        }
    }

    fun setVideoPlayerListener(listener: VideoPlayerListener) {
        this.listener = listener
    }

    override fun onSurfaceTextureAvailable(surfaceTexture: SurfaceTexture, width: Int, height: Int) {
        logD("SurfaceTexture available")
        surface = Surface(surfaceTexture)
        mediaPlayer?.setSurface(surface)
        if (videoPath != null && (state == VideoPlayerState.IDLE || state == VideoPlayerState.RELEASED)) {
            prepareMediaPlayer()
        }
    }

    override fun onSurfaceTextureSizeChanged(surfaceTexture: SurfaceTexture, width: Int, height: Int) {
        logD("SurfaceTexture size changed: width=$width, height=$height")
    }

    override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
        logD("SurfaceTexture destroyed")
        pause()
        surface?.release()
        surface = null
        return true
    }

    override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {
        // 每次更新时调用
    }

    private fun prepareMediaPlayer() {
        if (state == VideoPlayerState.PREPARING || state == VideoPlayerState.PLAYING || state == VideoPlayerState.PREPARED) {
            logW("MediaPlayer is already preparing or prepared, skip preparation")
            return
        }
        try {
            logD("Preparing MediaPlayer with path: $videoPath")
            state = VideoPlayerState.PREPARING
            startAfterPrepared = false
            mediaPlayer = MediaPlayer().apply {
                setAudioStreamType(AudioManager.STREAM_MUSIC)
                setDataSource(videoPath!!)
                surface?.let { setSurface(it) }
                setOnPreparedListener {
                    logD("MediaPlayer prepared successfully startAfterPrepared:$startAfterPrepared visibility:${textureView.visibility}")
                    state = VideoPlayerState.PREPARED
                    // 预解码：准备好后立即start+pause
                    start()
                    mainHandler.post {
                        pause()
                        if (startAfterPrepared) {
                            start()
                            startAfterPrepared = false
                        }
                    }
                    listener?.onPrepared()
                }
                setOnCompletionListener {
                    logD("MediaPlayer playback completed")
                    state = VideoPlayerState.COMPLETED
                    listener?.onCompletion()
                }
                setOnErrorListener { _, what, extra ->
                    logE("MediaPlayer error: what=$what, extra=$extra")
                    state = VideoPlayerState.ERROR
                    startAfterPrepared = false
                    listener?.onError(what, extra)
                    true
                }
                setOnVideoSizeChangedListener { _, width, height ->
                    logD("Video size changed: width=$width, height=$height")
                    adjustVideoSize(width, height)
                }
                prepareAsync()
            }
        } catch (e: Exception) {
            logE("Error preparing media player", e)
            state = VideoPlayerState.ERROR
            startAfterPrepared = false
            listener?.onError(MediaPlayer.MEDIA_ERROR_UNSUPPORTED, 0)
        }
    }

    private fun adjustVideoSize(videoWidth: Int, videoHeight: Int) {
        val viewWidth = textureView.width
        val viewHeight = textureView.height
        if (viewWidth == 0 || viewHeight == 0 || videoWidth == 0 || videoHeight == 0) return

        val viewRatio = viewWidth.toFloat() / viewHeight
        val videoRatio = videoWidth.toFloat() / videoHeight

        val scaleX: Float
        val scaleY: Float

        if (videoRatio > viewRatio) {
            // 视频更宽，按高等比放大，宽超出部分裁剪
            scaleX = videoRatio / viewRatio
            scaleY = 1f
        } else {
            // 视频更高，按宽等比放大，高超出部分裁剪
            scaleX = 1f
            scaleY = viewRatio / videoRatio
        }
        textureView.scaleX = scaleX
        textureView.scaleY = scaleY
    }

    interface VideoPlayerListener {
        fun onPrepared()
        fun onPlay()
        fun onPause()
        fun onStop()
        fun onCompletion()
        fun onError(what: Int, extra: Int)
    }
}