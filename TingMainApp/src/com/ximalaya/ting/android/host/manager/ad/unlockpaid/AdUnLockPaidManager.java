package com.ximalaya.ting.android.host.manager.ad.unlockpaid;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener;
import com.ximalaya.ting.android.host.manager.OnlyUseMainProcessSharePreUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.host.manager.ad.AdTokenManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.CanPauseCountDownTimer;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardGiveUpHintDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdStateData;
import com.ximalaya.ting.android.host.model.ad.AdUnLockAdvertisModel;
import com.ximalaya.ting.android.host.model.ad.AdUnLockPayBaseModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.ad.VideoUnLockResult;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.view.SmallProgressDialog;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.preciseye.OriginalAdParams;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

/**
 * Created by le.xin on 2020/5/12.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AdUnLockPaidManager {
    public static class UnLockPaidPageSource {
        private String unlockPageSource;

        public UnLockPaidPageSource(String unlockPageSource) {
            this.unlockPageSource = unlockPageSource;
        }
    }

    private static boolean loginJumped = false;
    private static long selectTrackId;
    private static boolean loginJumpStatusForAlbumDestory = false;

    private static Map<Long, UnLockPaidPageSource> unLockPaidPageSourceMap;

    public static void updatePaidPageSource(long albumId, String unlockPageSource) {
        if(unLockPaidPageSourceMap == null) {
            unLockPaidPageSourceMap = new HashMap<>();
        }

        unLockPaidPageSourceMap.put(albumId, new UnLockPaidPageSource(unlockPageSource));
    }

    public static String getUnLockPaidPageSource(long albumId) {
        if(unLockPaidPageSourceMap == null) {
            return null;
        }

        UnLockPaidPageSource unLockPaidPageSource = unLockPaidPageSourceMap.get(albumId);
        if(unLockPaidPageSource == null) {
            return null;
        }

        return unLockPaidPageSource.unlockPageSource;
    }

    public static void removePaidPageSource(long albumId) {
        if(unLockPaidPageSourceMap == null) {
            return;
        }

        unLockPaidPageSourceMap.remove(albumId);
    }

    public interface IAdUnLockDataCallBackNeedLogin<T> extends IAdUnLockDataCallBack<T> {
        void gotoLogin();
    }

    public interface IAdUnLockDataCallBack<T> {
        void onSuccess(@NonNull T object);

        void onError();
    }

    public interface IAdUnLockDataCallBackHasDialogToOtherPage<T> extends IAdUnLockDataCallBack<T> {
        void onGotoOtherPage();
    }

    public static ILoginStatusChangeListener sILoginStatusChangeListener = new ILoginStatusChangeListener() {
        @Override
        public void onLogout(LoginInfoModelNew olderUser) {

        }

        @Override
        public void onLogin(LoginInfoModelNew model) {
            loginJumped = true;
            loginJumpStatusForAlbumDestory = true;
        }

        @Override
        public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

        }
    };

    public static void registerLoginStatus(long trackId) {
        selectTrackId = trackId;
        registerLoginStatus();
    }

    public static void registerLoginStatus() {
        UserInfoMannage.getInstance().addLoginStatusChangeListener(sILoginStatusChangeListener);
    }

    public static void release() {
        loginJumped = false;
        loginJumpStatusForAlbumDestory = false;
        selectTrackId = 0;
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(sILoginStatusChangeListener);
    }

    /**
     *
     * @param activity
     * @param dataBean 请求广告前的页面接口信息：adId/positionName/responseId
     * @param albumId
     * @param trackId
     * @param callBack
     */
    public static void unlockPaid(Activity activity, AdUnLockPayBaseModel dataBean,
                                  long albumId, long trackId,
                                  IAdUnLockDataCallBack<VideoUnLockResult> callBack) {

        if(!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity);
            registerLoginStatus(trackId);
            return;
        }

        if(dataBean == null) {
            return;
        }

        if (dataBean.isIsUnlockLimit()) {
            // 7.0.8版本要求 已达专辑解锁上限／已达当日解锁上限，均需去掉弹窗，直接跳转到h5页面
//            UnLockChanceOverDialog unLockChanceOverDialog = new UnLockChanceOverDialog(activity);
//            unLockChanceOverDialog.setData(dataBean, new IHandleOk() {
//                @Override
//                public void onReady() {
//                    if (callBack instanceof IAdUnLockDataCallBackHasDialogToOtherPage) {
//                        ((IAdUnLockDataCallBackHasDialogToOtherPage<VideoUnLockResult>) callBack).onGotoOtherPage();
//                    }
//                }
//            });
//            unLockChanceOverDialog.show();

            Activity mainActivity = MainApplication.getMainActivity();
            if(mainActivity instanceof MainActivity && !TextUtils.isEmpty(dataBean.getRecommendAlbums())) {
                ToolUtil.clickUrlAction((MainActivity) mainActivity,
                        dataBean.getRecommendAlbums(), null);
            }
            if (callBack instanceof IAdUnLockDataCallBackHasDialogToOtherPage) {
                ((IAdUnLockDataCallBackHasDialogToOtherPage<VideoUnLockResult>) callBack).onGotoOtherPage();
            }
            return;
        }

        long last = OnlyUseMainProcessSharePreUtil.getInstance(activity).
                        getLong(PreferenceConstantsInHost.KEY_LAST_UNLOCK_TIME);
        // 解锁时间间隔
        long nextTime = dataBean.getUnlockIntervalTime() * 1000 + last - System.currentTimeMillis();
        if(nextTime > 0) {
            CustomToast.showFailToast("距离下次解锁：" + StringUtil.toTime((int) (nextTime / 1000)));
            return;
        }

        // 上报点击
        String sourceName = TextUtils.isEmpty(dataBean.getSourceName())
                ? AppConstants.AD_POSITION_NAME_INCENTIVE_BUY_POP
                : dataBean.getSourceName();

        int adid = dataBean.getAdid();
        long responseId = dataBean.getResponseId();

        requestAdUnlockInfo(activity, albumId, trackId, callBack, sourceName, adid,
                responseId, 0);
    }

    public static class UnLockLoginWrapper {
        public ILoginStatusChangeListener mLoginStatusChangeListener;
    }

    // 请求具体的广告物料
    public static UnLockLoginWrapper requestAdUnlockInfo(Activity activity,
                                            long albumId, long trackId,
                                            IAdUnLockDataCallBack<VideoUnLockResult> callBack,
                                            String sourceName, int adid, long responseId, int requestType) {
        UnLockLoginWrapper wrapper = new UnLockLoginWrapper();
        AdRequest.incentiveIncentive(albumId, trackId, sourceName, adid, responseId, requestType,
                new IDataCallBack<List<AdUnLockAdvertisModel>>() {
                    @Override
                    public void onSuccess(@Nullable List<AdUnLockAdvertisModel> object) {
                        if (!ToolUtil.isEmptyCollects(object)) {
                            LookVideoModel lookVideoModel = new LookVideoModel();
                            lookVideoModel.unLockAdvertisModels = object;
                            lookVideoModel.index = 0;
                            lookVideoModel.needAdCount = object.size();
                            lookVideoModel.albumId = albumId + "";
                            lookVideoModel.trackId = trackId + "";
                            lookVideoModel.sourceName = sourceName;
                            lookVideoModel.unlockTimes = object.get(0).getUnlockTimes();
                            lookVideoModel.isGotoUnlocked = false;

                            lookNextVideo(activity, lookVideoModel,
                                    new IHandleOk() {
                                        @Override
                                        public void onReady() {
                                            if(lookVideoModel.isGotoUnlocked) {
                                                return;
                                            }

                                            lookVideoModel.isGotoUnlocked = true;

                                            if(UserInfoMannage.hasLogined()) {
                                                // 所有的广告都播放完了 ,可以解锁了
                                                unLockPaid(trackId, albumId, adid, sourceName,
                                                        activity, callBack, lookVideoModel, requestType);
                                            } else {
                                                ILoginStatusChangeListener listener =
                                                        new ILoginStatusChangeListener() {
                                                    @Override
                                                    public void onLogout(LoginInfoModelNew olderUser) {

                                                    }

                                                    @Override
                                                    public void onLogin(LoginInfoModelNew model) {
                                                        UserInfoMannage.getInstance().removeLoginStatusChangeListener(this);
                                                        Logger.log("AdUnLockPaidManager : onLogin");
                                                        // 所有的广告都播放完了 ,可以解锁了
                                                        unLockPaid(trackId, albumId, adid,
                                                                sourceName,
                                                                activity, callBack,
                                                                lookVideoModel, requestType);
                                                    }

                                                    @Override
                                                    public void onUserChange(LoginInfoModelNew oldModel, LoginInfoModelNew newModel) {

                                                    }
                                                };
                                                wrapper.mLoginStatusChangeListener = listener;
                                                UserInfoMannage.getInstance().addLoginStatusChangeListener(listener);

                                                UserInfoMannage.gotoLogin(MainApplication.getOptActivity());
                                            }

                                        }
                                    });
                        } else {
                            CustomToast.showFailToast("请求解锁信息为空");
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(TextUtils.isEmpty(message) ? "请求解锁信息出错" : message);
                    }
                });

        return wrapper;
    }


    // 激励视频播放完成后，真正解锁
    private static void unLockPaid(long trackId, long albumId, int adid, String sourceName,
                                   Activity mActivity,
                                   IAdUnLockDataCallBack<VideoUnLockResult> callBack,
                                   LookVideoModel lookVideoModel, int requestType) {
        SmallProgressDialog mProgressDialog = null;
        try {
            mProgressDialog = new SmallProgressDialog(mActivity,
                    SmallProgressDialog.STYLE_PROGRESS_CENTER);
            mProgressDialog.setMyMessage(new String(Base64.decode("5q2j5Zyo6Kej6ZSB5LitLi4u",
                    Base64.DEFAULT)));
            mProgressDialog.show();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(ConstantsOpenSdk.isDebug) {
            Logger.log("AdUnLockPaidManager : unLockPaid " + Log.getStackTraceString(new Throwable()));
        }

        SmallProgressDialog finalMProgressDialog = mProgressDialog;
        AdManager.postRecord(new Runnable() {
            @Override
            public void run() {
                Map<String, String> maps = new HashMap<>();
                maps.put("uid", UserInfoMannage.getUid() + "");
                maps.put("trackId", trackId + "");
                maps.put("adid", adid + "");
                maps.put("xt", System.currentTimeMillis() + "");
                maps.put("token", AdTokenManager.getInstance().getShowToken());

                EncryptUtil.getInstance(mActivity).getPlaySignature(mActivity, maps);

                maps.put("albumId", albumId + "");
                maps.put("unlockTimes", lookVideoModel.unlockTimes + "");
                maps.put("needAdCount", lookVideoModel.needAdCount + "");
                maps.put("version", DeviceUtil.getVersion(mActivity));
                maps.put("prevPositionName", sourceName);
                maps.put("appid", "0");

                AdRequest.lookUnLockPaid(maps, new IDataCallBack<VideoUnLockResult>() {
                    @Override
                    public void onSuccess(@Nullable VideoUnLockResult object) {
                        try {
                            if (finalMProgressDialog != null) {
                                finalMProgressDialog.dismiss();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (callBack == null) {
                            return;
                        }

                        if (object != null && object.isSuccess()) {
                            if(requestType != 3) {
                                OnlyUseMainProcessSharePreUtil.getInstance(mActivity).
                                        saveLong(PreferenceConstantsInHost.KEY_LAST_UNLOCK_TIME,
                                                System.currentTimeMillis());

                                // 解锁成功直接开始播放
                                PlayTools.playTrackHistoy(MainApplication.getMyApplicationContext(),
                                        trackId, albumId,
                                        null, ConstantsOpenSdk.PLAY_FROM_NONE, true);
                            }
                            callBack.onSuccess(object);
                        } else {
                            // 服务端返回 toast， 则使用服务端的
                            String toastStr = "解锁失败,需要重新观看视频解锁";
                            if (object != null && !TextUtils.isEmpty(object.getToast())) {
                                toastStr = object.getToast();
                            }
                            CustomToast.showFailToast(
                                    toastStr,
                                    Toast.LENGTH_LONG);
                            callBack.onError();
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (finalMProgressDialog != null) {
                            finalMProgressDialog.dismiss();
                        }

                        CustomToast.showFailToast(
                                TextUtils.isEmpty(message) ? "解锁失败,需要重新观看视频解锁" : message,
                                Toast.LENGTH_LONG);

                        if (callBack != null) {
                            callBack.onError();
                        }
                    }
                });
            }
        });
    }

    private static class LoadNextRunnable implements Runnable {

        Activity mActivity;
        LookVideoModel lookVideoModel;
        IHandleOk allComplete;

        public LoadNextRunnable(Activity activity,
                                LookVideoModel lookVideoModel, IHandleOk allComplete) {
            mActivity = activity;
            this.lookVideoModel = lookVideoModel;
            this.allComplete = allComplete;
        }

        @Override
        public void run() {
            if(ToolUtil.activityIsValid(mActivity)) {
                HandlerManager.removeCallbacks(lookVideoModel.mLoadNextRunnable);

                RewardVideoAdManager.getInstance().finishRecentActivity();
                lookVideoModel.isRetryed = false;
                lookVideoModel.curSlotIdIndex = 0;
                lookVideoModel.mLoadNextRunnable = null;
                lookNextVideo(mActivity, lookVideoModel, allComplete);
            }
        }
    }

    private static void lookNextVideo(Activity mActivity,
                                      LookVideoModel lookVideoModel, IHandleOk allComplete) {

        if (ToolUtil.isEmptyCollects(lookVideoModel.unLockAdvertisModels)) {
            return;
        }

        if (lookVideoModel.needAdCount <= lookVideoModel.index) {
            if(ConstantsOpenSdk.isDebug) {
                Logger.log("AdUnLockPaidManager : 播放完成了 " + Log.getStackTraceString(new Throwable()));
            }
            if (allComplete != null) {
                allComplete.onReady();
            }
            return;
        }

        int unLockIndex = lookVideoModel.index;
        if (lookVideoModel.index >= lookVideoModel.unLockAdvertisModels.size()) {
            unLockIndex = 0;
        }

        AdUnLockAdvertisModel adUnLockAdvertisModel =
                lookVideoModel.unLockAdvertisModels.get(unLockIndex);

        lookVideoModel.videoCloseTime = adUnLockAdvertisModel.getVideoCloseTime();
        lookVideoModel.videoTime = adUnLockAdvertisModel.getVideoTime();

        String dspPositionId = null;
        if(AdManager.isThirdAd(adUnLockAdvertisModel)) {
            String[] slotIds = adUnLockAdvertisModel.getSlotIds();
            if(slotIds != null) {
                if (lookVideoModel.curSlotIdIndex >= slotIds.length) {
                    lookVideoModel.curSlotIdIndex = 0;
                }

                dspPositionId = slotIds[lookVideoModel.curSlotIdIndex];
                adUnLockAdvertisModel.setDspPositionId(dspPositionId);
                int[] dspIds = adUnLockAdvertisModel.getDspIds();
                if(dspIds != null) {
                    adUnLockAdvertisModel.setAdtype(dspIds[lookVideoModel.curSlotIdIndex]);
                }

                int[] dspAdTypes = adUnLockAdvertisModel.getDspAdTypes();
                if(dspAdTypes != null) {
                    adUnLockAdvertisModel.setDspAdType(dspAdTypes[lookVideoModel.curSlotIdIndex]);
                }
            }
        }

        long requestTime = System.currentTimeMillis();

        RewardExtraParams.RecordModel recordModel = new RewardExtraParams.RecordModel();

        String finalDspPositionId = dspPositionId;

        RewardExtraParams extraParams = new RewardExtraParams(lookVideoModel.needAdCount,
                lookVideoModel.index, new RewardExtraParams.IRewardStateCallBack() {
            @Override
            public void onVideoCountDownOver() {
                // 服务端计时完毕
                recordVideoShowTime(adUnLockAdvertisModel, finalDspPositionId, lookVideoModel, recordModel);

                ++lookVideoModel.index;

                LoadNextRunnable loadNextRunnable = new LoadNextRunnable(mActivity,
                        lookVideoModel, allComplete);
                lookVideoModel.mLoadNextRunnable = loadNextRunnable;
                HandlerManager.postOnUIThreadDelay(loadNextRunnable,
                        adUnLockAdvertisModel.getEndFrameTime() * 1000);
            }

            @Override
            public void onVideoPlayNextClick() {
                new LoadNextRunnable(mActivity, lookVideoModel, allComplete).run();
            }

            boolean isRemoveLoadNextRunnable = false;
            @Override
            public void onActivityPause() {
                if(lookVideoModel.mLoadNextRunnable != null) {
                    isRemoveLoadNextRunnable = true;
                    HandlerManager.removeCallbacks(lookVideoModel.mLoadNextRunnable);
                }
            }

            @Override
            public void onActivityResume() {
                if(isRemoveLoadNextRunnable) {
                    HandlerManager.postOnUIThreadDelay(lookVideoModel.mLoadNextRunnable, 10000);
                }

                isRemoveLoadNextRunnable = false;
            }
        });

        // 从配置中心 或 后台服务 获取到相应数据进行设置
        JSONObject json =
                ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_UN_LOCK_PAID_VIDEO_CONFIG);
        boolean watchVideoClosenable = true;

//        int watchVideoTime = RewardExtraParams.DEFAULT_CLOSE_TIME;
        if (json != null) {
            watchVideoClosenable = json.optBoolean("watchVideoClosenable", true);
//            watchVideoTime = json.optInt("watchVideoTime", RewardExtraParams.DEFAULT_CLOSE_TIME);
        }

        extraParams.setCloseable(watchVideoClosenable);
        extraParams.setVideoPlayOverTime(lookVideoModel.videoTime);
        extraParams.setCanCloseTime(lookVideoModel.videoCloseTime);
        extraParams.setXmVideoAdvertisModel(adUnLockAdvertisModel, AppConstants.AD_POSITION_NAME_INCENTIVE);

        lookVideoModel.uuid = UUID.randomUUID().toString();

        // 请求开始进行上报
        AdStateReportManager.getInstance().onUnlockRequestBegin(adUnLockAdvertisModel,
                lookVideoModel.sourceName, new AdStateReportManager.IAdStateBuilderInterceptor() {
            @Override
            public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                builder.albumId(lookVideoModel.albumId + "");
                builder.trackId(lookVideoModel.trackId + "");
                builder.adNum(lookVideoModel.index + "");
                builder.setUnlockTimes(lookVideoModel.unlockTimes + "");
                builder.uuid(lookVideoModel.uuid);
            }
        });

        RewardVideoAdManager.getInstance().loadRewardAd(mActivity, adUnLockAdvertisModel, dspPositionId,
                adUnLockAdvertisModel.getAdtype(),
                adUnLockAdvertisModel.getDspAdType(),
                extraParams,
                new IVideoAdStatueCallBack() {
                    Dialog mDialog;
                    boolean isClosed;
                    boolean onVideoClicked = false;
                    @Override
                    public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                        lookVideoModel.isRetryed = false;
                        if (lookVideoModel.index == 0) {
                            Context context = MainApplication.getMyApplicationContext();
                            lookVideoModel.isPlaying =
                                    XmPlayerManager.getInstance(context).isPlaying();
                            if (lookVideoModel.isPlaying) {
                                XmPlayerManager.getInstance(context).pause(PauseReason.Business.AdUnlockPaidManager);
                            }
                        }

                        AdStateReportManager.getInstance().onShowSuccess(adUnLockAdvertisModel, false, false,
                                requestTime, lookVideoModel.sourceName,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(lookVideoModel.albumId + "");
                                        builder.trackId(lookVideoModel.trackId + "");
                                        builder.adNum(lookVideoModel.index + "");
                                        builder.setUnlockTimes(lookVideoModel.unlockTimes + "");
                                        builder.uuid(lookVideoModel.uuid);
                                    }
                                });
                    }

                    @Override
                    public void onAdLoadError(int code, String message) {
                        int status = AdStateReportManager.STATUS_REQUEST_TIMEOUT_OR_ERROR;
                        if (IVideoAdStatueCallBack.ERROR_CODE_NO_AD == code) {
                            status = AdStateReportManager.STATUS_SDK_NO_BACK;
                        }
                        AdStateReportManager.getInstance().onShowFail(adUnLockAdvertisModel, status, requestTime,
                                lookVideoModel.sourceName,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(lookVideoModel.albumId);
                                        builder.trackId(lookVideoModel.trackId);

                                        builder.adNum(lookVideoModel.index + "");
                                        builder.setUnlockTimes(lookVideoModel.unlockTimes + "");
                                    }
                                });

                        // 播放失败后重试
                        if (!lookVideoModel.isRetryed) {
                            lookVideoModel.curSlotIdIndex++;
                            lookVideoModel.isRetryed = true;
                            lookNextVideo(mActivity, lookVideoModel, allComplete);
                        } else {
                            new DialogBuilder(mActivity).setMessage("请求失败,是否重试?")
                                    .setCancelBtn("取消")
                                    .setOkBtn("重试", new DialogBuilder.DialogCallback() {

                                        @Override
                                        public void onExecute() {
                                            lookVideoModel.curSlotIdIndex++;
                                            lookVideoModel.isRetryed = true;
                                            lookNextVideo(mActivity,
                                                    lookVideoModel, allComplete);
                                        }

                                    }).showConfirm();
                        }
                    }

                    @Override
                    public void onAdPlayStart() {
                        recordModel.setBeginShowTime(System.currentTimeMillis());
                        AdManager.adRecord(MainApplication.getMyApplicationContext(), adUnLockAdvertisModel,
                                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                                        .sdkType(AdManager.getSDKType(adUnLockAdvertisModel) + "")
                                        .dspPositionId(finalDspPositionId)
                                        .adNum(lookVideoModel.index + "")
                                        .uid(UserInfoMannage.getUid() + "")
                                        .unlockTimes(lookVideoModel.unlockTimes + "")
                                        .albumIdUseStr(lookVideoModel.albumId)
                                        .trackId(lookVideoModel.trackId)
                                        .sourceName(lookVideoModel.sourceName)
                                        .build());
                    }

                    @Override
                    public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                        if(onVideoClicked && AdManager.isThirdAd(adUnLockAdvertisModel)) {
                            return;
                        }

                        onVideoClicked = true;

                        AdReportModel.Builder builder =
                                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_INCENTIVE)
                                // 仅做上报
                                .sdkType(AdManager.getSDKType(adUnLockAdvertisModel) + "")
                                .dspPositionId(finalDspPositionId)
                                .adNum(lookVideoModel.index + "")
                                .uid(UserInfoMannage.getUid() + "")
                                .unlockTimes(lookVideoModel.unlockTimes + "")
                                .albumIdUseStr(lookVideoModel.albumId)
                                .trackId(lookVideoModel.trackId)
                                .sourceName(lookVideoModel.sourceName);
                        if(AdManager.isThirdAd(adUnLockAdvertisModel)) {
                            builder.ignoreTarget(true)
                                    .onlyClickRecord(true);
                        }
                        AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), adUnLockAdvertisModel,
                                builder.build());
                    }

                    @Override
                    public void onAdClose(boolean isCustomCloseBtn) {
                        CanPauseCountDownTimer countDownTimer = extraParams.getCountDownTimer();
                        if(countDownTimer != null) {
                            countDownTimer.cancel();
                        }

                        if (isCustomCloseBtn) {
                            CustomToast.showFailToast("解锁失败，需要重新观看视频解锁");
                        }

                        if (mDialog != null) {
                            mDialog.dismiss();
                            mDialog = null;
                        }

                        if(isClosed) {
                            return;
                        }
                        isClosed = true;

                        if(!isCustomCloseBtn) {
                            HandlerManager.removeCallbacks(lookVideoModel.mLoadNextRunnable);

                            if(lookVideoModel.needAdCount <= lookVideoModel.index) {
                                recordVideoShowTime(adUnLockAdvertisModel, finalDspPositionId,
                                        lookVideoModel, recordModel);

                                if (allComplete != null) {
                                    allComplete.onReady();
                                }
                            }
                        }
                    }

                    @Override
                    public void onAdPlayComplete() {
                        if (mDialog != null) {
                            mDialog.dismiss();
                            mDialog = null;
                        }
                    }

                    @Override
                    public void onAdPlayError(int code, String message) {
                        Logger.log("AdUnLockPaidManager :  " + code + "    " + message);
                        new DialogBuilder<>(RewardVideoAdManager.getInstance().getRecentActivity())
                                .setMessage("视频播放失败,是否重试?")
                                .setOkBtn("重试", new DialogBuilder.DialogCallback() {
                                    @Override
                                    public void onExecute() {
                                        RewardVideoAdManager.getInstance().finishRecentActivity();

                                        lookNextVideo(mActivity, lookVideoModel,
                                                allComplete);
                                    }
                                }).setCancelBtn("取消", new DialogBuilder.DialogCallback() {
                            @Override
                            public void onExecute() {
                                RewardVideoAdManager.getInstance().finishRecentActivity();

                                onAdClose(true);
                            }
                        }).setCancelable(false)
                                .setOutsideTouchCancel(false)
                                .showConfirm();
                    }

                    @Override
                    public View.OnClickListener getCloseClickListener(Activity myActivity) {
                        return new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if (!ToolUtil.activityIsValid(myActivity)) {
                                    return;
                                }

                                RewardGiveUpHintDialog rewardGiveUpHintDialog =
                                        new RewardGiveUpHintDialog(myActivity);
                                rewardGiveUpHintDialog.setShowNum(lookVideoModel.needAdCount - lookVideoModel.index - 1);
                                rewardGiveUpHintDialog.setShowStyle(RewardGiveUpHintDialog.SHOW_STYLE_UNLOCK_PAID);
                                rewardGiveUpHintDialog.setCancleHandle(new IHandleOk() {
                                    @Override
                                    public void onReady() {
                                        onAdClose(true);

                                        myActivity.finish();
                                    }
                                });

                                rewardGiveUpHintDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                                    @Override
                                    public void onDismiss(DialogInterface dialog) {
                                        mDialog = null;
                                    }
                                });
                                rewardGiveUpHintDialog.show();
                                mDialog = rewardGiveUpHintDialog;
                            }
                        };
                    }
                });
    }

    private static void recordVideoShowTime(AdUnLockAdvertisModel adUnLockAdvertisModel,
                                            String finalDspPositionId,
                                            LookVideoModel lookVideoModel,
                                            RewardExtraParams.RecordModel recordModel) {
        if(lookVideoModel.isRecordShowTime) {
            return;
        }

        lookVideoModel.isRecordShowTime = true;
        AdManager.adRecord(MainApplication.getMyApplicationContext(), adUnLockAdvertisModel,
                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SHOW_TIME,
                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                        .sdkType(AdManager.getSDKType(adUnLockAdvertisModel) + "")
                        .dspPositionId(finalDspPositionId)
                        .adNum(lookVideoModel.index + "")
                        .uid(UserInfoMannage.getUid() + "")
                        .unlockTimes(lookVideoModel.unlockTimes + "")
                        .albumIdUseStr(lookVideoModel.albumId)
                        .trackId(lookVideoModel.trackId)
                        .sourceName(lookVideoModel.sourceName)
                        .showTimeMs((int) (System.currentTimeMillis() - recordModel.getBeginShowTime()))
                        .build());
    }

    public static class LookVideoModel {
        int videoCloseTime;
        int videoTime;
        int needAdCount;
        int unlockTimes;
        int index;
        boolean isPlaying;
        int curSlotIdIndex;
        boolean isRetryed;
        String albumId;
        String trackId;
        String sourceName;
        private List<AdUnLockAdvertisModel> unLockAdvertisModels;

        LoadNextRunnable mLoadNextRunnable;
        String uuid;
        boolean isGotoUnlocked = false;
        boolean isRecordShowTime = false;
    }

    public static final String ON_TRACK_UNLOCK_ACTION = "ON_TRACK_UNLOCK_ACTION";
    public static final String TRACK_ID = "TRACK_ID";
    public static final String EXPIRE_TIME = "EXPIRE_TIME";
    private static final IntentFilter mUnLockFilter = new IntentFilter(ON_TRACK_UNLOCK_ACTION);
    private static Map<String, UnLockBroadCastReceiver> mLockBroadCastReceiverMap = new HashMap<>();

    public static void registerUnLockSuccess(Context context, long albumId, String pageClassName,
                                             List<Track> trackList) {
        if (context == null) {
            return;
        }

        String pageId = createPageId(albumId, pageClassName);

        LocalBroadcastManager.getInstance(context.getApplicationContext()).
                registerReceiver(obtainBroadCast(pageId, trackList),
                        mUnLockFilter);
    }

    public static void unRegisterUnLockBroadCast(Context context, long albumId,
                                                 String pageClassName) {
        String pageId = createPageId(albumId, pageClassName);
        UnLockBroadCastReceiver unLockBroadCastReceiver = mLockBroadCastReceiverMap.get(pageId);

        if (unLockBroadCastReceiver == null) {
            return;
        }

        LocalBroadcastManager.getInstance(context.getApplicationContext()).
                unregisterReceiver(unLockBroadCastReceiver);
        mLockBroadCastReceiverMap.remove(pageId);
    }

    private static String createPageId(long albumId, String pageClassName) {
        return pageClassName + albumId;
    }

    private static UnLockBroadCastReceiver obtainBroadCast(String pageId, List<Track> trackList) {
        UnLockBroadCastReceiver unLockBroadCastReceiver = mLockBroadCastReceiverMap.get(pageId);
        if (unLockBroadCastReceiver == null) {
            unLockBroadCastReceiver = new UnLockBroadCastReceiver(trackList);
        } else {
            unLockBroadCastReceiver.setTracks(trackList);
        }
        return unLockBroadCastReceiver;
    }

    public static class UnLockBroadCastReceiver extends BroadcastReceiver {
        private List<Track> mTracks;

        public void setTracks(List<Track> tracks) {
            mTracks = tracks;
        }

        public UnLockBroadCastReceiver(List<Track> tracks) {
            mTracks = tracks;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || ToolUtil.isEmptyCollects(mTracks)) {
                return;
            }

            long trackId = intent.getLongExtra(TRACK_ID, 0);
            long expireTime = intent.getLongExtra(EXPIRE_TIME, 0);

            for (Track track : mTracks) {
                if (track != null && track.getDataId() == trackId) {
                    track.setExpireTime(expireTime);
                    return;
                }
            }
        }
    }


    public static void sendUnLockSuccess(Context context, long trackId, long expireTime) {
        Intent intent = new Intent(AdUnLockPaidManager.ON_TRACK_UNLOCK_ACTION);
        intent.putExtra(AdUnLockPaidManager.TRACK_ID, trackId);
        intent.putExtra(AdUnLockPaidManager.EXPIRE_TIME, expireTime);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    public static void scaleAnimationView(View unlockBtn) {
        if (unlockBtn == null) {
            return;
        }

        float v = 1.1f;
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(unlockBtn, "scaleX", 1f,
                v, 1.0f, v, 1.0f, v, 1.0f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(unlockBtn, "scaleY", 1f,
                v, 1.0f, v, 1.0f, v, 1.0f);

        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleXAnimator, scaleYAnimator);
        animatorSet.setDuration(2000);
        animatorSet.start();
    }

    public static void setChildViewClip(View childView) {
        if (childView instanceof ViewGroup) {
            ((ViewGroup) childView).setClipChildren(false);
            ((ViewGroup) childView).setClipToPadding(false);

            int childCount = ((ViewGroup) childView).getChildCount();
            for (int i = 0; i < childCount; i++) {
                setChildViewClip(((ViewGroup) childView).getChildAt(i));
            }
        }
    }

    private static String mVersionName;

    private static String versionName() {

        if (!TextUtils.isEmpty(mVersionName)) {
            return mVersionName;
        }

        String versionName = null;
        try {
            versionName = MainApplication.getMyApplicationContext().getPackageManager()
                    .getPackageInfo(MainApplication.getMyApplicationContext().getPackageName(),
                            0).versionName;
            if (!TextUtils.isEmpty(versionName)) {
                String[] str = versionName.split("\\.");
                if (str.length > 3) {
                    StringBuilder sb = null;
                    for (int i = 0; i < 3; i++) {
                        if (sb == null) {
                            sb = new StringBuilder();
                            sb.append(str[i]);
                        } else {
                            sb.append(".");
                            sb.append(str[i]);
                        }
                    }
                    versionName = sb.toString();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        mVersionName = versionName;

        return versionName;

    }

    public static boolean canShowGuideUnLockDialog() {
        String version = OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext())
                        .getString(PreferenceConstantsInOpenSdk.KEY_SHOW_GUIDE_UNLOCK_SHOW_VERSION);

        if (TextUtils.equals(versionName(), version)) {
            return false;
        }
        return true;
    }

    public static void saveShowedGuidUnlockDialog() {
        OnlyUseMainProcessSharePreUtil.getInstance(MainApplication.getMyApplicationContext())
                .saveString(PreferenceConstantsInOpenSdk.KEY_SHOW_GUIDE_UNLOCK_SHOW_VERSION,
                        versionName());
    }


    public static String unlockContent(@NonNull Track track) {
        if (track.isAudition()) {
            return track.getSampleDuration() + "秒试听结束，还可通过以下方式畅听完整版";
        } else {
            return "付费节目，还可通过以下方式畅听完整版";
        }
    }

    // 0 表示初始状态,什么事情也不用做, 1 表示显示解锁图标 2 表示显示倒计时
    public static int unLockState(Track track) {
        if (track == null) {
            return 0;
        }

        if (!track.isFree() && track.isPaid()) {
            if (track.getExpireTime() > 0 && track.getExpireTime() - System.currentTimeMillis() > 0) {
                return 2;
            } else {
                return 1;
            }
        }

        return 0;
    }

    public static CharSequence getGuideCopy(String guideCopy) {
        if(TextUtils.isEmpty(guideCopy)) {
            return guideCopy;
        }

        String[] split = guideCopy.split("#");
        if (split.length % 2 == 0) {
            return guideCopy;
        }

        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        for (int i = 0; i < split.length; i++) {
            if (i % 2 != 0) {
                int startIndex = spannableStringBuilder.length();
                spannableStringBuilder.append(split[i]);
                AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(12, true);
                spannableStringBuilder.setSpan(sizeSpan, startIndex,
                        spannableStringBuilder.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else {
                spannableStringBuilder.append(split[i]);
            }
        }

        return spannableStringBuilder;
    }

    public static boolean isLoginJumped() {
        return loginJumped;
    }

    public static boolean isLoginJumpStatusForAlbumDestory() {
        return loginJumpStatusForAlbumDestory;
    }
    public static long loginJumpBeforeTrackId() {
        return selectTrackId;
    }

    public static void onAlbumFragmentDestory() {
        loginJumpStatusForAlbumDestory = false;
        selectTrackId = 0;

        UserInfoMannage.getInstance().removeLoginStatusChangeListener(sILoginStatusChangeListener);
    }

    public static void resetSelectTrackId() {
        selectTrackId = 0;
    }
}
