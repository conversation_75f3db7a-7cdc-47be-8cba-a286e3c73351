package com.ximalaya.ting.android.host.manager.ad.videoad;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.MimeTypeMap;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.IDownloadServiceStatueListener;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.service.DownloadService;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Blur;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.framework.util.NavigationUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.base.BaseFragmentActivity2;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockVipTrackManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.ui.AnimationUtil;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.util.Locale;

import androidx.annotation.Nullable;

/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2020/8/12
 */
public class VideoAdActivity extends BaseFragmentActivity2 implements View.OnClickListener, IDownloadServiceStatueListener,
        VideoAdVideoPlayerManager.IVideoAdPlayerLister {
    private static final String TAG = "VideoAdActivity";

    public ViewGroup mVideoContainer;
    private ImageView mIvVoiceSwitch;
    public ImageView mIvVideoState;
    public TextView mTvCountdown;
    public ImageView mBgIv;

    //bottom
    private View mLayoutBottom;
    private RoundImageView mIvBottom;
    private TextView mTvBottomTitle;
    private TextView mTvBottomContent;
    private ProgressBar mProBottomBtn;
    private TextView mTvBottomBtn;

    //video end
    private View mLayoutVideoEnd;
    private RoundImageView mIvVideoEnd;
    private TextView mTvVideoEndTitle;
    private TextView mTvVideoEndContent;
    private ProgressBar mProVideoEndBtn;
    private TextView mTvVideoEndBtn;

    private Advertis mAdvertis;
    private int mVideoPlayOverTime;
    private int mVideoCanCloseTime;
    private CanPauseCountDownTimer mCountDownTimer;
    private Drawable mCloseDrawable;
    @Nullable
    private String mDownloadUrl;
    private String mLocalUrl;

    private boolean mIsTimerStart = false;      //用于防止CanPauseCountDownTimer.resume使未开始的timer开始计时
    public boolean isVisibleToUser = false;    //下载回调是在onDestroy销毁的 此参数用于不让app后台更新界面；

    public VideoAdVideoPlayerManager mAdVideoPlayerManager;
    private String mPositionName;
    private long mRequestKey;
    private int mRewardCountDownStyle;
    private CountDownTimer adMaxLoadCountDownTimer; // 广告加载超时检测

    public static void startVideoAdActivity(long requestKey, RewardExtraParams extraParams) {
        if (BaseApplication.getMainActivity() == null) {
            return;
        }
        Advertis advertis = extraParams.getAdvertis();
        String positionName = extraParams.getPositionName();
        int videoPlayOverTime = extraParams.getVideoPlayOverTime();
        int canCloseTime = extraParams.getCanCloseTime();
        int rewardCountDownStyle = extraParams.getRewardCountDownStyle();
        Intent intent = new Intent(BaseApplication.getMainActivity(), VideoAdActivity.class);
        intent.putExtra("requestKey", requestKey);
        intent.putExtra("ParamAdvertis", advertis);
        intent.putExtra("ParamPosition", positionName);
        intent.putExtra("videoPlayOverTime", videoPlayOverTime);
        intent.putExtra("canCloseTime", canCloseTime);
        intent.putExtra("rewardCountDownStyle", rewardCountDownStyle);
        BaseApplication.getMainActivity().startActivity(intent);
        BaseApplication.getMainActivity().overridePendingTransition(com.ximalaya.ting.android.framework.R.anim.framework_slide_in_right,
                com.ximalaya.ting.android.framework.R.anim.framework_slide_out_keep_state);
    }

    @Override
    protected void onCreate(Bundle savedState) {
        super.onCreate(savedState);

        if (getIntent() != null) {
            mAdvertis = getIntent().getParcelableExtra("ParamAdvertis");
            mPositionName = getIntent().getStringExtra("ParamPosition");
            mRequestKey = getIntentExtras().getLong("requestKey");
            mVideoPlayOverTime = getIntent().getIntExtra("videoPlayOverTime", 20);
            mRewardCountDownStyle = getIntent().getIntExtra("rewardCountDownStyle", RewardExtraParams.REWARD_COUNT_DOWN_STYLE);
            mVideoCanCloseTime = getIntent().getIntExtra("canCloseTime",5);
            if (mVideoCanCloseTime == 0) {
                mVideoCanCloseTime = 5;
            }
        }

        if (mAdvertis == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));
            finish();
            return;
        }

        if(AdManager.isDownloadAd(mAdvertis)) {
            mDownloadUrl = mAdvertis.getRealLink();

            if(DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADED) {
                mLocalUrl = DownloadServiceManage.getInstance().getDownloadSavePath(mDownloadUrl);
            }
        }

        VideoAdActivity_XmLifecycleBinder.bind(this, this.getLifecycle());
        adMaxLoadCountDownTimer = null;
        if (RewardExtraParams.isFreeListen(mRewardCountDownStyle)) {
            adMaxLoadCountDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(mRewardCountDownStyle), 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                }

                @Override
                public void onFinish() {
                    // 10秒之后广告仍未加载完成，认为此次广告加载失败
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                            callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME, "广告加载超时"));
                    finish();
                }
            };
            adMaxLoadCountDownTimer.start();
            AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(mRewardCountDownStyle));
        }

        mCountDownTimer = new CanPauseCountDownTimer(mVideoPlayOverTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                setCountDownContent((int) (millisUntilFinished / 1000) + 1, mVideoCanCloseTime, mVideoPlayOverTime);
            }

            @Override
            public void onFinish() {
                mTvCountdown.setText(null);
                mTvCountdown.setCompoundDrawablePadding(0);
                mTvCountdown.setOnClickListener(VideoAdActivity.this);
            }
        };

        mBgIv = findViewById(R.id.host_video_ad_video_bg_iv);
        mTvCountdown = findViewById(R.id.host_video_ad_countdown_tv_new);
        mIvVoiceSwitch = findViewById(R.id.host_video_ad_voice_switch);
        mVideoContainer = findViewById(R.id.host_video_ad_video_container);
        mIvVideoState = findViewById(R.id.host_video_ad_video_state);

        mLayoutBottom = findViewById(R.id.host_video_ad_bottom_layout);
        mIvBottom = findViewById(R.id.host_video_ad_bottom_iv);
        mTvBottomTitle = findViewById(R.id.host_video_ad_bottom_title);
        mTvBottomContent = findViewById(R.id.host_video_ad_bottom_content);
        mProBottomBtn = findViewById(R.id.host_video_ad_bottom_btn_pro);
        mTvBottomBtn = findViewById(R.id.host_video_ad_bottom_btn_btn);

        mLayoutVideoEnd = findViewById(R.id.host_video_ad_video_end_layout);
        mIvVideoEnd = findViewById(R.id.host_video_ad_video_end_iv);
        mTvVideoEndTitle = findViewById(R.id.host_video_ad_video_end_title);
        mTvVideoEndContent = findViewById(R.id.host_video_ad_video_end_content);
        mProVideoEndBtn = findViewById(R.id.host_video_ad_video_end_btn_pro);
        mTvVideoEndBtn = findViewById(R.id.host_video_ad_video_end_btn_btn);

        mIvVoiceSwitch.setOnClickListener(this);
        mLayoutBottom.setOnClickListener(this);
        mLayoutVideoEnd.setOnClickListener(this);
        mVideoContainer.setOnClickListener(this);

        mIvVideoEnd.setUseCache(false);
        mCloseDrawable = LocalImageUtil.getDrawable(this, R.drawable.host_close_white_height_48);
        DownloadServiceManage.getInstance().addDownloadListener(this);

        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                callback->callback.onAdLoad(XmNativeAd.createXmNativeAdByAdvertis(mAdvertis)));

        initView();
    }

    private void setCountDownContent(int time, int canCloseTime, int finalWatchVideoAllTime) {
        String str;
        if (time < 10) {
            str = "0" + time;
        } else {
            str = time + "";
        }

        SpannableStringBuilder msp = new SpannableStringBuilder(str);
        ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(Color.parseColor("#FF431C"));
        msp.setSpan(foregroundColorSpan, 0, str.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        String str1 = " | ";
        msp.append(str1);
        ForegroundColorSpan foregroundColorSpanLine = new ForegroundColorSpan(Color.parseColor("#33FFFFFF"));
        msp.setSpan(foregroundColorSpanLine, msp.length() - str1.length(), msp.length(),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        if(time < (finalWatchVideoAllTime - canCloseTime)) {
            mTvCountdown.setCompoundDrawablesWithIntrinsicBounds(null, null, mCloseDrawable, null);
            mTvCountdown.setOnClickListener(this);
        } else {
            String str2 = (canCloseTime - (finalWatchVideoAllTime - time)) + "s后可关闭";
            msp.append(str2);
            mTvCountdown.setOnClickListener(null);
            mTvCountdown.setCompoundDrawables(null, null, null, null);
        }
        mTvCountdown.setText(msp);
    }


    private void initView() {
        if (mAdvertis == null) {
            return;
        }
        mTvBottomBtn.setText(mAdvertis.getButtonText());
        mTvBottomTitle.setText(mAdvertis.getName());
        mTvBottomContent.setText(mAdvertis.getDescription());
        ImageManager.from(this).displayImage(mIvBottom, mAdvertis.getLogoUrl(), -1,
                BaseUtil.dp2px(this, 50), BaseUtil.dp2px(this, 50));

        mLayoutBottom.setAlpha(0f);

        mAdVideoPlayerManager = new VideoAdVideoPlayerManager(this, mAdvertis.getVideoCover());

//        ImageManager.from(this).downloadBitmap(mAdvertis.getImageUrl(), new ImageManager.DisplayCallback() {
//            @Override
//            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
//                if (!canUpdateUi()) {
//                    return;
//                }
//                if (bitmap != null) {
//                    doBlurTask(bitmap);
//                }
//            }
//        });
    }

    @SuppressLint("StaticFieldLeak")
    public void doBlurTask(Bitmap originBitmap) {
        Context context = getContext();
        new MyAsyncTask<Void, Void, Bitmap>() {
            @Override
            protected Bitmap doInBackground(Void... voids) {
                return Blur.fastBlur(context, originBitmap, 10);
            }

            @Override
            protected void onPostExecute(Bitmap bitmap) {
                if (!canUpdateUi()) {
                    return;
                }
                if (bitmap != null) {
                    mBgIv.setBackground(null);
                    mBgIv.setImageBitmap(bitmap);
                }
            }
        }.execute();
    }


    public void showVideoCompleteView() {
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayComplete);

        mLayoutBottom.setVisibility(View.INVISIBLE);
        mIvVoiceSwitch.setVisibility(View.INVISIBLE);
        mLayoutVideoEnd.setVisibility(View.VISIBLE);

        mTvVideoEndTitle.setText(mAdvertis.getName());
        mTvVideoEndContent.setText(mAdvertis.getDescription());

        initDownLoadStatus();

        ImageManager.from(this).displayImage(mIvVideoEnd, mAdvertis.getLogoUrl(), -1,
                BaseUtil.dp2px(this, 90), BaseUtil.dp2px(this, 90));
    }


    @Override
    public int getContainerLayoutId() {
        return R.layout.host_act_video_ad;
    }


    @Override
    public void onPause() {
        super.onPause();
        isVisibleToUser = false;
        mAdVideoPlayerManager.onPauseMy();
        ViewUtil.keepScreenOn(this, false);
    }

    @Override
    public void onResume() {
        super.onResume();
        isVisibleToUser = true;
        mAdVideoPlayerManager.onResumeMy();
        ViewUtil.keepScreenOn(this, true);
        initDownLoadStatus();
        NavigationUtil.hideNavigationBar(getWindow(), true);
    }


    private boolean isNormal = false;
    @Override
    public void finish() {
        isNormal = true;
        super.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
        //DownloadServiceManage.getStatueByUrl获取的状态不准确，本页面根据回调确定状态，即使应用在后台也需要更新状态  所以在这里解注册
        DownloadServiceManage.getInstance().removeDownloadListener(this);

        if (!isNormal) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callback -> callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT,
                            "播放出错"));
            if (adMaxLoadCountDownTimer != null) {
                adMaxLoadCountDownTimer.cancel();
            }
        }
        mAdVideoPlayerManager.release();
        mAdVideoPlayerManager.onDestroyMy();
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback->callback.onAdClose(false));
    }


    //播放失败的回调
    public void onPlayError() {
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                callback->callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "播放出错"));
        if (adMaxLoadCountDownTimer != null) {
            adMaxLoadCountDownTimer.cancel();
        }
        if (!canUpdateUi()) {
            return;
        }
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
        }
        mTvCountdown.setVisibility(View.VISIBLE);
        mTvCountdown.setText(null);
        mTvCountdown.setCompoundDrawablesWithIntrinsicBounds(null, null, mCloseDrawable, null);
        mTvCountdown.setCompoundDrawablePadding(0);
        mTvCountdown.setOnClickListener(VideoAdActivity.this);
        AnimationUtil.stopAnimation(mIvVideoState);
        mIvVideoState.setVisibility(View.INVISIBLE);

        finish();
    }

    //可以返回时返回键及左上角关闭按钮都会走到这里
    private void onMyClose() {
        finish();
    }

    public void onPlayStart() {
        if (adMaxLoadCountDownTimer != null) {
            adMaxLoadCountDownTimer.cancel();
        }
    }

    public void startCountDown() {
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayStart);

        if (mCountDownTimer != null) {
            mIsTimerStart = true;
            mTvCountdown.setVisibility(View.VISIBLE);
            mCountDownTimer.start();

            HandlerManager.postOnUIThread(() -> {
                ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(mLayoutBottom,
                        "alpha", 0f, 1.0f);
                ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(mLayoutBottom,
                        "translationY", mLayoutBottom.getHeight(), 0);
                alphaAnimator.setDuration(500);
                translationYAnimator.setDuration(500);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(alphaAnimator, translationYAnimator);
                animatorSet.start();
            });

        }
    }

    @Override
    public View getVideoStateView() {
        return mIvVideoState;
    }

    @Override
    public ViewGroup getVideoContainer() {
        return mVideoContainer;
    }

    public void onTimerPause() {
        if (mCountDownTimer != null) {
            mCountDownTimer.pause();
        }
    }

    public void onTimerResume() {
        if (mCountDownTimer != null && mIsTimerStart) {
            mCountDownTimer.resume();
        }
    }

    @Override
    public void onBackPressed() {
        // 屏蔽返回键
    }

    @Override
    public void onClick(View v) {
        if (!canUpdateUi() || v == null) {
            return;
        }
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.host_video_ad_countdown_tv_new) {
            onMyClose();
        } else if (id == R.id.host_video_ad_voice_switch) {
            mAdVideoPlayerManager.setVolume(mIvVoiceSwitch.isSelected());
            mIvVoiceSwitch.setSelected(!mIvVoiceSwitch.isSelected());
        } else if (id == R.id.host_video_ad_bottom_layout || id == R.id.host_video_ad_video_end_layout) {
            NavigationUtil.hideNavigationBar(getWindow(), true);

            //底部下载按钮、视频结束下载按钮
            if (TextUtils.isEmpty(mDownloadUrl)) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback->callback.onAdVideoClick(false, 0));
                return;
            }
            if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADING) {
                //正在下载 点击暂停
                DownloadServiceManage.getInstance().pauseDownload(mDownloadUrl);
            } else if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADED) {
                //下载完成 点击安装
                gotoInstallApk();
            } else if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_PAUSE) {
                //暂停 点击继续下载
                DownloadServiceManage.getInstance().startDownload(mDownloadUrl);
            } else if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOAD_FAILED) {
                //失败 点击重新下载
                DownloadServiceManage.getInstance().startDownload(mDownloadUrl);
            } else {
                //DownloadServiceManage.getInstance().downLoadAPK(mDownloadUrl, 888);
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback->callback.onAdVideoClick(false, 0));
            }

        } else if (id == R.id.host_video_ad_video_container) {
            NavigationUtil.hideNavigationBar(getWindow(), true);
        }
    }


    public boolean canUpdateUi() {
        return !isFinishing() && !isDestroyed();
    }

    @Override
    public void onProgress(long curPosition, long duration) {

    }

    public boolean isVisibleToUser() {
        return isVisibleToUser;
    }

    @Override
    public void onPauseCallBack(String url) {
        if (!canUpdateUi() || !isDownLoadUrlSame(url)) {
            return;
        }
        Logger.d(TAG, "onPauseCallBack");
        initDownLoadStatus();
    }

    @Override
    public void onStartCallBack(String url, boolean isRestart) {
        if (!canUpdateUi() || !isDownLoadUrlSame(url)) {
            return;
        }
        Logger.d(TAG, "onStartCallBack");
        CustomToast.showSuccessToast("开始下载");
        initDownLoadStatus();
    }

    @Override
    public void onRemoveCallBack(String url) {
        Logger.d(TAG, "onRemoveCallBack");
    }

    @Override
    public void onDownloadErrorCallBack(String url) {
        if (!canUpdateUi() || !isDownLoadUrlSame(url)) {
            return;
        }
        Logger.d(TAG, "onDownloadErrorCallBack");

        CustomToast.showFailToast("下载失败");
        initDownLoadStatus();
    }

    @Override
    public void onDownloadSuccessCallBack(String url, String filePath) {
        if (!canUpdateUi() || !isDownLoadUrlSame(url)) {
            return;
        }
        Logger.d(TAG, "onDownloadSuccessCallBack");
        mLocalUrl = filePath;
        initDownLoadStatus();
    }

    @Override
    public void onServiceBindSuccess() {
        if(DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADED) {
            mLocalUrl = DownloadServiceManage.getInstance().getDownloadSavePath(mDownloadUrl);
        }

        initDownLoadStatus();
    }

    private boolean isDownLoadUrlSame(String url) {
        return !TextUtils.isEmpty(mDownloadUrl) && !TextUtils.isEmpty(url) && mDownloadUrl.equals(url);
    }


    private void initDownLoadStatus() {
        if (isVisibleToUser) {
            if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADING && !TextUtils.isEmpty(mDownloadUrl)) {
                handleDownLoadBtn(true, "下载中");
            } else if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_DOWNLOADED && !TextUtils.isEmpty(mDownloadUrl)) {
                handleDownLoadBtn(false, "安装");
            } else if (DownloadServiceManage.getInstance().getStatueByUrl(mDownloadUrl) == DownloadService.STATUS_PAUSE && !TextUtils.isEmpty(mDownloadUrl)) {
                handleDownLoadBtn(false, "继续");
            } else {
                if (mAdvertis != null && mAdvertis.getButtonText() != null) {
                    handleDownLoadBtn(false,  mAdvertis.getButtonText());
                } else {
                    handleDownLoadBtn(false,  "下载");
                }
            }
        }
    }


    private void handleDownLoadBtn(boolean showProgress, String str) {
        if (!canUpdateUi()) {
            return;
        }
        if (mLayoutVideoEnd.getVisibility() == View.VISIBLE) {
            //视频结束的全屏界面
            mProVideoEndBtn.setVisibility(showProgress ? View.VISIBLE : View.GONE);
            mTvVideoEndBtn.setText(str);
        } else {
            mProBottomBtn.setVisibility(showProgress ? View.VISIBLE : View.GONE);
            mTvBottomBtn.setText(str);
        }
    }


    private void gotoInstallApk() {
        if (!canUpdateUi() || TextUtils.isEmpty(mLocalUrl)) {
            return;
        }
        File file = new File(mLocalUrl);
        if (file.exists()) {
            String fileName = file.getName().toUpperCase(Locale.getDefault());
            if (TextUtils.isEmpty(fileName) || !fileName.endsWith(".APK")) {
                return;
            }

            Uri uri = FileProviderUtil.fromFile(file);
            String exName = MimeTypeMap.getFileExtensionFromUrl(uri.toString());
            String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(exName);
            if (!TextUtils.isEmpty(mimeType)) {
                Intent intent2 = new Intent(Intent.ACTION_VIEW);
                intent2.setDataAndType(uri, mimeType);
                intent2.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                if (Build.VERSION.SDK_INT >= 24) {
                    intent2.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                ToolUtil.checkIntentAndStartActivity(this, intent2);
            }
        } else {
            initDownLoadStatus();
            CustomToast.showFailToast("下载失败，请重新下载", Toast.LENGTH_SHORT);
        }
    }


    public void bindVolumeClick(View volumeView) {
        if (volumeView == null || volumeView.getVisibility() != View.VISIBLE) {
            return;
        }
        volumeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAdVideoPlayerManager.setVolume(volumeView.isSelected());
                volumeView.setSelected(!volumeView.isSelected());
            }
        });
    }

}
