package com.ximalaya.ting.android.host.manager.ad.egg.manager;

import android.app.Activity;
import android.content.Context;
import android.view.View;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdTokenManager;
import com.ximalaya.ting.android.host.manager.ad.egg.view.AdVideoBackInterceptDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * @Time 11/26/20
 * @Description ad video view  激励视频 管理 -- 针对用户营销福利， 砸金蛋活动
 */
public class AdVideoWelfareManager {

    private AdVideoWelfareManager() {
    }

    private static class AdInstance {
        private static final AdVideoWelfareManager sAdManager = new AdVideoWelfareManager();
    }

    public static AdVideoWelfareManager getInstance() {

        return AdInstance.sAdManager;
    }

    public void getVideoAdInfo(Context context, VideoCallback videoCallback) {

        AdRequest.getAdLookVideo(new IDataCallBack<List<Advertis>>() {
            @Override
            public void onSuccess(@Nullable List<Advertis> object) {
                if (object != null && object.get(0) != null) {
                    lookVideo(context, object.get(0), videoCallback);
                } else {
                    // 获取广告失败回调
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            new Advertis() , AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_SHOW_OB,
                                    AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                    .setSdkFail("0")
                                    .ignoreTarget(true).build());

                    if (videoCallback != null) {
                        videoCallback.onFail(-1);
                    }
                }
            }
            @Override
            public void onError(int code, String message) {
                Logger.i("-----msg", "onError result ---- errMsg  " + message);
                if (videoCallback != null) {
                    videoCallback.onFail(-1);
                }
                // 获取广告失败回调
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        new Advertis() , AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                .setSdkFail("0")
                                .ignoreTarget(true).build());
            }
        }, AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO);
    }


    private long beginTime;
    
    private void lookVideo(Context context, Advertis advertis, VideoCallback videoCallback) {

        if (!ToolUtil.activityIsValid((Activity) context)) {
            return;
        }

        RewardExtraParams params = new RewardExtraParams();
        params.setCloseable(true);
        params.setCanCloseTime(RewardExtraParams.DEFAULT_CLOSE_TIME);

        String posId = AdManager.getDspPositionId(advertis, AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO);

        int adType = advertis.getAdtype();


        RewardVideoAdManager.getInstance().loadRewardAd((Activity) context, advertis, posId, adType, params
                , new IVideoAdStatueCallBack() {
                    @Override
                    public void onAdLoad(AbstractThirdAd thirdAd) {

                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                advertis,
                                AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                        AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                        .promptPlay("0")
                                        .showSource(advertis.getSoundType() + "")
                                        .ignoreTarget(true)
                                        .uid(UserInfoMannage.getUid()+"")
                                        .dspPositionId(advertis.getDspPositionId())
                                        .build());
                    }

                    @Override
                    public void onAdLoadError(int code, String message) {
                        CustomToast.showFailToast("出了点小问题，请稍后重试");
                        if (videoCallback != null) {
                            videoCallback.onFail(-1);
                        }

                        // 获取广告失败回调
                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                new Advertis() , AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                                        AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                        .setSdkFail("0")
                                        .ignoreTarget(true).build());
                    }

                    @Override
                    public void onAdPlayStart() {
                        beginTime = System.currentTimeMillis();
                    }

                    @Override
                    public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {

                        AdManager.handlerAdClick(MainApplication.getMyApplicationContext(),
                                advertis,
                                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                        .ignoreTarget(true)
                                        .onlyClickRecord(true)
                                        .showSource(advertis.getSoundType() + "")
                                        .uid(UserInfoMannage.getUid()+"")
                                        .dspPositionId(advertis.getDspPositionId())
                                        .build());
                    }

                    @Override
                    public void onAdClose(boolean isCustomCloseBtn) {
                        if (interceptDialog != null) {
                            interceptDialog.dismiss();
                            interceptDialog = null;
                        }
                        if (!isCustomCloseBtn) {
                            HandlerManager.obtainMainHandler().removeCallbacks(closeAdRunnable);
                        }
                    }

                    @Override
                    public void onAdPlayComplete() {
                        MyAsyncTask.execute(new Runnable() {
                            @Override
                            public void run() {
                                String adToken = AdTokenManager.getInstance().getShowToken(advertis);
                                if (TextUtils.isEmpty(adToken)) {
                                    adToken = AdTokenManager.getInstance().getShowToken();
                                }
                                if (videoCallback != null) {
                                    videoCallback.onSuccess(adToken);
                                }
                                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                        advertis , AdReportModel.newBuilder(
                                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                                AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                                .promptSuc("0")
                                                .showSource(advertis.getSoundType() + "")
                                                .ignoreTarget(true).build());

                                if (interceptDialog != null) {
                                    interceptDialog.dismiss();
                                    interceptDialog = null;
                                }
                                HandlerManager.postOnBackgroundThreadDelay(closeAdRunnable, 5000);
                            }
                        });
                    }

                    @Override
                    public void onAdPlayError(int code, String message) {
                        CustomToast.showFailToast("出了点小问题，请稍后重试");
                        if (videoCallback != null) {
                            videoCallback.onFail(-1);
                        }

                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                advertis , AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                                        AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                        .promptSuc("1")
                                        .showSource(advertis.getSoundType() + "")
                                        .ignoreTarget(true).build());
                    }

                    @Override
                    public View.OnClickListener getCloseClickListener(Activity activity) {

                        return new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                if(!ToolUtil.activityIsValid(activity)) {
                                    return;
                                }
                                AdManager.adRecord(activity, advertis , AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                                        AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                        .skipAd("0")
                                        .showSource(advertis.getSoundType() + "")
                                        .skipTime((System.currentTimeMillis() - beginTime) + "")
                                        .ignoreTarget(true).build());

                                showAdVideoBackDialog(activity,  new AdVideoBackInterceptDialog.onBtnClickListener() {
                                    @Override
                                    public void onConfirm() {
                                        if (videoCallback != null) {
                                            videoCallback.onFail(-99);
                                        }
                                        if (interceptDialog != null) {
                                            interceptDialog.dismiss();
                                            interceptDialog = null;
                                        }
                                        activity.finish();

                                        AdManager.adRecord(activity, advertis , AdReportModel.newBuilder(
                                                AppConstants.AD_LOG_TYPE_CLICK_OB,
                                                AppConstants.AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO)
                                                .skipAd("1")
                                                .showSource(advertis.getSoundType() + "")
                                                .skipTime((System.currentTimeMillis() - beginTime) + "")
                                                .ignoreTarget(true).build());
                                    }

                                    @Override
                                    public void onCancel() {
                                    }
                                });
                            }
                        };
                    }
                });
    }



    private Runnable closeAdRunnable = new Runnable() {
        @Override
        public void run() {
            RewardVideoAdManager.getInstance().finishRecentActivity();
        }
    };
    private AdVideoBackInterceptDialog interceptDialog;

    public void showAdVideoBackDialog(Context context, AdVideoBackInterceptDialog.onBtnClickListener clickListener) {
        if (interceptDialog != null) {
            interceptDialog.dismiss();
            interceptDialog = null;
        }
        interceptDialog  = new AdVideoBackInterceptDialog(context);
        interceptDialog.setOnBtnClickListener(clickListener);
        interceptDialog.show();
    }

    public interface VideoCallback {
        void onSuccess(String adToken);
        void onFail(int errCode);
    }
}
