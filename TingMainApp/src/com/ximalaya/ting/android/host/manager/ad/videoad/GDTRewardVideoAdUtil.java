package com.ximalaya.ting.android.host.manager.ad.videoad;

import android.app.Activity;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import com.qq.e.ads.cfg.VideoOption;
import com.qq.e.ads.immersive.ImmersiveADFlow;
import com.qq.e.ads.immersive.ImmersiveADFlowListener;
import com.qq.e.ads.rewardvideo.RewardVideoAD;
import com.qq.e.ads.rewardvideo.RewardVideoADListener;
import com.qq.e.comm.util.AdError;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.GdtRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.external.IBaseLoadListener;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.GDTSDKManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCountDownView;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;
import java.lang.ref.WeakReference;
import java.util.Map;

/**
 * Created by le.xin on 2020/5/6.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class GDTRewardVideoAdUtil {
    private static boolean isAdLoaded;
    private static boolean isAdLoadOverTime;
    private static CountDownTimer countDownTimer;
    private static RewardVideoADListener rewardVideoADListener;
    private static IVideoAdStatueCallBack mCallback;

    private static class SingletonHolder {
        private static final GDTRewardVideoAdUtil INSTANCE = new GDTRewardVideoAdUtil();
    }

    @NonNull
    public static GDTRewardVideoAdUtil getInstance() {
        return GDTRewardVideoAdUtil.SingletonHolder.INSTANCE;
    }

    public static class RewardVideoAdWrapper {
        public RewardVideoAD mRewardVideoAD;

        public void setRewardVideoAD(RewardVideoAD rewardVideoAD) {
            mRewardVideoAD = rewardVideoAD;
        }
    }

    public static class ImmersiveRewardVideoAdWrapper {
        public ImmersiveADFlow mImmersiveADFlow;

        public void setImmersiveRewardVideoAD(ImmersiveADFlow rewardVideoAD) {
            mImmersiveADFlow = rewardVideoAD;
        }
    }

    public static void loadRewardVideoAd(@NonNull Activity activity,
                                         Advertis advertis,
                                         String posId,
                                         IVideoAdStatueCallBack callback,
                                         @NonNull RewardExtraParams extraParams,
                                         IDataCallBack<RewardVideoAdWrapper> loadedRewardVideoAdWrapperCallback) {

        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        if (!GDTSDKManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (callback != null) {
                    if (isAdLoaded) {
                        callback.onAdPlayError(i, s);
                    } else {
                        if (countDownTimer != null) {
                            if (isAdLoadOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        callback.onAdLoadError(i, s);
                    }
                }
            }
        })) {
            return;
        }

        RewardVideoAdWrapper rewardAd = new RewardVideoAdWrapper();

        isAdLoaded = false;
        isAdLoadOverTime = false;
        countDownTimer = null;
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                isAdLoadOverTime = true;
                AdLogger.log("GDTRewardVideoAdUtil : adLoadOverTime ");
                if (callback != null) {
                    callback.onAdLoadError(
                            IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                            "广告加载超时");
                }
            }
        };
        countDownTimer.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()));

        rewardVideoADListener = new RewardVideoADListener() {
            @Override
            public void onADLoad() {
                //广告加载成功，可在此回调后进行广告展示
                //广告加载回调
                AdLogger.log("GDTRewardVideoAdUtil : onADLoad ");
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                isAdLoaded = true;
                if(activityWeakReference == null || !ToolUtil.activityIsValid(activityWeakReference.get())) {
                    if (callback != null) {
                        callback.onAdLoadError(
                                IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                "Activity 已不能展示广告");
                    }

                    return;
                }
                if (rewardAd.mRewardVideoAD != null && rewardAd.mRewardVideoAD.hasShown()) {
                    if (callback != null) {
                        callback.onAdLoadError(
                                IVideoAdStatueCallBack.ERROR_CODE_DEFUALT,
                                "广告已经展示过");
                    }
                    return;
                }

                // 广告视频素材缓存完毕，展示view之前将数据返回处理
                if(loadedRewardVideoAdWrapperCallback != null) {
                    loadedRewardVideoAdWrapperCallback.onSuccess(rewardAd);
                }
                if(callback != null) {
                    callback.onAdLoad(new GdtRewardVideoAd(advertis, rewardAd.mRewardVideoAD ,posId));
                }

                if(rewardAd.mRewardVideoAD != null) {
                    rewardAd.mRewardVideoAD.showAD(activityWeakReference.get());
                }
            }

            @Override
            public void onVideoCached() {
                //视频素材缓存成功，可在此回调后进行广告展示
                AdLogger.log("GDTRewardVideoAdUtil : onVideoCached ");
            }

            @Override
            public void onADShow() {
                //激励视频广告页面展示

                AdLogger.log("GDTRewardVideoAdUtil : onADShow ");

            }

            @Override
            public void onADExpose() {
                //激励视频广告曝光

                if(callback != null) {
                    callback.onAdPlayStart();
                }

                AdLogger.log("GDTRewardVideoAdUtil : onADExpose ");
            }

            @Override
            public void onReward(Map<String, Object> map) {
                //激励视频触发激励（观看视频大于一定时长或者视频播放完毕）

                AdLogger.log("GDTRewardVideoAdUtil : onReward ");
                if (callback != null && callback instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) callback).onRewardVerify();
                }
            }

            @Override
            public void onADClick() {
                //激励视频广告被点击

                if(callback != null) {
                    callback.onAdVideoClick(false, 0);
                }

                AdLogger.log("GDTRewardVideoAdUtil : onADClick ");
            }

            @Override
            public void onVideoComplete() {
                //激励视频播放完毕
                if(callback != null) {
                    callback.onAdPlayComplete();
                }

                AdLogger.log("GDTRewardVideoAdUtil : onVideoComplete ");
            }

            @Override
            public void onADClose() {
                //激励视频广告被关闭

                if(callback != null) {
                    callback.onAdClose(false);
                }

                AdLogger.log("GDTRewardVideoAdUtil : onADClose ");
            }

            @Override
            public void onError(AdError adError) {
                // 超时之后广告加载再报错误，无需再次触发回调
                //广告流程出错
                if (callback != null) {
                    if (isAdLoaded) {
                        if (adError != null) {
                            callback.onAdPlayError(adError.getErrorCode(), adError.getErrorMsg());
                        } else {
                            callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "未知错误");
                        }
                    } else {
                        if (countDownTimer != null) {
                            if (isAdLoadOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if (adError != null) {
                            callback.onAdLoadError(adError.getErrorCode(), adError.getErrorMsg());
                        } else {
                            callback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "未知错误");
                        }
                    }
                }

                AdLogger.log("GDTRewardVideoAdUtil : onError code=" + adError.getErrorCode() + " msg=" + adError.getErrorMsg() + "  isAdLoaded=" + isAdLoaded);
            }
        };

        RewardVideoAD rewardVideoAD ;

        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            rewardVideoAD = new RewardVideoAD(BaseApplication.getMyApplicationContext(), posId, rewardVideoADListener, true, advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价gdt video 6 + " + advertis.getSlotAdm());
        } else {
            rewardVideoAD = new RewardVideoAD(BaseApplication.getMyApplicationContext(), posId, rewardVideoADListener, true);
            Logger.v("------msg", " ---- 不 竞价gdt video 6 + " + advertis.getSlotAdm());
        }

//        RewardVideoAD rewardVideoAD = new RewardVideoAD(activity,
//                posId, rewardVideoADListener);

        rewardAd.setRewardVideoAD(rewardVideoAD);
        rewardVideoAD.loadAD();
    }

    //沉浸式视频广告加载方法（广点通短剧）
    public static void loadImmersiveRewardVideoAd (@NonNull Activity activity,
                                                   Advertis advertis,
                                                   String posId,
                                IVideoAdStatueCallBack callback,
                                @NonNull RewardExtraParams extraParams,
                                IDataCallBack<ImmersiveRewardVideoAdWrapper> loadedRewardVideoAdWrapperCallback){

        WeakReference<Activity> activityWeakReference = new WeakReference<>(activity);
        //初始化sdk检查是否初始化成功，如果没初始化成功则不加载广告
        if (!GDTSDKManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (callback != null) {
                    if (isAdLoaded) {
                        callback.onAdPlayError(i, s);
                    } else {
                        if (countDownTimer != null) {
                            if (isAdLoadOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        callback.onAdLoadError(i, s);
                    }
                }
            }
        })) {
            return;
        }

        //倒计时时间设置
        ImmersiveRewardVideoAdWrapper rewardAd = new ImmersiveRewardVideoAdWrapper();
        mCallback = callback;
        isAdLoaded = false;
        isAdLoadOverTime = false;
        countDownTimer = null;
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                isAdLoadOverTime = true;
                AdLogger.log("GDTRewardVideoAdUtil : adLoadOverTime ");
                if (callback != null) {
                    callback.onAdLoadError(
                            IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                            "广告加载超时");
                }
            }
        };
        countDownTimer.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()));


        VideoOption.Builder builder = new VideoOption.Builder();
        builder.setAutoPlayPolicy(VideoOption.AutoPlayPolicy.ALWAYS);
        builder.setAutoPlayMuted(false);
        VideoOption videoOption = builder.build();
        // 创建 ad 对象
        ImmersiveADFlow immersiveADFlow =  new ImmersiveADFlow(activityWeakReference.get(), posId, new ImmersiveADFlowListener() {
            int pageIndex = 1;
            //广告点击回调
            @Override
            public void onADClick(String s) {
                //短剧 激励视频广告被点击
                if(callback != null) {
                    callback.onAdVideoClick(false, 0);
                }

                AdLogger.log("GDTRewardVideoAdUtil : 短剧 immersiveADFlow onADClick ");
            }

            //广告曝光回调
            @Override
            public void onADExpose(String s) {

                if (advertis != null && pageIndex > 1) {
                    AdReportModel.Builder builder = AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_SHOW_OB, advertis.getPositionName())
                            .sdkType(AdManager.getSDKType(advertis) + "")
                            .dspPositionId(posId)
                            .uid(UserInfoMannage.getUid() + "")
                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                            .position(pageIndex)
                            .showStyle(advertis.getShowstyle() + "");
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            advertis, builder.build());
                } else if (callback != null && pageIndex == 1) {
                    //激励视频广告曝光
                    callback.onAdPlayStart();
                }
                pageIndex++;
                AdLogger.log("GDTRewardVideoAdUtil : 短剧 immersiveADFlow onADExpose ");
            }

            //广告数据获取成功时回调
            @Override
            public void onADLoaded() {
                //广告加载回调
                AdLogger.log("GDTRewardVideoAdUtil : loadImmersiveRewardVideoAd onADLoad ");
                //广告加载成功，停止倒计时
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                isAdLoaded = true;
                //activity不为空 且 有效时，展示广告
                if(activityWeakReference == null || !ToolUtil.activityIsValid(activityWeakReference.get())) {
                    if (callback != null) {
                        callback.onAdLoadError(
                                IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                "Activity 已不能展示广告");
                    }
                    return;
                }

                // 广告视频素材缓存完毕，展示view之前将数据返回处理
                if(loadedRewardVideoAdWrapperCallback != null) {
                    loadedRewardVideoAdWrapperCallback.onSuccess(rewardAd);
                }
                if(callback != null) {
                    callback.onAdLoad(null);
                }
                if(rewardAd.mImmersiveADFlow != null) {
                    //展示软广告页面
                    rewardAd.mImmersiveADFlow.showImmersiveADFlow();
                }
            }

            //广告页面销毁
            @Override
            public void onADPageDestroy() {
                if (callback != null) {
                    callback.onAdClose(false);
                }
                AdLogger.log("GDTRewardVideoAdUtil : 短剧 ImmersiveVideoAd onADPageDestroy ");
            }

            //广告页面展示
            @Override
            public void onADPageShow() {
                //当页面展示时，添加&展示 倒计时view

                AdLogger.log("GDTRewardVideoAdUtil : 短剧 ImmersiveVideoAd onADPageShow ");
            }
            //广告获取失败时的回调
            @Override
            public void onNoAD(AdError adError) {
                // 超时之后广告加载再报错误，无需再次触发回调
                //广告流程出错
                if (callback != null) {
                    if (isAdLoaded) {
                        if (adError != null) {
                            callback.onAdPlayError(adError.getErrorCode(), adError.getErrorMsg());
                        } else {
                            callback.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "未知错误");
                        }
                    } else {
                        if (countDownTimer != null) {
                            if (isAdLoadOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if (adError != null) {
                            callback.onAdLoadError(adError.getErrorCode(), adError.getErrorMsg());
                        } else {
                            callback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "未知错误");
                        }
                    }
                }

                AdLogger.log("GDTRewardVideoAdUtil : immersiveADFlow onError code=" + adError.getErrorCode() + " msg=" + adError.getErrorMsg() + "  isAdLoaded=" + isAdLoaded);
            }
        },null);

        //设置播放视频配置
        immersiveADFlow.setVideoOption(videoOption);
        //预加载广告流
        immersiveADFlow.preloadImmersiveADFlow();

        rewardAd.setImmersiveRewardVideoAD(immersiveADFlow);
    }

    private AdRewardCountDownView adRewardCashTasksView;
    public void addCountDownView(Activity activity, RewardExtraParams rewardExtraParams) {
        if (mCallback == null) {
            return;
        }
        try {
            View view = activity.getWindow().getDecorView().findViewById(android.R.id.content);
            if (view instanceof ViewGroup) {
                ViewGroup contentLayout = (ViewGroup) view;
                adRewardCashTasksView = new AdRewardCountDownView(contentLayout.getContext());
                contentLayout.removeView(adRewardCashTasksView);
                FrameLayout.LayoutParams marginParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
                marginParams.gravity = Gravity.RIGHT;
                marginParams.topMargin = BaseUtil.dp2px(contentLayout.getContext(), 100);
                marginParams.rightMargin = BaseUtil.dp2px(contentLayout.getContext(), 16);
                contentLayout.addView(adRewardCashTasksView, marginParams);
                adRewardCashTasksView.setCustomViewToActivity(contentLayout, rewardExtraParams, mCallback.getCloseClickListener(activity));
                adRewardCashTasksView.setData(rewardExtraParams.getAdvertis());
            }
        } catch (Exception e) {
            Logger.log("广点通 短剧addCountDownView Exception:" + e.getMessage());
        }
    }

    public static void loadRewardVideoNew(Advertis advertis,String dspPositionId,
                                          RewardVideoAdManager.IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback,
                                          IVideoAdStatueCallBack videoAdStatueCallBack) {

        if (!GDTSDKManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (thirdRewardAdLoadCallback != null) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), i, s);
                }
            }
        })) {
            return;
        }

        RewardVideoAdWrapper rewardAd = new RewardVideoAdWrapper();
        RewardVideoADListener rewardVideoADListener = new RewardVideoADListener() {
            boolean isAdLoadBack = false;
            @Override
            public void onADLoad() {
                //广告加载成功，可在此回调后进行广告展示
                //广告加载回调
                AdLogger.log("GDTRewardVideoAdUtil : onADLoad ");
                isAdLoadBack = true;
                if (thirdRewardAdLoadCallback != null) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new GdtRewardVideoAd(advertis, rewardAd.mRewardVideoAD, dspPositionId), 0, "");
                }
            }

            @Override
            public void onVideoCached() {
                //视频素材缓存成功，可在此回调后进行广告展示
                AdLogger.log("GDTRewardVideoAdUtil : onVideoCached ");
            }

            @Override
            public void onADShow() {
                //激励视频广告页面展示
                AdLogger.log("GDTRewardVideoAdUtil : onADShow ");
            }

            @Override
            public void onADExpose() {
                AdLogger.log("GDTRewardVideoAdUtil : onADExpose ");
                //激励视频广告曝光
                if (videoAdStatueCallBack != null) {
                    videoAdStatueCallBack.onAdPlayStart();
                }
            }

            @Override
            public void onReward(Map<String, Object> map) {
                //激励视频触发激励（观看视频大于一定时长或者视频播放完毕）
                AdLogger.log("GDTRewardVideoAdUtil : onReward ");
                if (videoAdStatueCallBack != null && videoAdStatueCallBack instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) videoAdStatueCallBack).onRewardVerify();
                }
            }

            @Override
            public void onADClick() {
                AdLogger.log("GDTRewardVideoAdUtil : onADClick ");
                //激励视频广告被点击
                if(videoAdStatueCallBack != null) {
                    videoAdStatueCallBack.onAdVideoClick(false, 0);
                }
            }

            @Override
            public void onVideoComplete() {
                AdLogger.log("GDTRewardVideoAdUtil : onVideoComplete ");
                //激励视频播放完毕
                if(videoAdStatueCallBack != null) {
                    videoAdStatueCallBack.onAdPlayComplete();
                }
            }

            @Override
            public void onADClose() {
                AdLogger.log("GDTRewardVideoAdUtil : onADClose ");
                //激励视频广告被关闭
                if(videoAdStatueCallBack != null) {
                    videoAdStatueCallBack.onAdClose(false);
                }
            }

            @Override
            public void onError(AdError adError) {
                AdLogger.log("GDTRewardVideoAdUtil : onError code=" + adError.getErrorCode() + " msg=" + adError.getErrorMsg() + "  isAdLoaded=" + isAdLoaded);
                if (isAdLoadBack) {
                    if (videoAdStatueCallBack == null) {
                        return;
                    }
                    if (adError != null) {
                        videoAdStatueCallBack.onAdPlayError(adError.getErrorCode(), adError.getErrorMsg());
                    } else {
                        videoAdStatueCallBack.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "未知错误");
                    }
                } else {
                    if (thirdRewardAdLoadCallback != null) {
                        if (adError != null) {
                            thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), adError.getErrorCode(), adError.getErrorMsg());
                        } else {
                            thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "未知错误");
                        }
                    }
                }
            }
        };

        RewardVideoAD rewardVideoAD ;
        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            rewardVideoAD = new RewardVideoAD(BaseApplication.getMyApplicationContext(), dspPositionId, rewardVideoADListener, true, advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价gdt video 6 + " + advertis.getSlotAdm());
        } else {
            rewardVideoAD = new RewardVideoAD(BaseApplication.getMyApplicationContext(), dspPositionId, rewardVideoADListener, true);
            Logger.v("------msg", " ---- 不 竞价gdt video 6 + " + advertis.getSlotAdm());
        }

        rewardAd.setRewardVideoAD(rewardVideoAD);
        rewardVideoAD.loadAD();
    }


    public void onActivityDestroyed(Activity activity) {
        if (mCallback != null) {
            mCallback = null;
        }
        if (adRewardCashTasksView != null) {
            adRewardCashTasksView.onActivityDestroyed();
        }
    }
}
