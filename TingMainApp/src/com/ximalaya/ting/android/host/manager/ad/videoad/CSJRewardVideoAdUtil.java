package com.ximalaya.ting.android.host.manager.ad.videoad;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.TTAdConstant;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.bytedance.sdk.openadsdk.TTDrawFeedAd;
import com.bytedance.sdk.openadsdk.TTFeedAd;
import com.bytedance.sdk.openadsdk.TTFullScreenVideoAd;
import com.bytedance.sdk.openadsdk.TTNativeAd;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.bytedance.sdk.openadsdk.TTRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJDrawThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.CSJExpressDrawThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjDrawTemplateVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjDrawVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjFullScreenVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.adsdk.external.IBaseLoadListener;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.CSJDrawAdActivity;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.CSJAdManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.preciseye.OriginalAdParams;
import com.ximalaya.ting.android.preciseye.csj.CSJPrecisEyeListenerUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

/**
 * Created by le.xin on 2020/5/6.
 * 穿山甲激励视频
 * <AUTHOR>
 * @email <EMAIL>
 */
public class CSJRewardVideoAdUtil {
    private static boolean isAdCacheOverTime;
    private static  boolean hasShowVideo;
    private static CountDownTimer countDownTimer;

    public static void loadRewardVideo(@NonNull Activity activity,
                                       String adCodeId,
                                       boolean isTemplate,
                                       @NonNull RewardExtraParams extraParams,
                                       IVideoAdStatueCallBack adStatueCallBack) {

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdLoadError(i, s);
                }
            }
        })) {
            return;
        }

        Advertis advertis = extraParams.getAdvertis();
        int screenWidth = BaseUtil.getScreenWidth(MainApplication.getMyApplicationContext());
        int screenHeight = BaseUtil.getScreenHeight(MainApplication.getMyApplicationContext());

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder;
        if (isTemplate) {
            builder = new AdSlot.Builder()
                    .setCodeId(adCodeId)
                    .setSupportDeepLink(true)
                    .setImageAcceptedSize(screenWidth, screenHeight)
                    .setUserID(UserInfoMannage.getUid() + "")
                    .setExpressViewAcceptedSize(BaseUtil.px2dip(activity, screenWidth), BaseUtil.px2dip(activity, screenHeight))
                    .setOrientation(TTAdConstant.VERTICAL);
        } else {
            builder = new AdSlot.Builder()
                    .setCodeId(adCodeId)
                    .setSupportDeepLink(true)
                    .setImageAcceptedSize(screenWidth, screenHeight)
                    .setUserID(UserInfoMannage.getUid() + "")
                    .setOrientation(TTAdConstant.VERTICAL);
        }
        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价csj 1 + " + advertis.getSlotAdm());
        }

        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if(advertis != null) {
            originalAdParams = new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId());
        }

        isAdCacheOverTime = false;
        hasShowVideo = false;
        countDownTimer = null;
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                isAdCacheOverTime = true;
                AdLogger.log("CSJExcitationVideoAdManager : adLoadOverTime ");
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdLoadError(
                            IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                            "广告加载超时");
                }
            }
        }.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()));

        //step5:请求广告
        TTAdSdk.getAdManager()
                .createAdNative(MainApplication.getMyApplicationContext())
                .loadRewardVideoAd(adSlot, CSJPrecisEyeListenerUtil.getRewardVideoAdListenerExtends(originalAdParams,new TTAdNative.RewardVideoAdListener() {
                    @Override
                    public void onError(int code, String message) {
                        AdLogger.log("CSJExcitationVideoAdManager : onError code = " + code + "msg=" + message);
                        if (countDownTimer != null) {
                            if (isAdCacheOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if (adStatueCallBack != null) {
                            adStatueCallBack.onAdLoadError(code, message);
                        }
                    }

                    //视频广告加载后，视频资源缓存到本地的回调，在此回调后，播放本地视频，流畅不阻塞。
                    @Override
                    public void onRewardVideoCached() {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoCached1");
                    }

                    @Override
                    public void onRewardVideoCached(TTRewardVideoAd ttRewardVideoAd) {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoCached2");
                        if (ttRewardVideoAd == null) {
                            return;
                        }
                        if (hasShowVideo) {
                            return;
                        } else {
                            hasShowVideo = true;
                        }
                        if (countDownTimer != null) {
                            if (isAdCacheOverTime) {
                                // 广告缓存超时，此次广告不展示
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if(!ToolUtil.activityIsValid(activity)) {
                            if (adStatueCallBack != null) {
                                adStatueCallBack.onAdLoadError(
                                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                        "Activity 已不能展示广告");
                            }
                            return;
                        }

                        if(adStatueCallBack != null) {
                            adStatueCallBack.onAdLoad(new CsjRewardVideoAd(advertis, ttRewardVideoAd ,adCodeId));
                        }

                        showVideoComplete(ttRewardVideoAd, adStatueCallBack, adCodeId, activity);
                    }

                    //视频广告的素材加载完毕，比如视频url等，在此回调后，可以播放在线视频，网络不好可能出现加载缓冲，影响体验。
                    @Override
                    public void onRewardVideoAdLoad(TTRewardVideoAd ad) {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoAdLoad ");
                        if (ad == null) {
                            if (adStatueCallBack != null) {
                                adStatueCallBack.onAdLoadError(
                                        IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有广告返回");
                            }
                            return;
                        }
                        if (hasShowVideo) {
                            return;
                        } else {
                            hasShowVideo = true;
                        }
                        if (countDownTimer != null) {
                            if (isAdCacheOverTime) {
                                // 广告缓存超时，此次广告不展示
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if(!ToolUtil.activityIsValid(activity)) {
                            if (adStatueCallBack != null) {
                                adStatueCallBack.onAdLoadError(
                                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                        "Activity 已不能展示广告");
                            }
                            return;
                        }

                        if(adStatueCallBack != null) {
                            adStatueCallBack.onAdLoad(new CsjRewardVideoAd(advertis, ad ,adCodeId));
                        }

                        showVideoComplete(ad, adStatueCallBack, adCodeId, activity);
                    }
                }));
    }


    private static void showVideoComplete(TTRewardVideoAd ad,
                                   IVideoAdStatueCallBack adStatueCallBack,
                                   String adCodeId, Activity activity) {
        if (ad == null) {
            return;
        }
        ad.setRewardAdInteractionListener(new TTRewardVideoAd.RewardAdInteractionListener() {

            @Override
            public void onAdShow() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayStart();
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdShow ");
            }

            @Override
            public void onAdVideoBarClick() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdVideoClick(false, 0);
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdVideoBarClick ");
            }

            @Override
            public void onAdClose() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdClose(false);
                }

                AdLogger.log("CSJExcitationVideoAdManager : onAdClose ");
            }

            //视频播放完成回调
            @Override
            public void onVideoComplete() {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayComplete();
                }

                AdLogger.log("CSJExcitationVideoAdManager : onVideoComplete ");
            }

            @Override
            public void onVideoError() {
                if(adStatueCallBack != null) {
                    adStatueCallBack.onAdPlayError(IVideoAdStatueCallBack.ERROR_CODE_VIDEO_PLAY_ERROR ,"视频播放失败");
                }
                AdLogger.log("CSJExcitationVideoAdManager : onVideoError ");

            }
            //视频播放完成后，奖励验证回调，rewardVerify：是否有效，rewardAmount：奖励梳理，rewardName：奖励名称
            @Override
            public void onRewardVerify(boolean rewardVerify, int rewardAmount, String rewardName, int i, String s) {
                AdLogger.log("CSJExcitationVideoAdManager : onRewardVerify rewardVerify=" + rewardVerify);
                if (rewardVerify && adStatueCallBack != null && adStatueCallBack instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) adStatueCallBack).onRewardVerify();
                } else if (!rewardVerify) {
                    AdLogger.log("CSJExcitationVideoAdManager : onRewardVerify fail code =" + i + " msg =" + s);
                }
            }

            @Override
            public void onRewardArrived(boolean isRewardValid, int rewardType, Bundle extraInfo) {
                AdLogger.log("CSJExcitationVideoAdManager : onRewardArrived isRewardValid =" + isRewardValid);
                if (isRewardValid && adStatueCallBack != null && adStatueCallBack instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) adStatueCallBack).onRewardVerify();
                } else if (!isRewardValid && extraInfo != null) {
                    Object errorCode = extraInfo.get("reward_extra_key_error_code");
                    Object errorMsg = extraInfo.get("reward_extra_key_error_msg");
                    AdLogger.log("CSJExcitationVideoAdManager : onRewardArrived fail code =" + errorCode + " msg =" + errorMsg);
                }
            }

            @Override
            public void onSkippedVideo() {
                AdLogger.log("CSJExcitationVideoAdManager : onSkippedVideo ");
            }
        });
        ad.showRewardVideoAd(activity);
    }

    /**
     * 加载全屏视频
     * @param activity
     * @param adCodeId
     * @param isTemplate  是否是模板渲染
     * @param extraParams
     * @param adStatueCallBack
     */
    public static void loadFullScreenVideo(@NonNull Activity activity,
                                           String adCodeId,
                                           boolean isTemplate,
                                           RewardExtraParams extraParams, IVideoAdStatueCallBack adStatueCallBack) {

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdLoadError(i, s);
                }
            }
        })) {
            return;
        }

        Advertis advertis = extraParams.getAdvertis();
        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder;
        if(isTemplate) {
            builder = new AdSlot.Builder()
                    .setCodeId(adCodeId)
                    //模板广告需要设置期望个性化模板广告的大小,单位dp,全屏视频场景，只要设置的值大于0即可
                    .setExpressViewAcceptedSize(BaseUtil.px2dip(context, screenWidth), BaseUtil.px2dip(context, screenHeight))
                    .setSupportDeepLink(true)
                    .setOrientation(TTAdConstant.VERTICAL);//必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
        } else {
            builder = new AdSlot.Builder()
                    .setCodeId(adCodeId)
                    .setSupportDeepLink(true)
                    .setOrientation(TTAdConstant.VERTICAL);//必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
        }

        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价csj 2 + " + advertis.getSlotAdm());
        }

        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if(advertis != null) {
            originalAdParams = new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId());
        }

        isAdCacheOverTime = false;
        countDownTimer = null;
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                isAdCacheOverTime = true;
                AdLogger.log("CSJExcitationVideoAdManager : adLoadOverTime ");
                if (adStatueCallBack != null) {
                    adStatueCallBack.onAdLoadError(
                            IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                            "广告加载超时");
                }
            }
        }.start();
        AdLogger.log("StartCountDown: totalTime = " + RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()));

        //step5:请求广告
        TTAdSdk.getAdManager().createAdNative(MainApplication.getMyApplicationContext())
                .loadFullScreenVideoAd(adSlot, CSJPrecisEyeListenerUtil.getFullScreenVideoAdListenerExtends(originalAdParams, new TTAdNative.FullScreenVideoAdListener() {
                    @Override
                    public void onError(int code, String message) {
                        AdLogger.log("CSJExcitationVideoAdManager : onError code = " + code + "msg=" + message);
                        if (countDownTimer != null) {
                            if (isAdCacheOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if (adStatueCallBack != null) {
                            adStatueCallBack.onAdLoadError(code, message);
                        }
                    }

                    @Override
                    public void onFullScreenVideoAdLoad(TTFullScreenVideoAd ad) {
                        if (ad == null) {
                            if (adStatueCallBack != null) {
                                adStatueCallBack.onAdLoadError(
                                        IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有广告返回");
                            }
                            return;
                        }
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdLoad");
                    }

                    @Override
                    public void onFullScreenVideoCached() {
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdCached1");
                    }

                    @Override
                    public void onFullScreenVideoCached(TTFullScreenVideoAd ttFullScreenVideoAd) {
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdCached2");
                        if (ttFullScreenVideoAd == null) {
                            return;
                        }
                        if (countDownTimer != null) {
                            if (isAdCacheOverTime) {
                                return;
                            } else {
                                countDownTimer.cancel();
                            }
                        }
                        if (!ToolUtil.activityIsValid(activity)) {
                            if (adStatueCallBack != null) {
                                adStatueCallBack.onAdLoadError(
                                        IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                        "Activity 已不能展示广告");
                            }
                            return;
                        }
                        ttFullScreenVideoAd.setFullScreenVideoAdInteractionListener(new TTFullScreenVideoAd.FullScreenVideoAdInteractionListener() {
                            @Override
                            public void onAdShow() {
                                if(adStatueCallBack != null) {
                                    adStatueCallBack.onAdPlayStart();
                                }

                                AdLogger.log("CSJFullScreenVideoAdManager : onAdShow ");
                            }

                            @Override
                            public void onAdVideoBarClick() {
                                if(adStatueCallBack != null) {
                                    adStatueCallBack.onAdVideoClick(false, 0);
                                }
                                AdLogger.log("CSJFullScreenVideoAdManager : onAdVideoBarClick ");
                            }

                            @Override
                            public void onAdClose() {
                                if (adStatueCallBack != null) {
                                    adStatueCallBack.onAdClose(false);
                                }
                                AdLogger.log("CSJFullScreenVideoAdManager : onAdClose ");
                            }

                            @Override
                            public void onVideoComplete() {
                                if (adStatueCallBack != null) {
                                    adStatueCallBack.onAdPlayComplete();
                                }

                                AdLogger.log("CSJFullScreenVideoAdManager : onVideoComplete ");
                            }

                            @Override
                            public void onSkippedVideo() {
                                AdLogger.log("CSJFullScreenVideoAdManager : onSkippedVideo ");
                            }
                        });
                        if (adStatueCallBack != null) {
                            adStatueCallBack.onAdLoad(new CsjFullScreenVideoAd(advertis, ttFullScreenVideoAd, adCodeId));
                        }
                        ttFullScreenVideoAd.showFullScreenVideoAd(activity);
                    }
                }));
    }

    /**
     * 并行请求框架，只请求sdk，先不渲染
     */
    public static void loadRewardVideoNew(Advertis advertis,String dspPositionId, boolean isTemplate,
                                          RewardVideoAdManager.IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback) {

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (thirdRewardAdLoadCallback != null) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), i, s);
                }
            }
        })) {
            return;
        }

        Context context = MainApplication.getMyApplicationContext();

        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder;
        if (isTemplate) {
            builder = new AdSlot.Builder()
                    .setCodeId(dspPositionId)
                    .setSupportDeepLink(true)
                    .setImageAcceptedSize(screenWidth, screenHeight)
                    .setUserID(UserInfoMannage.getUid() + "")
                    .setExpressViewAcceptedSize(BaseUtil.px2dip(context, screenWidth), BaseUtil.px2dip(context, screenHeight))
                    .setOrientation(TTAdConstant.VERTICAL);
        } else {
            builder = new AdSlot.Builder()
                    .setCodeId(dspPositionId)
                    .setSupportDeepLink(true)
                    .setImageAcceptedSize(screenWidth, screenHeight)
                    .setUserID(UserInfoMannage.getUid() + "")
                    .setOrientation(TTAdConstant.VERTICAL);
        }
        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价csj 1 + " + advertis.getSlotAdm());
        }

        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if(advertis != null) {
            originalAdParams = new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId());
        }

        //step5:请求广告
        TTAdSdk.getAdManager()
                .createAdNative(MainApplication.getMyApplicationContext())
                .loadRewardVideoAd(adSlot, CSJPrecisEyeListenerUtil.getRewardVideoAdListenerExtends(originalAdParams,new TTAdNative.RewardVideoAdListener() {
                    boolean hasCallBackFinish = false;
                    @Override
                    public void onError(int code, String message) {
                        AdLogger.log("CSJExcitationVideoAdManager : onError code = " + code + "msg=" + message);
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), code, message);
                        hasCallBackFinish = true;
                    }

                    //视频广告加载后，视频资源缓存到本地的回调，在此回调后，播放本地视频，流畅不阻塞。
                    @Override
                    public void onRewardVideoCached() {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoCached1");
                    }

                    @Override
                    public void onRewardVideoCached(TTRewardVideoAd ad) {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoCached2");
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjRewardVideoAd(advertis, ad, dspPositionId), 0, "");
                        hasCallBackFinish = true;
                    }

                    //视频广告的素材加载完毕，比如视频url等，在此回调后，可以播放在线视频，网络不好可能出现加载缓冲，影响体验。
                    @Override
                    public void onRewardVideoAdLoad(TTRewardVideoAd ad) {
                        AdLogger.log("CSJExcitationVideoAdManager : onRewardVideoAdLoad ");
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjRewardVideoAd(advertis, ad, dspPositionId), 0, "");
                        hasCallBackFinish = true;
                    }
                }));
    }


    /**
     * 并行请求框架，只请求sdk，先不渲染
     */
    public static void loadFullScreenVideoNew(Advertis advertis,String dspPositionId, boolean isTemplate,
                                              RewardVideoAdManager.IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback) {

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (thirdRewardAdLoadCallback != null) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), i, s);
                }
            }
        })) {
            return;
        }

        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if (screenWidth <= 0) {
            screenWidth = 1080;
        }

        if (screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder;
        if (isTemplate) {
            builder = new AdSlot.Builder()
                    .setCodeId(dspPositionId)
                    //模板广告需要设置期望个性化模板广告的大小,单位dp,全屏视频场景，只要设置的值大于0即可
                    .setExpressViewAcceptedSize(BaseUtil.px2dip(context, screenWidth), BaseUtil.px2dip(context, screenHeight))
                    .setSupportDeepLink(true)
                    .setOrientation(TTAdConstant.VERTICAL);//必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
        } else {
            builder = new AdSlot.Builder()
                    .setCodeId(dspPositionId)
                    .setSupportDeepLink(true)
                    .setOrientation(TTAdConstant.VERTICAL);//必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
        }

        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
            Logger.v("------msg", " ---- 参与竞价csj 2 + " + advertis.getSlotAdm());
        }

        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if (advertis != null) {
            originalAdParams = new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId());
        }

        //step5:请求广告
        TTAdSdk.getAdManager().createAdNative(MainApplication.getMyApplicationContext())
                .loadFullScreenVideoAd(adSlot, CSJPrecisEyeListenerUtil.getFullScreenVideoAdListenerExtends(originalAdParams, new TTAdNative.FullScreenVideoAdListener() {
                    boolean hasCallBackFinish = false;

                    @Override
                    public void onError(int code, String message) {
                        AdLogger.log("CSJExcitationVideoAdManager : onError code = " + code + "msg=" + message);
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), code, message);
                        hasCallBackFinish = true;
                    }

                    @Override
                    public void onFullScreenVideoAdLoad(TTFullScreenVideoAd ad) {
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdLoad");
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjFullScreenVideoAd(advertis, ad, dspPositionId), 0, "");
                        hasCallBackFinish = true;

                    }

                    @Override
                    public void onFullScreenVideoCached() {
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdCached1");
                    }

                    @Override
                    public void onFullScreenVideoCached(TTFullScreenVideoAd ad) {
                        AdLogger.log("CSJExcitationVideoAdManager : onFullScreenVideoAdCached2");
                        if (hasCallBackFinish) {
                            return;
                        }
                        thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjFullScreenVideoAd(advertis, ad, dspPositionId), 0, "");
                        hasCallBackFinish = true;
                    }
                }));
    }

    /**
     * 并行请求框架，只请求sdk，先不渲染
     */
    public static void loadDrawTemplateVideoAd(Advertis advertis,String dspPositionId,
                                               RewardVideoAdManager.IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback) {
        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        if (!CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), code, message);
            }
        })) {
            return;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(dspPositionId)
                .setSupportDeepLink(true)
                .setExpressViewAcceptedSize(BaseUtil.px2dip(context, screenWidth), BaseUtil.px2dip(context, screenHeight)) //期望模板广告view的size,单位dp
                .setAdCount(1);
        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();

        OriginalAdParams originalAdParams = null;
        if(advertis != null) {
            originalAdParams = new OriginalAdParams(advertis.getAdPositionId(), advertis.getAdid(), advertis.getResponseId());
        }

        TTAdSdk.getAdManager()
                .createAdNative(context).loadExpressDrawFeedAd(adSlot, CSJPrecisEyeListenerUtil.getNativeExpressAdExtends(originalAdParams, new TTAdNative.NativeExpressAdListener() {
            @Override
            public void onError(int code, String message) {
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), code, message);
            }

            @Override
            public void onNativeExpressAdLoad(List<TTNativeExpressAd> ads) {
                if (ads == null || ads.isEmpty()) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD,"没有广告返回");
                    return;
                }
                TTNativeExpressAd ad = ads.get(0);
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjDrawTemplateVideoAd(advertis, ad, dspPositionId), 0, "");
            }
        }));
    }

    /**
     * 并行请求框架，只请求sdk，先不渲染
     */
    public static void loadDrawVideoNoTemplateAd(Advertis advertis,String dspPositionId, RewardVideoAdManager.IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback) {
        CSJAdManager.getInstance().checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), code, message);
            }
        });

        Context context = MainApplication.getMyApplicationContext();
        int screenWidth = BaseUtil.getScreenWidth(context);
        int screenHeight = BaseUtil.getScreenHeight(context);

        if(screenWidth <= 0) {
            screenWidth = 1080;
        }

        if(screenHeight <= 0) {
            screenHeight = 1920;
        }

        AdSlot.Builder builder = new AdSlot.Builder()
                .setCodeId(dspPositionId)
                .setSupportDeepLink(true)
                .setImageAcceptedSize(screenWidth, screenHeight)
                .setAdCount(1);//请求广告数量为1到3条

        //添加实时竞价参数
        if (advertis != null && advertis.isSlotRealBid() && !com.ximalaya.ting.android.framework.arouter.utils.TextUtils.isEmpty(advertis.getSlotAdm())) {
            builder.withBid(advertis.getSlotAdm());
        }
        AdSlot adSlot = builder.build();


        TTAdSdk.getAdManager()
                .createAdNative(context).loadDrawFeedAd(adSlot, new TTAdNative.DrawFeedAdListener() {
            @Override
            public void onError(int i, String s) {
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), i, s);
            }

            @Override
            public void onDrawFeedAdLoad(List<TTDrawFeedAd> list) {
                if (ToolUtil.isEmptyCollects(list)) {
                    thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有广告返回");
                    return;
                }
                TTDrawFeedAd ad = list.get(0);
                thirdRewardAdLoadCallback.loadThirdNativeAdFinish(new CsjDrawVideoAd(advertis, ad, dspPositionId),0, "");
            }
        });

    }
}
