package com.ximalaya.ting.android.host.manager.ad;

import static com.ximalaya.ting.android.host.manager.request.CommonRequestM.getContext;
import static com.ximalaya.ting.android.host.manager.request.CommonRequestM.postRequestWithGzipedStrSync;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_CATA_LIST;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_FIND_FOCUS;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_LOADING;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_BAIDU;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_BAIDU_REWARD_TEMP;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_CSJ;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_CSJ_TEMPLATE;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_GDT;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_GDT_WELCOME_SCREEN;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_IMMERSIVE_GDT;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_JAD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_VERTICAL_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_STATIC_VERTICAL_STYLE2_IMAGE_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.TYPE_VIP_PAUSE_VIDEO_AD;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringDef;
import androidx.annotation.WorkerThread;
import androidx.core.view.ViewCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.danikula.videocache.HttpProxyCacheServer;
import com.google.common.base.Splitter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.ximalaya.ting.android.ad.model.AdPreloadMaterialResult;
import com.ximalaya.ting.android.ad.model.LandingPageResData;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.ad.model.thirdad.VideoParamModel;
import com.ximalaya.ting.android.ad.preload.PreloadAdManager;
import com.ximalaya.ting.android.ad.splashad.SplashJumpHintViewHelper;
import com.ximalaya.ting.android.adsdk.InnerHelper;
import com.ximalaya.ting.android.adsdk.base.util.BuildProperties;
import com.ximalaya.ting.android.adsdk.bridge.IClickOver;
import com.ximalaya.ting.android.adsdk.bridge.importsdk.ImportSDKHelper;
import com.ximalaya.ting.android.adsdk.bridge.inner.IInnerProvider;
import com.ximalaya.ting.android.adsdk.bridge.inner.download.ITaskImpl;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.external.bean.XmDownloadInfo;
import com.ximalaya.ting.android.adsdk.util.EncryptPriceUtils;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.adapter.AbstractAdapter;
import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.service.DownloadAdvertisParams;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.SerialInfo;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.FixXiaomiInterceptOpenAppActivity;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.constant.ResPositionConstant;
import com.ximalaya.ting.android.host.fragment.web.IWebFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.XuidManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.apidownload.AdApiDownloadManager;
import com.ximalaya.ting.android.host.manager.ad.download.AdDownloadManager;
import com.ximalaya.ting.android.host.manager.ad.gamead.AdGameUtil;
import com.ximalaya.ting.android.host.manager.ad.inventory.AdInventoryCollectManager;
import com.ximalaya.ting.android.host.manager.ad.playweb.AdPlayNativeWebManager;
import com.ximalaya.ting.android.host.manager.ad.thirdgamead.ThirdGameAdConstants;
import com.ximalaya.ting.android.host.manager.ad.thirdgamead.handler.TuiaRouterHandler;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.host.manager.ad.util.IPV6Util;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.downloadapk.AdOtherInstallManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.router.XmUriRouter;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.AdCollectData;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AnchorAlbumAd;
import com.ximalaya.ting.android.host.model.ad.BannerModel;
import com.ximalaya.ting.android.host.model.ad.ShareAdRequestParams;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.rank.BannerM;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AdUrlConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil;
import com.ximalaya.ting.android.host.view.BannerView;
import com.ximalaya.ting.android.host.xdcs.model.UserTrackCookie;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTrackingUrlMatcher;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.AdShareDataForOpenSDK;
import com.ximalaya.ting.android.opensdk.model.advertis.AdWebVideoModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.model.album.Album;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.advertis.XmAdsManager;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil;
import com.ximalaya.ting.android.opensdk.util.OtherSharedPreferencesUtil;
import com.ximalaya.ting.android.opensdk.util.SystemUtil;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.storage.IStoragePathManager;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 广告的管理
 *
 * <AUTHOR>
 */
public class AdManager {

    //	点击动作，0:无动作，1:应用内打开link，2喜马拉雅活动3:第三方浏览器打开link，4：专辑详情，5：声音详情，6：播主详情7：直达广告 16打电话
    public static final int LINK_TYPE_NONE = 0;
    public static final int LINK_TYPE_WEB = 1;
    public static final int LINK_TYPE_ACTIVITY = 2;
    public static final int LINK_TYPE_OPEN_THIRD_BROWSER = 3;
    public static final int LINK_TYPE_ALBUM_INFO = 4;
    public static final int LINK_TYPE_TRACK_INFO = 5;
    public static final int LINK_TYPE_ANCHOR_INFO = 6;
    public static final int LINK_TYPE_GOTO_AD = 7;
    public static final int LINK_TYPE_CALL_PHONE = 16;

    /**
     * 广点通配置
     */
    public static final String GDT_APPID = "1105972338";
    public static final String GDT_WELCOME_ADID = "6070153943139639";   // 启动页模板
    public static final String GDT_WELCOME_NATIVE_ADID = "5071500179110984";    // 启动页原生
    public static final String PLAY_CENTER_LARGER_ADID = "5050426294900038";
    public static final String PLAY_CENTER_SMALL_ADID = "7080120214009170";
    public static final String CATEGORY_DETAIL_ADID = "8080027885500018";    // 二级分类原生
    public static final String GDT_PLAY_HORIZONTAL_LARGE_ADID = "1001807126856681";    // 通栏大图
    private static final String GDT_FORWARD_VIDEO = "8091916487892829"; // 前插激励视频
    public static final String GDT_VIDEO_PLAY_ADID = "5061416058180761";    // 视频通栏
    public static final String GDT_PLAY_LARGE_ADID = "7071711843885935";    // 播放页大图 可能返回 大图 ,组图或者视频
    public static final String GDT_FIND_NATIVE_ADID = "1011715806137611";    // 首页大图 可能返回 大图 ,组图或者视频
    public static final String GDT_ALBUM_NOTICE_ADID = "6011417843179535";    // 专辑通知原生
    public static final String GDT_VERTICAL_VIDEO = "9052401627925233"; //竖版视频贴片
    public static final String GDT_VERTICAL_STATIC_IMG = "4022803697723238"; // 竖版静态贴片

    /**
     * 穿山甲配置
     */
    public static final String CSJ_APPID = "5017118";
    public static final String CJS_APP_NAME = "喜马拉雅-安卓app_android";

    public static final String CSJ_WELCOME_ADID = "887302601";  // 启动页
    public static final String CSJ_PLAY_HORIZONTAL_LARGE_ADID = "945069576";  // 通栏
    public static final String CSJ_POSTER_ADID = "945069591";  // 海报
    public static final String CSJ_FORWARD_VIDEO = "945198121";  // 前插激励视频
    public static final String CSJ_CALL_VIDEO_ADID = "945198911"; //打call广告视频
    public static final String CSJ_VIDEO_PLAY_ADID = "945202164";    // 视频通栏
    public static final String CSJ_PLAY_LARGE_ADID = "945278634";  // 播放页大图
    public static final String CSJ_FIND_NATIVE_ADID = "945278595";  // 首页大图
    public static final String CSJ_ALBUM_NOTICE_ADID = "945278490";  // 专辑通知原生
    public static final String CSJ_VERTICAL_VIDEO = "946340127"; //竖版贴片视频
    public static final String CSJ_VERTICAL_STATIC_IMG = "946340155"; //竖版静态贴片

    /**
     * 百度配置
     */
    public static final String BAIDU_APPID = "ce91711a";
    public static final String BAIDU_APP_NAME = "喜马拉雅-安卓";

    // 1：免费声音页、2：免费专辑页、3：主播页、4：听单
    public static final int SHARE_AD_SOURCE_PAGE_SOUND = 1;
    public static final int SHARE_AD_SOURCE_PAGE_ALBUM = 2;
    public static final int SHARE_AD_SOURCE_PAGE_ANCHOR = 3;
    public static final int SHARE_AD_SOURCE_PAGE_LISTENER = 4;

    public static final int AD_MASK_HEIGHT = 12;

    private static ExecutorService pool = Executors.newCachedThreadPool(new ThreadFactory() {
        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "ad-record-thread");
        }
    });

    public final static String PARAM_AD_NAME = "name";

    @WorkerThread
    public static AdCollectData thirdAdToAdCollect(Context context, Advertis thirdAd, AdReportModel adReportModel) {
        if (context == null || thirdAd == null || adReportModel == null) {
            return null;
        }

        if (AppConstants.AD_LOG_TYPE_SOUND_TINGCLOSE.equals(adReportModel.getLogType())
                && thirdAd.isPreviewAd()) {
            return null;
        }

        AdCollectData logData = adReportModel.createAdCollectData();

        // 前插视频需要修改 positionName
        if (AdManager.isForwardVideo(thirdAd)) {
            logData.setPositionName(AppConstants.AD_POSITION_NAME_FORWARD_VIDEO);
            adReportModel.setPositionName(AppConstants.AD_POSITION_NAME_FORWARD_VIDEO);
        }

        if (thirdAd.getAdpr() == null) {
            logData.setAdpr("");
        } else {
            logData.setAdpr(thirdAd.getAdpr());
        }
        if (adReportModel.getAdid() != 0) {
            logData.setAdItemId((adReportModel.isAdIdIsNegative() ? -1 * Math.abs(adReportModel.getAdid()) :
                    adReportModel.getAdid()) + "");
        } else {
            logData.setAdItemId((adReportModel.isAdIdIsNegative() ? -1 * Math.abs(thirdAd.getAdid()) :
                    thirdAd.getAdid()) + "");
        }
        logData.setAdSource("" + thirdAd.getAdtype());
        logData.setAndroidId(SerialInfo.getAndroidId(context));

        logData.setResponseId(thirdAd.getResponseId() + "");
        logData.setTime("" + System.currentTimeMillis());
        if (BaseUtil.isPlayerProcess(context)) {
            XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
            if (playerSrvice != null) {
                PlayableModel currPlayModel = playerSrvice.getCurrPlayModel();
                if (currPlayModel != null) {
                    if (PlayableModel.KIND_TRACK.equals(currPlayModel.getKind())) {
                        if (thirdAd.getTrackId() > 0) {
                            logData.setTrackId("" + thirdAd.getTrackId());
                        } else {
                            logData.setTrackId("" + currPlayModel.getDataId());
                        }
                    } else {
                        if (thirdAd.getRadioId() > 0) {
                            logData.setTrackId("" + thirdAd.getRadioId());
                        } else {
                            logData.setTrackId("" + currPlayModel.getDataId());
                        }
                    }
                }
            }
        } else {
            PlayableModel currSound = XmPlayerManager.getInstance(context).getCurrSound();
            if (currSound != null) {
                if (PlayableModel.KIND_TRACK.equals(currSound.getKind())) {
                    if (thirdAd.getTrackId() > 0) {
                        logData.setTrackId("" + thirdAd.getTrackId());
                    } else {
                        logData.setTrackId("" + currSound.getDataId());
                    }
                } else {
                    if (thirdAd.getRadioId() > 0) {
                        logData.setTrackId("" + thirdAd.getRadioId());
                    } else {
                        logData.setTrackId("" + currSound.getDataId());
                    }
                }
            }
        }
        logData.setShowToken(AdTokenManager.getInstance().getShowToken(thirdAd));
        logData.setRecSrc(thirdAd.getRecSrc());
        logData.setRecTrack(thirdAd.getRecTrack());
        logData.setBucketIds(thirdAd.getBucketIds());
        logData.setAdBucketIds(thirdAd.getAdBucketIds());
        logData.setPopupId(thirdAd.getPopupId());
        if (thirdAd.getCloseStyle() > 0) {
            logData.setCloseStyle(thirdAd.getCloseStyle());
        }

        if (AppConstants.AD_POSITION_NAME_MY_COOPERATION.equals(adReportModel.getPositionName())
                || AppConstants.AD_POSITION_NAME_BROCASTER_COOPERATION.equals(adReportModel.getPositionName())) {
            logData.setRealLink(thirdAd.getRealLink() == null ? "" : thirdAd.getRealLink());
        }

        if (AppConstants.AD_POSITION_NAME_COLUMN_SPONSORSHIP.equals(adReportModel.getPositionName())) {
            logData.setColumnSequence(thirdAd.getColumnSequence());
        }

        if (!TextUtils.isEmpty(thirdAd.getAdPositionId())) {
            logData.setPositionId(thirdAd.getAdPositionId());
        }

        if (!TextUtils.isEmpty(thirdAd.getHomeRank())) {
            logData.setHomeRank(thirdAd.getHomeRank());
        }

        if (thirdAd.getDisplayAnimation() > 0
                && thirdAd.getDisplayAnimation() != IAdConstants.IAdAnimationType.HIGHT_LIGHT) {
            logData.setDisplayAnimation(thirdAd.getDisplayAnimation() + "");
        }

        logData.setDspPositionId(thirdAd.getDspPositionId());
        logData.setCommonReportMap(thirdAd.getCommonReportMap());
        logData.setUbtReportMap(thirdAd.getUbtReportMap());
        logData.setStrongType(thirdAd.getStrongType());

        String gameId = getGameIdFromAD(thirdAd);
        if (!TextUtils.isEmpty(gameId)) {
            logData.setGameId(gameId);
        }
        if (!TextUtils.isEmpty(adReportModel.getAdIds())) {
            logData.setAdIds(adReportModel.getAdIds());
        }
        logData.setAnimationType(thirdAd.getAnimationType());
        logData.setGestureCode(thirdAd.getGestureCode());
        logData.setAdScene(thirdAd.getAdScene());
        logData.setAnchorUid(adReportModel.getAnchorUid());
        logData.setInsertSoundOrder(adReportModel.getInsertSoundOrder());
        if (adReportModel.getDurationShowSrc() > 0) {
            logData.setDurationShowSrc(adReportModel.getDurationShowSrc());
        }
        if (!TextUtils.isEmpty(adReportModel.getAction())) {
            logData.setAction(adReportModel.getAction());
        }
        if (adReportModel.getGtDurationShowType() > 0) {
            logData.setGtDurationShowType(adReportModel.getGtDurationShowType());
        }
        if (thirdAd.getDurationPicStyle() > 0) {
            logData.setDurationPicStyle(thirdAd.getDurationPicStyle());
        }
        if (adReportModel.getFinishSeconds() > 0) {
            logData.setFinishSeconds(adReportModel.getFinishSeconds());
        }
        return logData;
    }

    public static String getGameIdFromAD(Advertis thirdAd) {
        String gameId = null;
        if (thirdAd != null && thirdAd.getOpenlinkType() == Advertis.LINK_TYPE_GAME_CENTER){
            gameId = getParam(thirdAd.getRealLink(), "id");
        }
        return gameId;
    }

    private static String getParam(String url, String name) {
        try {
            String params = url.substring(url.indexOf("?") + 1, url.length());
            Map<String, String> split = Splitter.on("&").withKeyValueSeparator("=").split(params);
            return split.get(name);
        } catch (Exception e) {
            return null;
        }
    }

    public static void batchAdRecord(final Context context, final List<? extends Advertis> thirdAdList, @logType final String logType, @positionName final String positionName) {
        batchAdRecord(context, thirdAdList, AdReportModel.newBuilder(logType, positionName).build());
    }

    public static void batchAdRecord(final Context context, final List<? extends Advertis> thirdAdList, final AdReportModel adReportModel) {
        batchAdRecord(context, thirdAdList, adReportModel, false);
    }

    public static void batchAdRecord(final Context context, final List<? extends Advertis> thirdAdList, final AdReportModel adReportModel, boolean isNoUseSdkRecord) {
        if (context == null || thirdAdList == null || thirdAdList.isEmpty() || adReportModel == null) {
            return;
        }
        // 防止多线程错误
        final ArrayList<Advertis> thirdAds = new ArrayList<>(thirdAdList);

        // 实时加载静态数据
        downloadLandingPageData(thirdAds);

        trimAdReportModel(adReportModel);

        boolean recordUseSDK = AdSDKManager.canUseSDKShowRecord(context);
        Logger.log("AdManager : batchAdRecord recordUseSDK = " + recordUseSDK);
        if (recordUseSDK && !isNoUseSdkRecord) {
            if (BaseUtil.isPlayerProcess(context)) {
                XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
                if (playerSrvice != null && playerSrvice.getIXmCommonBusinessDispatcher() != null) {
                    List<AdReportModel> adReportTempList = new ArrayList<AdReportModel>(1);
                    adReportTempList.add(adReportModel);
                    try {
                        Logger.log("AdManager : batchAdRecord recordUseSdk playingProcess");
                        playerSrvice.getIXmCommonBusinessDispatcher().batchAdRecord(thirdAdList, adReportTempList);
                        return;
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }

                AdSDKManager.init(context);
            }

            ArrayList<AdSDKAdapterModel> models = new ArrayList<>(thirdAds.size());
            for (Advertis advertis : thirdAds) {
                models.add(AdConversionUtil.conversionModel(advertis, adReportModel.getPositionName()));
            }

            InnerHelper.getInstance().batchAdRecord(context, models, adReportModel);
        } else {
            Runnable task = new Runnable() {
                @Override
                public void run() {
                    updateAdPlayVersion(adReportModel);

                    final List<AdCollectData> adCollectDatas = new ArrayList<AdCollectData>();

                    String lastLogType = null;
                    long lastAdId = 0;
                    String lastPositioName = null;
                    int index = 0;
                    for (Advertis thirdAd : thirdAds) {
                        if (thirdAd == null
                                || (thirdAd instanceof BannerModel && ((BannerModel) thirdAd).getNewUserBannerModel() != null)
                                // 如果是预览广告不上报
                                || thirdAd.isPreviewAd()
                                // 虚拟广告位也不进行上报
                                || AdInventoryCollectManager.isVirtualAd(thirdAd)) {
                            if (!AD_POSITION_NAME_CATA_LIST.equals(adReportModel.getPositionName())) {
                                index++;
                            }
                            continue;
                        }

                        if (isTingShowRecordType(adReportModel)) {

                            // 广告要求真实上报但是此时上报不是真实的
                            if (!adReportModel.isProductManagerStyle() && thirdAd.isTrueExposure()) {
                                if (!AD_POSITION_NAME_CATA_LIST.equals(adReportModel.getPositionName())) {
                                    index++;
                                }
                                continue;
                            }

                            // 不是真实上报
                            if (!adReportModel.isProductManagerStyle() || thirdAd.isTrueExposure()) {
                                if (!adReportModel.isAdIdIsNegative()) {
                                    if (!(AppConstants.AD_LOG_TYPE_SHOW_OB.equals(adReportModel.getLogType())
                                            || AppConstants.AD_LOG_TYPE_CLICK_OB.equals(adReportModel.getLogType()))) {
                                        if (thirdAd.getShowUrls() != null) {
                                            for (String showUrl : thirdAd.getShowUrls()) {
                                                ThirdAdStatUtil.getInstance(context).
                                                        thirdAdStatRequest(showUrl, thirdAd, adReportModel);
                                            }
                                        }

                                        if (thirdAd.getThirdShowStatUrls() != null) {
                                            for (String showUrl : thirdAd.getThirdShowStatUrls()) {
                                                ThirdAdStatUtil.getInstance(context).
                                                        thirdAdStatRequest(showUrl, thirdAd, adReportModel);
                                            }
                                        }

                                        updateAdIdFromThirdAd(thirdAd);

                                        ThirdAdStatUtil.getInstance(context).
                                                thirdAdStatRequest(thirdAd.getThirdStatUrl(), thirdAd, adReportModel);
                                    }
                                }
                            } else {
                                // 是真实上报但是服务端没有要求真实上报
                                lastLogType = adReportModel.getLogType();
                                lastAdId = adReportModel.getAdid();
                                lastPositioName = adReportModel.getPositionName();

                                // http://thoughts.ximalaya.com/workspaces/5ceab64fbe825bee8c1338af/docs/5eb3784ef6aaaf0001012efb
                                // 是真实上报但是服务端不要求真实上报时,将logType 变为showOb
                                adReportModel.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                                adReportModel.setAdid(-1 * thirdAd.getAdid());
                                adReportModel.setPositionName(adReportModel.getPositionName() + "_new");
                                adReportModel.setFrames(adReportModel.getFrames());
                            }
                        }

                        if (adReportModel.getFrames() <= 0) {
                            adReportModel.setFrames(index);
                        }

                        AdCollectData logData = thirdAdToAdCollect(context, thirdAd, adReportModel);
                        adCollectDatas.add(logData);

                        if (!TextUtils.isEmpty(lastLogType)) {
                            adReportModel.setLogType(lastLogType);
                            adReportModel.setAdid(lastAdId);
                            adReportModel.setPositionName(lastPositioName);
                            lastLogType = null;
                            lastAdId = 0;
                            lastPositioName = null;
                        }

                        // 是真实上报并且服务端也要求真实上报
                        if (isTingShowRecordType(adReportModel)
                                && adReportModel.isProductManagerStyle()
                                && thirdAd.isTrueExposure()) {

                            lastLogType = adReportModel.getLogType();
                            lastAdId = adReportModel.getAdid();
                            lastPositioName = adReportModel.getPositionName();

                            // http://thoughts.ximalaya.com/workspaces/5ceab64fbe825bee8c1338af/docs/5eb3784ef6aaaf0001012efb
                            // 如果是真实上报需要再上报一次showOb
                            adReportModel.setLogType(AppConstants.AD_LOG_TYPE_SHOW_OB);
                            adReportModel.setAdid(-1 * thirdAd.getAdid());
                            adReportModel.setPositionName(adReportModel.getPositionName() + "_new");
                            adReportModel.setFrames(adReportModel.getFrames());
                            adCollectDatas.add(thirdAdToAdCollect(context, thirdAd, adReportModel));

                            adReportModel.setLogType(lastLogType);
                            adReportModel.setAdid(lastAdId);
                            adReportModel.setPositionName(lastPositioName);
                        }

                        adReportModel.setFrames(0);


                        if (!AD_POSITION_NAME_CATA_LIST.equals(adReportModel.getPositionName())) {
                            index++;
                        }
                        /**
                         * ● 3=展示、点击均注册监听
                         * ● 4=展示注册监听
                         */
                        if (thirdAd != null) {
                            if (thirdAd.getDownloadMonitorMoment() == 3 || thirdAd.getDownloadMonitorMoment() == 4) {
                                AdOtherInstallManager.getInstance().registerAdShowInstall(thirdAd, adReportModel);
                            }
                        }
                    }

                    CommonRequestM.statOnlineAd(adCollectDatas, null);

                }
            };

            postRecord(task);
        }


    }

    private static void downloadLandingPageData(ArrayList<Advertis> thirdAds) {
        if (thirdAds.size() > 0) {
            Advertis advertis = thirdAds.get(0);

            if (advertis.getLandingPageResId() > 0 &&
                    (IAdConstants.IAdPositionId.LOADING.equals(advertis.getAdPositionId())
                    || IAdConstants.IAdPositionId.TRACK_SOUND_PATCH.equals(advertis.getAdPositionId()))) {
                if (advertis.getLandingPageResId() > 0) {
                    PreloadAdManager.getInstance().downloadLandingPageData(LandingPageResData.createByAdvertis(advertis));
                }
            }
        }
    }

    private static boolean isTingShowRecordType(AdReportModel adReportModel) {
        return AppConstants.AD_LOG_TYPE_SITE_SHOW.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_SITE_CLICK.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_RTBENTRY_CLICK.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_RTBENTRY_SHOW.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_SOUND_SHOW.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_SOUND_CLICK.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_SOUND_INTERACTI_CLICK.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_ANSWER_SHOW.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_ANSWER_CLICK.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_SHOW_OB.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_CLICK_OB.equals(adReportModel.getLogType())
                || AppConstants.AD_LOG_TYPE_AUDIO_MORE_AD_BAR.equals(adReportModel.getLogType());
    }

    public static void postRecord(Runnable task) {
        pool.submit(task);
    }

    public static void batchAdRecordByBannerM(Context context, List<BannerM> banners, AdReportModel adReportModel) {
        List<Advertis> thirdAds = bannerToThirdAd(banners);
        batchAdRecord(context, thirdAds, adReportModel);
    }

    public static void adRecord(Context context, final Advertis thirdAd, @logType final String logType, @positionName final String positionName) {
        adRecord(context, thirdAd, new AdReportModel.Builder(logType, positionName).build());
    }

    public static void adRecord(Context context, final Advertis thirdAd, AdReportModel adReportModel) {
        adRecord(context, thirdAd, adReportModel, false);
    }

    public static void adRecord(Context context, final Advertis thirdAd, AdReportModel adReportModel, boolean isNoUseSdkRecord) {
        if (thirdAd == null) {
            return;
        }

        // 如果是主播竞价,并且是老的数据不上报 (首页的数据是先加载本地的然后再加载网络,本地的数据不进行上报)
        if (thirdAd instanceof AnchorAlbumAd && ((AnchorAlbumAd) thirdAd).isOldData()) {
            return;
        }

        batchAdRecord(context, new ArrayList<Advertis>() {
            {
                add(thirdAd);
            }
        }, adReportModel, isNoUseSdkRecord);
    }

    /**
     * 上报声音流广告曝光
     */
    public static void recordSoundAdShow(Advertis advertis, Map<String, String> params, IDataCallBack<String> callBack) {
        if (advertis == null){
            return;
        }
        if (params == null) {
            params = new HashMap<>();
        }
        params.put("adId", String.valueOf(advertis.getAdid()));
        params.put("positionId", advertis.getAdPositionId());
        params.put("trackId", String.valueOf(advertis.getTrackId()));
        params.put("responseId", String.valueOf(advertis.getResponseId()));
        params.put("ts", String.valueOf(System.currentTimeMillis()));

        CommonRequestM.basePostRequestJsonStr(AdUrlConstants.getInstanse().recordSoundAdShow(),
                new Gson().toJson(params), callBack, content -> content);
    }


    private static long lastClickResponseId;
    private static int lastClickNum;

    public static void handlerAdClick(final Context context, final Advertis thirdAd, IHasOpenOtherApp goMyWebCallBack, AdReportModel adReportModel) {
        if (context == null || thirdAd == null || adReportModel == null) {
            handleClickOver(goMyWebCallBack);
            return;
        }
        switch (adReportModel.getCategoryId()) {
            case BannerView.PAY_CATEGORY_ID:
                //精品焦点图
                UserTrackCookie.getInstance().setXmContent("focus", "paidCategory", null);
                break;
            case BannerView.RECOMMEND_CATEGORY_ID:
                //首页焦点图
                UserTrackCookie.getInstance().setXmContent("focus", "homepage", null);
                break;
            case BannerView.LIVE_AUDIO_CATEGORY_ID:
                //直播页面焦点图
                UserTrackCookie.getInstance().setXmContent("focus", "live", null);
                break;
            case BannerView.RADIO_CONTENT_CATEGORY_ID:
                UserTrackCookie.getInstance().setXmContent("focus", "radio", null);
                break;
            case BannerView.LOCAL_CATEGORY_ID:
                UserTrackCookie.getInstance().setXmContent("focus", "localTing", null);
                break;
            default:
                if (adReportModel.getCategoryId() > 0) {
                    UserTrackCookie.getInstance().setXmContent("focus", "category", "" + adReportModel.getCategoryId());
                }
                break;
        }

        String xmAd = getAdIdFromUrl(thirdAd);
        if (!TextUtils.isEmpty(xmAd)) {
            UserTrackCookie.getInstance().setXmAdResource();
            UserTrackCookie.getInstance().setXmAdContent(xmAd, System.currentTimeMillis());
        }

        if (!canClick(thirdAd) && thirdAd.getShowstyle() != Advertis.IMG_SHOW_TYPE_WEB_AD) {
            handleClickOver(goMyWebCallBack);
            return;
        }

        // 增加点击调用堆栈日志
        boolean isByInvalidClick = false;
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < stackTrace.length; i++) {
                if (!TextUtils.isEmpty(stackTrace[i].toString()) && stackTrace[i].toString().contains("performAccessibilityAction")) {
                    isByInvalidClick = true;
                }
                builder.append(stackTrace[i]);
                builder.append("\n");
            }
            Logger.logToFile("--------ad click " + builder.toString());
        }

        // 拦截异常点击
        if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_INTERCEPT_INVALID_CLICK, true)
                && isByInvalidClick && !AD_POSITION_NAME_LOADING.equals(adReportModel.getPositionName())) {
            handleClickOver(goMyWebCallBack);
            return;
        }
        Runnable task = new Runnable() {
            @Override
            public void run() {
                CommonHandlerAdClick(context, thirdAd, goMyWebCallBack, adReportModel, false, false);
            }
        };

        recordClickFrequent(thirdAd, adReportModel);

        if (!thirdAd.isClickTokenEnable() || !ToolUtil.isEmptyCollects(thirdAd.getClickTokens())) {
            task.run();
        } else {
            postRecord(task);
        }

    }

    private static void recordClickFrequent(Advertis thirdAd, AdReportModel adReportModel) {
        try {
            if(thirdAd == null || adReportModel == null) {
                return;
            }

            if (lastClickResponseId != thirdAd.getResponseId()) {
                lastClickNum = 0;
            } else {
                lastClickNum ++;
                if (lastClickNum > 4) {
                    lastClickNum = 0;
                    XDCSCollectUtil.statErrorToXDCS("clickFrequent", "ad=" + thirdAd + " positionName" +
                            "=" + adReportModel.getPositionName() + "  " + Log.getStackTraceString(new Throwable()));
                }
            }

            lastClickResponseId = thirdAd.getResponseId();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void handleClickOver(IHasOpenOtherApp goMyWebCallBack) {
        handleClickOver(goMyWebCallBack, false);
    }

    private static void handleClickOver(IHasOpenOtherApp goMyWebCallBack, boolean openOtherApp) {
        if (goMyWebCallBack != null) {
            HandlerManager.postOnMainAuto(new Runnable() {
                @Override
                public void run() {
                    goMyWebCallBack.clickOver(openOtherApp);
                }
            });
        }
    }

    /**
     *
     * @param context
     * @param thirdAd
     * @param goMyWebCallBack
     * @param adReportModel
     * @param isSoundAd
     * @param isOriginalityDownloadAdClick 是否是附加创意，下载按钮点击事件
     */
    @WorkerThread
    private static void CommonHandlerAdClick(Context context, Advertis thirdAd, IHasOpenOtherApp goMyWebCallBack, AdReportModel adReportModel, boolean isSoundAd, boolean isOriginalityDownloadAdClick) {
        trimAdReportModel(adReportModel);

        updateAdPlayVersion(adReportModel);

        if (thirdAd == null) {
            handleClickOver(goMyWebCallBack);
            return;
        }

        if (AdSDKManager.canUseSDKClickRecord(context)) {
            updateWebVideoModel(thirdAd);
            AdSDKAdapterModel adapterModel = AdConversionUtil.conversionModel(thirdAd, adReportModel.getPositionName());
            if (adReportModel.isDownloadDirect()) {
                adapterModel.setEnableDownloadPopUp(false);
            }
            if (adReportModel.getAutoPull() == 0 || adReportModel.getAutoPull() == 1) {
                // 1s内不重复上报用户的主动点击
                if (thirdAd.getNeedDedupClick() == 1
                        && (System.currentTimeMillis() - thirdAd.getLastClickTime() < ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, "dedupClickInterval", 1000))) {
                    adReportModel.setOnlyGotoClickNoRecord(true);
                }
                thirdAd.setLastClickTime(System.currentTimeMillis());
            }
            InnerHelper.getInstance().clickAd(adapterModel, new IClickOver() {
                @Override
                public void clickOver(boolean openOtherApp) {
                    if(goMyWebCallBack != null) {
                        goMyWebCallBack.clickOver(openOtherApp);
                    }
                }
            }, adReportModel, isOriginalityDownloadAdClick ? IInnerProvider.IClickViewType.CREATIVE_CLICK_VIEW_TYPE :
                    IInnerProvider.IClickViewType.ONLY_NORMAL_CLICK_VIEW_TYPE);
        } else {
            boolean isAdPreviewAd = thirdAd.isPreviewAd() && !thirdAd.isClickReportFlag();

            if (!adReportModel.isAdIdIsNegative() && !isAdPreviewAd && !adReportModel.isOnlyGotoClickNoRecord()) {
                if (!(AppConstants.AD_LOG_TYPE_SHOW_OB.equals(adReportModel.getLogType())
                        || AppConstants.AD_LOG_TYPE_CLICK_OB.equals(adReportModel.getLogType()))) {
                    if (thirdAd.getClickUrls() != null) {
                        for (String clickUrl : thirdAd.getClickUrls()) {
                            ThirdAdStatUtil.getInstance(context).thirdAdStatRequest(clickUrl, thirdAd, adReportModel);
                        }
                    }

                    if (thirdAd.getThirdClickStatUrls() != null) {
                        for (String clickUrl : thirdAd.getThirdClickStatUrls()) {
                            if (!TextUtils.isEmpty(clickUrl)
                                    && (clickUrl.startsWith("http://ad.ximalaya.com/adrecord/thirdAdRecord")
                                    || clickUrl.startsWith("http://ad.test.ximalaya.com/adrecord/thirdAdRecord"))) {
                                postRecord(new Runnable() {
                                    @Override
                                    public void run() {
                                        String realClickUrl = ThirdAdStatUtil.getInstance(context).
                                                execAfterDecorateUrl(clickUrl, thirdAd, adReportModel);
                                        CommonRequestM.pingUrl(realClickUrl);
                                    }
                                });

                            } else {
                                ThirdAdStatUtil.getInstance(context).thirdAdStatRequest(clickUrl, thirdAd, adReportModel);
                            }
                        }
                    }
                }
            }

            String realUrl;

            String realLink = thirdAd.getRealLink();

            if (adReportModel.isOnlyClickRecord() && !adReportModel.isOnlyGotoClickNoRecord()) {
                realUrl = null;

                postRecord(new Runnable() {
                    @Override
                    public void run() {
                        // 这里面可能会有block操作
                        String recordUrl = ThirdAdStatUtil.getInstance(context).
                                execAfterDecorateUrl(thirdAd.getLinkUrl(),
                                        thirdAd, adReportModel);

                        if (!TextUtils.isEmpty(recordUrl) && !isAdPreviewAd) {
                            CommonRequestM.baseGetRequest(recordUrl, null, null, null, null, false);
                        }
                    }
                });

                return;
            } else {
                if (!TextUtils.isEmpty(realLink)) {
                    realUrl = ThirdAdStatUtil.getInstance(context).
                            replaceRequestStrBeforeUpdate(thirdAd, adReportModel, realLink, false, false);
                    if (!adReportModel.isOnlyGotoClickNoRecord()) {
                        postRecord(new Runnable() {
                            @Override
                            public void run() {
                                String recordUrl = ThirdAdStatUtil.getInstance(context).
                                        execAfterDecorateUrl(thirdAd.getLinkUrl(), thirdAd, adReportModel);

                                if (!TextUtils.isEmpty(recordUrl) && !isAdPreviewAd) {
                                    CommonRequestM.baseGetRequest(recordUrl, null, null, null, null, false);
                                }
                            }
                        });
                    }
                } else {
                    realUrl = ThirdAdStatUtil.getInstance(context).
                            execAfterDecorateUrl(thirdAd.getLinkUrl(), thirdAd, adReportModel, true);
                }
            }


            // 不处理handle
            if ((AdManager.isThirdAd(thirdAd)
                    || adReportModel.isIgnoreTarget()) && !adReportModel.isOnlyGotoClickNoRecord()) {

                if (TextUtils.isEmpty(thirdAd.getRealLink()) && !TextUtils.isEmpty(realUrl)) {
                    CommonRequestM.baseGetRequest(realUrl, null, null, null, null, false);
                }

                handleClickOver(goMyWebCallBack);
                return;
            }

            if ((AppConstants.AD_POSITION_NAME_BRAND_FEATURE.equals(adReportModel.getPositionName())
                    && adReportModel.getAlbumId() > 0)) {
                if (TextUtils.isEmpty(adReportModel.getRealUrl())) {
                    handleClickOver(goMyWebCallBack);
                    return;
                }
                realUrl = adReportModel.getRealUrl();
            }

            /**
             * 后台控制，是否开启 apk 安装完成广播监听， 用于监听三方安装或应用市场安装
             * 1. 下载完成时监测；2. 用户点击广告时就监测
             *  ● 3=展示、点击均注册监听
             *  ● 4=展示注册监听
             */
            if (thirdAd.getDownloadMonitorMoment() == 2 || thirdAd.getDownloadMonitorMoment() == 3) {

                if (thirdAd.getAdDownloaderType() == 2) {
                    // api下载器
                    AdApiDownloadManager.getInstance().registerInstallReceiver(thirdAd);
                } else {
                    // 喜马下载器
//                DownloadServiceManage.getInstance().registerInstallReceiver(thirdAd);
                }
                AdOtherInstallManager.getInstance().registerAdClickInstall(thirdAd, adReportModel);
            } else {
            }

            /**
             * 合规拦截弹窗， 如果展示弹窗，点击确定再响应
             */
            String finalRealUrl = realUrl;
            AdClickInterceptDialog.showInterceptClickDialog(MainApplication.getTopActivity(), thirdAd, new AdClickInterceptDialog.OnHandleClicked() {
                @Override
                public void onClick() {
                    doHandleClick(context, thirdAd, finalRealUrl, realLink, goMyWebCallBack, adReportModel, isSoundAd, isOriginalityDownloadAdClick);
                }
            });
        }
    }

    private static void doHandleClick(Context context, Advertis thirdAd, String realUrl, String realLink,
                                      IHasOpenOtherApp goMyWebCallBack, AdReportModel adReportModel, boolean isSoundAd, boolean isOriginalityDownloadAdClick) {

        /**
         * linktype == 101，内容推广落地页跳转， 优先级高于dp， 因为需要跳到播放页页面内，再处理dp
         */
        if (thirdAd.getLinkType() == Advertis.LINK_TYPE_PLAYVIEW_WEBVIEW) {
            if (handlePlayAdWebViewClick(thirdAd, adReportModel.getPositionName())) {
                handleClickOver(goMyWebCallBack);
                return;
            }
        }

        String dpRealLink = thirdAd.getDpRealLink();

        final int linkType = thirdAd.getLinkType();

        String positionName = adReportModel.getPositionName();
        // 是否是声音广告 ,声音广告和普通广告点击逻辑不一样

        String finalRealUrl = realUrl;
        int clickType = thirdAd.getClickType();

        // 如果是附加创意的点击事件，并且 downloadlink 为下载链接， 就直接下载，否则 走通用逻辑
        if (isOriginalityDownloadAdClick && isOriginalityDownloadAd(thirdAd) && !adReportModel.isXmClick()) {
            // 附加创意 按钮区域点击事件， 有dp先响应dp
            if (!TextUtils.isEmpty(dpRealLink)) {
                if (handleDpLink(dpRealLink, thirdAd, adReportModel, positionName, isSoundAd, realUrl, realLink, clickType, linkType)) {
                    handleClickOver(goMyWebCallBack, true);
                    return;
                }
            }
            // 附加创意广告，下载按钮的点击事件，直接唤起下载
            downloadOriginalityFile(thirdAd, positionName);
        } else {
            /**
             * dplink ！= null
             * 并且 没有开启附加创意，才会跳dp
             */
            if (!TextUtils.isEmpty(dpRealLink)
                    /**
                     * @产品lxl： 改成开了附加创意，点非按钮区域强制打开落地页
                     *           不管有没有dp，只要开了附加创意，就跳落地页
                     */
                    && !thirdAd.isEnableOriginality() && !adReportModel.isXmClick()) {
                if (handleDpLink(dpRealLink, thirdAd, adReportModel, positionName, isSoundAd, realUrl, realLink, clickType, linkType)) {
                    handleClickOver(goMyWebCallBack, true);
                    return;
                }
            }
            handleClickImpl(context, thirdAd, goMyWebCallBack, isSoundAd, realLink,
                    linkType, positionName, finalRealUrl, clickType, adReportModel);
        }
    }

    /**
     * 附加创意类广告下载点击事件， 点击不弹窗，直接下载
     * @param thirdAd
     * @param positionName
     */
    private static void downloadOriginalityFile(Advertis thirdAd, String positionName) {

        try {
            String downloadLink = thirdAd.getDownloadLink();
            if (!isDownloadApkUrl(downloadLink)) {
                return;
            }
            DownloadAdvertisParams params = new DownloadAdvertisParams(thirdAd, positionName);

            AdDownloadManager.getInstance().getDownloadStatusByAdvertis(thirdAd, new AdDownloadManager.DownloadStatusListener() {
                @Override
                public void onDownloadNothing(boolean isApiDownload, boolean isContinueStart) {
                    if (thirdAd == null || TextUtils.isEmpty(thirdAd.getDownloadAppName())) {
                        CustomToast.showToast("应用已开始下载，可前往“账号-设置-下载应用管理查看下载进度”");
                    }else {
                        CustomToast.showToast(thirdAd.getDownloadAppName() + "应用已开始下载，可前往“账号-设置-下载应用管理查看下载进度”");
                    }
                    /**
                     * 2021-3-17: wyx:产品逻辑， 如果是点击广告之后当前页下载，就需要弹toast；如果是点击广告之后跳转落地页在下载就不需要
                     */
                    if (isApiDownload) {
                        // api 下载
                        // 其他情况都下载
                        AdApiDownloadManager.getInstance().downLoadApkWithAdvertis(thirdAd);
                    } else {
                        // 喜马下载
                        if (isContinueStart) {
                            // 这个场景是 下载url相同，但是下载物料的id不同，按真实进度继续下载，但是需要上报【开始下载】埋点
                            DownloadServiceManage.getInstance().downLoadAPKContinue(downloadLink, params);
                        } else {
                            // 其他情况都下载
                            DownloadServiceManage.getInstance().downLoadAPK(downloadLink, params);
                        }
                    }
                }

                @Override
                public void onDownloadInstalled(boolean isApiDownload) {
                    // 如果是apk已安装状态，则点击直接打开apk
                    AdApkInstallManager.getInstance().startAppByDownloadUrlOrPath(downloadLink, "", thirdAd.getAppPackageName());
                }

                @Override
                public void onDownloading(boolean isApiDownload) {
                    if (thirdAd == null || TextUtils.isEmpty(thirdAd.getDownloadAppName())) {
                        CustomToast.showToast("应用正在下载中，可前往“账号-设置-下载应用管理查看下载进度”");
                    }else {
                        CustomToast.showToast(thirdAd.getDownloadAppName() + "应用正在下载中，可前往“账号-设置-下载应用管理查看下载进度”");
                    }
                }

                @Override
                public void onDownloadPause(boolean isApiDownload, boolean isRestart) {
                    if (!isApiDownload) {
                        // 喜马下载器 暂停下载，则继续下载
                        DownloadServiceManage.getInstance().downLoadAPK(downloadLink, params);
                    }
                }

                @Override
                public void onDownloadDone(boolean isApiDownload, String savePath) {
                    // 如果下载状态是完成， 则点击直接安装apk, 如果安装失败，则重新开始下载
                    if (!AdApkInstallManager.getInstance().installApkFromDownloadUrlOrPath(downloadLink, savePath, true)){
                        if (isApiDownload) {
                            AdApiDownloadManager.getInstance().downLoadApkWithAdvertis(thirdAd);
                        } else {
                            // 其他情况都下载
                            DownloadServiceManage.getInstance().downLoadAPK(downloadLink, params);
                        }
                    }
                }
                @Override
                public void onDownloadStatusError(boolean isApiDownload) {
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void handleClickImpl(Context context, Advertis thirdAd, IHasOpenOtherApp goMyWebCallBack,
                                       boolean isSoundAd, String realLink, int linkType,
                                       String positionName, String finalRealUrl, int clickType,
                                       @Nullable AdReportModel adReportModel) {
        HandlerManager.postOnMainAuto(new Runnable() {
            @Override
            public void run() {
                if (adReportModel != null && adReportModel.isXmClick()) {
                    if ((isSoundAd && clickType == LINK_TYPE_WEB)
                            || ((linkType == Advertis.LINK_TYPE_WEB || linkType == Advertis.LINK_TYPE_NONE) && thirdAd.getOpenlinkType() != 1)) {
                        if (finalRealUrl.startsWith("iting") || finalRealUrl.contains(TuiaRouterHandler.TUIA_WEB_HOST)) {
                            return;
                        }

                        Activity activity = MainApplication.getMainActivity();
                        if (activity instanceof MainActivity) {
                            Bundle bundle = createStartNativeHybridFragmentBundle(finalRealUrl, thirdAd, positionName);
                            bundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_AD_VI_CLICK, true);
                            ((MainActivity) activity).startAdFragment(bundle);
                        }
                    }
                    return;
                }

                // open link type = 4, 跳转到游戏中心
                if (thirdAd.getOpenlinkType() == Advertis.LINK_TYPE_GAME_CENTER) {
                    AdGameUtil.jumpToGameBundle(thirdAd, positionName, true);
                    handleClickOver(goMyWebCallBack);
                    return;
                }

                /**
                 * linktype == 100，声音播放页或视频播放页 - 原生落地页跳转
                 */
                if (thirdAd.getLinkType() == Advertis.LINK_TYPE_NATIVE_WEBVIEW) {
                   if (handleNativeWebViewClick(thirdAd)) {
                       handleClickOver(goMyWebCallBack);
                       return;
                   }
                }

                if (isSoundAd) {
                    if (linkType == Advertis.LINK_TYPE_FILE) {
                        downloadFile(finalRealUrl, thirdAd, positionName);
                        return;
                    }

                    switch (clickType) {
                        case LINK_TYPE_WEB:
                            gotoWeb(context, finalRealUrl, thirdAd, positionName);
                            break;
                        case LINK_TYPE_OPEN_THIRD_BROWSER:
                            gotoExteralWeb(context, finalRealUrl);
                            break;
                        case LINK_TYPE_GOTO_AD:
                            LocalBroadcastManager.getInstance(context).sendBroadcast(
                                    new Intent("goto_ad_action"/*YaoyiYaoAdManage.PlayFragmentAdClickBroadcastReceiver.GOTO_AD_ACTION*/));
                            break;
                        case LINK_TYPE_CALL_PHONE:
                            showCallPhoneDialog(thirdAd);
                            break;
                        case IAdConstants.IAdClickType.CLICK_TYPE_OPEN_WX_APPLETS:
                            openWxApplets(context, realLink, thirdAd, positionName);
                            break;
                        case IAdConstants.IAdClickType.CLICK_TYPE_DOWNLOAD:
                            downloadFile(finalRealUrl, thirdAd, positionName);
                            break;
                    }
                } else {
                    if (thirdAd.getOpenlinkType() == Advertis.LINK_TYPE_CALL_PHONE) {
                        showCallPhoneDialog(thirdAd);
                        handleClickOver(goMyWebCallBack);
                        return;
                    }

                    if (thirdAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_OPEN_WX_APPLETS) {
                        openWxApplets(context, realLink, thirdAd, positionName);
                        handleClickOver(goMyWebCallBack);
                        return;
                    }

                    if (linkType == Advertis.LINK_TYPE_FILE
                            || thirdAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_DOWNLOAD) {
                        handleClickOver(goMyWebCallBack);
                        downloadFile(finalRealUrl, thirdAd, positionName);
                    } else if (linkType == Advertis.LINK_TYPE_WEB || linkType == Advertis.LINK_TYPE_NONE) {
                        if (thirdAd.getOpenlinkType() == 1) {
                            gotoExteralWeb(context, finalRealUrl);
                            handleClickOver(goMyWebCallBack);
                        } else {
                            gotoWeb(context, finalRealUrl, thirdAd, positionName);
                            handleClickOver(goMyWebCallBack);
                        }
                    }
                }
            }
        });
    }

    /**
     * linktype == 101，内容推广落地页跳转， 优先级高于dp， 因为需要跳到播放页页面内，再处理dp
     */
    private static boolean handlePlayAdWebViewClick(Advertis advertis, String positionName) {
        if (advertis == null) {
            return false;
        }
        try {
            if (TextUtils.isEmpty(advertis.getPositionName())) {
                advertis.setPositionName(positionName);
            }

            if (advertis.getJumpTrackId() <= 0
                    && advertis.getBusinessExtraInfo() != null && advertis.getBusinessExtraInfo().getJumpTrackId() > 0) {
                advertis.setJumpTrackId(advertis.getBusinessExtraInfo().getJumpTrackId());
                advertis.setAutoJumpTime(advertis.getBusinessExtraInfo().getAutoJumpTime());
            }
            long trackId = advertis.getJumpTrackId();
            if (trackId > 0) {
                Bundle bundle = new Bundle();
                // 接口 未直接返回jumptrackid 和 autojumptime ，则 使用adx透传的
                bundle.putParcelable(AdPlayNativeWebManager.KEY_NATIVE_PLAY_ADVERTIS, advertis);
                bundle.putLong(AdPlayNativeWebManager.KEY_NATIVE_PLAY_TRACK_ID, trackId);
                AdPlayTools.goPlayByTrackId(trackId, bundle);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 原生落地页跳转事件
     *
     * @param advertis
     */
    private static boolean handleNativeWebViewClick(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        try {
            Bundle bundle = new Bundle();
            if (advertis.getClickJumpType() != 0) {
                // 默认跳到视频页，
                bundle.putBoolean(VideoPlayParamsBuildUtil.KEY_FOCUS_VIDEO_PLAY_TAB, true);
            }
            bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_LINK, advertis.getRealLink());
            bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_TITLE, advertis.getName());
            bundle.putBoolean(VideoPlayParamsBuildUtil.KEY_IS_OPEN_NATIVE_WEBVIEW, true);
            bundle.putParcelable(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_ADVERITS, advertis);
            bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_POSITION_NAME, ((AnchorAlbumAd) advertis).getPositionName());

            bundle.putString(VideoPlayParamsBuildUtil.KEY_NATIVE_WEBVIEW_AD_TRACK_ID, ((AnchorAlbumAd) advertis).getPromoteTrackId() + "");

            AdPlayTools.goVideoPlayByTrackId(((AnchorAlbumAd) advertis).getPromoteTrackId(), bundle);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static void openWxApplets(Context context, String realLink, Advertis advertis,
                                      String positionName) {
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                openWxApplets(advertis.getWxMiniProgramId(), advertis.getDpRealLink(), new IDataCallBack() {
                    @Override
                    public void onSuccess(@Nullable Object object) {

                    }

                    @Override
                    public void onError(int code, String message) {
                        openWxFail(context, realLink, advertis, positionName);
                    }
                });
            }
        });
    }

    public static void openWxApplets(String wxMiniProgramId, String path,
                                     IDataCallBack callBack) {
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().openWeChatLiteApp(
                    MainApplication.getMyApplicationContext(), wxMiniProgramId,
                    path, WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE,
                    callBack);
        } catch (Exception e) {
            e.printStackTrace();
            if (callBack != null) {
                callBack.onError(-1, "打开小程序失败");
            }
        }
    }

    private static void openWxFail(Context context, String realUrl, Advertis thirdAd, String positionName) {
        gotoWeb(context, realUrl, thirdAd, positionName);
    }

    private static void showCallPhoneDialog(Advertis advertis) {
        if (advertis == null || TextUtils.isEmpty(advertis.getRealLink())) {
            return;
        }
        new DialogBuilder(MainApplication.getTopActivity())
                .setTitle(TextUtils.isEmpty(advertis.getProviderName()) ?
                        null : advertis.getProviderName() + "官方热线")
                .setMessage(advertis.getRealLink())
                .setOkBtn("立即拨打", new DialogBuilder.DialogCallback() {
                    @Override
                    public void onExecute() {
                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + advertis.getRealLink()));
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        ToolUtil.checkIntentAndStartActivity(MainApplication.getMyApplicationContext(), intent);
                    }
                }).setCancelBtn("残忍取消").showConfirm();
    }

    // 因为之前太多了 先保留这种的
    public static void handlerAdClick(final Context context, final Advertis thirdAd, @positionName final String positionName) {
        handlerAdClick(context, thirdAd, null, new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_SITE_CLICK, positionName).build());
    }

    public static void handlerAdClick(final Context context, final Advertis thirdAd, AdReportModel adReportModel) {
        handlerAdClick(context, thirdAd, null, adReportModel);
    }

    public static void hanlderSoundAdClick(final Context context, @Nullable Advertis mSoundAd, @positionName final String positionName) {
        hanlderSoundAdClick(context, mSoundAd, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SOUND_CLICK, positionName).build());
    }

    public static void hanlderSoundAdClick(final Context context, @Nullable Advertis mSoundAd, AdReportModel adReportModel) {
        if (context == null || mSoundAd == null) {
            return;
        }
        int clickType = mSoundAd.getClickType();
        if (clickType == LINK_TYPE_NONE) {
            return;
        }
        // 增加点击调用堆栈日志
        boolean isByInvalidClick = false;
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < stackTrace.length; i++) {
                if (!TextUtils.isEmpty(stackTrace[i].toString()) && stackTrace[i].toString().contains("performAccessibilityAction")) {
                    isByInvalidClick = true;
                }
                builder.append(stackTrace[i]);
                builder.append("\n");
            }
            Logger.logToFile("--------ad click " + builder.toString());
        }

        // 拦截异常点击
        if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_INTERCEPT_INVALID_CLICK, true)
                && isByInvalidClick && !AD_POSITION_NAME_LOADING.equals(adReportModel.getPositionName())) {
            return;
        }
        UserTrackCookie.getInstance().setXmAdResource();
        UserTrackCookie.getInstance().setXmAdContent(mSoundAd.getAdid(), System.currentTimeMillis(), mSoundAd.getTrackId());

        recordClickFrequent(mSoundAd, adReportModel);

        postRecord(new Runnable() {
            @Override
            public void run() {
                CommonHandlerAdClick(context, mSoundAd, null, adReportModel, true, false);
            }
        });
    }

    /**
     * 附加创意类广告下载点击事件
     * @param context
     * @param advertis
     * @param adReportModel
     */
    public static void handleOriginalityDownloadAdClick(Context context, Advertis advertis, AdReportModel adReportModel) {
        postRecord(new Runnable() {
            @Override
            public void run() {
                CommonHandlerAdClick(context, advertis, null, adReportModel, false, true);
            }
        });
    }

    private static void gotoExteralWeb(final Context context, String url) {
        if (!TextUtils.isEmpty(url)) {
            final Intent i = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            if (!(context instanceof Activity)) {
                i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    ToolUtil.checkIntentAndStartActivity(context, i);
                }
            });
        }
    }

    /**
     * 判断是否直接从资源位跳转单款游戏（非梦工厂单款游戏）
     *
     * @param realUrl
     * @return
     */
    public static boolean isGoToSingleGame(String realUrl) {
        Uri url = Uri.parse(realUrl);
        if (realUrl.startsWith("iting://")) {
            String msg_type = url.getQueryParameter("msg_type");
            if (msg_type != null) {
                return AppConstants.ITING_GAME_LIST.contains(Integer.parseInt(msg_type));
            }
        } else if (realUrl.contains(TuiaRouterHandler.TUIA_WEB_HOST)) {
            return true;
        }
        return false;
    }

    public static String addGameRelatedParams(String realUrl, int adId, String positionName, int adType) {
        Uri.Builder builder = Uri.parse(realUrl).buildUpon();
        if (!realUrl.contains(ThirdGameAdConstants.GAME_ID)) {
            builder.appendQueryParameter(ThirdGameAdConstants.GAME_ID, String.valueOf(adId));
        }
        if (!realUrl.contains(ThirdGameAdConstants.POSITION_NAME)) {
            builder.appendQueryParameter(ThirdGameAdConstants.POSITION_NAME, positionName);
        }
        if (!realUrl.contains(ThirdGameAdConstants.AD_TYPE)) {
            builder.appendQueryParameter(ThirdGameAdConstants.AD_TYPE, String.valueOf(adType));
        }
        return builder.toString();
    }

    public static void gotoWeb(final Context context, String realUrl, Advertis advertis, String positionName) {
        if (advertis == null) {
            return;
        }
        Activity activity = MainApplication.getTopActivity();
        if (checkGotoGame(realUrl, advertis.getAdid(), advertis.getAdtype(), positionName)) {
            return;
        }

        Logger.log("AdManager : gotoWeb url = " + realUrl);

        Logger.log("SplashAdManager : clickCheck 8 ");
        if (activity != null && activity instanceof MainActivity) {
            Bundle bundle = createStartNativeHybridFragmentBundle(realUrl, advertis, positionName);
            Logger.log("SplashAdManager : clickCheck 9 ");
            ((MainActivity) activity).startAdFragment(bundle);
        } else {
            Logger.log("SplashAdManager : clickCheck 10 ");
            Intent intent = new Intent(MainApplication.getMyApplicationContext(), WebActivity.class);
            intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, realUrl);
            intent.putExtra(BundleKeyConstants.KEY_IS_LANDSCAPE, advertis.isLandScape());
            if (!(context instanceof Activity)) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            }
            if (advertis.getIsInternal() != -1) {
                intent.putExtra(NativeHybridFragment.IS_EXTERNAL_URL, advertis.getIsInternal() == 0);
            } else {
                intent.putExtra(NativeHybridFragment.IS_EXTERNAL_URL, false);
            }
            AdShareDataForOpenSDK shareData = advertis.isShareFlag() ? advertis.getShareData() : null;
            if (shareData != null) {
                AdManager.setIntentShare(intent, shareData, advertis.getIsInternal() == -1);
            }

            intent.putExtra(BundleKeyConstants.KEY_GOTO_HYBRID_VIEW_AD, advertis);
            intent.putExtra(BundleKeyConstants.KEY_IS_FROM_AD_LANDING_PAGE, true);
            if (advertis.isHideInfoForWebUa()) {
                intent.putExtra(BundleKeyConstants.KEY_HIDE_UA_INFO, true);
            }
            intent.putExtra(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
            intent.putExtra(BundleKeyConstants.KEY_AD_POSITION_NAME, positionName);
            intent.putExtra(BundleKeyConstants.KEY_USE_LOTTIE, false);
            if (canGotoAdHybridFragment(advertis)) {
                intent.putExtra(BundleKeyConstants.KEY_USE_AD_HYBRID_FRAGEMENT, true);
            }
            context.startActivity(intent);
        }
    }

    public static boolean checkGotoGame(String realUrl, int adId, int adType, String positionName) {
        return checkGotoGame(realUrl, adId, adType, positionName, null);
    }

    public static boolean checkGotoGame(String realUrl, int adId, int adType, String positionName, Activity customActivity) {
        Activity activity;
        if (customActivity != null && !customActivity.isFinishing()) {
            activity = customActivity;
        } else {
            activity = MainApplication.getTopActivity();
        }
        if (!TextUtils.isEmpty(realUrl) && isGoToSingleGame(realUrl)) {
            realUrl = addGameRelatedParams(realUrl, adId, positionName, adType);
        }

        Logger.log("SplashAdManager : clickCheck 9 ");
        if (!TextUtils.isEmpty(realUrl)) {
            if (realUrl.startsWith("iting://")) {
                if (!realUrl.contains("source")) {
                    realUrl = realUrl + "&source=ad";
                }
                try {
                    if (AppConstants.AD_POSITION_NAME_FOCUS.equals(positionName)) {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(activity, Uri.parse(realUrl), ResPositionConstant.FOCUS);
                    } else if (AppConstants.AD_POSITION_NAME_FIREWORK_POPUP.equals(positionName)) {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(activity, Uri.parse(realUrl), ResPositionConstant.UNI_POP);
                    } else {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleITing(activity, Uri.parse(realUrl));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return true;
            }

            if (realUrl.contains(TuiaRouterHandler.TUIA_WEB_HOST)) {
                if (XmUriRouter.isXmRouteUrl(realUrl)) {
                    try {
                        XmUriRouter.route(activity, realUrl);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return true;
                }
            }
        }

        return false;
    }

    public static boolean canGotoAdHybridFragment(Advertis advertis) {
        if (advertis != null
                && advertis.isEnableContinuePlay()
                && advertis.getWebVideoModel() != null
//                && isLandingPageWebAd(advertis)
        ) {
            return true;
        }
        if (advertis != null && advertis.getWebVideoModel() != null
                && (advertis.getLinkType() == Advertis.LINK_TYPE_IMMERSIVE_VIDEO_WEBVIEW ||
                advertis.getLinkType() == Advertis.LINK_TYPE_FULL_VIDEO_WEBVIEW ||
                advertis.getLinkType() == Advertis.LINK_TYPE_FULL_VIDEO_NEW ||
                advertis.getLinkType() == Advertis.LINK_TYPE_VIDEO_WEBVIEW_NEW)) {
            return true;
        }

        return false;
    }

    public static Bundle createStartNativeHybridFragmentBundle(String realUrl, Advertis advertis,
                                                               String positionName) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, realUrl);
        if (advertis != null) {
            bundle.putBoolean(BundleKeyConstants.KEY_IS_LANDSCAPE, advertis.isLandScape());
            if (advertis.getIsInternal() != -1) {
                bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, advertis.getIsInternal() == 0);
            } else {
                bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, false);
            }
            AdShareDataForOpenSDK shareData = advertis.isShareFlag() ? advertis.getShareData() : null;
            if (shareData != null) {
                AdManager.setBundleShare(bundle, shareData);
            }
            bundle.putParcelable(BundleKeyConstants.KEY_GOTO_HYBRID_VIEW_AD, advertis);
            if (advertis.isHideInfoForWebUa()) {
                bundle.putBoolean(BundleKeyConstants.KEY_HIDE_UA_INFO, true);
            }
        }
        bundle.putBoolean(BundleKeyConstants.KEY_FIT_SOFT_KEYBOARD, true);
        bundle.putString(BundleKeyConstants.KEY_AD_POSITION_NAME, positionName);
        AdManager.addResPositionTag(bundle, positionName);
        bundle.putBoolean(BundleKeyConstants.KEY_USE_LOTTIE, false);
        bundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_AD_LANDING_PAGE, true);

        if (canGotoAdHybridFragment(advertis)) {
            bundle.putBoolean(BundleKeyConstants.KEY_USE_AD_HYBRID_FRAGEMENT, true);
        }
        return bundle;
    }

    public static void downloadFile(String realUrl, Advertis thirdAd, String positionName) {
        try {

            DownloadAdvertisParams params = new DownloadAdvertisParams(thirdAd, positionName);

            AdDownloadManager.getInstance().getDownloadStatusByAdvertis(thirdAd, new AdDownloadManager.DownloadStatusListener() {
                @Override
                public void onDownloadNothing(boolean isApiDownload, boolean isContinueStart) {
                    try {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction()
                                .interceptAdAppDownload(MainApplication.getOptActivity(), "", thirdAd,
                                        new IHandleOk() {
                                            @Override
                                            public void onReady() {
                                                if (thirdAd == null || TextUtils.isEmpty(thirdAd.getDownloadAppName())) {
                                                    CustomToast.showToast("应用已开始下载，可前往“账号-设置-下载应用管理查看下载进度”");
                                                }else {
                                                    CustomToast.showToast(thirdAd.getDownloadAppName() + "应用已开始下载，可前往“账号-设置-下载应用管理查看下载进度”");
                                                }
                                                /**
                                                 * 2021-3-17: wyx:产品逻辑， 如果是点击广告之后当前页下载，就需要弹toast；如果是点击广告之后跳转落地页在下载就不需要
                                                 */
                                                if (isApiDownload) {
                                                    // api 下载
                                                    // 其他情况都下载
                                                    AdApiDownloadManager.getInstance().downLoadApkWithAdvertis(thirdAd);
                                                } else {
                                                    // 喜马下载
                                                    if (isContinueStart) {
                                                        // 这个场景是 下载url相同，但是下载物料的id不同，按真实进度继续下载，但是需要上报【开始下载】埋点
                                                        DownloadServiceManage.getInstance().downLoadAPKContinue(realUrl, params);
                                                    } else {
                                                        // 其他情况都下载
                                                        DownloadServiceManage.getInstance().downLoadAPK(realUrl, params);
                                                    }
                                                }
                                            }
                                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onDownloadInstalled(boolean isApiDownload) {
                    // 如果是apk已安装状态，则点击直接打开apk
                    AdApkInstallManager.getInstance().startAppByDownloadUrlOrPath(realUrl, "", thirdAd.getAppPackageName());
                }

                @Override
                public void onDownloading(boolean isApiDownload) {
                    if (thirdAd == null || TextUtils.isEmpty(thirdAd.getDownloadAppName())) {
                        CustomToast.showToast("应用正在下载中，可前往“账号-设置-下载应用管理查看下载进度”");
                    }else {
                        CustomToast.showToast(thirdAd.getDownloadAppName() + "应用正在下载中，可前往“账号-设置-下载应用管理查看下载进度”");
                    }
                }

                @Override
                public void onDownloadPause(boolean isApiDownload, boolean isRestart) {
                    if (!isApiDownload) {
                        // 喜马下载器 暂停下载，则继续下载
                        DownloadServiceManage.getInstance().downLoadAPK(realUrl, params);
                    }
                }

                @Override
                public void onDownloadDone(boolean isApiDownload, String savePath) {
                    // 如果下载状态是完成， 则点击直接安装apk, 如果安装失败，则重新开始下载
                   if (!AdApkInstallManager.getInstance().installApkFromDownloadUrlOrPath(realUrl, savePath, true)){
                       if (isApiDownload) {
                           AdApiDownloadManager.getInstance().downLoadApkWithAdvertis(thirdAd);
                       } else {
                           // 其他情况都下载
                           DownloadServiceManage.getInstance().downLoadAPK(realUrl, params);
                       }
                   }
                }
                @Override
                public void onDownloadStatusError(boolean isApiDownload) {
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    // 是否是下载类app
    public static boolean isDownloadAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        if ((advertis.getLinkType() == Advertis.LINK_TYPE_FILE
                || advertis.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_DOWNLOAD)
                && !TextUtils.isEmpty(advertis.getRealLink())) {
            return true;
        }

        return false;
    }

    // 是否是 附加创意 下载类app
    public static boolean isOriginalityDownloadAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        // 开启了附加创意的开关，并且downloadlink 是下载链接
        if (advertis.isEnableOriginality() && isDownloadApkUrl(advertis.getDownloadLink())) {
            return true;
        }

        return false;
    }


    /**
     * 是否是 dp跳转类型
     * @param advertis
     * @return
     */
    public static boolean isDpLinkAd(Advertis advertis) {

        return advertis != null && !TextUtils.isEmpty(advertis.getDpRealLink());
    }

    /**
     * 是否是微信小程序
     * @param advertis
     * @return
     */
    public static boolean isWxAppletsAd(Advertis advertis) {
        return advertis != null && !TextUtils.isEmpty(advertis.getWxMiniProgramId())
                && advertis.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_OPEN_WX_APPLETS;
    }

    public static void updateAdIdFromThirdAd(Advertis thirdAd) {
        if (thirdAd == null) {
            return;
        }

        if (thirdAd.getAdid() <= 0) {
            String id = getAdIdFromUrl(thirdAd);
            if (TextUtils.isEmpty(id)) {
                return;
            }

            try {
                int ids = Integer.valueOf(id);
                thirdAd.setAdid(ids);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static String getAdIdFromUrl(Advertis thirdAd) {
        if (thirdAd == null) {
            return null;
        }
//        return getAdIdFromUrl(thirdAd.getLinkUrl());
        return thirdAd.getAdid() + "";
    }

    public static String getAdIdFromUrl(String url) {
        if (TextUtils.isEmpty(url) || !url.contains("ad"))
            return null;
        String params = url.substring(url.indexOf('?') + 1);
        if (TextUtils.isEmpty(params))
            return null;

        String[] paramaters = params.split("&");
        for (String param : paramaters) {
            String[] values = param.split("=");
            if ("ad".equals(values[0]))
                return values[1];
        }

        return null;
    }

    /**
     * 添加水印 (默认在右下角)
     */
    public static Bitmap addWaterMark(Bitmap src, String water, float textSize, int textColor, int bottomPadding, int rightPadding) {
        if (src == null || TextUtils.isEmpty(water)) {
            return null;
        }
        Bitmap.Config config = src.getConfig();

        if (config == null) {
            config = Bitmap.Config.ARGB_8888;
        }

        Bitmap tarBitmap = src.copy(config, true);
        Canvas canvas = new Canvas(tarBitmap);
        Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DEV_KERN_TEXT_FLAG);
        textPaint.setTextSize(textSize);
        textPaint.setColor(textColor);

        Rect bounds = new Rect();
        textPaint.getTextBounds(water, 0, water.length(), bounds);

        textPaint.setDither(true);
        textPaint.setFilterBitmap(true);

        canvas.drawText(water, tarBitmap.getWidth() - bounds.width() - rightPadding, tarBitmap.getHeight() - bounds.bottom - bottomPadding, textPaint);
        canvas.save();
        canvas.restore();
        return tarBitmap;
    }

    public static Intent setIntentShare(Intent intent, AdShareDataForOpenSDK model, boolean needSetExtenal) {
        if (intent == null || model == null) {
            return null;
        }

        intent.putExtra(IWebFragment.SHOW_SHARE_BTN, true);
        intent.putExtra(IWebFragment.SHARE_COVER_PATH, model.getLinkCoverPath());
        intent.putExtra(IWebFragment.SHARE_CONTENT, model.getLinkContent());
        intent.putExtra(IWebFragment.SHARE_TITLE, model.getLinkTitle());
        intent.putExtra(IWebFragment.SHARE_URL, model.getLinkUrl());
        if (needSetExtenal) {
            intent.putExtra(IWebFragment.IS_EXTERNAL_URL, model.isExternalUrl());
        }

        return intent;
    }

    /**
     * 为Bundle 添加分享相关数据,可参考setIntentShare
     *
     * @param bundle
     * @param model
     * @return
     */
    public static Bundle setBundleShare(Bundle bundle, AdShareDataForOpenSDK model) {
        if (bundle == null || model == null) {
            return null;
        }

        bundle.putBoolean(IWebFragment.SHOW_SHARE_BTN, true);
        bundle.putString(IWebFragment.SHARE_COVER_PATH, model.getLinkCoverPath());
        bundle.putString(IWebFragment.SHARE_CONTENT, model.getLinkContent());
        bundle.putString(IWebFragment.SHARE_TITLE, model.getLinkTitle());
        bundle.putString(IWebFragment.SHARE_URL, model.getLinkUrl());
        if (!bundle.containsKey(IWebFragment.IS_EXTERNAL_URL)) {
            bundle.putBoolean(IWebFragment.IS_EXTERNAL_URL, model.isExternalUrl());
        }
        return bundle;
    }

    public static Advertis bannerToThirdAd(BannerM oldBanner) {
        if (oldBanner == null) {
            return null;
        }

        BannerM bannerM = new BannerM(oldBanner);

        Advertis thirdAd = new Advertis();
        thirdAd.setAdid((int) bannerM.getBannerId());
        thirdAd.setAdtype(bannerM.getAdtype());
        thirdAd.setName(bannerM.getBannerShortTitle());
        thirdAd.setDescription(bannerM.getBannerTitle());
        thirdAd.setImageUrl(bannerM.getBannerUrl());
        thirdAd.setClickUrls(bannerM.getClickUrls());
        thirdAd.setShowUrls(bannerM.getShowUrls());
        thirdAd.setLinkUrl(bannerM.getBannerRedirectUrl());
        thirdAd.setShareFlag(bannerM.isShareFlag());
        thirdAd.setShareData(bannerM.getShareData());
        thirdAd.setOpenlinkType(bannerM.getOpenlinkType());

        thirdAd.setThirdStatUrl(bannerM.getThirdStatUrl());
        thirdAd.setThirdShowStatUrls(bannerM.getThirdShowStatUrls());
        thirdAd.setThirdClickStatUrls(bannerM.getThirdClickStatUrls());

        thirdAd.setClickTokens(bannerM.getClickTokens());
        thirdAd.setShowTokens(bannerM.getShowTokens());
        thirdAd.setRealLink(bannerM.getRealLink());
        thirdAd.setResponseId(bannerM.getResponseId());
        thirdAd.setIsInternal(bannerM.getIsInternal());
        thirdAd.setLandScape(bannerM.isLandScape());
        return thirdAd;
    }

    public static List<Advertis> bannerToThirdAd(List<BannerM> bannerMs) {
        if (bannerMs == null) {
            return null;
        }

        // 去重
        HashSet<BannerM> bannerSet = new HashSet<BannerM>(bannerMs);

        List<Advertis> thirdAds = new ArrayList<>();

        for (BannerM bannerM : bannerSet) {
            if (bannerM != null && bannerM.getBannerContentType() == BannerM.TYPE_AD) {
                thirdAds.add(bannerToThirdAd(bannerM));
            }
        }
        return thirdAds;
    }

    private static Gson mAdGson;

    private static Type listAdvertisType;

    private static Type advertisListType;

    private static TypeToken<List<Advertis>> listAdvertisTypeToken;

    public static Type getAdvertisListType() {
        if (advertisListType != null) {
            return advertisListType;
        }

        synchronized (AdManager.class) {
            if (advertisListType == null) {
                advertisListType = new TypeToken<AdvertisList>() {
                }.getType();
            }
        }

        return advertisListType;
    }

    @NonNull
    public static Gson getAdGson() {
        if (mAdGson != null) {
            return mAdGson;
        }

        synchronized (AdManager.class) {
            if (mAdGson == null) {
                mAdGson = new Gson();
            }
        }

        return mAdGson;
    }

    public static Type getListAdvertisType() {
        if (listAdvertisType != null) {
            return listAdvertisType;
        }

        synchronized (AdManager.class) {
            if (listAdvertisType == null) {
                listAdvertisType = getListAdvertisTypeToken().getType();
            }
        }

        return listAdvertisType;
    }

    public static TypeToken<List<Advertis>> getListAdvertisTypeToken() {
        if (listAdvertisTypeToken != null) {
            return listAdvertisTypeToken;
        }

        listAdvertisTypeToken = new TypeToken<List<Advertis>>() {
        };
        return listAdvertisTypeToken;
    }

    public static <T extends Advertis> List<T> parseToThirdAd(TypeToken<List<T>> typeToken, String json, String adPositionId) throws Exception {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject(json);
        long respondId = jsonObject.optLong("responseId");
        String data = jsonObject.optString("data");
        String clientIp = jsonObject.optString("clientIp");
        if (TextUtils.isEmpty(data)) {
            return null;
        }

        List<T> ads = getAdGson().fromJson(data, typeToken.getType());
        if (!ToolUtil.isEmptyCollects(ads)) {
            for (Advertis thirdAd : ads) {
                thirdAdUpdateRequestInfo(thirdAd, adPositionId, respondId, clientIp);
            }
        }
        return ads;
    }

    public static List<Advertis> parseToThirdAd(String json, String adPositionId) throws Exception {
        return parseToThirdAd(json, adPositionId, null);
    }

    /**
     * cookie信息是否添加已安装列表
     * @param url
     * @return
     */
    public static boolean isNeedAdCookie(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        for (String pluginUrl : UserTrackingUrlMatcher.URL_AD_COOKIE_INSTALLED) {
            if (url.contains(pluginUrl)) {
                return true;
            }
        }
        return false;
    }

    public static String addIPV6ToCookie(String url, String cookie) {
        if (TextUtils.isEmpty(url) || TextUtils.isEmpty(cookie)) {
            return cookie;
        }
        boolean needAddIPV6 = false;
        for (String targetUrl : UserTrackingUrlMatcher.IPV6_AD_REQUEST) {
            if (url.contains(targetUrl)) {
                needAddIPV6 = true;
                break;
            }
        }
        if (!needAddIPV6){
            return cookie;
        }
        if (!cookie.contains("IPV6")){
            StringBuilder stringBuilder = new StringBuilder(cookie);
            String ipv6 = IPV6Util.getAllIPv6();
            stringBuilder.append("IPV6")
                    .append("=")
                    .append(ipv6)
                    .append(";");
            cookie = stringBuilder.toString();
        }
        return cookie;
    }

    public interface IInterceptParse {
        void onParseAfter(AdvertisList advertisList);
    }

    public static List<Advertis> parseToThirdAd(String json, String adPositionId, IInterceptParse interceptParse) throws Exception {
        if (TextUtils.isEmpty(json)) {
            return null;
        }

        AdvertisList advertisList = getAdGson().fromJson(json, getAdvertisListType());

        if (advertisList != null) {
            if (!ToolUtil.isEmptyCollects(advertisList.getAdvertisList())) {
                for (Advertis advertis : advertisList.getAdvertisList()) {
                    thirdAdUpdateRequestInfo(advertis, adPositionId, advertisList.getResponseId(), advertisList.getClientIp());
                }
            }
        }

        if (interceptParse != null) {
            interceptParse.onParseAfter(advertisList);
        }

        return advertisList != null ? advertisList.getAdvertisList() : null;
    }

    public static List<Advertis> parseToThirdAd(JSONObject jsonObject) throws Exception {
        if (jsonObject == null) {
            return null;
        }
        long respondId = jsonObject.optLong("responseId");
        String data = jsonObject.optString("data");
        String clientIp = jsonObject.optString("clientIp");
        if (TextUtils.isEmpty(data)) {
            return null;
        }
        List<Advertis> ads = new Gson().fromJson(data, new TypeToken<List<Advertis>>() {
        }.getType());
        if (!ToolUtil.isEmptyCollects(ads)) {
            for (Advertis thirdAd : ads) {
                thirdAd.setResponseId(respondId);
                thirdAd.setClientIp(clientIp);
            }
        }
        return ads;
    }

    public static void thirdAdUpdateRequestInfo(Advertis thirdAd, String adPositionId,
                                                long respondId, String clientIp) {
        if (thirdAd == null) {
            return;
        }
        thirdAd.setClientIp(clientIp);
        thirdAd.setResponseId(respondId);
        if (!TextUtils.isEmpty(adPositionId) && TextUtils.isEmpty(thirdAd.getAdPositionId())) {
            thirdAd.setAdPositionId(adPositionId);
        }
    }

    public static Advertis parseToThirdAdSingle(String json) throws Exception {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject(json);
        long respondId = jsonObject.optLong("responseId");
        String data = jsonObject.optString("data");
        if (TextUtils.isEmpty(data) || "{}".equals(data)) {
            return null;
        }

        Advertis ads = new Gson().fromJson(data,
                new TypeToken<Advertis>() {
                }.getType());
        if (ads != null) {
            ads.setResponseId(respondId);
        }
        return ads;
    }

    public static void onWebViewDestory(Advertis advertis, String poistionName) {
        if (advertis == null) {
            return;
        }

        AdManager.postRecord(() -> {
            AdReportModel.Builder builder =
                    new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_LINK_CLOSE, poistionName)
                            .clickTime(advertis.getOnClickCurrTime())
                            .closeTime(System.currentTimeMillis());
            if (advertis.getSoundType() >= 0) {
                builder.adPlayVersion(getAdPlayVersion());
            }

            AdCollectData adCollectData = AdManager.thirdAdToAdCollect(MainApplication.getMyApplicationContext(), advertis,
                    builder.build());
            CommonRequestM.statOnlineAd(adCollectData);
        });
    }

    private static final String NAME_AD_SAVE_PATH = "name_ad_save_path";
    private static final String KEY_SAVE_PATH = "save_path";

    public static String getSavePathForOldSource(String downloadUrl) {
        // 实时获取地址的函数会耗时所以将其缓存下来
        OtherSharedPreferencesUtil otherSharedPreferencesUtil =
                new OtherSharedPreferencesUtil(MainApplication.getMyApplicationContext(),
                        NAME_AD_SAVE_PATH);
        String savePath = otherSharedPreferencesUtil.getString(KEY_SAVE_PATH);
        if (TextUtils.isEmpty(savePath)) {
            if (TextUtils.isEmpty(ImageManager.DOWNLOAD_CACHE_DIR)) {
                IStoragePathManager iStoragePathManager = RouterServiceManager.getInstance().getService(
                        IStoragePathManager.class);
                if (iStoragePathManager != null) {
                    // 此函数首次加载时会耗时
                    ImageManager.DOWNLOAD_CACHE_DIR = iStoragePathManager.getCurImagePath();
                }
            }
            savePath = ImageManager.DOWNLOAD_CACHE_DIR;
            otherSharedPreferencesUtil.saveString(KEY_SAVE_PATH, savePath);
        } else {
            if (TextUtils.isEmpty(ImageManager.DOWNLOAD_CACHE_DIR)) {
                MyAsyncTask.execute(new Runnable() {
                    @Override
                    public void run() {
                        if (TextUtils.isEmpty(ImageManager.DOWNLOAD_CACHE_DIR)) {
                            IStoragePathManager iStoragePathManager = RouterServiceManager.getInstance().getService(
                                    IStoragePathManager.class);
                            if (iStoragePathManager != null) {
                                ImageManager.DOWNLOAD_CACHE_DIR = iStoragePathManager.getCurImagePath();
                            }
                        }
                        otherSharedPreferencesUtil.saveString(KEY_SAVE_PATH, ImageManager.DOWNLOAD_CACHE_DIR);
                    }
                });
            } else {
                otherSharedPreferencesUtil.saveString(KEY_SAVE_PATH, ImageManager.DOWNLOAD_CACHE_DIR);
            }
        }

        return savePath + File.separator + MD5.md5(downloadUrl);
    }

    public static String getSavePath(@Nullable String downloadUrl) {
        return PreloadAdManager.getInstance().getLocalPath(downloadUrl);
    }

    public static int getSDKType(Advertis advertis) {
        if (advertis == null) {
            return -1;
        }

        int adtype = advertis.getAdtype();

        return getSDKType(adtype);
    }

    public static int getSDKType(int adtype) {
        if (isGdtAd(adtype)) {
            return IAdConstants.SDKType.GDT;
        } else if (isCSJAd(adtype)) {
            return IAdConstants.SDKType.CSJ;
        } else if (isJadAd(adtype)) {
            return IAdConstants.SDKType.JAD;
        } else {
            return IAdConstants.SDKType.XM;
        }
    }

    @StringDef({
            AppConstants.AD_LOG_TYPE_SITE_SHOW,
            AppConstants.AD_LOG_TYPE_SITE_CLICK,
            AppConstants.AD_LOG_TYPE_SOUND_SHOW,
            AppConstants.AD_LOG_TYPE_SOUND_CLICK,
            AppConstants.AD_LOG_TYPE_SOUND_TINGCLOSE,
            AppConstants.AD_LOG_TYPE_RTBENTRY_CLICK,
            AppConstants.AD_LOG_TYPE_RTBENTRY_SHOW,
            AppConstants.AD_LOG_TYPE_SOUND_INTERACTI_CLICK,
            AppConstants.AD_LOG_TYPE_ANSWER_SHOW,
    })
    public @interface logType {
    }

    @StringDef({
            AppConstants.AD_POSITION_NAME_LOADING,
            AD_POSITION_NAME_FIND_FOCUS,
            AppConstants.AD_POSITION_NAME_CATEGORY_FOCUS,
            AppConstants.AD_POSITION_NAME_SOUND_PATCH,
            AppConstants.AD_POSITION_NAME_CATEGORY_LIST,

            AppConstants.AD_POSITION_NAME_FIND_BANNER,
            AppConstants.AD_POSITION_NAME_FIND_NATIVE,
            AppConstants.AD_POSITION_NAME_CATA_BANNER,
            AppConstants.AD_POSITION_NAME_CATA_LIST,
            AppConstants.AD_POSITION_NAME_CATA_INDEX_BANNER,
            AppConstants.AD_POSITION_NAME_FEED_BANNER,
            AppConstants.AD_POSITION_NAME_FEED_COLLECT,
            AppConstants.AD_POSITION_NAME_PLAY_COMMENT_TOP,
            AppConstants.AD_POSITION_NAME_PLAY_NATIVE,
            AppConstants.AD_POSITION_NAME_FEED_FOLLOW,
            AppConstants.AD_POSITION_NAME_FEED_COLLECT_LIST,
            AppConstants.AD_POSITION_NAME_CITY_COLUMN,
            AppConstants.AD_POSITION_NAME_CITY_NATIVE,
            AppConstants.AD_POSITION_NAME_ALBUM_NOTICE,
            AppConstants.AD_POSITION_NAME_POPUP,
            AppConstants.AD_POSITION_NAME_QUIT,
            AppConstants.AD_POSITION_NAME_SEARCH,
            AppConstants.AD_POSITION_NAME_PAYPAGE_POP,
            AppConstants.AD_POSITION_NAME_HOME_TITLEBAR,
            AppConstants.AD_POSITION_NAME_HOME_MIDDLE,
            AppConstants.AD_POSITION_NAME_HOME_BOTTOM,
            AppConstants.AD_POSITION_NAME_PLAY_SKIN,
            AppConstants.AD_POSITION_NAME_PLAY_READ,
            AppConstants.AD_POSITION_NAME_CHAT_ROOM,
            AppConstants.AD_POSITION_NAME_FOCUS,
            AppConstants.AD_POSITION_NAME_PLAY_CENTER,
            AppConstants.AD_POSITION_NAME_BROADCASTER_BANNER,
            AppConstants.AD_POSITION_NAME_BROADCAST_NATIVE,
            AppConstants.AD_POSITION_NAME_SOUND_PATCH_BROADCAST,
            AppConstants.AD_POSITION_NAME_SHARE_FLOAT,
            AppConstants.AD_POSITION_NAME_PURCHASE_BOTTOM,
            AppConstants.AD_POSITION_NAME_PURCHASE_MIDDLE,
            AppConstants.AD_POSITION_NAME_XIAOYA_FLOAT,
            AppConstants.AD_POSITION_NAME_FIND_FLOAT,
            AppConstants.AD_POSITION_NAME_WALLET_BANNER,
            AppConstants.AD_POSITION_NAME_PLAY_YELLOW_BAR,
            AppConstants.AD_POSITION_NAME_BRAND_FEATURE,
            AppConstants.AD_POSITION_NAME_MY_COOPERATION,
            AppConstants.AD_POSITION_NAME_BROCASTER_COOPERATION,
            AppConstants.AD_POSITION_NAME_HOME_DROP_DOWN,
            AppConstants.AD_POSITION_NAME_LIVE_BANNER,
            AppConstants.AD_POSITION_NAME_GIANT_SCREEN,
            AppConstants.AD_POSITION_NAME_FIREWORK_POPUP,
            AppConstants.AD_POSITION_NAME_PLAY_ICON,
            AppConstants.AD_POSITION_NAME_SEARCH_EGGS,
            AppConstants.AD_POSITION_NAME_COLUMN_SPONSORSHIP,
            AppConstants.AD_POSITION_NAME_PLAY_COMMENT,
            AppConstants.AD_POSITION_NAME_MAIN_SEARCH_BANNER,
            AppConstants.AD_POSITION_NAME_WELFARE_LAYER,
            AppConstants.AD_POSITION_NAME_WELFARE_DIALOG,
            AppConstants.AD_POSITION_NAME_PLAY_IMMERSIVE_SKIN,
            AppConstants.AD_POSITION_NAME_LIVE_LITTLE_BANNER
    })
    public @interface positionName {
    }

    @IntDef({Advertis.SHOW_TYPE_STATIC, Advertis.SHOW_TYPE_GIF, Advertis.SHOW_TYPE_VIDEO})
    public @interface ShowType {
    }

    /**
     * 可以决定是否处理到WebFragment
     */
    public interface IHasOpenOtherApp {
        void clickOver(boolean openOtherApp);
    }

    public static boolean checkAdImageIsChange(Advertis advertis1, Advertis advertis2) {
        if (advertis1 == null && advertis2 == null) {
            return false;
        }

        if (advertis1 == null || advertis2 == null) {
            return true;
        }

        boolean empty = TextUtils.isEmpty(advertis1.getImageUrl());
        boolean empty1 = TextUtils.isEmpty(advertis2.getImageUrl());
        if (empty && empty1) {
            return false;
        }

        if (empty || empty1) {
            return true;
        }

        if (advertis1.getImageUrl().equals(advertis2.getImageUrl())) {
            return false;
        }

        return true;
    }

    public static boolean checkAdImageIsChanges(List<Advertis> advertises1, List<Advertis> advertises2) {

        if (advertises1 != null && advertises1.size() == 0) {
            advertises1 = null;
        }

        if (advertises2 != null && advertises2.size() == 0) {
            advertises2 = null;
        }

        if (advertises1 == null && advertises2 == null) {
            return false;
        }
        if (advertises1 == null || advertises2 == null) {
            return true;
        }
        if (advertises1.size() != advertises2.size()) {
            return true;
        }

        for (int i = 0; i < advertises1.size(); i++) {
            if (checkAdImageIsChange(advertises1.get(i), advertises2.get(i))) {
                return true;
            }
        }
        return false;
    }

    public static boolean checkAdIdIsChanges(AdvertisList advertisList1, AdvertisList advertisList2) {
        if (advertisList1 == null && advertisList2 == null) {
            return false;
        }
        if (advertisList1 == null || advertisList2 == null) {
            return true;
        }

        return checkAdIdIsChanges(advertisList1.getAdvertisList(), advertisList2.getAdvertisList());
    }


    public static boolean checkAdIdIsChanges(List<Advertis> advertises1, List<Advertis> advertises2) {

        if (advertises1 != null && advertises1.size() == 0) {
            advertises1 = null;
        }

        if (advertises2 != null && advertises2.size() == 0) {
            advertises2 = null;
        }

        if (advertises1 == null && advertises2 == null) {
            return false;
        }
        if (advertises1 == null || advertises2 == null) {
            return true;
        }
        if (advertises1.size() != advertises2.size()) {
            return true;
        }

        for (int i = 0; i < advertises1.size(); i++) {
            if (checkAdIdIsChange(advertises1.get(i), advertises2.get(i))) {
                return true;
            }
        }
        return false;
    }

    private static boolean checkAdIdIsChange(Advertis advertis1, Advertis advertis2) {
        if (advertis1 == null && advertis2 == null) {
            return false;
        }

        if (advertis1 == null || advertis2 == null) {
            return true;
        }

        if (advertis1.getAdid() == advertis2.getAdid()) {
            return false;
        }

        return true;
    }

    public static void handlerGDTAd(@Nullable final AbstractThirdAd nativeADDataRef, final Advertis advertis, Activity activity,
                                    final View clickView, @logType final String logType, @positionName final String positionName) {
        final AdReportModel reportModel = new AdReportModel.Builder(logType, positionName).build();
        handlerGDTAd(nativeADDataRef, advertis, activity, clickView, reportModel);
    }

    public static void handlerGDTAd(@Nullable final AbstractThirdAd nativeADDataRef, final Advertis advertis, Activity activity,
                                    final View clickView, AdReportModel adReportModel) {
        if (nativeADDataRef != null) {
            if (nativeADDataRef.isAppAd() && nativeADDataRef.getAPPStatus() == AbstractThirdAd.APP_STATUS_DOWNLOADING) {
                CustomToast.showToast(R.string.host_please_pause_by_notification);
            }
            AdManager.hanlderSoundAdClick(getContext(), advertis, adReportModel);
        } else {
            AdManager.hanlderSoundAdClick(getContext(), advertis, adReportModel);
        }
    }


//    public static void handlerGDTAd(@Nullable final NativeADDataRef nativeADDataRef, final Advertis advertis, @NonNull Activity activity,
//                                    final View clickView, @logType final String logType, @positionName final String positionName) {
//        final AdReportModel reportModel = new AdReportModel.Builder(logType, positionName).build();
//        if (nativeADDataRef != null) {
//            if (nativeADDataRef.isAPP()) {
//                // 获取应用状态，0：未开始下载；1：已安装；2：需要更新;4:下载中;8:下载完成;16:下载失败
//                if (nativeADDataRef.getAPPStatus() == 0 || nativeADDataRef.getAPPStatus() == 2 || nativeADDataRef.getAPPStatus() == 16) {
//                    if (activity != null) {
//
//                        try {
//                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction()
//                                    .interceptAdAppDownload(activity, nativeADDataRef.getTitle(), null, new IHandleOk
//                                            () {
//                                        @Override
//                                        public void onReady() {
//                                            if (nativeADDataRef != null) {
//                                                nativeADDataRef.onClicked(clickView);
//                                            }
//                                            AdManager.hanlderSoundAdClick(getContext(), advertis, reportModel);
//                                        }
//                                    });
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                    }
//                } else if (nativeADDataRef.getAPPStatus() == 4) {
//                    CustomToast.showToast(R.string.host_please_pause_by_notification);
//                } else {
//                    nativeADDataRef.onClicked(clickView);
//                    AdManager.hanlderSoundAdClick(getContext(), advertis, reportModel);
//                }
//            } else {
//                nativeADDataRef.onClicked(clickView);
//                AdManager.hanlderSoundAdClick(getContext(), advertis, reportModel);
//            }
//        } else {
//            AdManager.hanlderSoundAdClick(getContext(), advertis, reportModel);
//        }
//    }

    public static void adRecordAnchorAd(List<? extends Album> albums, int indexOffset) {
        if (albums != null) {
            int index = indexOffset;
            for (Object album : albums) {
                if (album instanceof AlbumM) {
                    AlbumM albumM = (AlbumM) album;
                    if (albumM.getAdInfo() != null) {
                        AnchorAlbumAd adInfo = albumM.getAdInfo();
                        AdManager.adRecord(getContext(), adInfo, adInfo.createAdReportModel(AppConstants
                                .AD_LOG_TYPE_SITE_SHOW, index).build());
                    }
                }

                index++;
            }
        }
    }

    public static void adRecordAnchorAdOnResume(@Nullable AbstractAdapter adapter, @Nullable RefreshLoadMoreListView listView) {
        if (adapter != null && listView != null && listView.getRefreshableView() != null) {
            int firstShowIndex, showCount;
            firstShowIndex = listView.getRefreshableView().getFirstVisiblePosition() - listView.getRefreshableView().getHeaderViewsCount();
            if (firstShowIndex < 0) {
                firstShowIndex = 0;
            }
            showCount = listView.getRefreshableView().getChildCount();

            int count = adapter.getCount();
            if (count > showCount + firstShowIndex) {
                List listData = adapter.getListData();
                if (!ToolUtil.isEmptyCollects(listData)) {
                    for (int i = firstShowIndex; i < firstShowIndex + showCount; i++) {
                        Object o = listData.get(i);
                        if (o instanceof AlbumM) {
                            if (((AlbumM) o).getAdInfo() != null) {
                                AnchorAlbumAd adInfo = ((AlbumM) o).getAdInfo();
                                AdManager.adRecord(getContext(), adInfo,
                                        adInfo.createAdReportModel(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                                ((AlbumM) o).getIndexOfList()).build());
                            } else if (((AlbumM) o).getAd() != null && ((AlbumM) o).getAd().getColumnSequence() > 0) {
                                AdManager.adRecord(getContext(), ((AlbumM) o).getAd(),
                                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                                AppConstants.AD_POSITION_NAME_COLUMN_SPONSORSHIP).
                                                sponsorStyle(AppConstants.AD_SPONSOR_STYLE_BANNER).build());
                            }
                        }
                    }
                }
            }
        }
    }

    public static void adRecordAnchorAdOnResume(@Nullable HolderAdapter adapter, @Nullable RefreshLoadMoreListView listView) {
        if (adapter != null && listView != null && listView.getRefreshableView() != null) {
            int firstShowIndex, showCount;
            firstShowIndex = listView.getRefreshableView().getFirstVisiblePosition() - listView.getRefreshableView().getHeaderViewsCount();
            if (firstShowIndex < 0) {
                firstShowIndex = 0;
            }
            showCount = listView.getRefreshableView().getChildCount();

            int count = adapter.getCount();
            if (count > showCount + firstShowIndex) {
                List listData = adapter.getListData();
                if (!ToolUtil.isEmptyCollects(listData)) {
                    for (int i = firstShowIndex; i < firstShowIndex + showCount; i++) {
                        Object o = listData.get(i);
                        if (o instanceof AlbumM && ((AlbumM) o).getAdInfo() != null) {
                            AnchorAlbumAd adInfo = ((AlbumM) o).getAdInfo();
                            AdManager.adRecord(getContext(), adInfo,
                                    adInfo.createAdReportModel(AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                            ((AlbumM) o).getIndexOfList()).build());
                        }
                    }
                }
            }
        }
    }

    public static boolean checkAnchorAdCanClick(AnchorAlbumAd anchorAlbumAd) {
        if (anchorAlbumAd == null) {
            return false;
        }

        if (TextUtils.isEmpty(anchorAlbumAd.getRealLink()) && TextUtils.isEmpty(anchorAlbumAd.getLinkUrl())) {
            return false;
        }
        if (!anchorAlbumAd.isNeedReportCollect()) {
            return false;
        }

        return true;
    }

    /**
     * 根据广告位去掉不应该上报的字段
     *
     * @param adReportModel
     */
    public static void trimAdReportModel(AdReportModel adReportModel) {
        if (adReportModel == null) {
            return;
        }
        // 首页猜你喜欢
        if (IAdConstants.IAlbumAdInfoPoisitionName.HOME_GUESS_YOU_LIKE.equals(adReportModel.getPositionName())
                // 首页为你推荐
                || IAdConstants.IAlbumAdInfoPoisitionName.HOME_RECOMMEND_FOR_YOU.equals(adReportModel.getPositionName())
                // 猜你喜欢列表页
                || IAdConstants.IAlbumAdInfoPoisitionName.GUESS_YOU_LIKE.equals(adReportModel.getPositionName())
                // 专辑详情页/声音详情页/搜索结果页相关推荐列表
                || IAdConstants.IAlbumAdInfoPoisitionName.ALBUM_DETAIL_RELATIVE_RECOMMEND.equals(adReportModel.getPositionName())
                || IAdConstants.IAlbumAdInfoPoisitionName.TRACK_DETAIL_RELATIVE_RECOMMEND.equals(adReportModel.getPositionName())
                || IAdConstants.IAlbumAdInfoPoisitionName.SEARCH_RESULT_RELATIVE_RECOMMEND.equals(adReportModel.getPositionName())) {
            adReportModel.setCategoryId(0);
            adReportModel.setSubcategoryId(0);
            adReportModel.setKeywordId(null);
        } else if (IAdConstants.IAlbumAdInfoPoisitionName.HOME_CATEGORY_CARD.equals(adReportModel.getPositionName())) { // 首页分类卡片
            adReportModel.setKeywordId(null);
        } else if (IAdConstants.IAlbumAdInfoPoisitionName.CATEGORY_RECOMMEND.equals(adReportModel.getPositionName())
                || IAdConstants.IAlbumAdInfoPoisitionName.PAYABLE_RECOMMEND.equals(adReportModel.getPositionName())
                || IAdConstants.IAlbumAdInfoPoisitionName.CATEGORY_KEYWORD.equals(adReportModel.getPositionName())
                || IAdConstants.IAlbumAdInfoPoisitionName.PAYABLE_KEYWORD.equals(adReportModel.getPositionName())) {
            adReportModel.setSubcategoryId(0);
        }
    }

    private static long lastCheckTime = System.currentTimeMillis();

    public static boolean canClick(Advertis advertis) {
        if (advertis == null ||
                (advertis.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_CANNOT_CLICK
                        && advertis.getShowstyle() != Advertis.IMG_SHOW_TYPE_WEB_AD)) {
            return false;
        }
        return true;
    }


    private static Rect mRect = new Rect();

    public static boolean checkViewIsVisOverHalfOnListView(View view, ListView listView) {
        if (view == null || listView == null || view.getHeight() == 0) {
            return false;
        }

        int i = listView.indexOfChild(view);
        // 向上取两次
        if (i == -1) {
            if (view.getParent() instanceof View) {
                i = listView.indexOfChild((View) view.getParent());
                if (i == -1) {
                    if (view.getParent().getParent() instanceof View) {
                        i = listView.indexOfChild((View) view.getParent().getParent());
                    }
                }
            }
        }

        if (i > 0 && i < listView.getLastVisiblePosition() - listView.getFirstVisiblePosition()) {
            return true;
        }

        if (i == (listView.getLastVisiblePosition() - listView.getFirstVisiblePosition()) || i == 0) {
            if (isShowHalfOnLocalRect(view)) {
                return true;
            }
        }

        return false;
    }

    public static boolean checkViewIsVisOverHalfOnListView(View view, RecyclerView recyclerView, StaggeredGridLayoutManager layoutManager) {
        if (view == null || recyclerView == null || view.getHeight() == 0) {
            return false;
        }

        int i = recyclerView.indexOfChild(view);
        // 向上取两次
        if (i == -1) {
            if (view.getParent() instanceof View) {
                i = recyclerView.indexOfChild((View) view.getParent());
                if (i == -1) {
                    if (view.getParent().getParent() instanceof View) {
                        i = recyclerView.indexOfChild((View) view.getParent().getParent());
                    }
                }
            }
        }

        int[] rangeStaggeredGrid = findRangeStaggeredGrid(layoutManager);

        if (rangeStaggeredGrid == null || rangeStaggeredGrid.length != 2) {
            return false;
        }

        if(i >= rangeStaggeredGrid[0] && i <= rangeStaggeredGrid[1]) {
            if (isShowHalfOnLocalRect(view)) {
                return true;
            }
        }
        return false;
    }

    private static int[] findRangeStaggeredGrid(StaggeredGridLayoutManager manager) {
        int[] startPos = new int[manager.getSpanCount()];
        int[] endPos = new int[manager.getSpanCount()];
        manager.findFirstVisibleItemPositions(startPos);
        manager.findLastVisibleItemPositions(endPos);
        return findRange(startPos, endPos);
    }

    private static int[] findRange(int[] startPos, int[] endPos) {
        int start = startPos[0];
        int end = endPos[0];
        for (int i = 1; i < startPos.length; i++) {
            if (start > startPos[i]) {
                start = startPos[i];
            }
        }
        for (int i = 1; i < endPos.length; i++) {
            if (end < endPos[i]) {
                end = endPos[i];
            }
        }
        return new int[]{start, end};
    }

    public static boolean checkViewIsVisOverHalfOnListView(View view, RecyclerView listView, LinearLayoutManager linearLayoutManager) {
        if (view == null || linearLayoutManager == null || view.getHeight() == 0) {
            return false;
        }

        int i = listView.indexOfChild(view);
        // 向上取两次
        if (i == -1) {
            if (view.getParent() instanceof View) {
                i = listView.indexOfChild((View) view.getParent());
                if (i == -1) {
                    if (view.getParent().getParent() instanceof View) {
                        i = listView.indexOfChild((View) view.getParent().getParent());
                    }
                }
            }
        }

        if (i > 0 && i < linearLayoutManager.findLastVisibleItemPosition() - linearLayoutManager.findFirstVisibleItemPosition()) {
            return true;
        }

        if (i == (linearLayoutManager.findLastVisibleItemPosition() - linearLayoutManager.findFirstVisibleItemPosition()) || i == 0) {
            if (isShowHalfOnLocalRect(view)) {
                return true;
            }
        }

        return false;
    }

    // 这种是检测 getLocalVisibleRect
    public static boolean isShowHalfOnLocalRect(View view) {
        if (view == null) {
            return false;
        }
        if (view.getGlobalVisibleRect(mRect) && view.getHeight() > 0) {
            if ((mRect.bottom - mRect.top) * 2 > view.getHeight()) {
                return true;
            }
        }
        return false;
    }

    public static boolean checkViewShowPercentOnListView(View view, ListView listView, float percent) {
        if (view == null || listView == null || view.getHeight() == 0) {
            return false;
        }

        int i = listView.indexOfChild(view);
        // 向上取两次
        if (i == -1) {
            if (view.getParent() instanceof View) {
                i = listView.indexOfChild((View) view.getParent());
                if (i == -1) {
                    if (view.getParent().getParent() instanceof View) {
                        i = listView.indexOfChild((View) view.getParent().getParent());
                    }
                }
            }
        }

        if (i > 0 && i < listView.getLastVisiblePosition() - listView.getFirstVisiblePosition()) {
            return true;
        }

        if (i == (listView.getLastVisiblePosition() - listView.getFirstVisiblePosition()) || i == 0) {
            if (isShowOverPercentOnLocalRect(view, percent)) {
                return true;
            }
        }

        return false;
    }

    // 这种是检测 getLocalVisibleRect
    public static boolean isShowOverPercentOnLocalRect(View view,float percent) {
        if (view == null) {
            return false;
        }
        if (view.getGlobalVisibleRect(mRect) && view.getHeight() > 0) {
            if ((mRect.bottom - mRect.top) > view.getHeight() * percent) {
                return true;
            }
        }
        return false;
    }

    private static float showPercent = -1;
    public static float getShowOverPercent() {
        if(showPercent == -1) {
            if (!ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                    CConstants.Group_ad.ITEM_FEED_AD_SHOW_RECORD_REPEAT, true)) {
                return showPercent = 0.1f;
            }
            float aFloat = ConfigureCenter.getInstance().getFloat(CConstants.Group_ad.GROUP_NAME,
                    CConstants.Group_ad.ITEM_AD_SHOW_OVER_PERCENT, 0.5f);
            if(aFloat <= 0 || aFloat > 1) {
                aFloat = 0.5f;
            }
            return showPercent = aFloat;
        }

        return showPercent;
    }

    public static boolean addUrlTs = true;

    public static String addTsToUrl(String url) {
        if (addUrlTs) {
            if (!TextUtils.isEmpty(url)) {
                if (url.endsWith("/")) {
                    url = url + "ts-" + System.currentTimeMillis();
                } else {
                    url = url + "/ts-" + System.currentTimeMillis();
                }
            }
        }
        return url;
    }

    public static String addTsToUrl(String url, long timeStamp) {
        if (!TextUtils.isEmpty(url)) {
            if (url.endsWith("/")) {
                url = url + "ts-" + timeStamp;
            } else {
                url = url + "/ts-" + timeStamp;
            }
        }
        return url;
    }

    public static final String KEY = "&key=KIxLswbHwd2uzczYUcZm4w*+";
    public static final String RESPONE_HEADER = "x-ad-dg";

    public static Set<String> dontRequestAdList = new HashSet<>();

    public static boolean isAdRequest(String host) {
        if (host != null
                && (host.equals("adse.ximalaya.com")
                || host.equals("adse.test.ximalaya.com")
                || host.equals("adse.uat.ximalaya.com"))) {
            return true;
        }
        return false;
    }

    public static String checkAdIsRight(String content, String responeHeader) {
        if (XmAdsManager.checkAdContent == null || !XmAdsManager.checkAdContent) {
            return content;
        }

        if (ConstantsOpenSdk.isDebug) {
            Logger.log("AdManager : checkAdContent true");
        }

        // 如果header中的字段不存在那么也认为没有广告
        if (TextUtils.isEmpty(responeHeader)) {
            return "";
        }

        if (responeHeader.equals(MD5.md5(content + KEY))) {
            return content;
        }
        return "";
    }

    public static <T> boolean checkNeedRequestAd(Map<String, String> params, IDataCallBack<T> callBackM) {
        if (!ConstantsOpenSdk.isDebug && !ToolUtil.isEmptyMap(params)) {
            String name = params.get(PARAM_AD_NAME);
            if (dontRequestAdList.contains(name)) {
                if (callBackM != null) {
                    callBackM.onSuccess(null);
                }
                return false;
            }
        }
        return true;
    }

    public static AlbumEventManage.IOnCollectByUser onCollectByUser = new AlbumEventManage.IOnCollectByUser() {
        @Override
        public void onCollect(Album album) {
            if (album instanceof AlbumM) {
                AnchorAlbumAd adInfo = ((AlbumM) album).getAdInfo();
                if (AdManager.checkAnchorAdCanClick(adInfo)) {
                    AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), adInfo, adInfo
                            .createAdReportModel(AppConstants.AD_LOG_TYPE_SITE_CLICK, 0).ignoreTarget(true).build());
                }
            }
        }
    };


    public static boolean canRecord(AnchorAlbumAd adInfo) {
        return adInfo != null;
    }

    public static boolean isDropDownSecondType(int showStyle) {
        return showStyle == Advertis.IMG_SHOW_TYPE_DROP_DOWN_SECOND_TYPE
                || showStyle == Advertis.IMG_SHOW_TYPE_DROP_DOWN_H5
                || showStyle == Advertis.IMG_SHOW_TYPE_DROP_DOWN_STATIC_IMG;

    }

    public static String getLoadingAdFilePath(Context context) {
        if (context == null) {
            context = MainApplication.getMyApplicationContext();
        }

        if (context == null) {
            return "";
        }

        return new File(context.getFilesDir(),
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_LOADING_REQUEST_FILE).getAbsolutePath();
    }

    public static String getNextLoadSDKAdFilePath(Context context, String positionName) {
        if (context == null) {
            context = MainApplication.getMyApplicationContext();
        }

        if (context == null) {
            return "";
        }

        return new File(context.getFilesDir(),
                PreferenceConstantsInOpenSdk.TINGMAIN_KEY_LOADING_NEXT_SDK_REQUEST_FILE +
                        File.separator + positionName).getAbsolutePath();
    }

    @Nullable
    public static String getDspPositionId(Advertis advertis, String positionName) {
        if (advertis == null) {
            return null;
        }

        if (!StringUtil.isEmpty(advertis.getDspPositionId())) {
            return advertis.getDspPositionId();
        }

        String adId = null;

        if (AdManager.isForwardVideo(advertis)) {
            if (advertis.getAdtype() == AD_SOURCE_CSJ) {
                return adId = AdManager.CSJ_FORWARD_VIDEO;
            } else if (advertis.getAdtype() == AD_SOURCE_GDT) {
                return AdManager.GDT_FORWARD_VIDEO;
            }
        }

        if (TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_LOADING)) {
            if (advertis.getAdtype() == AD_SOURCE_CSJ) {
                adId = AdManager.CSJ_WELCOME_ADID;
            } else if (advertis.getAdtype() == AD_SOURCE_GDT) {
                adId = AdManager.GDT_WELCOME_NATIVE_ADID;
            } else if (advertis.getAdtype() == AD_SOURCE_GDT_WELCOME_SCREEN) {
                adId = AdManager.GDT_WELCOME_ADID;
            }
        } else if (TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_SOUND_PATCH)
                || TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_SOUND_PATCH_BROADCAST)) {
            if (advertis.getAdtype() == AD_SOURCE_GDT) {
                if (advertis.getSoundType() == Advertis.TYPE_NORMAL
                        || advertis.getSoundType() == Advertis.TYPE_NORMAL_AND_DANMU
                        || advertis.getSoundType() == Advertis.TYPE_HIGHT_LIGHT_AD
                        || advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_AD
                        || advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_FLLOW
                        || advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_FLLOW_HAVE_SOUND
                        || AdManager.isForwardVideo(advertis)) {
                    adId = AdManager.GDT_PLAY_HORIZONTAL_LARGE_ADID;
                } else if (advertis.getSoundType() == Advertis.TYPE_VIDEO
                        || advertis.getSoundType() == Advertis.TYPE_VIDEO_AND_DANMU) {
                    adId = AdManager.GDT_VIDEO_PLAY_ADID;
                } else if (advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL || advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL_FLOWER) {
                    adId = AdManager.GDT_VERTICAL_VIDEO;
                } else if (advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE || advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE_FLOWER) {
                    adId = AdManager.GDT_VERTICAL_STATIC_IMG;
                }
            } else if (advertis.getAdtype() == AD_SOURCE_CSJ) {
                if (advertis.getSoundType() == Advertis.TYPE_NORMAL
                        || advertis.getSoundType() == Advertis.TYPE_NORMAL_AND_DANMU) {
                    adId = AdManager.CSJ_POSTER_ADID;
                } else if (advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_AD
                        || AdManager.isForwardVideo(advertis)
                        || advertis.getSoundType() == Advertis.TYPE_HIGHT_LIGHT_AD) {
                    adId = AdManager.CSJ_PLAY_HORIZONTAL_LARGE_ADID;
                } else if (advertis.getSoundType() == Advertis.TYPE_VIDEO
                        || advertis.getSoundType() == Advertis.TYPE_VIDEO_AND_DANMU) {
                    adId = AdManager.CSJ_VIDEO_PLAY_ADID;
                } else if (advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL || advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL_FLOWER) {
                    adId = AdManager.CSJ_VERTICAL_VIDEO;
                } else if (advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE || advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE_FLOWER) {
                    adId = AdManager.CSJ_VERTICAL_STATIC_IMG;
                }
            }
        } else if (TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_PLAY_CENTER)) {
            adId = advertis.getAdtype() == AD_SOURCE_GDT ?
                    AdManager.GDT_PLAY_LARGE_ADID :
                    AdManager.CSJ_PLAY_LARGE_ADID;
        } else if (TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_FIND_NATIVE)) {
            adId = advertis.getAdtype() == AD_SOURCE_GDT ?
                    AdManager.GDT_FIND_NATIVE_ADID :
                    AdManager.CSJ_FIND_NATIVE_ADID;
        } else if (TextUtils.equals(positionName, AppConstants.AD_POSITION_NAME_ALBUM_NOTICE)) {
            adId = advertis.getAdtype() == AD_SOURCE_GDT
                    ? AdManager.GDT_ALBUM_NOTICE_ADID
                    : AdManager.CSJ_ALBUM_NOTICE_ADID;
        }

        if (TextUtils.isEmpty(adId) && ConstantsOpenSdk.isDebug) {
//            throw new RuntimeException("adId 不应该为null的");
            Logger.e(new Exception("AdManager :  " + "adId 不应该为null的" + advertis));
        }

        return adId;
    }

    public static String getProgressText(IAbstractAd mAdItem) {

        if (mAdItem != null &&  mAdItem.getAdvertis() != null
                && AdCommonUtils.checkAppInstalled(mAdItem.getAdvertis().getAppPackageName())) {
            return "立即打开";
        }

        String progressTitle = "查看详情";

        if (mAdItem == null || !mAdItem.isAppAd()) {
            return progressTitle;
        }
        switch (mAdItem.getAPPStatus()) {
            case AbstractThirdAd.APP_STATUS_NO_DOWNLOADED:
            case AbstractThirdAd.APP_STATUS_DOWNLOAD_PAUSED:
            case AbstractThirdAd.APP_STATUS_DOWNLOAD_REMOVED:
                progressTitle = "立即下载";
                break;
            case AbstractThirdAd.APP_STATUS_INSTALLED:
                progressTitle = "立即启动";
                break;
            case AbstractThirdAd.APP_STATUS_UPDATE:
                progressTitle = "点击更新";
                break;
            case AbstractThirdAd.APP_STATUS_DOWNLOADING:
                progressTitle = mAdItem.getProgress() > 0 ? "已下载" + mAdItem.getProgress() + "%" : "下载中"; // 特别注意：当进度小于0时，不要使用进度来渲染界面
                break;
            case AbstractThirdAd.APP_STATUS_DOWNLOADED:
                progressTitle = "立即安装";
                break;
            case AbstractThirdAd.APP_STATUS_DOWNLOAD_FAIL:
                progressTitle = "下载失败,点击重试";
                break;
            default:
                progressTitle = "查看详情";
        }
        return progressTitle;
    }

    public static int getAppDownloadStatus(Advertis advertis) {
        if (advertis == null || !isDownloadAd(advertis)){
            return -1;
        }
        if (AdCommonUtils.checkAppInstalled(advertis.getAppPackageName())) {
            return XmDownloadInfo.Status.INSTALLED;
        } else {
            AdSDKAdapterModel adSDKAdapterModel = AdConversionUtil.conversionModel(advertis, advertis.getPositionName());
            ITaskImpl downloadTaskManager = InnerHelper.getInstance().getDownloadTaskManager();
            if (adSDKAdapterModel != null && downloadTaskManager != null) {
                XmDownloadInfo downloadInfoByAdModel = downloadTaskManager.createDownloadInfoByAdModel(adSDKAdapterModel);
                if (downloadInfoByAdModel != null) {
                    XmDownloadInfo downloadInfoByOnlyKey = downloadTaskManager.getDownloadInfoByOnlyKey(downloadInfoByAdModel.onlyKey());
                    if (downloadInfoByOnlyKey != null) {
                        return downloadInfoByOnlyKey.status;
                    } else {
                        return XmDownloadInfo.Status.WAITING;
                    }
                }
            }
        }
        return -1;
    }


    private static boolean hasPreloaded = false;

    public static void resetPreloadState() {
        hasPreloaded = false;
    }

    // 预加载物料
    public static void preloadMaterials() {
        if (!hasPreloaded) {
            hasPreloaded = true;

            PreloadAdManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    String postStr = PreloadAdManager.getInstance().getRequestPostStr();
                    AdRequest.preloadMaterial(postStr, new IDataCallBack<AdPreloadMaterialResult>() {
                        @Override
                        public void onSuccess(@Nullable AdPreloadMaterialResult materialResult) {
                            if (materialResult != null && materialResult.getRet() == 0) {
                                if(!ToolUtil.isEmptyCollects(materialResult.getData())) {
                                    PreloadAdManager.getInstance().preloadAd(materialResult.getData());
                                }

                                if(!ToolUtil.isEmptyCollects(materialResult.getLandingPageResDatas())) {
                                    PreloadAdManager.getInstance().preloadLandingPage(materialResult.getLandingPageResDatas());
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                }
            });
        }
    }

    /**
     * 是否是第三sdk广告
     *
     * @param advertis
     * @return
     */
    public static boolean isThirdAd(Advertis advertis) {
        return isCSJAd(advertis) || isGdtAd(advertis) || isJadAd(advertis) || isBaiduAd(advertis);
    }

    public static boolean isThirdAd(IAbstractAd abstractAd) {
        if (abstractAd != null) {
            return isThirdAd(abstractAd.getAdvertis());
        }

        return false;
    }

    public static boolean isThirdAd(int adType) {
        return isCSJAd(adType) || isGdtAd(adType) || isJadAd(adType) || isBaiduAd(adType);
    }

    // 是否支持下载合规信息悬浮展示
    public static boolean isAdSupportDowloadInfoFloat(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (isThirdAd(advertis)) {
            return  false;
        }
        return isDownloadAd(advertis) && advertis.getBusinessExtraInfo() != null && advertis.getBusinessExtraInfo().isEnableShowAppInfo();
    }

    private static int dspMarkHeight = 0;

    private static int dspMarkWidth = 0;

    public static int dspMarkHeight() {
        if (dspMarkHeight > 0) {
            return dspMarkHeight;
        }

        return dspMarkHeight = com.ximalaya.ting.android.framework.util.BaseUtil.dp2px(MainApplication.getMyApplicationContext(), 14);
    }

    public static int dspMarkWidth() {
        if (dspMarkWidth > 0) {
            return dspMarkWidth;
        }
        return dspMarkWidth = com.ximalaya.ting.android.framework.util.BaseUtil.dp2px(MainApplication.getMyApplicationContext(), 40);
    }

    /**
     * 删除广点通sdk 自己添加的adMask
     *
     * @param nativeAdContainer
     */
    public static void removeGdtAdMask(NativeAdContainer nativeAdContainer) {
        try {
            if (nativeAdContainer != null) {
                int childCount = nativeAdContainer.getChildCount();
                if (childCount != 0) {
                    for (int i = childCount - 1; i >= 0; i--) {
                        View childAt = nativeAdContainer.getChildAt(i);
                        if (childAt instanceof ImageView &&
                                childAt.getLayoutParams() instanceof CustomFrameLayoutLayoutParams) {
                            nativeAdContainer.removeView(childAt);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static class CustomFrameLayoutLayoutParams extends FrameLayout.LayoutParams {
        public CustomFrameLayoutLayoutParams(int width, int height) {
            super(width, height);
        }
    }

    public static CustomFrameLayoutLayoutParams createCustomLayoutParamsForGdt() {
        return new CustomFrameLayoutLayoutParams(dspMarkWidth(), dspMarkHeight());
    }

    public static int dspRequestTimeMs() {
        int timeOut = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_GDTREQUESTTIMEOUTMS, 1900);
        Logger.log("ThirdAdSDKManager : gdtRequestTimeoutMs " + timeOut);
        return timeOut;
    }

    /**
     * dplink 跳转
     *
     * @param dpLink
     * @return true 表示dp跳转成功 ,这里有个特殊情况小米因为会拦截跳转
     */
    public static boolean handleDpLink(String dpLink, Advertis advertis, AdReportModel adReportModel, String positionName,
                                        boolean isSoundAd, String realUrl, String realLink,
                                        int clickType, int linkType) {
        Context context = MainApplication.getMyApplicationContext();
        DpCallRecordManager.getInstance().recordDpCallStart(advertis);
        String replaceDpUrl = ThirdAdStatUtil.getInstance(context).
                replaceRequestStrBeforeUpdate(advertis, adReportModel, dpLink, false, false);
        final Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setData(Uri.parse(replaceDpUrl));

        try {
            ComponentName componentName = intent.resolveActivity(context.getPackageManager());
            if (componentName != null) {
                String requestPackageName = null;
                if (dpLink.startsWith("market://details")) {
                    requestPackageName = getDefaultMarket(context, intent);

                    if (!TextUtils.isEmpty(requestPackageName)) {
                        intent.setPackage(requestPackageName);
                    }
                }

                Context finalContext = context;
                // miui系统因为会拦截跳转导致可能点击了广告而不能跳转,所以会继续走下面的逻辑
                if (!TextUtils.isEmpty(ToolUtil.getXiaomiVersion())) {
                    String finalRequestPackageName = requestPackageName;
                    Runnable runnable = new Runnable() {
                        @Override
                        public void run() {
                            Intent fixIntent = new Intent(context, FixXiaomiInterceptOpenAppActivity.class);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_ADVERTIS_MODEL, advertis);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_POSITION_NAME, positionName);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_IS_SOUND_AD, isSoundAd);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_REAL_URL, realUrl);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_REAL_LINK, realLink);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_CLICK_TYPE, clickType);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_LINK_TYPE, linkType);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INTERCEPT_DP_LINK, replaceDpUrl);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_REQUEST_PACKAGE_NAME, finalRequestPackageName);
                            fixIntent.putExtra(FixXiaomiInterceptOpenAppActivity.FIX_INSTALL_PACKAGE_NAME, componentName.getPackageName());
                            ToolUtil.checkIntentAndStartActivity(context, fixIntent);
                        }
                    };

                    if (MainApplication.getMainActivity() != null) {
                        runnable.run();
                    } else {
                        HandlerManager.postOnUIThreadDelay(runnable, 100);
                    }
                } else {
                    // 因为在启动页如果点击后会先跳转到其他app,然后跳转到首页,导致其他app不能先显示
                    Runnable runnable = new Runnable() {
                        @Override
                        public void run() {
                            try {
                                boolean isDpSuccess = ToolUtil.checkAdDpIntentAndStartActivity(finalContext, intent);
                                if (isDpSuccess) {
                                    // 真正的成功才上报成功
                                    DpCallRecordManager.getInstance().recordDpCallSuccess(advertis, componentName.getPackageName());
                                } else {
                                    // 上报未知异常
                                    DpCallRecordManager.getInstance().recordDpCallError(advertis, "unknown error");
                                }
                            } catch (Exception e) {
                                // 上报抛出的异常
                                DpCallRecordManager.getInstance().recordDpCallError(advertis, e.toString());
                                Logger.e("--------msg", " ---- e : " + e.toString());
                                e.printStackTrace();
                            }
                        }
                    };
                    if (MainApplication.getMainActivity() != null) {
                        runnable.run();
                    } else {
                        HandlerManager.postOnUIThreadDelay(runnable, 100);
                    }
                }
                return true;
            } else {
                DpCallRecordManager.getInstance().recordDpCallNotInstall(advertis);
            }
        } catch (Exception e) {
            e.printStackTrace();
            DpCallRecordManager.getInstance().recordDpCallError(advertis, e.toString());
        }

        return false;
    }

    @Nullable
    public static String getDefaultMarket(Context context, Intent intent) {
        PackageManager pm = context.getPackageManager();
        List<ResolveInfo> infos = pm.queryIntentActivities(intent, 0);

        JSONObject json = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_DEFAULT_MARKET_PACKAGE_NAME);

        Set<String> defaultMarkPackageNameSet = null;
        if (json == null) {
            defaultMarkPackageNameSet = defaultPackageNames();
        } else {
            JSONArray packageNames = json.optJSONArray("packageNames");
            if (packageNames != null) {
                defaultMarkPackageNameSet = new HashSet<>();
                for (int i = 0; i < packageNames.length(); i++) {
                    defaultMarkPackageNameSet.add(packageNames.optString(i));
                }
            }

            if (ToolUtil.isEmptyCollects(defaultMarkPackageNameSet)) {
                defaultMarkPackageNameSet = defaultPackageNames();
            }
        }

        if (infos != null) {
            for (ResolveInfo info : infos) {
                ActivityInfo activityInfo = info.activityInfo;
                if (activityInfo.packageName != null) {
                    if (defaultMarkPackageNameSet.contains(activityInfo.packageName)) {
                        return activityInfo.packageName;
                    }
                }
            }
        }

        return null;
    }

    private static Set<String> defaultPackageNames() {
        return new HashSet<String>() {
            {
                add("com.xiaomi.market");
                add("com.hihonor.appmarket");
                add("com.huawei.appmarket");
                add("com.meizu.mstore");
                add("com.oppo.market");
                add("com.bbk.appstore");
                add("com.sec.android.app.samsungapps");
                add("com.lenovo.leos.appstore");
                add("zte.com.market");
                add("com.gionee.aora.market");
            }
        };
    }

    public static void setAdvertisePositionId(List<? extends Advertis> advertisList,
                                              String adPositionId) {
        if (ToolUtil.isEmptyCollects(advertisList)) {
            return;
        }

        for (Advertis advertis : advertisList) {
            if (advertis != null && TextUtils.isEmpty(advertis.getAdPositionId())) {
                advertis.setAdPositionId(adPositionId);
            }
        }
    }

    public static String updatePositionIdParams(Map<String, String> params) {
        return updatePositionIdParams(params, "name");
    }

    public static String updatePositionIdParams(Map<String, String> params, String paramNameKey) {
        String adPositionId = null;
        if (!ToolUtil.isEmptyMap(params)) {
            String name = params.get(paramNameKey);

            // 因为此广告位返回了
            if (TextUtils.equals(AppConstants.AD_POSITION_NAME_TITLEBAR_MIDDLE_BOTTOM, name)) {
                return null;
            }
            adPositionId = AdPositionIdManager.getPositionIdByPositionName(name);

            params.put(AdRequest.POSITIONID_PARAMS_NAME, adPositionId);
        }

        return adPositionId;
    }

    public static String getFocusPositionId(long categoryId) {
        if (categoryId < 0) {
            return (66000000 + -1 * categoryId) * -1 + "";
        }

        return 66000000 + categoryId + "";
    }

    /**
     * 属于广点通的广告
     */
    public static boolean isGdtAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        return isGdtAd(advertis.getAdtype());
    }

    public static boolean isGdtAd(int adType) {
        return adType == AD_SOURCE_GDT_WELCOME_SCREEN
                || adType == AD_SOURCE_IMMERSIVE_GDT
                || adType == AD_SOURCE_GDT;
    }


    /**
     * 属于穿山甲的广告
     */
    public static boolean isCSJAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        return isCSJAd(advertis.getAdtype());
    }

    public static boolean isCSJAd(int adType) {
        return adType == AD_SOURCE_CSJ || adType == AD_SOURCE_CSJ_TEMPLATE;
    }

    /**
     * 属于京东jad 广告
     * @return
     */
    public static boolean isJadAd(IAbstractAd abstractAd) {
        if (abstractAd != null) {
            return isJadAd(abstractAd.getAdvertis());
        }
        return false;
    }

    /**
     * 属于京东jad 广告
     * @return
     */
    public static boolean isJadAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        return isJadAd(advertis.getAdtype());
    }

    public static boolean isJadAd(int adType) {
        return adType == AD_SOURCE_JAD;
    }

    /**
     * 属于百度的广告
     */
    public static boolean isBaiduAd(IAbstractAd abstractAd) {
        if (abstractAd == null) {
            return false;
        }
        return isBaiduAd(abstractAd.getAdvertis());
    }

    /**
     * 属于百度的广告
     */
    public static boolean isBaiduAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        return isBaiduAd(advertis.getAdtype());
    }

    public static boolean isBaiduAd(int adType) {
        return adType == AD_SOURCE_BAIDU || adType == AD_SOURCE_BAIDU_REWARD_TEMP;
    }

    public static boolean isForwardVideo(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        return IAdConstants.IAdPositionId.FORWARD_VIDEO.equals(advertis.getAdPositionId());
    }

    public static boolean isAutoPlayForwardVideoAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (!MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBooleanCompat(CConstants.Group_ad.ITEM_AD_X_AUTO_PLAY_REWARD_VIDEO, true)) {
            return false;
        }
        return IAdConstants.IAdPositionId.FORWARD_VIDEO.equals(advertis.getAdPositionId()) && advertis.isPullForwardVideo();
    }


    public static ShareAdRequestParams getShareAdRequestParamsByH5Url(String dataUrl) {
        if (TextUtils.isEmpty(dataUrl)) {
            return null;
        }

        boolean isListenList = dataUrl.contains(UrlConstants.SUBJECT_URL);

        if (!isListenList) {
            return null;
        }

        Map<String, String> map = null;

        Uri uri;
        try {
            uri = Uri.parse(dataUrl);
        } catch (Exception e) {
            uri = null;
        }

        if (uri == null) {
            return null;
        }


        map = ToolUtil.getQueryMap(uri.getQuery());
        if (map != null) {
            String id = map.get("id");
            if (!TextUtils.isEmpty(id)) {
                return new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_LISTENER, id);
            }
        }

        return null;
    }

    // 分享广告
    private static boolean isFromPlayFraAd(String positionName) {
        if (TextUtils.isEmpty(positionName)) {
            return false;
        }

        if (AppConstants.AD_POSITION_NAME_SHARE_FLOAT.equals(positionName)
                || AppConstants.AD_POSITION_NAME_MORE_OPERATE.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_YELLOW_BAR.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_NATIVE.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_SKIN.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_CENTER.equals(positionName)
                || AppConstants.AD_POSITION_NAME_PLAY_COMMENT.equals(positionName)
                || AppConstants.AD_POSITION_NAME_FORWARD_VIDEO.equals(positionName)
                || IAdConstants.IAlbumAdInfoPoisitionName.TRACK_DETAIL_RELATIVE_RECOMMEND.equals(positionName)) {
            return true;
        }

        return false;
    }

    public static void updateAdPlayVersion(AdReportModel adReportModel) {
        if (adReportModel.getAdPlayVersion() == null && isFromPlayFraAd(adReportModel.getPositionName())) {
            adReportModel.setAdPlayVersion(AdManager.getAdPlayVersion());
        }
    }

    // 是否是暂停广告或者二次曝光广告
    public static boolean isPauseOrSecondAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        return advertis.isDuringPlay() || advertis.isPausedRequestAd();
    }

    public static boolean needPlayMute(IAbstractAd abstractAd){
        if (abstractAd == null){
            return false;
        }
        return needPlayMute(abstractAd.getAdvertis());
    }

    public static boolean needPlayMute(Advertis advertis){
        if (advertis == null){
            return false;
        }
        if (isBaiduAd(advertis)) {
            return true;
        }
        boolean soundPatchMuteEnable =
                ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_SOUND_PATCH_ALWAYS_MUTE, true);
        boolean playOptimizeEnable = UserInteractivePlayStatistics.Optimizer_StartPlayWhileAdRequest.enable() &&
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBoolean(CConstants.Group_ad.ITEM_PLAY_OPTIMIZE_ENABLE, false);
        return AdManager.isPauseOrSecondAd(advertis)
                || isDurationModeOpenSilence(advertis)
                || playOptimizeEnable
                || soundPatchMuteEnable;
    }

    // 是否是畅听时长模式静音优化
    public static boolean isDurationModeOpenSilence(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        return advertis.isDurationModeOpenSilence();
    }

    // 是否是畅听时长模式静音优化
    public static boolean isDurationModeOpenSilence(IAbstractAd abstractAd) {
        if (abstractAd == null || abstractAd.getAdvertis() == null) {
            return false;
        }

        return abstractAd.getAdvertis().isDurationModeOpenSilence();
    }

    public static long getDelayTimeToShowGesture(Advertis advertis) {
        if (advertis == null) {
            return 0;
        }

        if (!TextUtils.isEmpty(advertis.getButtonIconUrl())) {
            if (advertis.getGestureStartMs() < 3400) {
                return 3400;
            }
        }

        return advertis.getGestureStartMs() < 300 ? 300 : advertis.getGestureStartMs();
    }

    private static Rect VISIBLE_RECT = new Rect();
    private static int[] VISIBLE_LOCAL = new int[2];

    // 视图真正在显示
    public static boolean viewIsRealShowing(View view) {
        if (view == null) {
            return false;
        }

        boolean flag = ViewCompat.isAttachedToWindow(view)
                && view.getWindowVisibility() == View.VISIBLE
                && view.isShown()
                && view.getLocalVisibleRect(VISIBLE_RECT);

        if (flag) {
            view.getLocationInWindow(VISIBLE_LOCAL);
            if (VISIBLE_LOCAL[1] + view.getHeight() * 1.0f / 6 * 5 <= 0) {
                return false;
            }
        }
        return flag;
    }

    @Nullable
    public static Advertis getDeferAdvertis(List<Advertis> advertisList, int adItemId) {
        if (!ToolUtil.isEmptyCollects(advertisList)) {
            for (Advertis advertis : advertisList) {
                if (!AdManager.isThirdAd(advertis) && advertis.getAdid() != adItemId) {
                    return advertis;
                }
            }
        }

        return null;
    }

    // 是否是主播弹幕广告
    public static boolean isLiveDanmuAd(Advertis advertis) {
        if (advertis != null && advertis.getExternalType() == 1) {
            return true;
        }

        return false;
    }

    private static String adPlayVersion;

    // 获取播放页version
    public static String getAdPlayVersion() {
        if (adPlayVersion != null) {
            return adPlayVersion;
        }
        if (BaseUtil.isPlayerProcess(MainApplication.getMyApplicationContext())) {
            XmPlayerService playerSrvice = XmPlayerService.getPlayerSrvice();
            try {
                if (playerSrvice != null && playerSrvice.getIXmCommonBusinessDispatcher() != null
                        && playerSrvice.getIXmCommonBusinessDispatcher().isUseNewPlayFragment()) {
                    return adPlayVersion = IAdConstants.IAdPlayVersion.PLAY_VERSION_NEW_NEW_LAY;
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        } else {
            try {
                return adPlayVersion = IAdConstants.IAdPlayVersion.PLAY_VERSION_NEW_NEW_LAY;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return adPlayVersion = IAdConstants.IAdPlayVersion.PLAY_VERSION_OLD;
    }

    private static HttpProxyCacheServer mProxyCacheServer;

    public static HttpProxyCacheServer getHttpProxyCache(Context context) {
        try {
            if (mProxyCacheServer == null) {
                mProxyCacheServer = new HttpProxyCacheServer.Builder(context)
                        // 最大缓存5个文件
                        .maxCacheFilesCount(5)
                        .build();
            }
        } catch (Exception e) {
            return null;
        }

        return mProxyCacheServer;
    }

    @Nullable
    public static String wrapVideoPlayUrl(String playUrl) {
        if (TextUtils.isEmpty(playUrl)) {
            return playUrl;
        }

        if (!playUrl.startsWith("http")) {
            return playUrl;
        }

        if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_VIDEO_CACHE_ENABLE, true)) {
            HttpProxyCacheServer httpProxyCache = getHttpProxyCache(ToolUtil.getCtx());
            if (httpProxyCache != null) {
                String proxyUrl;
                // g网链接  && 配置g网自动播放
                if (NetworkType.isConnectMOBILE(ToolUtil.getCtx())
                        && !ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_AD_VIDEO_CAN_AUTO_PLAY_BY_GNET, false)) {
                    if (httpProxyCache.isCached(playUrl)) {
                        proxyUrl = httpProxyCache.getProxyUrl(playUrl);
                        if (proxyUrl != null && proxyUrl.startsWith("file://")) {
                            return proxyUrl.replace("file://", "");
                        }
                        return proxyUrl;
                    } else {
                        return null;
                    }
                }
                proxyUrl = httpProxyCache.getProxyUrl(playUrl);
                if (proxyUrl != null && proxyUrl.startsWith("file://")) {
                    return proxyUrl.replace("file://", "");
                }
                return proxyUrl;
            }
        }
        return playUrl;
    }

    // 是否是落地页为h5的广告
    public static boolean isLandingPageWebAd(Advertis thirdAd) {
        if (thirdAd == null || isThirdAd(thirdAd)) {
            return false;
        }
        String dpRealLink = thirdAd.getDpRealLink();

        if (!TextUtils.isEmpty(dpRealLink)) {
            final Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setData(Uri.parse(dpRealLink));

            ComponentName componentName = intent.resolveActivity(ToolUtil.getCtx().getPackageManager());
            if (componentName != null) {
                return false;
            }
        }

        if (thirdAd.getOpenlinkType() == Advertis.LINK_TYPE_CALL_PHONE
                || thirdAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_OPEN_WX_APPLETS) {
            return false;
        }

        final int linkType = thirdAd.getLinkType();
        if ((linkType == Advertis.LINK_TYPE_WEB || linkType == Advertis.LINK_TYPE_NONE)
                && thirdAd.getOpenlinkType() != 1) {
            return true;
        }

        return false;
    }

    /**
     * 从配置中心读取声播广告标签配置
     *
     * @param positionName 声播广告的位置
     * @return 广告标签配置Url
     */
    public static String getAnchorAdTag(String positionName) {
        boolean needShowTag = ConfigureCenter.getInstance().getBool(
                CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_NEED_SHOW_ANCHOR_AD_TAG, true);
        if (!needShowTag) {
            return null;
        }
        JSONObject adImages = ConfigureCenter.getInstance().getJson(
                CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_ANCHOR_AD_TAG_STYLE);
        if (adImages == null) {
            return null;
        }
        try {
            String adMark = adImages.getString(positionName);
            return adMark;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断是否是运营广告
     *
     * @param advertis 广告物料
     * @return True为运营广告
     */
    public static boolean isOperationAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (AdManager.isThirdAd(advertis)) {
            return false;
        }
        String adUserType = advertis.getAdUserType();
        if (adUserType == null) {
            return false;
        }
        adUserType = adUserType.toUpperCase();
        return "OPERATION".equals(adUserType) || "MARKET".equals(adUserType)
                || "OTHER".equals(adUserType) || "PAY".equals(adUserType);
    }

    public static boolean isOperationAd(String adUserType) {
        return "OPERATION".equals(adUserType) || "MARKET".equals(adUserType)
                || "OTHER".equals(adUserType) || "PAY".equals(adUserType);
    }

    public static boolean isRTBAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }

        String adUserType = advertis.getAdUserType();
        return "RTB".equalsIgnoreCase(adUserType);
    }

    public static void updateWebVideoModel(Advertis advertis, VideoParamModel videoParamModel,
                                           boolean playMute) {
        // 视频拼接落地页类型
        if (advertis == null || videoParamModel == null
                || videoParamModel.getVideoControl() == null
                || TextUtils.isEmpty(videoParamModel.getVideoPath())
                || videoParamModel.getVideoControl().getCurPos() < 0) {
            return;
        }

        int curPos = videoParamModel.getVideoControl().getCurPos();
        // 播放即将结束的时候设置为0
        if (videoParamModel.getVideoControl().getDuration() - curPos < 1000) {
            curPos = 0;
        }

        advertis.setWebVideoModel(new AdWebVideoModel(
                videoParamModel.getVideoPath(),
                curPos, playMute));
    }

    public static void updateWebVideoModel(Advertis advertis) {
        if (advertis == null ||
                (advertis.getLinkType() != Advertis.LINK_TYPE_FULL_VIDEO_WEBVIEW && advertis.getLinkType() != Advertis.LINK_TYPE_IMMERSIVE_VIDEO_WEBVIEW && advertis.getLinkType() != Advertis.LINK_TYPE_VIDEO_WEBVIEW_NEW)) {
            return;
        }
        String videoUrl;
        if (advertis.getLinkType() == Advertis.LINK_TYPE_VIDEO_WEBVIEW_NEW) {
            videoUrl = advertis.getLandVideoUrl();
        } else {
            if (IAdConstants.IAdPositionId.HOME_BOTTOM.equals(advertis.getPositionId() + "")) {
                videoUrl = advertis.getDynamicCover(); // touch广告videoCover字段被占用
            } else {
                videoUrl = advertis.getVideoCover();
                if (TextUtils.isEmpty(videoUrl)) {
                    videoUrl = advertis.getDynamicImage(); // 贴片广告
                }
            }
        }
        if (!TextUtils.isEmpty(videoUrl)) {
            advertis.setWebVideoModel(new AdWebVideoModel(videoUrl, advertis.getVolume() == 0, 0,
                    advertis.getLogoUrl(),
                    advertis.getName(), advertis.getDescription(),
                    TextUtils.isEmpty(advertis.getClickTitle()) ? advertis.getButtonText() : advertis.getClickTitle()));
        }
    }

    public static boolean isDownloadApkUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        try {
            Uri uri = Uri.parse(url);
            String path = uri.getPath();
            if (!TextUtils.isEmpty(path) && path.contains(".apk")) {
                return true;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public static boolean isVideoAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (advertis.getSoundType() == Advertis.TYPE_VIDEO
                || advertis.getSoundType() == Advertis.TYPE_VIDEO_AND_DANMU
                || advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL
                || advertis.getSoundType() == Advertis.TYPE_VIDEO_VERTICAL_FLOWER
                || advertis.getSoundType() == Advertis.TYPE_STATIC_VIDEO_HORIZONTAL_BANNER
                || advertis.getSoundType() == Advertis.TYPE_STATIC_VIDEO_VERTICAL_BANNER
                || advertis.getSoundType() == Advertis.TYPE_VIP_PAUSE_VIDEO_AD
                || advertis.getSoundType() == TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD
                || advertis.getSoundType() == TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD
                || advertis.getSoundType() == Advertis.TYPE_GRADE_S_VIDEO
        ) {
            return true;
        }
        return false;
    }

    public static boolean isStaticAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_AD
                || advertis.getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE) {
            return true;
        }
        return false;
    }

    private static Boolean readCheatingParams;
    private static String bookMark;
    private static boolean dontReadCheatingParams() {
        if (readCheatingParams != null && !readCheatingParams) {
            return true;
        }

        if (readCheatingParams == null) {
            readCheatingParams =
                    ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                            CConstants.Group_ad.ITEM_READ_CHEATING_PARAMS, true);
        }

        return !readCheatingParams;
    }

    public static String getBootMark() {
        if (dontReadCheatingParams()) {
            return null;
        }

        if (TextUtils.isEmpty(bookMark)) {
            try {
                bookMark = ImportSDKHelper.getBootMark();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return bookMark;
    }

    private static String updateMark;
    public static String getUpdateMark() {
        if (dontReadCheatingParams()) {
            return null;
        }

        if (TextUtils.isEmpty(updateMark)) {
            try {
                updateMark = ImportSDKHelper.getUpdateMark();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return updateMark;
    }

    // 大陆厂商安卓设备AS包名
    private static String asVersion = null;
    public static String getAsVersion() {
        if (TextUtils.equals(asVersion, "")) {
            return null;
        }

        if (!TextUtils.isEmpty(asVersion)) {
            return asVersion;
        }

        if (!com.ximalaya.ting.android.framework.util.BaseUtil.isForegroundIsMyApplication(BaseApplication.getTopActivity())) {
            return null;
        }

        List<String> markets = new ArrayList<String>() {
            {
//                add("com.heytap.market"); //oppo国际版
//                add("com.oppo.market");
//                add("com.bbk.appstore");
//                add("com.xiaomi.market");
//                add("com.huawei.appmarket");
            }
        };
        if (SystemUtil.isHuaweiDevice()){
            markets.add("com.huawei.appmarket");
        }else if (SystemUtil.isOppoDevice()){
            markets.add("com.oppo.market");
            markets.add("com.heytap.market"); //oppo国际版
        }else if (SystemUtil.isMIUI()){
            markets.add("com.xiaomi.market");
        }else if (SystemUtil.isVivoDevice()){
            markets.add("com.bbk.appstore");
        }


        for (String market : markets) {
            PackageInfo pkginfo = getPackageInfo(ToolUtil.getCtx(), market);
            if (pkginfo != null) {
                asVersion = pkginfo.versionCode + "";
                break;
            }
        }

        if (asVersion == null) {
            asVersion = "";
        }

        return asVersion;
    }

    private static String hmsCoreVersion;
    public static String getHmsCoreVersion() {
        if (!TextUtils.isEmpty(hmsCoreVersion)) {
            return hmsCoreVersion;
        }

        if (TextUtils.equals(hmsCoreVersion, "")) {
            return null;
        }

        if (!com.ximalaya.ting.android.framework.util.BaseUtil.isForegroundIsMyApplication(BaseApplication.getTopActivity())) {
            return null;
        }
        PackageInfo pkginfo = getPackageInfo(ToolUtil.getCtx(), "com.huawei.hwid");
        Log.d("AdManager", "getHmsCoreVersion getPackageInfo");
        if (pkginfo != null) {
            hmsCoreVersion = pkginfo.versionCode + "";
        }

        if (hmsCoreVersion == null) {
            hmsCoreVersion = "";
        }

        return hmsCoreVersion;
    }

    ///根据apk包名获取版本号
    private static PackageInfo getPackageInfo(Context context, String pkgName) {
        PackageInfo pi = null;
        try {
            PackageManager pm = context.getPackageManager();
            pi = pm.getPackageInfo(pkgName, PackageManager.GET_CONFIGURATIONS);
            return pi;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pi;
    }

    //   1-H5,4-专辑,5-声音,10-个人直播,8-专辑类听单,9-声音类听单
    public static String converContentType(int type) {
        String contentType = "0";
        switch (type) {
            case 1:
                contentType = "H5";
                break;
            case 4:
                contentType = "专辑";
                break;
            case 5:
                contentType = "声音";
                break;
            case 10:
                contentType = "个人直播";
                break;
            case 8:
                contentType = "专辑类听单";
                break;
            case 9:
                contentType = "声音类听单";
                break;
        }

        return contentType;
    }

    public static void addResPositionTag(Bundle bundle, String positionName) {
        if (AppConstants.AD_POSITION_NAME_FOCUS.equals(positionName)) {
            bundle.putString(ResPositionConstant.RES_POSITION, ResPositionConstant.FOCUS);
        } else if (AppConstants.AD_POSITION_NAME_FIREWORK_POPUP.equals(positionName)) {
            bundle.putString(ResPositionConstant.RES_POSITION, ResPositionConstant.UNI_POP);
        } else {
            return;
        }
    }


    /**
     *  撒花广告
     */
    public static boolean isFlowerAd(IAbstractAd abstractAd) {

        if (abstractAd == null || abstractAd.getAdvertis() == null) {
            return false;
        }

        return isFlowerAd(abstractAd.getAdvertis());
    }

    public static boolean isFlowerAd(Advertis advertis) {
        if(advertis != null) {
            if(Advertis.TYPE_STATIC_VERTICAL_FLOWER_NEW == advertis.getSoundType()
                    ||Advertis.TYPE_STATIC_HORIZONTAL_FLOWER_NEW == advertis.getSoundType()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isShakeAd(IAbstractAd abstractAd) {
        if (abstractAd != null && abstractAd.getAdvertis() != null) {
            return Advertis.TYPE_HORIZONTAL_STATIC_IMAGE_SHAKE == abstractAd.getAdvertis().getSoundType()
                    || Advertis.TYPE_VERTICAL_STATIC_IMAGE_SHAKE == abstractAd.getAdvertis().getSoundType()
                    || abstractAd.getAdvertis().getHasShake();
        }
        return false;
    }

    public static boolean isRadioSoundPatch(Advertis advertis) {
        return advertis != null && advertis.getAdScene() == Advertis.AD_SCENE_RADIO;
    }

    public static boolean isUerNewSmallBanner() {
        return "small".equals(ABTest.getString(CConstants.Group_ad.ITEM_NEW_RECOMMEND_FOCUS, "big"));
    }

    // 此方法不准确，尽量少用，最好用下面的重载方法
    public static boolean isInteractSoundAd(Advertis advertis) {
        if (!NewPlayPageUtil.isRnPageStyle()) {
            return false;
        }
        if (advertis == null) {
            return false;
        }
        if (advertis.getIsDisplayedInScreen() != 0) {
            // 非避免请求到的广告，实际服务端不会在亮屏下返回
            return false;
        }
        return advertis.getSoundType() == Advertis.TYPE_SOUND_INTERACT;
    }

    public static boolean isInteractSoundAd(AdvertisList advertisList) {
        if (!NewPlayPageUtil.isRnPageStyle() && !NewPlayPageUtil.isYPageStyle()) {
            return false;
        }
        if (advertisList == null || advertisList.getAdvertisList() == null || advertisList.getAdvertisList().size() < 2) {
            return false;
        }
        Advertis advertis = advertisList.getAdvertisList().get(0);
        if (advertis == null) {
            return false;
        }
        if (advertis.getIsDisplayedInScreen() != 0) {
            // 非避免请求到的广告，实际服务端不会在亮屏下返回
            return false;
        }
        return advertis.getSoundType() == Advertis.TYPE_SOUND_INTERACT;
    }

    public static boolean isVipPauseSoundPatch(Advertis mSoundAd){
        if(mSoundAd == null){
            return false;
        }
        int soundType = mSoundAd.getSoundType();
        return soundType == TYPE_VIP_PAUSE_STATIC_IMAGE_AD || soundType== TYPE_VIP_PAUSE_VIDEO_AD
                || soundType == TYPE_VIP_PAUSE_STATIC_VERTICAL_IMAGE_AD || soundType == TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD
                || soundType == TYPE_VIP_PAUSE_STATIC_VERTICAL_STYLE2_IMAGE_AD || soundType == TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD;
    }

    public static boolean isDisableShakeAd(IAbstractAd ad) {
        if (ad == null) {
            return false;
        }
        if (!AdManager.isThirdAd(ad)
                || isVerticalSoundPatchJadAd(ad)) {
            return false;
        }
        return true;
    }

    public static boolean isVerticalSoundPatchJadAd(IAbstractAd ad) {
        if (ad == null) {
            return false;
        }
        if (AdManager.isJadAd(ad)
                && (ad.getAdvertis() != null && ad.getAdvertis().getPositionId() == 0
                && (ad.getAdvertis().getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE_SHAKE))) {
            return true;
        }
        return false;
    }

    public static boolean isSoundPatchAd(Advertis advertis) {
        if (advertis == null) return false;
        return IAdConstants.IAdPositionId.TRACK_SOUND_PATCH.equals(advertis.getAdPositionId());
    }

    public static boolean isGradeSType(IAbstractAd abstractAd) {
        if (abstractAd != null && abstractAd.getAdvertis() != null) {
            return Advertis.TYPE_GRADE_S_VIDEO == abstractAd.getAdvertis().getSoundType()
                    || Advertis.TYPE_GRADE_S_STATIC_IMAGE == abstractAd.getAdvertis().getSoundType();
        }
        return false;
    }

    public static boolean isVerticalAd(IAbstractAd ad) {
        if (ad == null) {
            return false;
        }
        if ((ad.getAdvertis() != null && ad.getAdvertis().getPositionId() == 0
                && (ad.getAdvertis().getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_VERTICAL_STATIC_IMAGE_SHAKE
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_STATIC_VERTICAL_FLOWER_NEW
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_VIP_PAUSE_VERTICAL_STYLE2_VIDEO_AD
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_VIP_PAUSE_VERTICAL_VIDEO_AD
                || ad.getAdvertis().getSoundType() == Advertis.TYPE_VIDEO_VERTICAL))) {
            return true;
        }
        return false;
    }

    public static boolean isVerticalAd(int soundType) {
        if (soundType == 0) {
            return false;
        }
        if ((soundType == Advertis.TYPE_VERTICAL_STATIC_IMAGE
                || soundType == Advertis.TYPE_VERTICAL_STATIC_IMAGE_SHAKE
                || soundType == Advertis.TYPE_STATIC_VERTICAL_FLOWER_NEW
                || soundType == Advertis.TYPE_VIDEO_VERTICAL)) {
            return true;
        }
        return false;
    }

    public static boolean isYSoundPatchNewStyle(IAbstractAd abstractAd) {
        if (abstractAd != null && abstractAd.getAdvertis() != null && abstractAd.getAdvertis().isYSoundPatchNewStyle()) {
            return true;
        }
        return false;
    }

    public static boolean isUveSwitchOpen() {

        try {
            String uveSwitch = BuildProperties.getSystemProperty("debug.xima.ad.uve_switch", "");
            if (ConstantsOpenSdk.isDebug && ("true".equalsIgnoreCase(uveSwitch) || "false".equalsIgnoreCase(uveSwitch))) {
                Logger.e("msg_uve_switch", " isUveSwitchOpen ---- 使用 shell 配置的 uve switch 开关 = " + uveSwitch);
                return Boolean.parseBoolean(uveSwitch);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        Logger.i("msg_uve_switch", " isUveSwitchOpen ---- 使用football 配置的开关 ");
        return MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBooleanCompat(CConstants.Group_ad.ITEM_AD_UVE_SWITCH_CONFIG, false);
    }

    public static boolean isFoldScreenExpand(){
        return com.ximalaya.ting.android.framework.util.BaseUtil.isFoldScreen(ToolUtil.getCtx()) &&
                com.ximalaya.ting.android.framework.util.BaseUtil.getScreenWidth(ToolUtil.getCtx()) > com.ximalaya.ting.android.framework.util.BaseUtil.dp2px(ToolUtil.getCtx(), 550);
    }

    public static String getTicket(String business, String scene) {
        if (TextUtils.isEmpty(business) || TextUtils.isEmpty(scene)) {
            return "";
        }
        String sceneUrl = "b=" + business + "&s=" + scene + "&u=" + UserInfoMannage.getUid();
        return XuidManager.INSTANCE.getTicket(sceneUrl);
    }

    public static void reportSubPercentShow(Advertis advertis, String xmRequestId) {
        if (advertis == null) {
            return;
        }
        // 新首页-听单广告位实时采样  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(65242)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "0")
                .put("adId", advertis.getAdid() + "")
                .put("responseId", advertis.getResponseId() + "")
                .createTrace();
    }

    public static String getEcpm(Advertis advertis) {
        if (advertis != null && advertis.getCommonReportMap()!=null) {
            String commonReportMap = advertis.getCommonReportMap();
            try{
                JSONObject jsonObject = new JSONObject(commonReportMap);
                if (advertis.isMobileRtb() && advertis.getCurrentDspRtbPrice() > 0) {
                    return EncryptPriceUtils.encodeAdsPrice(advertis.getCurrentDspRtbPrice());
                }
                if (jsonObject.has("adxRtbSettlementPrice")) {
                    return jsonObject.optString("adxRtbSettlementPrice");
                } else {
                    return advertis.getPriceEncrypt() != null ? advertis.getPriceEncrypt() : "-1";
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "-1";
    }

    public static int getEncryptType(Advertis advertis) {
        if (advertis != null && advertis.getCommonReportMap()!=null) {
            String commonReportMap = advertis.getCommonReportMap();
            try{
                JSONObject jsonObject = new JSONObject(commonReportMap);
                if (jsonObject.has("encryptType")) {
                    return jsonObject.optInt("encryptType");
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 1;
    }

    public static boolean isTemplateSoundAd(int soundType){
        return soundType > 100000;
    }

    public static boolean isTemplateAd(int showStyle){
        return showStyle > 1000000;
    }
}
