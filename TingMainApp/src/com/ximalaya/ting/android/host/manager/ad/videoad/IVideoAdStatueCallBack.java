package com.ximalaya.ting.android.host.manager.ad.videoad;

import android.app.Activity;
import android.view.View;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;

/**
 * Created by le.xin on 2020/5/6.
 * 激励视频广告
 * <AUTHOR>
 * @email <EMAIL>
 */
public interface IVideoAdStatueCallBack {
    int ERROR_CODE_NO_AD = 1;    // 没有广告
    int ERROR_CODE_ACTIVITY_IS_NO_VALID = 2; // activity 失效了
    int ERROR_CODE_VIDEO_PLAY_ERROR = 3; // 视频播放失败
    int ERROR_CODE_AD_LOAD_OVER_TIME = 4; // 视频加载超时
    int ERROR_CODE_DEFUALT = 100; // 视频播放失败

    int ERROR_CODE_DSP_LOAD_OVER_TIME = 101; // dsp并行加载超时

    void onAdLoad(AbstractThirdAd thirdAd);

    void onAdLoadError(int code ,String message);

    void onAdPlayStart();

    void onAdVideoClick(boolean isAutoClick, int clickAreaType);

    // 注意onAdClose isCustomCloseBtn=false 可能会回调多次
    void onAdClose(boolean isCustomCloseBtn);

    void onAdPlayComplete();

    void onAdPlayError(int code, String message);

    View.OnClickListener getCloseClickListener(Activity activity);
}