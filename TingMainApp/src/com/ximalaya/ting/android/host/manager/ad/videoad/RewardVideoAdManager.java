package com.ximalaya.ting.android.host.manager.ad.videoad;

import static com.ximalaya.ting.android.adsdk.constants.IXmAdConstants.ILinkType.LINK_TYPE_REWARD_VIDEO_AD;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_PLAY_LET_TASK;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_BAIDU_REWARD_TEMP;
import static com.ximalaya.ting.android.opensdk.model.advertis.Advertis.AD_SOURCE_IMMERSIVE_GDT;

import android.app.Activity;
import android.app.Application;
import android.app.Dialog;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.bytedance.sdk.openadsdk.stub.activity.Stub_Standard_Portrait_Activity;
import com.qq.e.ads.PortraitADActivity;
import com.qq.e.ads.RewardvideoPortraitADActivity;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.XmRewardVideoAd;
import com.ximalaya.ting.android.adsdk.AdSDK;
import com.ximalaya.ting.android.adsdk.aggregationsdk.mediation.IMediationInfo;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.external.IRewardVideoAdListener;
import com.ximalaya.ting.android.adsdk.external.XmLoadAdParams;
import com.ximalaya.ting.android.adsdk.external.XmRewardExtraParam;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.Consumer;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.CSJDrawAdActivity;
import com.ximalaya.ting.android.host.activity.CSJDrawAdActivityParallel;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.ad.AdAdapterUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdSDKManager;
import com.ximalaya.ting.android.host.manager.ad.BaseAdSDKManager;
import com.ximalaya.ting.android.host.manager.ad.ForwardVideoManager;
import com.ximalaya.ting.android.host.manager.ad.StartSplashAdHelper;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockVipTrackManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdFreeListenProtocolDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivity;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForPoint;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForForwardVideo;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForPoint;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleNormal;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCashTasksView;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCountDownView;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.AdUnLockAdvertisModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by le.xin on 2020/5/6.
 * 激励视频广告
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class RewardVideoAdManager implements Application.ActivityLifecycleCallbacks {
    private static  final String TAG = "---RewardVideoAdManager";
    private static volatile RewardVideoAdManager mInstance;

    private RewardVideoAdManager() {
        MainApplication.getInstance().addActivityLifeListener(this);
    }

    public static RewardVideoAdManager getInstance() {
        if (mInstance == null) {
            synchronized (RewardVideoAdManager.class) {
                if (mInstance == null) {
                    mInstance = new RewardVideoAdManager();
                }
            }
        }
        return mInstance;
    }

    @Nullable
    private WeakReference<IVideoAdStatueCallBack> mStatueCallBackWeakReference;

    @Nullable
    private Dialog mDialog;
    private ICustomViewToActivity customViewByCountDownStyle;
    private RewardExtraParams mExtraParams;

    private Advertis mCurrentAdvertis;

    public void loadRewardAd(@NonNull Activity activity,
                             String posId,
                             int adType,
                             @NonNull RewardExtraParams extraParams,
                             @NonNull IVideoAdStatueCallBack callback) {
        loadRewardAd(activity,null, posId, adType, AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_NO_TEMPLATE, extraParams, callback);
    }

    public void loadRewardAd(@NonNull Activity activity,
                             Advertis advertis,
                             String posId,
                             int adType,
                             @NonNull RewardExtraParams extraParams,
                             @NonNull IVideoAdStatueCallBack callback) {
        loadRewardAd(activity, advertis, posId, adType, AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_NO_TEMPLATE, extraParams, callback);
    }



    public void loadRewardAdFromSDK(@NonNull Activity activity, String posId, @NonNull XmRewardExtraParam rewardExtraParam, @Nullable AdSDKAdapterModel adSDKAdapterModel
                                    ,@NonNull IRewardVideoAdListener callback){
        AdSDKManager.init(ToolUtil.getCtx());
        XmLoadAdParams xmLoadAdParams = new XmLoadAdParams(posId);
        if (adSDKAdapterModel != null && adSDKAdapterModel.getLinkType() == LINK_TYPE_REWARD_VIDEO_AD) {
            Map<String, String> requestParams = new HashMap();
            requestParams.put("originPositionId", adSDKAdapterModel.getAdPositionId());
            requestParams.put("originResponseId", adSDKAdapterModel.getResponseId() + "");
            requestParams.put("originMaterialId", adSDKAdapterModel.getAdid() + "");
            xmLoadAdParams.setRequestParams(requestParams);
        }
        xmLoadAdParams.setNeedToRecordShowOb(false);
        AdSDK.getInstance().loadRewardVideoAd(activity, MainApplication.getMyApplicationContext(), xmLoadAdParams, rewardExtraParam,callback);
    }




    // 加载激励视频
    public void loadRewardAd(@NonNull Activity activity,
                             Advertis advertis,
                             String posId,
                             int adType,
                             int dspAdType,
                             @NonNull RewardExtraParams extraParams,
                             @NonNull IVideoAdStatueCallBack callback) {

        callback = new VideoAdStatueCallBackWrapper(activity, callback, extraParams.getRewardCountDownStyle());

        mExtraParams = extraParams;
        extraParams.setAdType(adType);
        mCurrentAdvertis = advertis;
        mStatueCallBackWeakReference = new WeakReference<>(callback);

        if(adType == Advertis.AD_SOURCE_XIMALAYA) {
            if (AdUnlockUtil.isWebAd(extraParams)) {
                startWebFragment(callback, extraParams);
            } else if (RewardExtraParams.isFreeListen(extraParams.getRewardCountDownStyle()) || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
                startVideoFragment(callback, extraParams);
            } else {
                VideoAdActivity.startVideoAdActivity(createRequestKey(callback), extraParams);
            }
        } else if (AdManager.isGdtAd(adType)) {
            if (TextUtils.isEmpty(posId)) {
                if (callback != null) {
                    callback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "slotId不能为null");
                }
                return;
            }
            if (adType == AD_SOURCE_IMMERSIVE_GDT) {
                GDTRewardVideoAdUtil.loadImmersiveRewardVideoAd(activity, advertis, posId, callback, extraParams, new IDataCallBack<GDTRewardVideoAdUtil.ImmersiveRewardVideoAdWrapper>() {

                    @Override
                    public void onSuccess(@Nullable GDTRewardVideoAdUtil.ImmersiveRewardVideoAdWrapper data) {

                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            } else {
                GDTRewardVideoAdUtil.loadRewardVideoAd(activity, advertis, posId, callback, extraParams, new IDataCallBack<GDTRewardVideoAdUtil.RewardVideoAdWrapper>() {
                    @Override
                    public void onSuccess(@Nullable GDTRewardVideoAdUtil.RewardVideoAdWrapper object) {
                        if (object != null && object.mRewardVideoAD != null && mExtraParams != null) {
                            int durationS = object.mRewardVideoAD.getVideoDuration() / 1000;
                            // 根据广点通物料时长判断，小于后台配置时长，直接取物料时长，播放完成即可解锁，大于后台返回时长取后台配置时长
                            int videoPlayOverTime = (durationS < mExtraParams.getVideoPlayOverTime()) ? durationS :
                                    mExtraParams.getVideoPlayOverTime();
                            mExtraParams.setVideoPlayOverTime(videoPlayOverTime);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }
        } else if (AdManager.isCSJAd(adType)) {
            if (TextUtils.isEmpty(posId)) {
                if (callback != null) {
                    callback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "slotId不能为null");
                }
                return;
            }

            switch (dspAdType) {
                case AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_NO_TEMPLATE:
                case AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadRewardVideo(activity, posId, dspAdType == AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_TEMPLATE, extraParams, callback);
                    break;

                case AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_TEMPLATE:
                case AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_NO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadFullScreenVideo(activity, posId,
                            dspAdType == AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_TEMPLATE,extraParams, callback);
                    break;
                case AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_TEMPLATE:
                case AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_NO_TEMPLATE:
                    // draw信息流
                    CSJDrawAdActivity.startCSJDrawAdActivity(activity, createRequestKey(callback),
                            dspAdType == AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_TEMPLATE, posId, extraParams);
                    break;
                default:
                    if(callback != null) {
                        callback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有响应的dspAdType");
                    }
                    break;
            }

        } else if (AdManager.isBaiduAd(adType)) {
            IVideoAdStatueCallBack finalCallback = callback;
            AdAdapterUtil.obtainSDKManagerAsync(IMediationInfo.BAIDU, new AdAdapterUtil.OnSDKManagerReadyCallback() {
                @Override
                public void onReady(BaseAdSDKManager sdkManager) {
                    if (sdkManager == null) {
                        return;
                    }
                    if (adType == AD_SOURCE_BAIDU_REWARD_TEMP) {
                        sdkManager.loadRewardVideoTempAd(activity, posId, advertis, extraParams, finalCallback);
                    } else {
                        sdkManager.loadNativeAdForReward(advertis, (thirdAd, errorCode, msg) -> {
                            if (thirdAd == null || thirdAd instanceof NoLoadAd) {
                                if (finalCallback != null) {
                                    finalCallback.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, msg);
                                }
                            } else {
                                AdUnlockUtil.resetShowStyleWhenSdkMerge(extraParams, thirdAd);
                                if (RewardExtraParams.isFreeListen(extraParams.getRewardCountDownStyle()) || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
                                    startVideoFragment(finalCallback, extraParams);
                                } else {
                                    VideoAdActivity.startVideoAdActivity(createRequestKey(finalCallback), extraParams);
                                }
                            }
                        }, posId);
                    }
                }
            });
        }
    }

    public static void release() {
        if (mInstance != null) {
            MainApplication.getInstance().removeActivityLifeListener(mInstance);
        }
        mInstance = null;
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        Logger.log("ExcitationVideoAdManager : onActivityCreated " + activity);

        if ((isRewardVideoActivity(activity))) {
            finishPreFragment();
            finishRecentActivity();
            if (mStatueCallBackWeakReference != null) {
                IVideoAdStatueCallBack iVideoAdStatueCallBack =
                        mStatueCallBackWeakReference.get();
                if (iVideoAdStatueCallBack != null) {
                    statueMapping.put(activity, iVideoAdStatueCallBack);
                }
            }
        }

        if (mExtraParams != null && AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION.equals(mExtraParams.getPositionName())
                && !MmkvCommonUtil.getInstance(activity).getBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, false)
                && !isFromPlayPageRewardVip()) {
            // 时长模式展示规则弹窗
            AdFreeListenProtocolDialog protocolDialog = new AdFreeListenProtocolDialog(activity, new AdFreeListenProtocolDialog.IDismissCallBack() {
                @Override
                public void onDismiss() {
                }
            });
            protocolDialog.show();
            MmkvCommonUtil.getInstance(activity).saveBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, true);
        }

        // 特殊情况，增加特殊场景兜底保护：dsp激励视频未播放完退出应用，重新打开时，新建了mainActivity,但进程未杀死
        if (activity instanceof MainActivity && hookedActivitys.size() != 0) {
            finishRecentActivity();
            AdUnLockVipTrackManager.getInstance().notifyAdCancel();
            AdUnLockTimeManagerV2.getInstance().notifyAdCancel();
        }
    }

    public boolean isFromPlayPageRewardVip() {
        //时长模式下，播放页免广告弹窗来源不展示规则弹窗，因为其公用的是时长模式的广告位，但本身不是时长模式的奖励而是免广时间的奖励
        return mExtraParams != null && (mExtraParams.getRewardCountDownStyle() == REWARD_CLICK_STYLE_FOR_FREE_AD_NEW || mExtraParams.getRewardCountDownStyle() == REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
    }

    private boolean finishPreFragment() {
        Activity mainActivity = MainApplication.getMainActivity();
        if(mainActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) mainActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof IRewardAdFragment) {
                ((IRewardAdFragment) currentFragment).finish();
                return true;
            }
        }
        return false;
    }

    private void startWebFragment(@NonNull IVideoAdStatueCallBack callback, @NonNull RewardExtraParams extraParams) {
        boolean isNeedPost = finishPreFragment();
        finishRecentActivity();
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    ((MainActivity) mainActivity).startFragment(WebAdFragment.newInstance(createRequestKey(callback),
                            extraParams, callback.getCloseClickListener(mainActivity)));
                }
            };
            if (isNeedPost) {
                HandlerManager.postOnUIThread(runnable);
            } else {
                runnable.run();
            }
        }
    }

    private void startVideoFragment(@NonNull IVideoAdStatueCallBack callback, @NonNull RewardExtraParams extraParams) {
        boolean isNeedPost = finishPreFragment();
        finishRecentActivity();
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    if (extraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
                        ((MainActivity) mainActivity).startFragment(ImageAdFragment.newInstance(createRequestKey(callback),
                                extraParams, callback.getCloseClickListener(mainActivity)));
                    } else {
                        ((MainActivity) mainActivity).startFragment(VideoAdFragment.newInstance(createRequestKey(callback),
                                extraParams, callback.getCloseClickListener(mainActivity)));
                    }
                }
            };
            if (isNeedPost) {
                HandlerManager.postOnUIThread(runnable);
            } else {
                runnable.run();
            }
        }
    }

    private boolean isRewardVideoActivity(Activity activity) {
        return isGdtRewardVideoActivity(activity)
                || isCSJRewardVideoActivity(activity)
                || activity instanceof VideoAdActivity;
    }

    private boolean isCSJRewardVideoActivity(Activity activity) {
        return activity instanceof Stub_Standard_Portrait_Activity
                || activity instanceof CSJDrawAdActivity;
    }

    private boolean isGdtRewardVideoActivity(Activity activity) {
        return activity instanceof PortraitADActivity
                || activity instanceof RewardvideoPortraitADActivity;
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    private Set<Activity> hookedActivitys = new HashSet<>();

    private Map<Activity, IVideoAdStatueCallBack> statueMapping = new HashMap<>();

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        Logger.log("ExcitationVideoAdManager : onActivityResumed " + activity);
        if (hookedActivitys.contains(activity)) {
            if (mExtraParams != null) {
                CanPauseCountDownTimer countDownTimer = mExtraParams.getCountDownTimer();
                if (countDownTimer != null) {
                    countDownTimer.resume();
                }

                RewardExtraParams.IRewardStateCallBack rewardStateCallBack =
                        mExtraParams.getRewardStateCallBack();
                if(rewardStateCallBack != null) {
                    rewardStateCallBack.onActivityResume();
                }
            }
            return;
        }


        // 广点通激励视频的activity || 穿山甲激励视频
        if (isRewardVideoActivity(activity)) {
            hookedActivitys.add(activity);
            setCustomViewToActivity(activity, mExtraParams);
        }
    }

    private void setCustomViewToActivity(Activity activity, RewardExtraParams rewardExtraParams) {
        if (!ToolUtil.activityIsValid(activity)) {
            return;
        }

        if (activity.getWindow() == null || activity.getWindow().getDecorView() == null) {
            return;
        }
        if (isWelfareCashStyle(rewardExtraParams)) {
            addWelfareCashView(activity, rewardExtraParams);
        } else if (isImmersiveRewardAd(rewardExtraParams)) {
            GDTRewardVideoAdUtil.getInstance().addCountDownView(activity, rewardExtraParams);
        }

        if (rewardExtraParams != null  && rewardExtraParams.getRewardCountDownStyle() != RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
            return;
        }


        if (mExtraParams == null) {
            return;
        }
        boolean watchVideoClosenable = true;

        if (rewardExtraParams != null) {
            watchVideoClosenable = rewardExtraParams.isCloseable();
        }

        if (!watchVideoClosenable) {
            return;
        }

        if (getCountDownBtnClick(activity) == null) {
            // 异常情况增加兜底处理
            return;
        }
        View contentLayout = activity.getWindow().getDecorView().findViewById(android.R.id.content);
        if (contentLayout instanceof ViewGroup) {
            customViewByCountDownStyle =
                    getCustomViewByCountDownStyle(rewardExtraParams != null ?
                            rewardExtraParams.getRewardCountDownStyle() :
                            RewardExtraParams.REWARD_COUNT_DOWN_STYLE);
            customViewByCountDownStyle.setCustomViewToActivity((ViewGroup) contentLayout,
                    rewardExtraParams, getCountDownBtnClick(activity));
        }
        if (activity instanceof VideoAdActivity) {
            ((VideoAdActivity) activity).bindVolumeClick(customViewByCountDownStyle.getCustomVolumeView());
        }
    }

    public void addWelfareCashView(Activity activity, RewardExtraParams rewardExtraParams) {
        try {
            View view = activity.getWindow().getDecorView().findViewById(android.R.id.content);
            if (view instanceof ViewGroup) {
                ViewGroup contentLayout = (ViewGroup) view;
                AdRewardCashTasksView adRewardCashTasksView = new AdRewardCashTasksView(contentLayout.getContext());
                contentLayout.removeView(adRewardCashTasksView);
                FrameLayout.LayoutParams marginParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
                marginParams.gravity = Gravity.RIGHT;
                marginParams.topMargin = BaseUtil.dp2px(contentLayout.getContext(), 100);
                marginParams.rightMargin = BaseUtil.dp2px(contentLayout.getContext(), 16);
                contentLayout.addView(adRewardCashTasksView, marginParams);
                adRewardCashTasksView.setCustomViewToActivity(contentLayout, rewardExtraParams, null, null);
            }
        } catch (Exception e) {
            Logger.log("addWelfareCashView Exception:" + e.getMessage());
        }
    }

    public boolean isWelfareCashStyle(RewardExtraParams rewardExtraParams) {
        if (rewardExtraParams == null) {
            return false;
        }
        int rewardCountDownStyle = rewardExtraParams.getRewardCountDownStyle();
        String positionName = rewardExtraParams.getPositionName();
        if (positionName != null && positionName.equals(AD_POSITION_NAME_WELFARE_CASH_RECEIVE)) {
            return rewardCountDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE || rewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE;
        }
        return false;
    }

    public boolean isImmersiveRewardAd(RewardExtraParams rewardExtraParams) {
        if (rewardExtraParams == null) {
            return false;
        }
        String positionName = rewardExtraParams.getPositionName();
        if (positionName != null && positionName.equals(AD_POSITION_NAME_WELFARE_PLAY_LET_TASK)) {
            return true;
        }
        return false;
    }

    private ICustomViewToActivity getCustomViewByCountDownStyle(int countDownStyle) {
        if (countDownStyle == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
            return new RewardVideoCountDownStyleForForwardVideo();
        }
        return new RewardVideoCountDownStyleNormal();
    }

    private View.OnClickListener getCountDownBtnClick(Activity activity) {
        if (statueMapping != null) {
            IVideoAdStatueCallBack iVideoAdStatueCallBack = statueMapping.get(activity);

            if (iVideoAdStatueCallBack != null) {
                return iVideoAdStatueCallBack.getCloseClickListener(activity);
            }
        }

        return null;
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        if(hookedActivitys.contains(activity) && mExtraParams != null) {
            CanPauseCountDownTimer countDownTimer = mExtraParams.getCountDownTimer();
            if(countDownTimer != null) {
                countDownTimer.pause();
            }

            RewardExtraParams.IRewardStateCallBack rewardStateCallBack =
                    mExtraParams.getRewardStateCallBack();
            if(rewardStateCallBack != null) {
                rewardStateCallBack.onActivityPause();
            }


            ForwardVideoManager.getInstance().notifyVideoClose(activity);
//            if (mCurrentAdvertis != null && AdManager.isAutoPlayForwardVideoAd(mCurrentAdvertis)) {
//                if (activity != null) {
//                    Logger.d("-------msg_video_manager", " ------ video manager 激励视频 --- onActivityPaused 了 销毁当前activity-->  activity = " + activity);
//                    activity.finish();
//                }
//            }
        }
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        Logger.log("ExcitationVideoAdManager : onActivityDestroyed " + activity);

        hookedActivitys.remove(activity);
        statueMapping.remove(activity);
        if (!isWelfareRewardCashJump(mExtraParams)) {
            mExtraParams = null;
        }
        customViewByCountDownStyle = null;
        GDTRewardVideoAdUtil.getInstance().onActivityDestroyed(activity);
    }

    private boolean isWelfareRewardCashJump(RewardExtraParams mExtraParams) {
        if (mExtraParams == null) {
            return false;
        }
        boolean isRewardCashJumpNext = isWelfareCashStyle(mExtraParams) && mExtraParams.getRewardGoNextTimes() > 0;
        if (isRewardCashJumpNext) {
            mExtraParams.setRewardGoNextTimes(0);
        }
        return isRewardCashJumpNext;
    }


    // 销毁最近的activity
    public void finishRecentActivity() {
        Iterator<Activity> iterator = hookedActivitys.iterator();
        if (iterator.hasNext()) {
            Activity next = iterator.next();
            if (ToolUtil.activityIsValid(next)) {
                next.finish();
            }
        }
    }

    public Activity getRecentActivity() {
        Iterator<Activity> iterator = hookedActivitys.iterator();
        if (iterator.hasNext()) {
            Activity next = iterator.next();
            if (ToolUtil.activityIsValid(next)) {
                return next;
            }
        }
        return null;
    }

    public class VideoAdStatueCallBackWrapper implements IVideoAdStatueCallBackExt {

        private IVideoAdStatueCallBack mCallBack;

        private boolean isVideoLoadError = false;

        private LoadingDialog mLoadingDialog;

        public VideoAdStatueCallBackWrapper(Activity activity, IVideoAdStatueCallBack callBack, int countDownStyle) {

            // 全站畅听有单独的加载dialog
            if (ToolUtil.activityIsValid(activity) && !RewardExtraParams.isFreeListen(countDownStyle)) {
                mLoadingDialog = new LoadingDialog(activity);
                mLoadingDialog.setTitle("正在努力加载中");
                mLoadingDialog.showIcon(true);
                mLoadingDialog.show();
            }

            mCallBack = callBack;
        }

        @Override
        public void onAdLoad(AbstractThirdAd thirdAd) {
            if (mCallBack != null) {
                mCallBack.onAdLoad(thirdAd);
            }

            // 不去启动页
            StartSplashAdHelper.mNeedToWelComeNextTime = false;

            dismissLoading();
        }

        @Override
        public void onAdLoadError(int code, String message) {
            if (mCallBack != null) {
                mCallBack.onAdLoadError(code, message);
            }
//            finishRecentActivity();
            dismissLoading();
        }

        private void dismissLoading() {
            try {
                if (mLoadingDialog != null) {
                    mLoadingDialog.dismiss();
                    mLoadingDialog = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onAdPlayStart() {
            isVideoLoadError = false;

            if (mCallBack != null) {
                mCallBack.onAdPlayStart();
            }
        }

        @Override
        public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
            if (mCallBack != null) {
                mCallBack.onAdVideoClick(isAutoClick, clickAreaType);
            }
        }

        @Override
        public void onAdClose(boolean isCustomCloseBtn) {
            dismissLoading();

            if (mCallBack != null) {
                mCallBack.onAdClose(isCustomCloseBtn);
            }
        }

        @Override
        public void onAdPlayComplete() {
            if (mCallBack != null) {
                mCallBack.onAdPlayComplete();
            }

            if (mDialog != null) {
                mDialog.dismiss();
                mDialog = null;
            }

            if (mExtraParams != null) {
                if (mExtraParams.getCountDownTimer() != null) {
                    mExtraParams.getCountDownTimer().cancel();
                }
                mExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_VIDEO_PLAY_FINISH);
            }
            if (customViewByCountDownStyle != null) {
                customViewByCountDownStyle.onAdPlayComplete();
            }
        }

        @Override
        public void onAdPlayError(int code, String message) {

            if (isVideoLoadError) {
                return;
            }

            isVideoLoadError = true;

            if (mCallBack != null) {
                mCallBack.onAdPlayError(code, message);
            }
            dismissLoading();
        }

        @Override
        public View.OnClickListener getCloseClickListener(Activity activity) {
            if (mCallBack != null) {
                return mCallBack.getCloseClickListener(activity);
            }

            return null;
        }

        @Override
        public void onRewardVerify() {
            if (mCallBack instanceof IVideoAdStatueCallBackExt) {
                ((IVideoAdStatueCallBackExt) mCallBack).onRewardVerify();
            }
            if (mExtraParams != null && mExtraParams.getRewardThirdSDKSuccessCallBack() != null) {
                Log.d("AdRewardCashTasksView", "onRewardVerify: ");
                mExtraParams.setRewardBeforeClose(true);
                mExtraParams.getRewardThirdSDKSuccessCallBack().onSdkRewardSuccess();
            }
            if (customViewByCountDownStyle instanceof RewardVideoCountDownStyleForPoint ||
                    customViewByCountDownStyle instanceof RewardVideoClickStyleForPoint) {
                customViewByCountDownStyle.onAdPlayComplete();
                if (mExtraParams != null) {
                    if (mExtraParams.getCountDownTimer() != null) {
                        mExtraParams.getCountDownTimer().cancel();
                    }
                }
            }
        }
    }

    private Map<Long, IVideoAdStatueCallBack> mVideoAdStatuCallBack = new ConcurrentHashMap<>();

    private long createRequestKey(IVideoAdStatueCallBack videoAdStatueCallBack) {
        long l = System.currentTimeMillis();
        mVideoAdStatuCallBack.put(l, videoAdStatueCallBack);
        return l;
    }

    public void notifyAdStatueCallBack(long requestKey, Consumer<IVideoAdStatueCallBack> consumer) {
        IVideoAdStatueCallBack iVideoAdStatueCallBack = mVideoAdStatuCallBack.get(requestKey);

        if(iVideoAdStatueCallBack != null) {
            consumer.accept(iVideoAdStatueCallBack);
        }
    }

    /**
     * 并行加载广告
     * @param advertisList
     * @param requestAdCallBack
     */
    public void loadRewardVideoAdParallel(List<? extends Advertis> advertisList, @NonNull IRewardAdLoadCallBack requestAdCallBack, int maxParallelTime, IVideoAdStatueCallBackExt videoAdStatueCallBack) {
        Log.d(TAG, "loadRewardVideoAdParallel maxParallelTime = " + maxParallelTime);
        if (ToolUtil.isEmptyCollects(advertisList)) {
            requestAdCallBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "广告物料列表为空");
            return;
        }
        Advertis fistAd = advertisList.get(0);
        if (!AdManager.isThirdAd(fistAd)) {
            Log.d(TAG, "loadRewardVideoAdParallel 不用触发并行请求，第一个最优广告为喜马广告");
            // 不需进行dsp加载，喜马广告即为最优广告
            requestAdCallBack.onAdLoadSuccess(new XmRewardVideoAd(fistAd, fistAd, "0"));
            return;
        }

        // 开始并行加载dsp
        Advertis firstXmAd = null;
        List<Advertis> dspAds = new ArrayList<>();
        for (int i = 0; i < advertisList.size(); i++) {
            Advertis advertis = advertisList.get(i);
            if (advertis != null) {
                advertis.setCurAdIndex(i);
            }
            if (advertis != null && !AdManager.isThirdAd(advertis) && firstXmAd == null) {
                // 喜马物料之后的三方物料不用添加到并行请求列表中，其单价肯定低于当前的喜马物料
                firstXmAd = advertis;
                break;
            }
            if (AdManager.isThirdAd(advertis)) {
                dspAds.add(advertis);
            }
        }
        Log.d(TAG, "loadRewardVideoAdParallel fistXmAd = " + (firstXmAd != null ? firstXmAd.getAdid() : "0") + "dspAdSize =" + dspAds.size());
        printAdList(dspAds);

        final Advertis firstXmAdFinal = firstXmAd;
        LoadAdCallBackStatus adStatus = new LoadAdCallBackStatus();
        adStatus.isCallBackFinish = false;
        adStatus.isOverTime = false;
        adStatus.requestTime = System.currentTimeMillis();
        ConcurrentHashMap<Integer, AbstractRewardVideoAd> returnResult = new ConcurrentHashMap<>();

        Runnable overTimeCheck = new Runnable() {
            @Override
            public void run() {
                if (adStatus.isCallBackFinish) {
                    adStatus.isOverTime = true;
                    return;
                }
                if (adStatus.isOverTime) {
                    return;
                }
                Log.d(TAG, "并行加载超时，已返回的广告size =" + returnResult.size());
                adStatus.isOverTime = true;
                adStatus.isCallBackFinish = true;
                if (returnResult.isEmpty()) {
                    if (firstXmAdFinal != null) {
                        XmRewardVideoAd xmRewardVideoAd = new XmRewardVideoAd(firstXmAdFinal, firstXmAdFinal, "0");
                        Log.d(TAG, "并行加载超时，dsp全部未返回，返回喜马打底");
                        printBestAd(xmRewardVideoAd);
                        requestAdCallBack.onAdLoadSuccess(xmRewardVideoAd);
                    } else {
                        Log.d(TAG, "并行加载超时，dsp全部未返回，无喜马广告打底，返回加载失败");
                        requestAdCallBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DSP_LOAD_OVER_TIME, "dsp全部加载超时，无打底物料");
                    }
                } else {
                    boolean hasCallBack = false;
                    for (int i = 0; i < dspAds.size(); i++) {
                        // 按返回列表顺序，取最优物料
                        if (returnResult.get(i) != null && !(returnResult.get(i) instanceof NoLoadAd)) {
                            hasCallBack = true;
                            Log.d(TAG, "并行加载超时，集合不为空, 找到最优广告 index =" + i);
                            printBestAd(returnResult.get(i));
                            requestAdCallBack.onAdLoadSuccess(returnResult.get(i));
                            break;
                        }
                    }
                    if (!hasCallBack) {
                        if (firstXmAdFinal != null) {
                            XmRewardVideoAd xmRewardVideoAd = new XmRewardVideoAd(firstXmAdFinal, firstXmAdFinal, "0");
                            Log.d(TAG, "并行加载超时，dsp全部返回失败，返回喜马打底：");
                            printBestAd(xmRewardVideoAd);
                            requestAdCallBack.onAdLoadSuccess(xmRewardVideoAd);
                        } else {
                            Log.d(TAG, "并行加载超时，dsp全部返回失败，无喜马广告打底");
                            requestAdCallBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DSP_LOAD_OVER_TIME, "加载超时无dsp物料，无打底物料");
                        }
                    }
                }
            }
        };
        HandlerManager.postOnUIThreadDelay(overTimeCheck, maxParallelTime);
        for (Advertis dspAd : dspAds) {
            final long startTime = System.currentTimeMillis();
            IMultiThirdRewardAdLoadCallback thirdRewardAdLoadCallback = new IMultiThirdRewardAdLoadCallback() {
                @Override
                public void loadThirdNativeAdFinish(AbstractRewardVideoAd thirdAd, int code, String msg) {
                    RewardVideoReport.reportSdkRequestFinish(dspAd, System.currentTimeMillis() - startTime, (thirdAd != null && !(thirdAd instanceof NoLoadAd)),
                            adStatus.isOverTime, code, msg);
                    long index = -1;
                    if (thirdAd != null && thirdAd.getAdvertis() != null) {
                        index = thirdAd.getAdvertis().getCurAdIndex();
                    }
                    if (!TextUtils.isEmpty(msg)) {
                        Log.d(TAG, "loadThirdNativeAdFinish index = " + index + " thirdAd =" + thirdAd + " isOverTime =" + adStatus.isOverTime + " isCallBackFinish=" + adStatus.isCallBackFinish
                                + " errorMsg =" + msg);
                    } else {
                        Log.d(TAG, "loadThirdNativeAdFinish index = " + index + " thirdAd =" + thirdAd + " isOverTime =" + adStatus.isOverTime + " isCallBackFinish=" + adStatus.isCallBackFinish);
                    }
                    if (adStatus.isOverTime || adStatus.isCallBackFinish) {
                        return;
                    }

                    if (thirdAd != null) {
                        returnResult.put((int)thirdAd.getAdvertis().getCurAdIndex(), thirdAd);
                    } else {
                        returnResult.put((int)dspAd.getCurAdIndex(), new NoLoadAd(dspAd, dspAd, "0"));
                    }
                    if (checkLoadDspTerminal(dspAds, returnResult, requestAdCallBack, adStatus, firstXmAdFinal)) {
                        HandlerManager.removeCallbacks(overTimeCheck);
                    }
                }
            };
            loadSingleDspAd(dspAd, thirdRewardAdLoadCallback, videoAdStatueCallBack, startTime);
        }
    }

    private void loadSingleDspAd(Advertis advertis, IMultiThirdRewardAdLoadCallback callback, IVideoAdStatueCallBackExt videoAdStatueCallBack,  long startTime) {
        int adType = advertis.getAdtype();
        int showStyle = advertis.getShowstyle();
        String dspPositionId = advertis.getDspPositionId();
        Log.d(TAG, "loadSingleDspAd adIndex = "+ advertis.getCurAdIndex() + " adId =" + advertis.getAdid());
        int dspAdType = AdUnlockUtil.getDspAdType(adType, showStyle);
        if (dspAdType == -1) {
            // 类型不匹配
            Log.d(TAG, "loadSingleDspAd error 类型不匹配");
            callback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "dspAdType 类型不匹配");
            return;
        }
        if (TextUtils.isEmpty(dspPositionId)) {
            // dsp positionId为空
            Log.d(TAG, "loadSingleDspAd error dspPositionId为空");
            callback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "dspPositionId为空");
            return;
        }
        RewardVideoReport.reportSdkRequestStart(advertis, startTime);
        if (AdManager.isGdtAd(adType)) {
            GDTRewardVideoAdUtil.loadRewardVideoNew(advertis, dspPositionId, callback, videoAdStatueCallBack);
        } else if (AdManager.isCSJAd(adType)) {
            switch (dspAdType) {
                case AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_NO_TEMPLATE:
                case AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadRewardVideoNew(advertis, dspPositionId, dspAdType == AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_TEMPLATE, callback);
                    break;
                case AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_TEMPLATE:
                case AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_NO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadFullScreenVideoNew(advertis, dspPositionId, dspAdType == AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_TEMPLATE, callback);
                    break;
                case AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadDrawTemplateVideoAd(advertis, dspPositionId, callback);
                    break;
                case AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_NO_TEMPLATE:
                    CSJRewardVideoAdUtil.loadDrawVideoNoTemplateAd(advertis, dspPositionId, callback);
                    break;
                default:
                    if(callback != null) {
                        callback.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, dspPositionId), IVideoAdStatueCallBack.ERROR_CODE_DEFUALT, "dspAdType 类型不匹配");
                    }
                    break;
            }
        } else if (AdManager.isBaiduAd(adType)){
            AdAdapterUtil.obtainSDKManagerAsync(IMediationInfo.BAIDU, new AdAdapterUtil.OnSDKManagerReadyCallback() {
                @Override
                public void onReady(BaseAdSDKManager sdkManager) {
                    if (sdkManager == null) {
                        return;
                    }
                    if (adType == AD_SOURCE_BAIDU_REWARD_TEMP) {
                        sdkManager.loadRewardVideoTempAdNew(advertis, dspPositionId, true,videoAdStatueCallBack, callback);
                    } else {
                        sdkManager.loadNativeAdForReward(advertis, callback, dspPositionId);
                    }
                }
            });
        }
    }

    /**
     * 是否需要提前结束并行加载
     * @param dspAds
     * @param returnResult
     * @param rewardAdLoadCallBack
     * @param adStatus
     * @return
     */
    private boolean checkLoadDspTerminal(List<Advertis> dspAds, ConcurrentHashMap<Integer, AbstractRewardVideoAd> returnResult,
                                        IRewardAdLoadCallBack rewardAdLoadCallBack, LoadAdCallBackStatus adStatus, Advertis firstXmAd) {
        Log.d(TAG, "checkLoadDspTerminal");
        if (returnResult.isEmpty()) {
            return false;
        }
        int allAdCount = dspAds.size();
        if (returnResult.size() == allAdCount) {
            Log.d(TAG, "checkLoadDspTerminal dsp已经全部请求返回");
            adStatus.isCallBackFinish = true;
            // dsp已经全部加载完成,从前往后取加载成功的最高价广告
            boolean hasCallBack = false;
            for (int i = 0; i < allAdCount; i++) {
                if (returnResult.get(i) != null && !(returnResult.get(i) instanceof NoLoadAd)) {
                    hasCallBack = true;
                    Log.d(TAG, "checkLoadDspTerminal dsp已经全部请求返回 找到最优广告 index =" + i);
                    printBestAd(returnResult.get(i));
                    rewardAdLoadCallBack.onAdLoadSuccess(returnResult.get(i));
                    break;
                }
            }
            if (!hasCallBack) {
                if (firstXmAd != null) {
                    XmRewardVideoAd rewardVideoAd = new XmRewardVideoAd(firstXmAd, firstXmAd, "0");
                    Log.d(TAG, "checkLoadDspTerminal dsp已经全部请求返回 全部失败 最优广告为喜马广告");
                    printBestAd(rewardVideoAd);
                    rewardAdLoadCallBack.onAdLoadSuccess(rewardVideoAd);
                } else {
                    Log.d(TAG, "checkLoadDspTerminal dsp已经全部请求返回 全部失败 无喜马广告打底");
                    rewardAdLoadCallBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_DSP_LOAD_OVER_TIME, "dsp全部加载完成无有效物料，无打底物料");
                }
            }
            return true;
        }
        AbstractRewardVideoAd firstSuccessAd = null;
        for (int i = 0; i < allAdCount; i++) {
            if (returnResult.get(i) == null) {
                break;
            }
            if (returnResult.get(i) != null & !(returnResult.get(i) instanceof NoLoadAd)) {
                firstSuccessAd = returnResult.get(i);
                break;
            }
        }
        if (firstSuccessAd != null) {
            // firstSuccessAd之前的广告都已返回，并且失败，后续广告无需等待，firstSuccessAd肯定为最优广告
            adStatus.isCallBackFinish = true;
            Log.d(TAG, "checkLoadDspTerminal firstSuccessAd之前的广告都已返回，并且失败，后续广告无需等待，firstSuccessAd肯定为最优广告");
            printBestAd(firstSuccessAd);
            rewardAdLoadCallBack.onAdLoadSuccess(firstSuccessAd);
            return true;
        }
        Log.d(TAG, "checkLoadDspTerminal 继续等待");
        return false;
    }

    public void showRewardVideoAdReal(AbstractRewardVideoAd rewardVideoAd, @NonNull Activity activity, @NonNull RewardExtraParams extraParams, @NonNull IVideoAdStatueCallBack callback) {
        if (rewardVideoAd == null || rewardVideoAd.getAdvertis() == null) {
            return;
        }
        Log.d(TAG, "showRewardVideoAdReal 展示广告：");
        printBestAd(rewardVideoAd);
        Advertis advertis = rewardVideoAd.getAdvertis();
        callback = new VideoAdStatueCallBackWrapper(activity, callback, extraParams.getRewardCountDownStyle());

        mExtraParams = extraParams;

        extraParams.setAdType(advertis.getAdtype());
        mCurrentAdvertis = advertis;
        mStatueCallBackWeakReference = new WeakReference<>(callback);

        if(rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_XM || rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_BAIDU_NATIVE) {
            if (rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_BAIDU_NATIVE){
                AdUnlockUtil.resetShowStyleWhenSdkMerge(extraParams, rewardVideoAd);
            }
            if (AdUnlockUtil.isWebAd(extraParams)) {
                startWebFragment(callback, extraParams);
            } else if (RewardExtraParams.isFreeListen(extraParams.getRewardCountDownStyle()) || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO) {
                startVideoFragment(callback, extraParams);
            } else {
                VideoAdActivity.startVideoAdActivity(createRequestKey(callback), extraParams);
            }
        } else if (rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_CSJ_DRAW_VIDEO || rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_CSJ_EXPRESS_DRAW_VIDEO) {
            // 穿山甲draw信息流广告
            CSJDrawAdActivityParallel.startCSJDrawAdActivity(rewardVideoAd, activity, createRequestKey(callback), rewardVideoAd.getType() == AbstractThirdAd.THIRD_AD_CSJ_EXPRESS_DRAW_VIDEO);
        } else {
            rewardVideoAd.showRewardVideoAd(activity, extraParams, callback);
        }
    }

    private void printAdList(List<Advertis> advertisList) {
        Log.d(TAG,  "printAdList start");
        for (Advertis advertis : advertisList) {
            Log.d(TAG, "adIndex = " + advertis.getCurAdIndex() + " ， adId = " + advertis.getAdid() + " , adType = " + advertis.getAdtype());
        }
        Log.d(TAG,  "printAdList end");
    }

    private void printBestAd(AbstractRewardVideoAd rewardVideoAd) {
        if (rewardVideoAd == null) {
            return;
        }
        Advertis advertis  =rewardVideoAd.getAdvertis();
        if (advertis == null) {
            return;
        }
        Log.d(TAG, "bestAd:" +  " index =" + advertis.getCurAdIndex() + " adId=" +  advertis.getAdid() + " adType=" + advertis.getAdtype());
    }

    public interface  IRewardAdLoadCallBack {
        void onAdLoadSuccess(AbstractRewardVideoAd rewardVideoAd);
        void onAdLoadError(int code, String message);
    }

    public interface IMultiThirdRewardAdLoadCallback {
        //广告加载完成，成功或者失败被回调
        void loadThirdNativeAdFinish(AbstractRewardVideoAd thirdAd, int errorCode, String msg);
    }

    /**
     * 广告回调状态记录
     */
    private static class LoadAdCallBackStatus {
        //是否已经回调完成
        boolean isCallBackFinish = false;
        //是否超时
        boolean isOverTime = false;

        long requestTime;

        @NonNull
        @Override
        public String toString() {
            return "NativeAdCallBackStatus{" +
                    "isCallBackFinish=" + isCallBackFinish +
                    ", isOverTime=" + isOverTime +
                    ", requestTime=" + requestTime +
                    '}';
        }
    }
}
