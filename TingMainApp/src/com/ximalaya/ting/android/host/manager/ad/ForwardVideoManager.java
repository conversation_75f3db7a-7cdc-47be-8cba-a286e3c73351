package com.ximalaya.ting.android.host.manager.ad;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.adsdk.AdSDK;
import com.ximalaya.ting.android.adsdk.external.IRewardVideoAdListener;
import com.ximalaya.ting.android.adsdk.external.XmLoadAdParams;
import com.ximalaya.ting.android.adsdk.external.XmRewardExtraParam;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.ad.videoad.IRewardAdFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardGiveUpHintDialogNew;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.XVideoAutoPlayTipsDialog;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.statistic.FreeAdTimeManager;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.loginservice.BaseResponse;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by le.xin on 2020/5/7.
 * 前插视频广告
 * <AUTHOR>
 * @email <EMAIL>
 */
public class ForwardVideoManager {

    private static volatile ForwardVideoManager mInstance;

    public static boolean pausePlayByForwardVideo = false;
    public static boolean isEnterForwardVideo = false;

    private boolean isRequestHooking = false;
    private boolean isRunningHook = false;

    private int requestCount = 0;

    private boolean canGetReward = false; // 是否可以获得奖励，倒计时结束或者视频完播
    private boolean isRealFinishTask = false;// 是否真正完成任务获得奖励而非异常兜底
    private boolean isGetReward = false; // 是否已经获取过免广告权益
    private int getRewardTime;
    private String rewardTips = "您已获得免除%s分钟免广告打扰权益";
    private boolean isUseSdkReward = true;

    private final  static  String IS_SHOWED_AUTO_PLAY_TIPS_DIALOG = "is_showed_auto_play_tips_dialog"; //是否展示 自动播放tips 弹窗

    private WeakReference<IRewardVideoCallBack> mWeakReferenceCallBack;
    private String mBenefitTip;

    private  IVideoAdStatueCallBack mIVideoAdStatueCallBack;

    private Advertis mCurAdvertis;
    private RewardExtraParams currentExtraParams;

    private ForwardVideoManager() {
        JSONObject json = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITME_AD_FORWARD_VIDEO_CONFIG_NEW);
        if (json != null && json.has("rewardToastTips")) {
            rewardTips = json.optString("rewardToastTips");
        } else {
            rewardTips = "您已获得免除%s分钟免广告打扰权益";
        }
    }

    public static ForwardVideoManager getInstance() {
        if (mInstance == null) {
            synchronized (ForwardVideoManager.class) {
                if (mInstance == null) {
                    mInstance = new ForwardVideoManager();
                }
            }
        }
        return mInstance;
    }

    public static boolean isNotifyVideoClose = false;

    public void notifyVideoClose(Activity activity) {
        if (isAutoPlayForwardVideo(mCurAdvertis, mBenefitTip)) {

            Logger.i("-msg_video_manager", " ------ video manager 激励视频 --- onActivityPaused 了, 通知关闭激励视频 当前的是自动播放的激励视频， 闭屏了，关闭激励视频 ");
            if (mIVideoAdStatueCallBack != null) {
                mIVideoAdStatueCallBack.onAdClose(true);
            }
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    if (activity != null) {
                        Logger.i("-msg_video_manager", " ------ video manager 激励视频 --- 闭屏了，关闭激励视频 111   activity.isFinishing() = " + activity.isFinishing());
                        Logger.i("--msg_video_manager", " ------ video manager 激励视频 --- 闭屏了，关闭激励视频 222   activity.isDestroyed() = " + activity.isDestroyed());
                    }
                    if (activity != null && !activity.isFinishing()) {
                        finishAdActivityOrFragment(activity);
                        isNotifyVideoClose = true;
                    }
                }
            }, 25);
        }
    }

    public interface IRewardVideoCallBack {
        void onAdCloseByUse(boolean isGetReward, Advertis advertis);
        void onAdError(int code ,String message);
        void onRewardAdVideoComplete();
        void onAdPlayCompleteAndBack();
        void onGetReward();
    }

    public void lookVideo(Activity activity ,int showSource, String benefitTip,IRewardVideoCallBack callBack, Advertis advertis) {
        lookVideo(activity, showSource,benefitTip, callBack, new RetryModel(0), advertis);
    }

    private static class RetryModel {
        public RetryModel(int curAdIndex) {
            this.curAdIndex = curAdIndex;
        }

        int curAdIndex;
    }

    public void unLockBySdkTest(Activity activity) {
        AdSDKManager.init(ToolUtil.getCtx());
        XmLoadAdParams xmLoadAdParams = new XmLoadAdParams("250");
        xmLoadAdParams.setNeedToRecordShowOb(false);

        XmRewardExtraParam rewardExtraParam = new XmRewardExtraParam();
        rewardExtraParam.setCanCloseTime(5);// 可提前关闭时间
        AdSDK.getInstance().loadRewardVideoAd(activity, MainApplication.getMyApplicationContext(), xmLoadAdParams, rewardExtraParam,
                new IRewardVideoAdListener() {
                    @Override
                    public void onAdLoad(HashMap adInfoMap) {
                        CustomToast.showFailToast(
                                "onAdLoad",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onAdPlayStart() {
                        CustomToast.showFailToast(
                                "onAdPlayStart",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onAdClick() {
                        CustomToast.showFailToast(
                                "onAdClick",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onReward(boolean realFinishTask) {
                        CustomToast.showFailToast(
                                "onRewardSuccess",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onVideoComplete() {
                        CustomToast.showFailToast(
                                "onVideoComplete",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onAdClose() {
                        CustomToast.showFailToast(
                                "onAdClose",
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onAdPlayError(int i, String s) {
                        CustomToast.showFailToast(
                                "onAdPlayError code = " + i + " msg =" + s,
                                Toast.LENGTH_LONG);
                    }

                    @Override
                    public void onLoadError(int i, String s) {
                        CustomToast.showFailToast(
                                "onLoadError code = " + i + " msg =" + s,
                                Toast.LENGTH_LONG);
                    }
                });

    }

    private void lookVideo(Activity activity, int showSource,String benefitTip, @Nullable IRewardVideoCallBack callBack, RetryModel retryModel, Advertis advertis) {
//        if (isUseSdkReward) {
//            unLockBySdkTest(activity);
//            return;
//        }
        List<Advertis> mTempAdvertisList = XmPlayerManager.getInstance(activity).getForwardAdvertis();
        if(ToolUtil.isEmptyCollects(mTempAdvertisList)) {
            ToolUtil.throwIllegalNoLogicException();
            return;
        }

        if(retryModel.curAdIndex >= mTempAdvertisList.size()) {
            hookForwardVideo(mCurAdvertis, showSource, benefitTip, false, callBack, new WeakReference<>(activity));
            return;
        }

        Advertis mTempAdvertis = mTempAdvertisList.get(retryModel.curAdIndex);
        // 激励视频广告不支持视频拼接落地页
        mTempAdvertis.setEnableContinuePlay(false);
        if (mTempAdvertis.getLinkType() == Advertis.LINK_TYPE_FULL_VIDEO_WEBVIEW || mTempAdvertis.getLinkType() == Advertis.LINK_TYPE_IMMERSIVE_VIDEO_WEBVIEW
                || mTempAdvertis.getLinkType() == Advertis.LINK_TYPE_VIDEO_WEBVIEW_NEW) {
            mTempAdvertis.setLinkType(Advertis.LINK_TYPE_WEB);
        }

        String posId = AdManager.getDspPositionId(mTempAdvertis, AppConstants.AD_POSITION_NAME_FORWARD_VIDEO);

        int adType = mTempAdvertis.getAdtype();

        RewardExtraParams extraParams = new RewardExtraParams();
        currentExtraParams = extraParams;
        extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO);

        mWeakReferenceCallBack = new WeakReference<>(callBack);
        mBenefitTip = benefitTip;
        isHookReport = false;
        isNotifyVideoClose = false;
        canGetReward = false;
        isRealFinishTask = false;
        isGetReward  = false;
        isRunningHook  = false;
        isEnterForwardVideo = false;
        mCurAdvertis = mTempAdvertis;
        extraParams.setXmVideoAdvertisModel(mCurAdvertis, AppConstants.AD_POSITION_NAME_FORWARD_VIDEO);
        extraParams.setRewardTime(mTempAdvertis.getIncreaseFreeTime());
        getRewardTime = mTempAdvertis.getForwardVideoTime(); // xx秒后可获得奖励
        extraParams.setCanCloseTime(getRewardTime);
        Logger.d("-------msg", " ------ getRewardTime = " + getRewardTime);
        Runnable getRewardRunnable = new Runnable() {
            @Override
            public void run() {
                canGetReward = true;
                isRealFinishTask = true;
            }
        };
        if (mIVideoAdStatueCallBack != null) {
            mIVideoAdStatueCallBack = null;
        }

        mIVideoAdStatueCallBack = new IVideoAdStatueCallBack() {
            boolean isPlaying;

            Dialog mDialog;

            long beginTime;

            boolean isClosed;

            WeakReference<Activity> mActivityWeakReference;

            public boolean isClosed() {
                return isClosed;
            }

            @Override
            public void onAdLoad(AbstractThirdAd thirdAd) {
                beginTime = System.currentTimeMillis();
                if (getRewardTime > 0) {
                    HandlerManager.postOnUIThreadDelay(getRewardRunnable, getRewardTime * 1000);
                }

                isEnterForwardVideo = true;
                isPlaying = XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).isPlaying();

                if (isPlaying) {
                    pausePlayByForwardVideo = true;
                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).pause(PauseReason.Business.ForwardVideo);
                }

                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        mTempAdvertis, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                .promptPlay("0")
                                .benefitTip(benefitTip)
                                .showSource(showSource + "")
                                .ignoreTarget(true).build());
            }

            @Override
            public void onAdLoadError(int code, String message) {
                Logger.i("-------msg_video", "onAdLoadError code=" + code + "msg=" + message);
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        mTempAdvertis, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                .promptSuc("1")
                                .benefitTip(benefitTip)
                                .showSource(showSource + "")
                                .ignoreTarget(true).build());
                if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                    // 加载超时，直接发放奖励
                    canGetReward  = true;
                    isRealFinishTask = false;
                    HandlerManager.removeCallbacks(getRewardRunnable);
                    onAdClose(false);
                } else {
                    // 重试
                    retryModel.curAdIndex++;
                    lookVideo(activity, showSource, benefitTip, callBack, retryModel, mCurAdvertis);
                }
            }

            @Override
            public void onAdPlayStart() {
                // 展示自动播放激励视频提示弹窗
                if (!MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getBooleanCompat(IS_SHOWED_AUTO_PLAY_TIPS_DIALOG, false)) {
                    if (AdManager.isAutoPlayForwardVideoAd(mCurAdvertis)) {
                        if (mActivityWeakReference == null || mActivityWeakReference.get() == null) {
                            Logger.e("-------msg_video", " ---------- 展示自动播放激励视频提示弹窗 失败 ------ mActivityWeakReference = " + mActivityWeakReference);
                            return;
                        }

                        Logger.i("-------msg_video", " ---------- 展示自动播放激励视频提示弹窗 ------ ");
                        XVideoAutoPlayTipsDialog autoPlayTipsDialog = new XVideoAutoPlayTipsDialog(mActivityWeakReference.get());
                        if (mTempAdvertis != null) {
                            autoPlayTipsDialog.setShowTimeInfo(mTempAdvertis.getIncreaseFreeTime(), mTempAdvertis.getForwardVideoTime());
                        }
                        autoPlayTipsDialog.setCancelHandle(new IHandleOk() {
                            @Override
                            public void onReady() {
                                if (callBack != null) {
                                    callBack.onAdCloseByUse(false, mCurAdvertis);
                                }
                                AdManager.adRecord(mActivityWeakReference.get(), mTempAdvertis, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                        .skipAd("0")
                                        .benefitTip(benefitTip)
                                        .setExitPopupType(2)
                                        .showSource(showSource + "")
                                        .ignoreTarget(true).build());
//                                        onAdClose(true);
//                                        mActivityWeakReference.get().finish();
                                onCloseViewClick(mActivityWeakReference.get(), null);
                            }
                        });

                        autoPlayTipsDialog.setOkHandle(new IHandleOk() {
                            @Override
                            public void onReady() {
                                AdManager.adRecord(mActivityWeakReference.get(), mTempAdvertis, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                        .skipAd("0")
                                        .benefitTip(benefitTip)
                                        .setExitPopupType(1)
                                        .showSource(showSource + "")
                                        .ignoreTarget(true).build());
                            }
                        });
                        autoPlayTipsDialog.setAutoCloseHandel(new IHandleOk() {
                            @Override
                            public void onReady() {
                                AdManager.adRecord(mActivityWeakReference.get(), mTempAdvertis, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                        .skipAd("0")
                                        .benefitTip(benefitTip)
                                        .setExitPopupType(3)
                                        .showSource(showSource + "")
                                        .ignoreTarget(true).build());
                            }
                        });
                        autoPlayTipsDialog.show();
                        AdManager.adRecord(mActivityWeakReference.get(), mTempAdvertis, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                .skipAd("0")
                                .benefitTip(benefitTip)
                                .showSource(showSource + "")
                                .setExitPopupType(0)
                                .ignoreTarget(true).build());
                        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveBoolean(IS_SHOWED_AUTO_PLAY_TIPS_DIALOG, true);
                    }
                }
                HandlerManager.removeCallbacks(mRunnable);
                if (beginTime <= 0) {
                    beginTime = System.currentTimeMillis();
                }
                // 这里又一次暂停声音是为了解决有声视频贴片被暂停后，专辑声音得到焦点又起播，导致和激励视频声音重叠的问题
                if (XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).isPlaying()) {
                    isPlaying = true;
                    pausePlayByForwardVideo = true;
                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).pause(PauseReason.Business.ForwardVideo);
                }

            }

            @Override
            public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                AdManager.handlerAdClick(MainApplication.getMyApplicationContext(),
                        mTempAdvertis,
                        AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
//                                .ignoreTarget(true)
//                                .onlyClickRecord(true)
                                .showSource(showSource + "")
                                .build());
            }

            @Override
            public void onAdClose(boolean isCustomCloseBtn) {
                // 广告activity自动finish(不包括广点通)、手动点击了关闭按钮的时候都会调用此处
                // 关闭激励视频， 停止计时
                HandlerManager.removeCallbacks(getRewardRunnable);
                HandlerManager.removeCallbacks(mRunnable);
                if (canGetReward) {
                    hookForwardVideo(mTempAdvertis, showSource, benefitTip, false, callBack, mActivityWeakReference);
                } else {
                    if (mActivityWeakReference != null) {
                        finishAdActivityOrFragment(mActivityWeakReference.get());
                    }
                    if (callBack != null) {
                        callBack.onAdCloseByUse(false, mCurAdvertis);
                    }
                    CustomToast.showToast("您已放弃免广告权益");
                }
                replay(isPlaying);
            }

            @Override
            public void onAdPlayComplete() {
                Logger.i("--------msg", " -------- hookForwardVideo 333333 isGetReward = " + isGetReward + ", isRunningHook = " + isRunningHook);
                canGetReward  = true;
                isRealFinishTask = true;

                if (mDialog != null) {
                    mDialog.dismiss();
                    mDialog = null;
                }

                HandlerManager.postOnMainAuto(new Runnable() {
                    @Override
                    public void run() {
                        if (callBack != null) {
                            callBack.onRewardAdVideoComplete();
                        }
                    }
                });
                if (mCurAdvertis != null && !AdManager.isThirdAd(mCurAdvertis) && mCurAdvertis.isVideoAutoJump()) {
                    return; // 完播之后自动跳转，不需要自动关闭
                }
                // 完播之后xxs后自动关闭
                int anInt =
                        ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME,
                                CConstants.Group_ad.ITEM_FORWARD_VIDEO_ENDCARD_TIME, 3);
                Logger.log("----msg ForwardVideoManager : ForwardvideoEndcardtime 需要停留的时间是 " + anInt);
                HandlerManager.postOnUIThreadDelay(mRunnable, anInt * 1000);
            }

            private Runnable mRunnable = new Runnable() {
                @Override
                public void run() {
                    onAdClose(false);
                }
            };

            @Override
            public void onAdPlayError(int code, String message) {
                Logger.i("-------msg_video", "onAdPlayError code=" + code + "msg=" + message);
                canGetReward  = true;
                isRealFinishTask = false;
                HandlerManager.removeCallbacks(getRewardRunnable);
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        mTempAdvertis, AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SHOW_OB,
                                AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                .promptSuc("1")
                                .showSource(showSource + "")
                                .ignoreTarget(true).build());
                if (!AdManager.isThirdAd(mCurAdvertis)) {
                    onAdClose(false);
                }
            }

            @Override
            public View.OnClickListener getCloseClickListener(Activity myActivity) {
                mActivityWeakReference = new WeakReference<>(myActivity);
                return new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCloseViewClick(myActivity, v);
                    }
                };
            }

            private void onCloseViewClick(Activity myActivity, View clickView) {
                if (!ToolUtil.activityIsValid(myActivity)) {
                    return;
                }
                if (clickView != null && clickView.getId() == R.id.host_reward_open_vip) {
                    // 跳转vip
                    isClosed = true;
                    finishAdActivityOrFragment(myActivity);
                    HandlerManager.removeCallbacks(getRewardRunnable);
                    HandlerManager.removeCallbacks(mRunnable);
                    if (callBack != null) {
                        callBack.onAdCloseByUse(false, mCurAdvertis);
                    }
                    replay(isPlaying);
                    return;
                }
                Logger.d("-------msg", " -------- getCloseClickListener click  isGetReward = " + isGetReward);
                if (canGetReward) {
                    onAdClose(true);
                } else {
                    // 展示挽留弹窗
                    RewardGiveUpHintDialogNew rewardGiveUpHintDialog = new RewardGiveUpHintDialogNew(myActivity);
                    rewardGiveUpHintDialog.setCancelHandle(new IHandleOk() {
                        @Override
                        public void onReady() {
                            if (!canGetReward) {
                                AdManager.adRecord(myActivity, mTempAdvertis, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                        .skipAd("1")
                                        .benefitTip(benefitTip)
                                        .showSource(showSource + "")
                                        .skipTime((System.currentTimeMillis() - beginTime) + "")
                                        .ignoreTarget(true).build());
                            }
                            onAdClose(true);
                        }
                    });
                    rewardGiveUpHintDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                        @Override
                        public void onDismiss(DialogInterface dialog) {
                            mDialog = null;
                        }
                    });
                    rewardGiveUpHintDialog.show();
                    extraParams.setVipFreeCloseAlertDialog(rewardGiveUpHintDialog);
                    mDialog = rewardGiveUpHintDialog;
                }
                AdManager.adRecord(myActivity, mTempAdvertis, AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_CLICK_OB,
                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                        .skipAd("0")
                        .benefitTip(benefitTip)
                        .showSource(showSource + "")
                        .skipTime((System.currentTimeMillis() - beginTime) + "")
                        .ignoreTarget(true).build());
            }
        };

        RewardVideoAdManager.getInstance().loadRewardAd(activity, mTempAdvertis,
                posId, adType,extraParams, mIVideoAdStatueCallBack);
    }

    // 结束广告相关的三方activity或者fragment
    private void finishAdActivityOrFragment(Activity adActivity) {
        if (currentExtraParams != null) {
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            try {
                XmBaseDialog closeDialog = currentExtraParams.getVipFreeCloseAlertDialog();
                if (closeDialog != null && closeDialog.isShowing()) {
                    closeDialog.dismiss();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            currentExtraParams = null;
        }
        if (!ToolUtil.activityIsValid(adActivity)) {
            return;
        }
        if (adActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) adActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof IRewardAdFragment) {
                ((IRewardAdFragment) currentFragment).finish();
            }
        } else {
            adActivity.finish();
        }
    }

    private void replay(boolean isPlaying) {

        if (isPlaying) {
            HandlerManager.postOnUIThreadDelay(new Runnable() {
                @Override
                public void run() {
                    pausePlayByForwardVideo = false;
                    XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).play();
                }
            }, 800);
        }
    }

    private void hookForwardVideo(Advertis advertis, int showSource, String benefitTip, boolean isRetry, @Nullable IRewardVideoCallBack callBack,
                                  WeakReference<Activity>  mActivityWeakReference) {

        Logger.v("--------msg", " -------- hookForwardVideo  requestCount = " + (requestCount ++) + "  , isRunningHook = " + isRunningHook + " , isRequestHooking = " + isRequestHooking);
        isRunningHook = true;
        if(isRequestHooking) {
            Logger.e("--------msg return", " -------- hookForwardVideo  isRequestHooking = " + isRequestHooking);
            return;
        }
        if (isGetReward) {
            Logger.e("--------msg return", " -------- hookForwardVideo  isGetReward = " + isGetReward);
            // 确保异常情况下，广告会关闭
            if (mActivityWeakReference != null) {
                finishAdActivityOrFragment(mActivityWeakReference.get());
            }
            return;
        }
        isRequestHooking = true;

        AdManager.postRecord(new Runnable() {
            @Override
            public void run() {
                Context context =
                        MainApplication.getMyApplicationContext();
                Map<String ,String> maps = new HashMap<>();
                maps.put("adId" ,advertis.getAdid() + "");
                JSONObject json =
                        ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                                CConstants.Group_ad.ITEM_FORWARD_VIDEO_CONFIG);
                int freeTime = 3600;
                int expireTime = 86400;
                if(json != null) {
                    freeTime = json.optInt("limitTime" ,freeTime);
                    expireTime = json.optInt("expireTime" ,86400);
                }
                int increaseFreeTime = advertis.getIncreaseFreeTime(); // 服务端下发的累加时长
                if (increaseFreeTime > 0) {
                    freeTime = increaseFreeTime * 60; // increaseFreeTime 单位 是分钟
                }
                int totalFreeTime; // 播放时长模式下剩余的免广时长
                if (!advertis.isUseNatureTime()) {
                    int preLimitTime = FreeAdTimeManager.getLimitTime(context); // 未使用完的免广告时长
                    totalFreeTime = freeTime + preLimitTime; // 总的免广告时长
                } else {
                    totalFreeTime = 0;
                }
                maps.put("freeTime" , totalFreeTime + "");
                advertis.setShowTokenEnable(true);
                maps.put("token" , AdTokenManager.getInstance().getShowToken(advertis));
                maps.put("increaseFreeTime", advertis.getIncreaseFreeTime() + "");
                EncryptUtil.getInstance(context).getPlaySignature(context ,maps);
                maps.put("fallbackReq", isRealFinishTask ? "0" : "1");
                maps.put("positionName", AppConstants.AD_POSITION_NAME_FORWARD_VIDEO);

                int tempExpireTime = expireTime;
                int tempFreeTime = totalFreeTime;
                AdRequest.hookForwardVideo(maps, new IDataCallBack<BaseResponse>() {
                    @Override
                    public void onSuccess(@Nullable BaseResponse object) {
                        reportPullForwardVideo(advertis, "获取到了权益了， 上报");
                        isGetReward = true; // 已经获取了权益
                        isRequestHooking = false;
                        if (object != null && object.getRet() == 0) {
//                                CustomToast.showToast(MainApplication.getTopActivity(), String.format(rewardTips, advertis.getIncreaseFreeTime()), Toast.LENGTH_LONG);
                            if (!advertis.isUseNatureTime()) {
                                FreeAdTimeForMainProcessManager.onLookedAdTime(context, tempFreeTime, tempExpireTime);
                            }
                            Logger.i("---------msg", " -------- 获取免广告成功回调 -----   ");
                            if (callBack != null) {
                                callBack.onGetReward();
                                callBack.onAdCloseByUse(true, advertis);
                                // 播放成功 回调成功
                                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                        advertis, AdReportModel.newBuilder(
                                                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                                                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                                                .promptSuc("0")
                                                .benefitTip(benefitTip)
                                                .showSource(showSource + "")
                                                .ignoreTarget(true).build());
                            }

                            if (mActivityWeakReference != null) {
                                finishAdActivityOrFragment(mActivityWeakReference.get());
                            }
                        } else {
//                              http://thoughts.ximalaya.com/workspaces/5ceab64fbe825bee8c1338af/docs/5ea675bdf6aaaf0001fb9d66
                            // 播放成功 回调失败
                            playSuccessFreeAdFail(advertis, showSource, benefitTip);
                            if (callBack != null) {
                                callBack.onAdCloseByUse(false, advertis);
                            }
                            if (mActivityWeakReference != null) {
                                finishAdActivityOrFragment(mActivityWeakReference.get());
                            }
                            HandlerManager.postOnUIThreadDelay(new Runnable() {
                                @Override
                                public void run() {
                                    if (TextUtils.isEmpty(object.getMsg())) {
                                        CustomToast.showFailToast("免广告失败, 请稍后重试");
                                    } else {
                                        CustomToast.showFailToast(object.getMsg());
                                    }
                                }
                            }, 500);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        isRequestHooking = false;

                        if(isRetry || code == 701) {
                            playSuccessFreeAdFail(advertis, showSource, benefitTip);

                            if (callBack != null) {
                                callBack.onAdCloseByUse(false, advertis);
                            }
                            if(mActivityWeakReference != null) {
                                finishAdActivityOrFragment(mActivityWeakReference.get());
                            }
                            HandlerManager.postOnUIThreadDelay(new Runnable() {
                                @Override
                                public void run() {
                                    if (TextUtils.isEmpty(message)) {
                                        CustomToast.showFailToast("免广告失败, 请稍后重试");
                                    } else {
                                        CustomToast.showFailToast(message);
                                    }
                                }
                            }, 500);
                        } else {
                            Logger.i("--------msg", " -------- hookForwardVideo 44444");
                            hookForwardVideo(advertis, showSource, benefitTip,true, callBack, mActivityWeakReference);
                        }
                    }
                });
            }
        });
    }

    private void playSuccessFreeAdFail(Advertis mTempAdvertis, int showSource, String benefitTip) {
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                mTempAdvertis , AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                        AppConstants.AD_POSITION_NAME_FORWARD_VIDEO)
                        .promptSuc("2")
                        .showSource(showSource + "")
                        .benefitTip(benefitTip)
                        .ignoreTarget(true).build());
    }

    public static void addCommonRequestParams(Map<String ,String> requestMap , Context mContext) {
        addCommonRequestParams(requestMap, mContext, FreeAdTimeManager.getLimitTime(mContext));
    }

    public static void addCommonRequestParams(Map<String ,String> requestMap , Context mContext ,int freeTime) {
        Map<String ,String> freeMap = new HashMap<>();
        freeMap.put("freeTime" ,freeTime + "");
        freeMap.put("deviceId" , DeviceUtil.getDeviceToken(mContext));
        freeMap.put("xt" ,System.currentTimeMillis() + "");
        EncryptUtil.getInstance(mContext).getPlaySignature(mContext ,freeMap);
        if(requestMap != null) {
            freeMap.remove("deviceId");
            requestMap.putAll(freeMap);
        }
    }

    public boolean isAutoPlayForwardVideo(Advertis advertis, String benefitTip) {
        try {
            return IAdConstants.IRemoveAdHintBenefit.REMOVE_AD_HINT_BENEFIT_AUTO_PLAY.equals(benefitTip) && AdManager.isAutoPlayForwardVideoAd(advertis);
        } catch (Exception e) {
            e.printStackTrace();
        }
       return false;
    }

    private boolean isHookReport = false;

    private void reportPullForwardVideo(Advertis advertis, String fromTag) {

        if (isAutoPlayForwardVideo(advertis, mBenefitTip)) {
            if (isHookReport) {
                Logger.e("--------msg_video_report", " -------- reportPullForwardVideo  已经上报过了， 不再上报了" );
                return;
            }
            if (advertis == null) {
                Logger.e("--------msg_video_report", " -------- advertis == null , 拦截 return" );
                return;
            }
            isHookReport = true;

            AdManager.postRecord(new Runnable() {
                @Override
                public void run() {
                    Context context =
                            MainApplication.getMyApplicationContext();
                    Map<String ,String> maps = new HashMap<>();
                    maps.put("reportType", isGetReward ? "1" : "0");
                    maps.put("deviceId", DeviceUtil.getDeviceToken(context));
                    maps.put("xt", System.currentTimeMillis() + "");
                    EncryptUtil.getInstance(context).getPlaySignature(context ,maps);

                    AdRequest.hookReportPullForwardVideo(maps, new IDataCallBack<BaseResponse>() {
                        @Override
                        public void onSuccess(@Nullable BaseResponse object) {
                        }

                        @Override
                        public void onError(int code, String message) {
                        }
                    });
                }
            });
        } else {
            Logger.v("--------msg_video_report", " -------- reportPullForwardVideo 不上报，  因为这个是 非自动拉起的激励视频 -->" + fromTag);
        }
    }
}
