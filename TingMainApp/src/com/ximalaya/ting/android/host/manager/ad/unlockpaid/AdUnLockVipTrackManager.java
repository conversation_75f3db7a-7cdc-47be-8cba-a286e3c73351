package com.ximalaya.ting.android.host.manager.ad.unlockpaid;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdTokenManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBackExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoTaskUtil;
import com.ximalaya.ting.android.host.manager.ad.videoad.VideoAdFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree3;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenConstant;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenHostManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdStateData;
import com.ximalaya.ting.android.host.model.ad.AdUnLockAdvertisModel;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.ad.VideoUnLockResult;
import com.ximalaya.ting.android.host.model.free.UnlockResult;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.view.dialog.FreeUnlockCommonDialog;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmlog.XmLogger;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 全站畅听，看视频广告解锁会员畅听权益
 * 产品方案（https://thoughts.ximalaya.com/workspaces/5e5df0fa2d54160012c3ebc5/docs/607e81331c496100013c8372）
 */
public class AdUnLockVipTrackManager {
    public static int getNewestVipFreeType() {
        return TYPE_TEST_VIP_FREE_5;
    }

    private static final String TAG = "AdUnLockVipTrackManager";
    public static final long ONE_DAY_TIME = 24 * 60 * 60 * 1000;
    public static final int UNLOCK_REPEAT_ERROR_CODE = 710; // 重复解锁异常

    public static final int SHOW_STYLE_REWARD_VIDEO = 52;
    public static final int SHOW_STYLE_FULL_VIDEO = 8501;
    public static final int SHOW_STYLE_DRAW_VIDEO = 8502;
    public static final int SHOW_STYLE_FEED_VIDEO = 8503;
    public static final int SHOW_STYLE_REWARD_VIDEO_HORIZONTAL = 53;
    public static final int SHOW_STYLE_FULL_VIDEO_HORIZONTAL = 8504;
    public static final int SHOW_STYLE_DRAW_VIDEO_HORIZONTAL = 8505;

    public static final int TYPE_TEST_VIP_FREE_2 = 1; // 全站畅听2
    public static final int TYPE_TEST_VIP_FREE_3_AD_FIRST = 2; //全站畅听3广告优先
    public static final int TYPE_TEST_VIP_FREE_3_AD_FIRST_NOT = 3; //全站畅听3非广告优先
    public static final int TYPE_TEST_VIP_FREE_4 = 4; //全站畅听4
    public static final int TYPE_TEST_VIP_FREE_5 = 5; //全站畅听5

    private static volatile AdUnLockVipTrackManager mInstance;

    private int currentAdIndex;

    private List<AdUnLockVipTrackAdvertis> currentAdvertisList = new ArrayList<>();

    private AdUnLockVipTrackAdvertis currentAd;
//
//    private List<IAdRequestStateListener> adRequestStateListeners = new ArrayList<>();

    private long currentAlbumId;
    private long currentTrackId;
    private String currentTrackTitle;
    private IAdUnLockStatusCallBack currentCallBack;

    private long adBeginShowTime;

    private int lastAdRemainTime;

    private static final int MAX_LOAD_TIME = 10000;

    private int maxAdLoadTime = MAX_LOAD_TIME; // 最长加载广告时间为10秒，超时报错

    private long startLoadAdTime; // 开始加载广告的时间

    private boolean isAdComplete = false;

    private boolean isAdRequestOverTime = false;

    private boolean hasNotifyClose = false;

    private boolean hasHandleUserClick = false;

    private LoadingDialog loadingDialog;

    private RewardExtraParams currentExtraParams;
    private RewardExtraParams lastExtraParams;
    private UnlockResult lastUnlockResult;

    private boolean isReceiverRegister;

    private boolean isAdCloseReceiverRegistered;

    // 畅听的实验标记，默认畅听2
    private int vipFreeTestType = TYPE_TEST_VIP_FREE_2;

    private String curSourceName = "";
    private boolean curIsAutoPerform;
    private int rewardTime = 30;
    private String rewardSuccessTip = "恭喜你！\r\n获得免费畅听时长%d分钟";
    private int twiceRewardMaxTimes = 2; // 同一个用户每天出现两次二次解锁弹窗
    private boolean isSupportTwiceReward = true;

    private AdUnLockVipTrackManager() {
        JSONObject config = ConfigureCenter.getInstance()
                .getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_TWICE_CONFIG);
        if (config != null) {
            try {
                rewardTime = config.optInt("rewardTime", 30);
                rewardSuccessTip = config.optString("rewardSuccessTip", "恭喜你！\r\n获得免费畅听时长%d分钟");
                twiceRewardMaxTimes = config.optInt("twiceRewardMaxTimes", 2);
                isSupportTwiceReward = config.optBoolean("isSupportTwiceReward", true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static AdUnLockVipTrackManager getInstance() {
        if (mInstance == null) {
            synchronized (AdUnLockVipTrackManager.class) {
                if (mInstance == null) {
                    mInstance = new AdUnLockVipTrackManager();
                }
            }
        }
        return mInstance;
    }

    public interface IAdUnLockStatusCallBack{
        /**
         * 广告物料请求失败，主站需要弹窗提示，回到入口页
         */
        void onAdRequestError();

        /**
         * 广告加载失败，主站需要弹窗提示，回到入口页
         */
        void onAdLoadError();

        /**
         * 广告播放失败，主站需要弹窗提示，回到入口页
         */
        void onAdPlayError();

        /**
         * 用户点击跳过广告，主站判断是否已经登录：
         * （1）若已登录，则调用onLoginSuccess；
         * （2）若未登录，则吊起登录弹窗：若登录成功则主站调用广告侧onLoginSuccess，广告侧调用解锁接口；若放弃登录则主站处理回到入口页
         */
        void onAdClose(Activity thirdSdkActivity);

        void onAdLoadOverTime();

        /**
         * 广告未正常解锁，无需提示
         */
        void onAdCancel();

        /**
         * 解锁会员专辑成功，主站播放解锁的声音
         *  @param trackId
         * @param albumId
         * @param unlockResult
         */
        void onRewardSuccess(long trackId, long albumId, UnlockResult unlockResult);

        /**
         * 解锁会员专辑失败，主站处理回到入口页
         *
         * @param trackId
         * @param albumId
         */
        void onRewardFail(long trackId, long albumId);

        /**
         * 用户开通会员，未开通成功返回时需要续播广告
         */
        void onAdPlayAgain();

        /**
         * 畅听三阶段专用
         * 点击左上角免广告畅听开启按钮，展示其他解锁类型浮层
         */
        void onClickVipFreeBtn(Activity activity, boolean autoClick);
    }

    /**
     * 主站回调：用户登录成功
     */
    public void onLoginSuccess() {
        Log.d(TAG,"onLoginSuccess");
        unLockVipTrack(true);
    }

    /**
     * 主站回调：用户放弃开会员，广告侧重新加载之前的广告视频
     */
    public void onOpenVipGiveUp() {
        Log.d(TAG,"onOpenVipGiveUp");
        if (currentCallBack == null) {
            return;
        }
        currentCallBack.onAdPlayAgain();
        maxAdLoadTime = MAX_LOAD_TIME;
        currentExtraParams = null;
        showLoadingDialog();
        loadVideoAd(true);
    }

    /**
     * 广告内部接口：查询上次倒计时剩余时长
     */
    public int getLastAdRemainTime() {
        return lastAdRemainTime;
    }

    public long getCurrentTrackId() {
        return currentTrackId;
    }

    public long getCurrentAlbumId() {
        return currentAlbumId;
    }

    public boolean isAdComplete() {
        return isAdComplete;
    }

    /**
     * 广告内部接口：激励视频倒计时结束,满足奖励条件
     */
    public void onCountDownFinish() {
        isAdComplete = true;
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SHOW_TIME,
                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .albumIdUseStr(currentAlbumId + "")
                        .trackId(currentTrackId+ "")
                        .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                        .adUserType(currentAd.getAdUserType())
                        .sourceName(curSourceName + "")
                        .build());
    }

    public void unlockTrack(int playMethod, long albumId, long trackId, String trackTitle,
                            @NonNull IAdUnLockStatusCallBack callBack) {
        unlockTrack(playMethod, albumId, trackId, trackTitle, "player", callBack);
    }

    public void unlockTrack(int playMethod, long albumId, long trackId, String trackTitle, String sourceName,
                            @NonNull IAdUnLockStatusCallBack callBack) {
        unlockTrack(playMethod, albumId, trackId, trackTitle, sourceName, callBack, null, null);
    }

    /**
     * 吊起广告接口
     *
     * @param playMethod 播放方式，手动或者自动或者切换
     * @param albumId 专辑id
     * @param trackId 声音id
     * @param trackTitle 声音标题
     * @param callBack 广告状态回调
     */
    public void unlockTrack(int playMethod, long albumId, long trackId, String trackTitle, String sourceName,
                            @NonNull IAdUnLockStatusCallBack callBack, RewardExtraParams lastParam, Activity lastActivity) {
        Log.d(TAG,"unlockTrack playMethod =" + playMethod + " albumId=" + albumId + " trackId=" + trackId);
        currentAlbumId = albumId;
        currentTrackId = trackId;
        currentTrackTitle = trackTitle;
        curSourceName = sourceName;
        currentCallBack = callBack;
        Activity topActivity = lastActivity;
        if (topActivity == null) {
            topActivity = MainApplication.getMainActivity();
        }
        if (topActivity == null) {
            if (callBack != null) {
                callBack.onAdCancel();
            }
            return;
        }
        loadingDialog = new LoadingDialog(topActivity);
        loadingDialog.setTitle("正在努力加载中");
        loadingDialog.showIcon(true);
        lastExtraParams = lastParam;
        currentExtraParams = lastExtraParams;
        lastUnlockResult = null;
        showLoadingDialog();
        getUnlockAdvertis(playMethod, false, callBack);
    }

    // 请求广告物料
    private void getUnlockAdvertis(int playMethod, boolean duringPlay, IAdUnLockStatusCallBack callBack) {
        currentAdIndex = 0;
        currentAdvertisList.clear();

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appid", "0");
        requestMap.put("name", AppConstants.AD_POSITION_NAME_INCENTIVE);
        requestMap.put("duringPlay", duringPlay + "");
        requestMap.put("isDisplayedInScreen",true + "");
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            requestMap.put("pageMode", Advertis.PAGE_MODE_ELDERLY + "");
        } else {
            requestMap.put("pageMode", Advertis.PAGE_MODE_NORMAL + "");
        }
        requestMap.put("playMethod", playMethod +"");

        requestMap.put("trackId", currentTrackId + "");
        requestMap.put("album", currentAlbumId + "");
        requestMap.put("uid", UserInfoMannage.getUid() + "");
        if (curSourceName != null && curSourceName.contains("tanghulu")) {
            if (!MMKVUtil.getInstance().getBoolean(
                    PreferenceConstantsInHost.KEY_HAS_SHOW_FIRST_UNLOCK_GUIDE_VIDEO, false)) {
                MMKVUtil.getInstance().saveBoolean(
                        PreferenceConstantsInHost.KEY_HAS_SHOW_FIRST_UNLOCK_GUIDE_VIDEO, true);
                requestMap.put("isGuideVideo", "true");
            }
        }
        maxAdLoadTime = MAX_LOAD_TIME;
        isAdRequestOverTime = false;
        long startRequestTime = System.currentTimeMillis();
        CountDownTimer countDownTimer = new CountDownTimer(AdUnLockVipTrackManager.getInstance().getAdMaxLoadTime(), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未请求完成，认为此次广告加载失败
                isAdRequestOverTime = true;
                dismissLoadingDialog();
                if (lastExtraParams != null && lastExtraParams.isRewardingSecond()) {
                    CustomToast.showFailToast("广告加载超时，请稍后重试");
                    lastExtraParams.setRewardingSecond(false);
                } else {
                    if (currentCallBack != null) {
                        currentCallBack.onAdLoadOverTime();
                    }
                }
            }
        };
        countDownTimer.start();
        AdRequest.getVipFreeAd(requestMap, new IDataCallBack<List<AdUnLockVipTrackAdvertis>>() {
            @Override
            public void onSuccess(@Nullable List<AdUnLockVipTrackAdvertis> object) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                if (object == null || object.size() == 0) {
                    dismissLoadingDialog();
                    if (lastExtraParams != null && lastExtraParams.isRewardingSecond()) {
                        lastExtraParams.setRewardingSecond(false);
                        CustomToast.showFailToast("广告请求失败，请稍后重试");
                    } else {
                        callBack.onAdRequestError();
                    }
                    return;
                }
                updateAd(object);
                maxAdLoadTime = maxAdLoadTime - (int)(System.currentTimeMillis() - startRequestTime);
                currentAdvertisList.addAll(object);
                loadVideoAd();
            }

            @Override
            public void onError(int code, String message) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                dismissLoadingDialog();
                if (lastExtraParams != null && lastExtraParams.isRewardingSecond()) {
                    lastExtraParams.setRewardingSecond(false);
                    CustomToast.showFailToast("广告请求失败，请稍后重试");
                } else {
                    callBack.onAdRequestError();
                }
            }
        });
    }

    private void updateAd(List<AdUnLockVipTrackAdvertis> object) {
        if (object == null || object.size() == 0) {
            return;
        }
        // 激励视频广告不支持视频拼接落地页
        for (AdUnLockVipTrackAdvertis advertis: object) {
            advertis.setEnableContinuePlay(false);
            if (advertis.getLinkType() == Advertis.LINK_TYPE_FULL_VIDEO_WEBVIEW || advertis.getLinkType() == Advertis.LINK_TYPE_IMMERSIVE_VIDEO_WEBVIEW
                    || advertis.getLinkType() == Advertis.LINK_TYPE_VIDEO_WEBVIEW_NEW) {
                advertis.setLinkType(Advertis.LINK_TYPE_WEB);
            }
        }
    }

    private void loadVideoAd() {
        loadVideoAd(false);
    }

    private void loadVideoAd(boolean isNeedSetLastTime) {
        isAdComplete = false;
        hasNotifyClose = false;
        hasHandleUserClick = false;
        if (currentCallBack == null) {
            return;
        }
        if (currentAdIndex >= currentAdvertisList.size()) {
            dismissLoadingDialog();
            if (currentExtraParams != null && currentExtraParams.isRewardingSecond()) {
                CustomToast.showFailToast("广告加载失败，请稍后重试");
                currentExtraParams.setRewardingSecond(false);
            } else {
                currentCallBack.onAdLoadError();
            }
            return;
        }
        if (!isNeedSetLastTime) {
            lastAdRemainTime = 0; // 0表示不需要取上次的倒计时剩余值,需要读取配置的值重新开始计时
        } else {
            if (lastAdRemainTime == RewardVideoCountDownStyleForVipFree.LAST_COUNT_DOWN_FINISH
                    || lastAdRemainTime == RewardVideoCountDownStyleForVipFree.LAST_VIDEO_PLAY_FINISH) {
                dismissLoadingDialog();
                // 会员落地页返回时，如果已满足跳过计时，则直接解锁；否则继续上次的计时
                notifyAdClose(null, 0);
                return;
            }
        }
        if (!isAdCloseReceiverRegistered){
            registerVideoAdTaskReceiver();
            isAdCloseReceiverRegistered = true;
        }
        currentAd = currentAdvertisList.get(currentAdIndex);
        String dspPotionId = currentAd.getDspPositionId();
        int adType = currentAd.getAdtype();
        int showStyle = currentAd.getShowstyle();

        if (isVipFeedAd(currentAd) && AdManager.isThirdAd(currentAd)) {
            // 服务端返回了横版dsp广告，异常情况.只展示横版非dsp广告
            dismissLoadingDialog();
            currentCallBack.onAdLoadError();
            return;
        }

        int dspAdType = getDspAdType(adType, showStyle);
        if (dspAdType == -1) {
            return;
        }
        RewardExtraParams extraParams;
        if (lastExtraParams != null) {
            extraParams = lastExtraParams;
        } else {
            extraParams = new RewardExtraParams();
            if (UserInfoMannage.hasLogined() && isSupportTwiceReward && isSupportRewardTwice()) {
                extraParams.setRewardTwiceCallBack(new RewardExtraParams.IRewardTwiceCallBack() {
                    @Override
                    public void callReward() {
                        // 达到解锁条件，调用解锁接口
                        unLockVipTrack(false);
                    }

                    @Override
                    public void onFirstVideoCloseWithOutReward(Activity rewardActivity) {
                        // 视频未播放完，且10秒倒计时未结束，不可解锁
                        finishAdActivityOrFragment(rewardActivity);
                        notifyAdCancel();
                    }

                    @Override
                    public void onFirstVideoCloseWithReward(Activity rewardActivity) {
                        notifyAdClose(rewardActivity, 1);
                    }

                    @Override
                    public void onLoadSecondVideo(RewardExtraParams rewardExtraParams, Activity thirdActivity) {
                        rewardExtraParams.setRewardingSecond(true);
                        unlockTrack(0, currentAlbumId, currentTrackId, currentTrackTitle, curSourceName + "_second",
                                currentCallBack, rewardExtraParams, thirdActivity);
                    }

                    @Override
                    public void onSecondVideoCloseWithOutReward(Activity rewardActivity) {
                        notifyAdClose(rewardActivity, 1);
                    }

                    @Override
                    public void onSecondVideoCloseWithReward(Activity rewardActivity) {
                        notifyAdClose(rewardActivity, 2);
                    }
                });
            }
        }
        extraParams.setUnLockTrackTitle(currentTrackTitle);
        int style;
        if(vipFreeTestType == TYPE_TEST_VIP_FREE_2){
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE;
            CustomToast.showDebugFailToast("畅听二样式");
        } else if (vipFreeTestType == TYPE_TEST_VIP_FREE_3_AD_FIRST_NOT){
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3;
            CustomToast.showDebugFailToast("畅听三非广告优先样式");
        } else if (vipFreeTestType == TYPE_TEST_VIP_FREE_3_AD_FIRST){
            CustomToast.showDebugFailToast("畅听三广告优先样式");
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST;
        } else if (vipFreeTestType == TYPE_TEST_VIP_FREE_4){
            CustomToast.showDebugFailToast("畅听四样式");
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_4;
        } else if (vipFreeTestType == TYPE_TEST_VIP_FREE_5){
//            CustomToast.showDebugFailToast("畅听六样式");
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_5;
        }  else {
            CustomToast.showDebugFailToast("异常类型样式");
            style = RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE;
        }
        extraParams.setRewardCountDownStyle(style);
        extraParams.setCloseable(true);
        extraParams.setVideoPlayOverTime(currentAd.getVideoDuration());
        if (lastAdRemainTime == 0) {
            // 可解锁时间优先读取服务端返回值
            if (currentAd.getUnlockTime() > 0) {
                extraParams.setCanCloseTime(currentAd.getUnlockTime());
            } else {
                extraParams.setCanCloseTime(0); // 0 表示需要读取football配置项的值
            }
        } else {
            extraParams.setCanCloseTime(lastAdRemainTime);
        }
        extraParams.setXmVideoAdvertisModel(currentAd, AppConstants.AD_POSITION_NAME_INCENTIVE);
        currentExtraParams = extraParams;

        // SDK请求进行上报
        AdStateReportManager.getInstance().onUnlockRequestBegin(currentAd,
                AppConstants.AD_POSITION_NAME_INCENTIVE, new AdStateReportManager.IAdStateBuilderInterceptor() {
                    @Override
                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                        builder.albumId(currentAlbumId + "");
                        builder.trackId(currentTrackId + "");
                        builder.uuid(UUID.randomUUID().toString());
                    }
                });
        startLoadAdTime = System.currentTimeMillis();
        RewardVideoAdManager.getInstance().loadRewardAd(BaseApplication.getMainActivity(), currentAd, dspPotionId, adType, dspAdType,
                extraParams,
                new IVideoAdStatueCallBackExt() {
                    @Override
                    public void onRewardVerify() {
                        Log.d(TAG, "onRewardVerify");
                        isAdComplete = true;
                    }

                    boolean isAdClicked = false;
                    @Override
                    public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                        // SDK请求返回
                        new XMTraceApi.Trace()
                                .pageView(32259, "videoAd")
                                .put("currPage", "videoAd")
                                .createTrace();
                        AdStateReportManager.getInstance().onSDKBackSuccess(currentAd,
                                startLoadAdTime, AppConstants.AD_POSITION_NAME_INCENTIVE,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });

                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                currentAd, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SITE_SHOW,
                                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                                        .sdkType(AdManager.getSDKType(currentAd) + "")
                                        .dspPositionId(currentAd.getDspPositionId())
                                        .uid(UserInfoMannage.getUid() + "")
                                        .albumIdUseStr(currentAlbumId + "")
                                        .adUserType(currentAd.getAdUserType())
                                        .trackId(currentTrackId+ "")
                                        .sourceName(curSourceName + "")
                                        .build());
                    }

                    @Override
                    public void onAdLoadError(int code, String message) {
                        Log.d(TAG, "onAdLoadError message = " + message + " code = " + code);
                        int status = AdStateReportManager.STATUS_REQUEST_TIMEOUT_OR_ERROR;
                        if (IVideoAdStatueCallBack.ERROR_CODE_NO_AD == code) {
                            status = AdStateReportManager.STATUS_SDK_NO_BACK;
                        }
                        AdStateReportManager.getInstance().onShowFail(currentAd, status, startLoadAdTime,
                                AppConstants.AD_POSITION_NAME_INCENTIVE,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });
                        if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                            // 加载超时时不顺延，直接返回错误
                            dismissLoadingDialog();
                            if (currentExtraParams != null && currentExtraParams.isRewardingSecond()) {
                                CustomToast.showFailToast("广告加载超时，请稍后重试");
                                currentExtraParams.setRewardingSecond(false);
                            } else {
                                currentCallBack.onAdLoadOverTime();
                            }
                        } else {
                            maxAdLoadTime = maxAdLoadTime - (int)(System.currentTimeMillis() - startLoadAdTime);
                            currentAdIndex++;
                            loadVideoAd();
                        }
                    }

                    @Override
                    public void onAdPlayStart() {
                        dismissLoadingDialog();
                        adBeginShowTime = System.currentTimeMillis();
                        if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST
                                || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3) {
                            AdUnlockFrequencyManager.recordShowAd();
                        }
                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                currentAd, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                                        .sdkType(AdManager.getSDKType(currentAd) + "")
                                        .dspPositionId(currentAd.getDspPositionId())
                                        .uid(UserInfoMannage.getUid() + "")
                                        .albumIdUseStr(currentAlbumId + "")
                                        .trackId(currentTrackId+ "")
                                        .sourceName(curSourceName + "")
                                        .build());
                    }

                    @Override
                    public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                        if(isAdClicked && AdManager.isThirdAd(currentAd)) {
                            return;
                        }
                        AdReportModel.Builder builder =
                                AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                                        .sdkType(AdManager.getSDKType(currentAd) + "")
                                        .dspPositionId(currentAd.getDspPositionId())
                                        .uid(UserInfoMannage.getUid() + "")
                                        .showStyle(currentAd.getShowstyle() + "")
                                        .albumIdUseStr(currentAlbumId + "")
                                        .trackId(currentTrackId + "")
                                        .autoPoll(isAutoClick ? 2 : 1)
                                        .sourceName(curSourceName + "");
                        if (!AdManager.isThirdAd(currentAd) && !isAutoClick && isVipFeedAd(currentAd)) {
                            // 横版喜马广告增加点击区域参数区分
                            builder.clickAreaType(clickAreaType);
                        }
                        if(AdManager.isThirdAd(currentAd) || currentAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT) {
                            builder.ignoreTarget(true)
                                    .onlyClickRecord(true);
                        }
                        if (isAdClicked && !isAutoClick) {
                            builder.onlyGotoClickNoRecord(true);
                        }
                        AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAd,
                                builder.build());
                        if (!isAutoClick) {
                            isAdClicked = true;
                        }
                    }

                    @Override
                    public void onAdClose(boolean isCustomCloseBtn) {
                        Log.d(TAG, "onAdClose isAdComplete = " + isAdComplete);
                        // 三方activity finish的时候会调用该方法
                        if (currentExtraParams != null && currentExtraParams.getRewardTwiceCallBack() != null) {
                            return;
                        }
                        if (AdManager.isThirdAd(currentAd) && !hasHandleUserClick) {
                            if (isAdComplete) {
                                // 视频已播放完成，可解锁
                                notifyAdClose(null, 0);
                            } else {
                                // 视频未播放完，且10秒倒计时未结束，不可解锁
                                notifyAdCancel();
                            }
                        }
                    }

                    @Override
                    public void onAdPlayComplete() {
                        isAdComplete = true;
                        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                currentAd, AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SHOW_OB,
                                        AppConstants.AD_POSITION_NAME_INCENTIVE)
                                        .sdkType(AdManager.getSDKType(currentAd) + "")
                                        .dspPositionId(currentAd.getDspPositionId())
                                        .uid(UserInfoMannage.getUid() + "")
                                        .albumIdUseStr(currentAlbumId + "")
                                        .trackId(currentTrackId + "")
                                        .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                                        .playFinish("1")
                                        .sourceName(curSourceName + "")
                                        .build());
                    }

                    @Override
                    public void onAdPlayError(int code, String message) {
                        Log.d(TAG, "onAdPlayError code =" + code + " msg = " + message);
                        dismissLoadingDialog();
                        currentCallBack.onAdPlayError();
                    }

                    @Override
                    public View.OnClickListener getCloseClickListener(Activity rewardActivity) {
                        return new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                hasHandleUserClick = true;
                                if (!ToolUtil.activityIsValid(rewardActivity)) {
                                    return;
                                }
                                if (v == null) {
                                    return;
                                }
                                dismissLoadingDialog();
                                if (v.getId() == R.id.host_reward_count_down) {
                                    // 10秒倒计时已结束，可获取解锁权限
                                    notifyAdClose(rewardActivity, 0);
                                } else if (v.getId() == R.id.host_reward_close_button) {
                                    if (isAdComplete) {
                                        // 视频已播放完成，可解锁
                                        notifyAdClose(rewardActivity, 0);
                                    } else {
                                        // 视频未播放完，且10秒倒计时未结束，不可解锁
                                        finishAdActivityOrFragment(rewardActivity);
                                        notifyAdCancel();
                                    }
                                } else if (v.getId() == R.id.host_reward_open_vip){
                                    // 点击开会员
                                    lastAdRemainTime = extraParams.getCanCloseTime();
                                    if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST
                                            || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3
                                            || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_5 ){
                                        notifyClickVipFreeBtn(rewardActivity, (Boolean)v.getTag(RewardVideoCountDownStyleForVipFree3.TAG_AUTO_CLICK));
                                    } else {
                                        CustomToast.showDebugFailToast("畅听二点击开会员");
                                    }
                                }
                            }
                        };
                    }
                });
    }

    private boolean isSupportRewardTwice() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy_MM_dd");
        String format = simpleDateFormat.format(new Date());

        String showTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getString(
                PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_DATE);
        int showCount = 0;
        if (TextUtils.equals(showTime, format)) {
            showCount = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_TIMES);
        }
        if (showCount >= twiceRewardMaxTimes) {
            return false;
        } else {
            format = simpleDateFormat.format(new Date());
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveString(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_DATE, format);
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveInt(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_TIMES, showCount + 1);
            return true;
        }
    }

    private void writeLockDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy_MM_dd");
        String lastDate = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getString(
                PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_UNLOCK_DATE);
        int lastContinueUnlockDays = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(
                PreferenceConstantsInHost.KEY_REWARD_VIDEO_CONTINUE_UNLOCK_DAYS);
        Date lastDateDate = null;
        try {
            if (!TextUtils.isEmpty(lastDate)) {
                lastDateDate = simpleDateFormat.parse(lastDate);
            }

            String currentDate = simpleDateFormat.format(new Date());
            Date currentDateDate = simpleDateFormat.parse(currentDate);

            int gap = getGapDay(currentDateDate, lastDateDate);
            if (gap == 0) {
                return;
            }
            if (gap == 1) {
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveInt(
                        PreferenceConstantsInHost.KEY_REWARD_VIDEO_CONTINUE_UNLOCK_DAYS, lastContinueUnlockDays + 1);
            } else {
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveInt(
                        PreferenceConstantsInHost.KEY_REWARD_VIDEO_CONTINUE_UNLOCK_DAYS, 1);
            }
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveString(
                    PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_UNLOCK_DATE, currentDate);
            Log.d(TAG, "lastUnlockDate = "+ MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getString(
                    PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_UNLOCK_DATE) + " continueUnlockDays=" + MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(
                    PreferenceConstantsInHost.KEY_REWARD_VIDEO_CONTINUE_UNLOCK_DAYS));
        } catch (Exception e) {
        }
    }

    private int getGapDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) return -1;
        return (int) (Math.abs(date1.getTime() - date2.getTime()) /(60 * 60 * 24 * 1000));
    }

    // 结束广告相关的三方activity或者fragment
    private void finishAdActivityOrFragment(Activity adActivity) {
        if (currentExtraParams != null) {
            currentExtraParams.setVipFreeCloseAlertDialog(null);
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            XmBaseDialog closeDialog = currentExtraParams.getVipFreeCloseAlertDialog();
            if (closeDialog != null && closeDialog.isShowing()) {
                closeDialog.dismiss();
            }
            currentExtraParams = null;
        }
        if (!ToolUtil.activityIsValid(adActivity)) {
            return;
        }
        if (adActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) adActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof VideoAdFragment) {
                ((VideoAdFragment) currentFragment).finish();
            }
        } else {
            adActivity.finish();
        }
    }

    private void showLoadingDialog() {
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private void dismissLoadingDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            try {
                loadingDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private int getDspAdType(int adType, int showStyle) {
        int dspAdType;
        switch (showStyle) {
            case SHOW_STYLE_FEED_VIDEO:
            case SHOW_STYLE_REWARD_VIDEO:
            case SHOW_STYLE_REWARD_VIDEO_HORIZONTAL:
                if (isTemplateAdType(adType)) {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_TEMPLATE;
                } else {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_REWARD_VIDEO_NO_TEMPLATE;
                }
                break;
            case SHOW_STYLE_FULL_VIDEO:
            case SHOW_STYLE_FULL_VIDEO_HORIZONTAL:
                if (isTemplateAdType(adType)) {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_TEMPLATE;
                } else {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_FULL_VIDEO_NO_TEMPLATE;
                }
                break;
            case SHOW_STYLE_DRAW_VIDEO:
            case SHOW_STYLE_DRAW_VIDEO_HORIZONTAL:
                if (isTemplateAdType(adType)) {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_TEMPLATE;
                } else {
                    dspAdType = AdUnLockAdvertisModel.DSP_AD_TYPES_DRAW_VIDEO_NO_TEMPLATE;
                }
                break;
            default:
                dspAdType = -1;
                break;
        }
        return dspAdType;
    }

    private boolean isTemplateAdType(int adType) {
        return adType == 8 || adType == 10026;
    }


    /**
     * 解锁权益
     */
    private void unLockVipTrack(boolean isNeedToast) {
        AdManager.postRecord(new Runnable() {
            @Override
            public void run() {
                if (FreeListenHostManager.getInstance().canUnlockWithTime()) {
                    unLockVipTrackForUnlockWithTime(isNeedToast);
                } else if (FreeListenHostManager.getInstance().canUnlockWithTrack()) {
                    unLockVipTrackForUnlockWithTrack();
                } else {
                    unLockVipTrackForDefault();
                }
            }
        });
    }

    private void unLockVipTrackForDefault() {
        unLockVipTrackForUnlockWithTrack();
    }

    private void unLockVipTrackForUnlockWithTrack() {
        Map<String, String> maps = new HashMap<>();
        String timeStamp = System.currentTimeMillis() + "";
        maps.put("token", AdTokenManager.getInstance().getShowToken());
        maps.put("uid", UserInfoMannage.getUid() + "");
        maps.put("trackId", currentTrackId + "");
        maps.put("xt", timeStamp);
        EncryptUtil.getInstance(MainApplication.getMyApplicationContext()).getPlaySignature(MainApplication.getMyApplicationContext(), maps);

        maps.put("albumId", currentAlbumId + "");
        maps.put("adid", currentAd.getAdid() + "");
        maps.put("appid", "0");
        maps.put("device", "android");
        maps.put("version", DeviceUtil.getVersion(BaseApplication.getMainActivity()));
        maps.put("prevResponseId", currentAd.getResponseId() + "");
        AdRequest.lookUnLockPaid(maps, new IDataCallBack<VideoUnLockResult>() {
            @Override
            public void onSuccess(@Nullable VideoUnLockResult object) {
                if (object != null && object.isSuccess()) {
                    // 权益解锁成功
                    if ((object.getToastType() == VideoUnLockResult.TOAST_TYPE_ALBUM_DIALOG || object.getToastType() == VideoUnLockResult.TOAST_TYPE_ACTIVITY_DIALOG)
                            && MainApplication.getMainActivity() != null) {
                        showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS_BUT_ACTIVITY_SOON_END, object.getToast());
                        XmLogger.log(XmLogger.Builder.buildLog("XmAd", "rewardVideoDialog")
                                .putString("albumId", currentAlbumId + "")
                                .putString("trackId", currentTrackId + "")
                                .putString("toastType", object.getToastType() + "")
                                .putString("responseId", object.getResponseId() + "")
                                .putString("time", System.currentTimeMillis() + ""));
                    } else {
                        if (!TextUtils.isEmpty(object.getToast())) {
                            showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS, object.getToast());
                        } else {
                            showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS,"解锁成功！\n本集声音可免费畅听24小时");
                        }
                    }
                    currentCallBack.onRewardSuccess(currentTrackId, currentAlbumId, null);
                } else {
                    if (object != null && object.getFailCode() == UNLOCK_REPEAT_ERROR_CODE && !ConstantsOpenSdk.isDebug) {
                        showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS,"解锁成功！\n本集声音可免费畅听24小时");
                        currentCallBack.onRewardSuccess(currentTrackId, currentAlbumId, null);
                        return;
                    }
                    // 服务端返回 toast， 则使用服务端的
                    currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                    String toastStr = "解锁失败，请稍后重试";
                    if (object != null && !TextUtils.isEmpty(object.getToast())) {
                        toastStr = object.getToast();
                    }
                    CustomToast.showFailToast(
                            toastStr,
                            Toast.LENGTH_LONG);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (code == UNLOCK_REPEAT_ERROR_CODE && !ConstantsOpenSdk.isDebug) {
                    showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS, "解锁成功！\n本集声音可免费畅听24小时");
                    currentCallBack.onRewardSuccess(currentTrackId, currentAlbumId, null);
                    return;
                }
                currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                CustomToast.showFailToast(
                        TextUtils.isEmpty(message) ? "解锁失败，请稍后重试" : message,
                        Toast.LENGTH_LONG);
            }
        });
    }

    private void unLockVipTrackForUnlockWithTime(boolean isNeedToast) {
        long trackId = currentTrackId;
        String unlockMode = "FREE_AD";
        CommonRequestM.requestUnlockTrack(trackId, unlockMode, false, new IDataCallBack<UnlockResult>() {
            @Override
            public void onSuccess(@Nullable UnlockResult result) {
                lastUnlockResult = result;
                if (null != result && result.success) {
                    if (0 < result.localNewPermissionExpireSecond) {
                        AdMakeVipLocalManager.getInstance().updateFreeListenInfo(result.remainTimes, result.localNewPermissionExpireSecond);
                    }
                    long newPermissionExpireSecond = null == result ? 0 : result.localNewPermissionExpireSecond;
                    long newLocalBaseTimeStamp = null == result ? 0 : result.localNewBaseTimeStamp;
                    FreeListenHostManager.getInstance().sendUnlockSuccessBroadCast(FreeListenConstant.VIA_TYPE_UNLOCK_AD, newPermissionExpireSecond, newLocalBaseTimeStamp);
                    writeLockDate();
                }
                if (!isNeedToast) {
                    if (currentExtraParams != null) {
                        if (result != null && result.success) {
                            if (result.remainTimes > 0) {
                                currentExtraParams.setSupportRewardAgain(true);
                            }
                            if (currentExtraParams.isRewardingSecond()) {
                                currentExtraParams.setSecondRewardSuccess(true);
                            } else {
                                currentExtraParams.setFirstRewardSuccess(true);
                            }
                        }
                    }
                    return;
                }
                if (result == null) {
                    if (null != currentCallBack) {
                        currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                    }
                    String toastStr = "解锁失败，请稍后重试";
                    CustomToast.showFailToast(toastStr, Toast.LENGTH_LONG);
                    return;
                }

                if (result.success) {
                    if (null != currentCallBack) {
                        currentCallBack.onRewardSuccess(currentTrackId, currentAlbumId, result);
                    }
                    AdMakeVipLocalManager.ReplaceConfig config = AdMakeVipLocalManager.getInstance().getReplaceConfig();
                    if (isFromHomePage() && config != null && config.showInFirstPage == 0) {
                        AdUnLockSnackBarManager.showUseTimeSnackBar(rewardTime, curSourceName);
                    } else {
                        showResultDialog(result);
                    }
                } else {
                    //失败
                    if (null != currentCallBack) {
                        currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                    }
                    CustomToast.showFailToast(
                            TextUtils.isEmpty(result.errorMsg) ? "解锁失败，请稍后重试" : result.errorMsg,
                            Toast.LENGTH_LONG);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (!isNeedToast) {
                    return;
                }
                if (currentCallBack != null) {
                    currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                }
                CustomToast.showFailToast(
                        TextUtils.isEmpty(message) ? "解锁失败，请稍后重试" : message,
                        Toast.LENGTH_LONG);
            }
        });
    }

    private void showResultDialog(UnlockResult result) {
        //普通解锁成功弹窗
        FreeUnlockCommonDialog.Type dialogType = FreeUnlockCommonDialog.Type.SUCCESS;
        String msg = "解锁成功! ";

        if (result.isNearEnd && !TextUtils.isEmpty(result.activityMsg)) {
            //活动快结束的弹窗
            dialogType = FreeUnlockCommonDialog.Type.SUCCESS_BUT_ACTIVITY_SOON_END;
            msg = result.activityMsg;
            Context context = BaseApplication.getTopActivity();
            new FreeUnlockCommonDialog(context,dialogType, msg).showDialog();
        } else {
            if (!TextUtils.isEmpty(result.unlockSuccessMsg)) {
                msg = result.unlockSuccessMsg;
            }
            showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS, msg);
        }

    }

    private void showLockSuccessToast(FreeUnlockCommonDialog.Type type, String toastText) {
        new FreeUnlockCommonDialog(BaseApplication.getTopActivity(),type, toastText).showDialog();
    }

    private void notifyAdClose(Activity thirdSdkActivity, int lastUnlockTimes) {
        int realRewardTime = getRealRewardTime();
        finishAdActivityOrFragment(thirdSdkActivity);
        if (hasNotifyClose) {
            return;
        }
        hasNotifyClose = true;
        // 隐藏关闭挽留弹窗
        if (lastUnlockTimes != 0) {
//            finishAdActivityOrFragment(thirdSdkActivity);
            if (realRewardTime > 0) {
                AdMakeVipLocalManager.ReplaceConfig config = AdMakeVipLocalManager.getInstance().getReplaceConfig();
                if (isFromHomePage() && config != null && config.showInFirstPage == 0) {
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            // 等到首页开始展示时再展示snackbar
                            AdUnLockSnackBarManager.showUseTimeSnackBar(realRewardTime, curSourceName);
                        }
                    }, 500);
                } else {
                    if (lastUnlockResult != null && lastUnlockResult.isNearEnd && !TextUtils.isEmpty(lastUnlockResult.activityMsg)) {
                        //活动快结束的弹窗
                        Context context = BaseApplication.getTopActivity();
                        new FreeUnlockCommonDialog(context, FreeUnlockCommonDialog.Type.SUCCESS_BUT_ACTIVITY_SOON_END, lastUnlockResult.activityMsg).showDialog();
                    } else {
                        String msg = String.format(rewardSuccessTip, realRewardTime);
                        showLockSuccessToast(FreeUnlockCommonDialog.Type.SUCCESS, msg);
                    }
                }
                if (currentCallBack != null) {
                    currentCallBack.onRewardSuccess(currentTrackId, currentAlbumId, lastUnlockResult);
                }
            } else {
                //失败
                CustomToast.showFailToast("解锁失败，请稍后重试", Toast.LENGTH_LONG);
                if (currentCallBack != null) {
                    currentCallBack.onRewardFail(currentTrackId, currentAlbumId);
                }
            }
            return;
        }
        // 喜马自己的激励视频实现方式为fragment,thirdSdkActivity为mainActivity,不能finish activity，需要finish fragment
        if (thirdSdkActivity != null && thirdSdkActivity instanceof MainActivity) {
            if (currentCallBack != null) {
                currentCallBack.onAdClose(null);
            }
        } else {
            if (currentCallBack != null) {
                currentCallBack.onAdClose(thirdSdkActivity);
            }
        }
        new XMTraceApi.Trace()
                .pageExit2(32260)
                .put("currPage", "videoAd")
                .createTrace();
    }

    private int getRealRewardTime() {
        int realTime = 0;
        if (currentExtraParams != null) {
            if (currentExtraParams.isFirstRewardSuccess()) {
                realTime = realTime + rewardTime;
            }
            if (currentExtraParams.isSecondRewardSuccess()) {
                realTime = realTime + rewardTime;
            }
            return  realTime;
        }
        return 0;
    }

    public void notifyAdCancel() {
        if (currentCallBack == null) {
            return;
        }
        currentCallBack.onAdCancel();
        CustomToast.showFailToast("广告未看完，解锁失败", Toast.LENGTH_LONG);
    }

    private void notifyClickVipFreeBtn(Activity rewardActivity, boolean autoClick) {
        if (currentCallBack == null) {
            CustomToast.showDebugFailToast("弹浮层失败，回调为空");
            return;
        }
        currentCallBack.onClickVipFreeBtn(rewardActivity, autoClick);
    }

    public int getAdMaxLoadTime()  {
        if (maxAdLoadTime < 0) {
            maxAdLoadTime = 0;
        }
        return maxAdLoadTime;
    }

    /**
     * 判断是否是全站畅听信息流广告
     *
     * @param advertis
     * @return
     */
    public static boolean isVipFeedAd(Advertis advertis) {
        if (advertis == null) {
            return false;
        }
        if (advertis.getShowstyle() == SHOW_STYLE_FEED_VIDEO) {
            return true;
        }
        // 喜马打底横版贴片广告
        if (advertis.getAdtype() == Advertis.AD_SOURCE_XIMALAYA && advertis.getShowstyle() == SHOW_STYLE_REWARD_VIDEO_HORIZONTAL) {
            return true;
        }
        return false;
    }

    private void unregisterVideoAdTaskReceiver() {
        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).unregisterReceiver(mVideoAdReceiver);
    }

    private void registerVideoAdTaskReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(RewardVideoTaskUtil.INTENT_FILTER_ACTION_VIDEO_AD_TASK);
        LocalBroadcastManager.getInstance(MainApplication.getMyApplicationContext()).registerReceiver(mVideoAdReceiver, filter);
    }

    private final BroadcastReceiver mVideoAdReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int rewardTaskType = intent.getIntExtra(RewardVideoTaskUtil.INTENT_KEY_REWARD_VIDEO_TASK_TYPE, RewardVideoTaskUtil.REWARD_VIDEO_TASK_NONE);
            if (rewardTaskType == RewardVideoTaskUtil.REWARD_VIDEO_TASK_CLOSE_AD){
                // 关闭广告
                tryCloseVideoAd();
            } else if (rewardTaskType == RewardVideoTaskUtil.REWARD_VIDEO_TASK_CONTINUE_AD){
                // 重新拉起续播广告
                onOpenVipGiveUp();
            }
        }
    };

    public void tryCloseVideoAd(){
        if (currentExtraParams != null) {
            currentExtraParams.setVipFreeCloseAlertDialog(null);
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            lastAdRemainTime = currentExtraParams.getCanCloseTime();
            currentExtraParams = null;
        }
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) mainActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof VideoAdFragment) {
                ((VideoAdFragment) currentFragment).finish();
                return;
            }
        }
        RewardVideoAdManager.getInstance().finishRecentActivity();
    }

    public void setReceiverRegister(boolean isRegister) {
        this.isReceiverRegister = isRegister;
    }

    public boolean isReceiverRegister() {
        return isReceiverRegister;
    }

    public int getVipFreeTestType() {
        return vipFreeTestType;
    }

    public void setVipFreeTestType(int vipFreeTestType) {
        this.vipFreeTestType = vipFreeTestType;
    }

    public boolean isAutoPerform() {
        return curIsAutoPerform;
    }

    public void setIsAutoPerform(boolean curIsAutoPerform) {
        this.curIsAutoPerform = curIsAutoPerform;
    }

    public boolean isFromHomePage() {
        if (curSourceName == null) {
            return false;
        }
        return curSourceName.contains(AppConstants.AD_POSITION_NAME_FOCUS)
                || curSourceName.contains(AppConstants.AD_POSITION_NAME_HOME_BOTTOM)
                || curSourceName.contains(AppConstants.AD_POSITION_NAME_FIREWORK_POPUP)
                || curSourceName.contains(AppConstants.AD_POSITION_NAME_HOME_PAGE_SNACK_BAR);
    }
}
