package com.ximalaya.ting.android.host.manager.ad.videoad;

import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.tencent.smtt.sdk.WebView;
import com.tencent.smtt.sdk.WebViewClient;
import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.NavigationUtil;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdFreeListenProtocolDialog;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdUnlockCloseDialogV2;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCashTasksView;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.ad.AdActionBtnView;
import com.ximalaya.ting.android.host.view.ad.XAdActionBtnView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;

/**
 * 激励视频H5广告Fragment (包括游戏试玩、电商浏览)
 */
public class WebAdFragment extends BaseFragment2 implements IRewardAdFragment{
    private static final String TAG = "WebAdFragment";
    private long mRequestKey;
    private View.OnClickListener mClickListener;
    private RewardExtraParams mExtraParams;
    private Advertis mAdvertis;

    private RelativeLayout mRootContainer;
    private View mLoadingView;
    private ImageView mLoadingClose;
    private XmLottieAnimationView mLoadingAnimation;

    private View mEndView;
    private RoundImageView mEndImage;
    private TextView mEndTitle;
    private AdActionBtnView mEndButton;

    protected TextView titleView;
    protected View countDown;
    protected View closeButton;

    public int closeTime = 0; // 单位s 总的关闭倒计时时长
    private int remainCloseTime = 0; // 单位s 剩余关闭倒计时时长

    public static int rewardTime = 30; // 单位分钟
    private CanPauseCountDownTimer countDownTimer;
    private CountDownTimer webMaxLoadCountDownTimer; // 落地页加载超时检测

    private String defaultTitle = "试玩%s秒领取奖励";
    private String rewardTitle = "已获得奖励";
    private boolean isRewardSuccess;
    private boolean isWebPageShow;
    private boolean isCloseDialogShow;

    private boolean isPagePause;

    private Drawable rewardIconDrawable;
    private String realConfigPositionName;

    private ViewGroup mCardContainer;
    private View mCardContent;
    private ImageView mCardIcon;
    private TextView mCardTitle;
    private TextView mCardSubtitle;
    private XAdActionBtnView hostCardDownloadButton;
    private TextView hostCardButton;
    private ImageView mCardCollapse;
    private View mCardCollapseArea;
    private ViewGroup mClickAnimationContainer;
    private XmLottieAnimationView mClickAnimationView;
    private AdRewardCashTasksView mAdRewardCashTasksView;

    private ViewGroup mTopClickAnimationContainer;
    private XmLottieAnimationView mTopClickAnimation;

    private boolean isCardExpanded = false;
    private boolean willFinish = false;
    private boolean mIsWebPlayStyle; // 是否是游戏试玩样式

    private boolean isWebClicked;

    private boolean isCountDownStart;

    public WebAdFragment(RewardExtraParams extraParams, View.OnClickListener clickListener) {
        super(false, null);
        this.mClickListener = clickListener;
        this.mExtraParams = extraParams;
        this.mAdvertis = extraParams.getAdvertis();
        if (mExtraParams != null && mExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_TRY_WEB_PLAY) {
            mIsWebPlayStyle = true;
        }
    }

    public static WebAdFragment newInstance(long requestKey, RewardExtraParams extraParams, View.OnClickListener clickListener) {
        WebAdFragment fragment = new WebAdFragment(extraParams, clickListener);
        Bundle args = new Bundle();
        args.putLong("requestKey", requestKey);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_fragment_game_ad;
    }

    @Override
    protected String getPageLogicName() {
        return TAG;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        if (mAdvertis == null || mExtraParams == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));
            finish();
            return;
        }
        mRootContainer = findViewById(com.ximalaya.ting.android.host.R.id.root_container);
        Bundle args = getArguments();
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        if (args != null) {
            mRequestKey = args.getLong("requestKey");
        }
        mLoadingView = mRootContainer.findViewById(R.id.host_loading_view);
        mLoadingAnimation = mRootContainer.findViewById(R.id.host_loading_animation_view);
        if (mLoadingAnimation != null) {
            ViewGroup.LayoutParams lp = mLoadingAnimation.getLayoutParams();
            if (mIsWebPlayStyle) {
                lp.width = BaseUtil.dp2px(getContext(), 102);
                lp.height = BaseUtil.dp2px(getContext(), 102);
                mLoadingAnimation.setAnimation("lottie/host_web_game_loading_animation.json");
            } else {
                mLoadingAnimation.setAnimation("lottie/host_web_loading_animation.json");
                lp.width = BaseUtil.dp2px(getContext(), 48);
                lp.height = BaseUtil.dp2px(getContext(), 48);
            }
            mLoadingAnimation.setLayoutParams(lp);
            mLoadingAnimation.playAnimation();
        }
        mLoadingClose = mRootContainer.findViewById(R.id.host_loading_close);
        // 初始化顶部倒计时布局
        initTopLayout();
        initCardView();
        initWebView();
        initEndView();
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                callback->callback.onAdLoad(XmNativeAd.createXmNativeAdByAdvertis(mAdvertis)));
    }

    private NativeHybridFragment.IWebClickListener webClickListener = new NativeHybridFragment.IWebClickListener() {
        @Override
        public void onWebClick() {
            if (mClickAnimationContainer.getVisibility() == View.VISIBLE) {
                hideCenterClickAnimationContainer();
            }
            if (mTopClickAnimationContainer.getVisibility() == View.VISIBLE) {
                hideTopAnimationContainer();
            }
            if (!isWebClicked && !mIsWebPlayStyle) {
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.updateCountDownByExternal(remainCloseTime, closeTime);
                }
                startCountDown();
            }
            isWebClicked = true;
        }
    };

    private void initWebView() {
        NativeHybridFragment hybridFragment = new NativeHybridFragment();
        Bundle bundle = new Bundle();
        if (mIsWebPlayStyle) {
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, mAdvertis.getContentLink());
        } else {
            hybridFragment.setWebClickListener(webClickListener);
            bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, mAdvertis.getRealLink());
        }
        bundle.putBoolean(BundleKeyConstants.KEY_IS_LANDSCAPE, false);
        bundle.putBoolean(NativeHybridFragment.IS_EXTERNAL_URL, true);
        bundle.putString(BundleKeyConstants.KEY_AD_POSITION_NAME, mExtraParams.getPositionName());
        bundle.putBoolean("fullScreenWithStatusBar", true);
        bundle.putBoolean(BundleKeyConstants.KEY_SHOW_STATUS_BAR, false);
        bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, false);
        bundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_AD_LANDING_PAGE, true);
        bundle.putParcelable(BundleKeyConstants.KEY_GOTO_HYBRID_VIEW_AD, mAdvertis);
        hybridFragment.setArguments(bundle);
        hybridFragment.setCustomWebClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView webView, String url) {
                Log.d(TAG, "onPageFinished: url = " + url);
                super.onPageFinished(webView, url);
                if (isWebPageShow) {
                    return;
                }
                isWebPageShow = true;
                webMaxLoadCountDownTimer.cancel();
                hideLoadingView();
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayStart);
                if (mIsWebPlayStyle) {
                    startCountDown();
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            expandCard();
                        }
                    }, 500);
                } else {
                    initWebBrowseStyle();
                }
            }
        });
        getChildFragmentManager()
                .beginTransaction()
                .add(R.id.host_webview_container, hybridFragment)
                .commitNowAllowingStateLoss();
        int maxLoadTime = RewardExtraParams.getMaxLoadTime(mExtraParams);
        webMaxLoadCountDownTimer = new CountDownTimer(maxLoadTime, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                if (isWebPageShow) {
                    return;
                }
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                        callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME, "广告加载超时"));
                finish();
            }
        };
        webMaxLoadCountDownTimer.start();
    }

    private void initWebBrowseStyle() {
        mTopClickAnimationContainer = mRootContainer.findViewById(R.id.host_top_click_tip_container);
        mTopClickAnimation = mRootContainer.findViewById(R.id.host_top_click_tip_animation);

        mClickAnimationContainer = mRootContainer.findViewById(R.id.host_web_click_animation_container);
        mClickAnimationView = mRootContainer.findViewById(R.id.host_web_click_animation_view);
        // 展示滑动手势动效
        showCenterClickAnimationContainer();
        // 动画展示3秒自动隐藏
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (mClickAnimationContainer.getVisibility() == View.VISIBLE) {
                    hideCenterClickAnimationContainer();
                    startNoClickDetect();
                }
            }
        }, 6000);

        int delayClickRecordTime = 0;
        if (mAdvertis != null) {
            delayClickRecordTime = mAdvertis.getReportClickTime() * 1000;
        }
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                // 延迟1秒报点击
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback -> callback.onAdVideoClick(true, 5));
            }
        }, delayClickRecordTime);
        initCashView();
    }

    private void initCashView() {
        if (mExtraParams == null || !AD_POSITION_NAME_WELFARE_CASH_RECEIVE.equals(mExtraParams.getPositionName())) {
            return;
        }
        mAdRewardCashTasksView = findViewById(R.id.reward_cash_task_view);
        mAdRewardCashTasksView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mAdRewardCashTasksView.setVisibility(View.VISIBLE);
        mAdRewardCashTasksView.setCustomViewToActivity((ViewGroup) getView(), mExtraParams, mClickListener, null);
        mAdRewardCashTasksView.setData(mAdvertis, false);
        mAdRewardCashTasksView.updateCountDownByExternal(remainCloseTime, closeTime);
    }

    private void startNoClickDetect() {
        // 5秒之后无滑动行为顶部强提示
        HandlerManager.postOnUIThreadDelay(new Runnable() {
            @Override
            public void run() {
                if (!isWebClicked) {
                    if (!canUpdateUi()) {
                        return;
                    }
                    showTopAnimationContainer();
                }
            }
        }, 5000);
    }

    private void showTopAnimationContainer() {
        mTopClickAnimationContainer.setAlpha(0f);
        mTopClickAnimationContainer.setVisibility(View.VISIBLE);
        mTopClickAnimationContainer.animate()
                .alpha(1f)
                .setDuration(300)
                .withEndAction(() -> {
                    mTopClickAnimation.playAnimation();
                })
                .start();
    }

    private void hideTopAnimationContainer() {
        mTopClickAnimationContainer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    mTopClickAnimation.cancelAnimation();
                    mTopClickAnimationContainer.setVisibility(View.GONE);
                })
                .start();
    }

    private void showCenterClickAnimationContainer() {
        mClickAnimationContainer.setAlpha(0f);
        mClickAnimationContainer.setVisibility(View.VISIBLE);
        mClickAnimationContainer.animate()
                .alpha(1f)
                .setDuration(300)
                .start();
        mClickAnimationView.playAnimation();
    }

    private void hideCenterClickAnimationContainer() {
        mClickAnimationContainer.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    mClickAnimationView.cancelAnimation();
                    mClickAnimationContainer.setVisibility(View.GONE);
                })
                .start();
    }

    private void initTopLayout() {
        titleView = mRootContainer.findViewById(R.id.host_reward_tip);
        closeButton = mRootContainer.findViewById(R.id.host_reward_close_real);
        rewardTime = mExtraParams.getRewardTime();
        if (mExtraParams.getCanCloseTime() > 0) {
            closeTime = mExtraParams.getCanCloseTime();
        }
        if (closeTime == 0) {
            closeTime = 20;
        }
        realConfigPositionName = RewardVideoConfigManager.getInstance().getRealConfigPositionName(mExtraParams);
        defaultTitle = RewardVideoConfigManager.getInstance().getHeaderText(realConfigPositionName,
                RewardVideoConfigManager.getInstance().getStyle(mExtraParams), rewardTime, 0);
        rewardTitle = RewardVideoConfigManager.getInstance().getHeaderRewardedText(realConfigPositionName, rewardTime);
        String iconImageUrl = RewardVideoConfigManager.getInstance().getRewardIconImageUrl(realConfigPositionName);
        if (!TextUtils.isEmpty(iconImageUrl)) {
            ImageManager.from(ToolUtil.getCtx()).downloadBitmap(iconImageUrl, null, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (bitmap != null) {
                        rewardIconDrawable = new BitmapDrawable(getContext().getResources(), bitmap);
                    }
                }
            });
        }
        remainCloseTime = closeTime;
        titleView.setText(String.format(defaultTitle, getCountDownTime(remainCloseTime)));
        countDownTimer = new CanPauseCountDownTimer(closeTime * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                remainCloseTime = (int) ((millisUntilFinished / 1000) + 1);
                titleView.setText(String.format(defaultTitle, getCountDownTime(remainCloseTime)));
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.updateCountDownByExternal(remainCloseTime, closeTime);
                }
                if (remainCloseTime > 0) {
                    mExtraParams.setCanCloseTime(remainCloseTime);
                }
            }

            @Override
            public void onFinish() {
                remainCloseTime = 0;
                updateRewardTip();
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.updateCountDownByExternal(remainCloseTime, closeTime);
                }
                mExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_COUNT_DOWN_FINISH);
            }
        };
        if (mExtraParams != null) {
            mExtraParams.setCountDownTimer(countDownTimer);
        }
        countDown = new View(getContext());
        countDown.setId(R.id.host_reward_count_down);

        TextView vipBtn = mRootContainer.findViewById(R.id.host_reward_vip);
        if (mAdvertis != null && !TextUtils.isEmpty(mAdvertis.getCopywriting()) && !TextUtils.isEmpty(mAdvertis.getVipPaymentLink())){
            vipBtn.setVisibility(View.VISIBLE);
            vipBtn.setText(mAdvertis.getCopywriting());
            String xmRequestId = AdUnLockTimeManagerV2.getInstance().getXmRequestId();
            vipBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mClickListener != null) {
                        mClickListener.onClick(v);
                    }
                    // 广告解锁页-免广告按钮  点击事件
                    new XMTraceApi.Trace()
                            .click(51514) // 用户点击时上报
                            .put("currPage", "adLocking")
                            .put("currAlbumId", AdUnLockTimeManagerV2.getInstance().getCurrentAlbumId() + "")
                            .put("currTrackId", AdUnLockTimeManagerV2.getInstance().getCurrentTrackId() + "")
                            .put("sourceType", "激励视频-时长模式")
                            .put("Item", mAdvertis.getCopywriting())
                            .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                            .createTrace();
                }
            });
            new XMTraceApi.Trace()
                    .setMetaId(51515)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "adLocking")
                    .put("currAlbumId", AdUnLockTimeManagerV2.getInstance().getCurrentAlbumId() + "")
                    .put("currTrackId", AdUnLockTimeManagerV2.getInstance().getCurrentTrackId() + "")
                    .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                    .put(XmRequestIdManager.CONT_ID, AdUnLockTimeManagerV2.getInstance().getCurrentAdId(mExtraParams) + "")
                    .put(XmRequestIdManager.CONT_TYPE, "rewardForCountDown")
                    .put("Item", mAdvertis.getCopywriting())
                    .put("sourceType", "激励视频-时长模式")
                    .createTrace();
        }

        initCloseAction();
    }

    private void initEndView() {
        mEndView = mRootContainer.findViewById(R.id.host_end_view);
        mEndImage = mRootContainer.findViewById(R.id.host_end_iv);
        mEndTitle = mRootContainer.findViewById(R.id.host_end_title);
        ImageManager.from(getContext()).displayImage(mEndImage, mAdvertis.getLogoUrl(), -1,
                BaseUtil.dp2px(getContext(), 88), BaseUtil.dp2px(getContext(), 88));
        mEndTitle.setText(mAdvertis.getName());
        mEndButton = mRootContainer.findViewById(R.id.host_end_btn);
        mEndButton.setAdvertis(mAdvertis, mExtraParams.abstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD_END);
        mEndButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback -> callback.onAdVideoClick(false, 4));
            }
        });
        mEndView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
    }

    private void initCardView() {
        mCardContainer = mRootContainer.findViewById(R.id.host_card_container);
        if (!mIsWebPlayStyle) {
            return;
        }
        mCardContainer.setVisibility(View.VISIBLE);
        mCardCollapse = mRootContainer.findViewById(R.id.host_card_collapse);
        mCardCollapseArea = mRootContainer.findViewById(R.id.host_card_collapse_click_area);
        mCardContent = mRootContainer.findViewById(R.id.host_card_content);
        mCardIcon = mRootContainer.findViewById(R.id.host_card_icon);
        mCardTitle = mRootContainer.findViewById(R.id.host_card_title);
        mCardSubtitle = mRootContainer.findViewById(R.id.host_card_subtitle);
        hostCardDownloadButton = mRootContainer.findViewById(R.id.host_card_download_button);
        hostCardButton = mRootContainer.findViewById(R.id.host_card_button);
        ImageManager.from(getContext()).displayImage(mCardIcon, mAdvertis.getLogoUrl(), -1);
        mCardTitle.setText(mAdvertis.getName());
        mCardSubtitle.setText(mAdvertis.getDescription());
        mCardCollapseArea.setOnClickListener(v -> {
            if (isCardExpanded) {
                collapseCard();
            } else {
                expandCard();
            }
        });
        mCardContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback -> callback.onAdVideoClick(false, 2));
            }
        });
        hostCardDownloadButton.setVisibility(AdManager.isDownloadAd(mAdvertis) ? View.VISIBLE : View.GONE);
        hostCardButton.setVisibility(AdManager.isDownloadAd(mAdvertis) ? View.GONE : View.VISIBLE);
        if (AdManager.isDownloadAd(mAdvertis) && hostCardDownloadButton != null) {
            hostCardDownloadButton.setAdvertis(mAdvertis, null, XAdActionBtnView.ACTION_BTN_STYLE_TRY_PLAY_GAME);
            mCardTitle.setText(mAdvertis.getDownloadAppName());
            mCardSubtitle.setText(mAdvertis.getDownloadAppDesc());
        }
    }

    private void expandCard() {
        if (isCardExpanded) {
            return;
        }
        mCardContainer.animate().translationX(0).setDuration(600).start();
        mCardContent.animate().alpha(1f).setDuration(600)
                .withEndAction(() -> {
                    isCardExpanded = true;
                    mCardCollapse.setImageResource(R.drawable.host_ic_arrow_close_right_line_regular_16);
                }).start();
    }

    private void collapseCard() {
        if (!isCardExpanded) {
            return;
        }
        mCardContainer.animate().translationX(mCardContent.getWidth()).setDuration(600).start();
        mCardContent.animate().alpha(0f).setDuration(600)
                .withEndAction(() -> {
                    mCardCollapse.setImageResource(R.drawable.host_ic_arrow_close_left_line_regular_16);
                    isCardExpanded = false;
                })
                .start();
    }

    private void startCountDown() {
        if (isPagePause || isCloseDialogShow) {
            return;
        }
        Log.d(TAG, "startCountDown remainTome = " + remainCloseTime);
        if (countDownTimer != null) {
            isCountDownStart = true;
            countDownTimer.start();
        }
    }

    private void updateRewardTip() {
        if (isRewardSuccess) {
            return;
        }
        isRewardSuccess = true;
        if (titleView != null) {
            titleView.setText(rewardTitle);
            String textColor = RewardVideoConfigManager.getInstance().getHeaderRewardedTextColor(realConfigPositionName);
            titleView.setTextColor(Color.parseColor(textColor));
            int iconStyle = RewardVideoConfigManager.getInstance().getRewardIconImageStyle(realConfigPositionName);
            Drawable drawable;
            if (rewardIconDrawable != null) {
                drawable = rewardIconDrawable;
            } else {
                drawable = getRewardDrawable(iconStyle);
            }
            if (drawable != null) {
                drawable.setBounds(0, 0, BaseUtil.dp2px(ToolUtil.getCtx(), 16), BaseUtil.dp2px(ToolUtil.getCtx(), 16));
                titleView.setCompoundDrawables(drawable, null, null, null);
            }
        }
        if (mExtraParams != null && mExtraParams.getRewardPageStatusCallBack() != null) {
            mExtraParams.getRewardPageStatusCallBack().onRewardVerify();
        }
        if (mExtraParams != null) {
            mExtraParams.setCountDownTimer(null);
        }
        countDownTimer = null;
        showEndView();
        AdUnLockTimeManagerV2.getInstance().onCountDownFinish();
        AdUnLockTimeManagerV3.getInstance().onCountDownFinish();
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayComplete);
    }

    private Drawable getRewardDrawable(int style) {
        switch (style) {
            case 1:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_vip_free_6);
            case 2:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_point_coin);
            case 3:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_vip);
            default:
                return null;
        }
    }

    private void cancelCountdown(){
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    private String getCountDownTime(int time) {
        if (time <= 0) {
            return "00";
        } else if (time < 10) {
            return "0" + time;
        } else {
            return time+"";
        }
    }

    // 点击关闭时达到解锁条件
    protected void onRewardSuccess(View.OnClickListener onClickListener) {
        if (onClickListener != null) {
            onClickListener.onClick(countDown);
        }
    }

    // 提前关闭，不可解锁
    protected void onRewardFail(View.OnClickListener onClickListener) {
        if (onClickListener != null) {
            onClickListener.onClick(closeButton);
        }
    }

    private void initCloseAction() {
        View.OnClickListener closeListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 倒计时已结束
                if (isRewardSuccess) {
                    onRewardSuccess(mClickListener);
                    return;
                }
                if (RewardVideoConfigManager.getInstance().isNotShowCloseAlertDialog(realConfigPositionName)) {
                    // 不展示挽留弹窗
                    cancelCountdown();
                    onRewardFail(mClickListener); // 不可解锁
                } else {
                    showCloseAlertDialog();
                }
            }
        };
        closeButton.setOnClickListener(closeListener);
        mLoadingClose.setOnClickListener(closeListener);
    }

    private void showCloseAlertDialog() {
        if (countDownTimer != null) {
            countDownTimer.pause();
        }
        String style = RewardVideoConfigManager.getInstance().getStyle(mExtraParams);
        String title = RewardVideoConfigManager.getInstance().getCloseTipText(realConfigPositionName, style, rewardTime, 0);
        AdUnlockCloseDialogV2 closeDialog =
                new AdUnlockCloseDialogV2(remainCloseTime, false, title, realConfigPositionName, style,
                        getActivity(), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!isRewardSuccess) {
                            // 倒计时未结束并且视频未完播
                            cancelCountdown();
                            onRewardFail(mClickListener); // 不可解锁
                        } else {
                            // 倒计时已结束或者已完播
                            onRewardSuccess(mClickListener); // 可以解锁
                        }
                        isCloseDialogShow = false;
                    }
                }, new AdUnlockCloseDialogV2.DialogCancelListener() {
                    @Override
                    public void onDialogCancel() {
                        if (countDownTimer != null && isCountDownStart) {
                            countDownTimer.resume();
                        }
                        isCloseDialogShow = false;
                    }
                });
        closeDialog.show();
        isCloseDialogShow = true;
        if (mExtraParams != null) {
            mExtraParams.setVipFreeCloseAlertDialog(closeDialog);
        }
    }

    private void hideLoadingView() {
        if (mLoadingView != null) {
            mLoadingView.setVisibility(View.GONE);
        }
        if (mLoadingAnimation != null) {
            mLoadingAnimation.cancelAnimation();
        }
    }

    private void showEndView() {
        if (mEndView != null) {
            mEndView.setVisibility(View.VISIBLE);
        }
    }

    public boolean isFromPlayPageRewardVip() {
        //时长模式下，播放页免广告弹窗来源不展示规则弹窗，因为其公用的是时长模式的广告位，但本身不是时长模式的奖励而是免广时间的奖励
        return mExtraParams != null && (mExtraParams.getRewardCountDownStyle() == REWARD_CLICK_STYLE_FOR_FREE_AD_NEW || mExtraParams.getRewardCountDownStyle() == REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        isPagePause = false;
        if (countDownTimer != null && !isCloseDialogShow && isCountDownStart) {
            countDownTimer.resume();
        }
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        ViewUtil.keepScreenOn(getActivity(), true);
        if (AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION.equals(mExtraParams.getPositionName())
                && !MmkvCommonUtil.getInstance(getContext()).getBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, false)
                && !isFromPlayPageRewardVip()) {
            // 时长模式展示规则弹窗
            AdFreeListenProtocolDialog protocolDialog = new AdFreeListenProtocolDialog(BaseApplication.getMainActivity(), new AdFreeListenProtocolDialog.IDismissCallBack() {
                @Override
                public void onDismiss() {
                }
            });
            protocolDialog.show();
            MmkvCommonUtil.getInstance(getContext()).saveBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        isPagePause  = true;
        if (countDownTimer != null && !isCloseDialogShow && isCountDownStart) {
            countDownTimer.pause();
        }
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        ViewUtil.keepScreenOn(getActivity(), false);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelCountdown();
        if (webMaxLoadCountDownTimer != null) {
            webMaxLoadCountDownTimer.cancel();
        }
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        if (mLoadingAnimation != null) {
            mLoadingAnimation.cancelAnimation();
        }
        if (mClickAnimationView != null) {
            mClickAnimationView.cancelAnimation();
        }
        if (mTopClickAnimation != null) {
            mTopClickAnimation.cancelAnimation();
        }
    }

    @Override
    public void finish() {
        willFinish = true;
        super.finish();
    }

    @Override
    public boolean onBackPressed() {
        if (willFinish) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    protected void loadData() {
    }
} 