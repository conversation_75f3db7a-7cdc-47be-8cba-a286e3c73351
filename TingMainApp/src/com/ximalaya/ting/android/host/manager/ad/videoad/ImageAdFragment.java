package com.ximalaya.ting.android.host.manager.ad.videoad;

import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK;
import static com.ximalaya.ting.android.host.model.ad.RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Paint;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.service.DownloadAdvertisParams;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.NavigationUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.util.toast.ToastOption;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IThirdAdStatueCallBack;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdFreeListenProtocolDialog;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivity;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivityExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForDownload;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForFreeAd;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForFreeListenV2;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForGetVip;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForPoint;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForSingleTrack;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoClickStyleForWelfareCash;
import com.ximalaya.ting.android.host.manager.ad.videoad.view.AdRewardCashTasksView;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadAdRecordManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.util.ColorUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.view.LocalImageUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.ad.AdActionBtnView;
import com.ximalaya.ting.android.host.view.ad.AdDownloadPermissionAndPrivacyDialog;
import com.ximalaya.ting.android.host.view.ad.AdSourceFromView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.player.advertis.followheart.ShakeUtilsNew;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 全站畅听喜马广告渲染
 */
public class ImageAdFragment extends BaseFragment2 implements View.OnClickListener, IRewardAdFragment{
    private static final String TAG = "VideoAdFragment";

    public View mBgIv;
    public ViewGroup mRootLayout;
    public ImageView mImageContainer;
    private XmLottieAnimationView mTipsAnimation;

    // 线索类 bottom 小卡片
    private View mLandStartLayout;
    private RoundImageView mLandStartCover;
    private TextView mLandStartTitle;
    private TextView mLandStartContent;
    private AdActionBtnView mLandStartBtn;
    private ImageView mLandStartClose;
    private ImageView mLandStartTag;

    // 线索类 bottom 大卡片
    private View mLandEndLayout;
    private RoundImageView mLandEndCover;
    private TextView mLandEndTitle;
    private TextView mLandEndContent;
    private AdActionBtnView mLandEndBtn;
    private ImageView mLandEndTag;


    // 下载类 bottom 小卡片
    private View mAppStartLayout;
    private RoundImageView mAppStartCover;
    private TextView mAppStartTitle;
    private TextView mAppStartContent;
    private AdActionBtnView mAppStartBtn;
    private ImageView mAppStartClose;
    private TextView mAppStartDeveloper;
    private TextView mAppStartVersion;
    private TextView mAppStartPermission;
    private TextView mAppStartPolicy;
    private ImageView mAppStartTag;

    // 下载类 bottom 大卡片
    private View mAppEndLayout;
    private RoundImageView mAppEndCover;
    private TextView mAppEndTitle;
    private TextView mAppEndContent;
    private AdActionBtnView mAppEndBtn;
    private TextView mAppEndDeveloper;
    private TextView mAppEndVersion;
    private TextView mAppEndPermission;
    private TextView mAppEndPolicy;
    private ImageView mAppEndTag;

    private AdSourceFromView mLandAdSource;

    private AdSourceFromView mAppAdSource;

    private Advertis mAdvertis;
    private AbstractRewardVideoAd<?> mAbstractThirdAd;
    private String mPositionName;

    public boolean isVisibleToUser = false;

    private long mRequestKey;
    private int mRewardCountDownStyle;
    private ICustomViewToActivity customViewByCountDownStyle;
    private View mTopCustomView;
    private View.OnClickListener mClickListener;
    private RewardExtraParams mExtraParams;
    private boolean willFinish = false;
    private boolean isFirstResume = true;
    private boolean isAdClicked = false;

    private String clickButtonText;
    private int tipsShowTime;

    private int defaultUiVisibility;
    public static int systemUiVisibilityFlag = View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

    private long startLoadTime;
    private CountDownTimer jumpTimer; // 跳转计时器
    private boolean isJumping; // 点击后发生跳转
    private boolean isJumpCountdownFinish; //点击后发生跳转 倒计时是否结束
    private long mJumpMillis = 0;

    private ShakeUtilsNew mShakeUtils;
    private boolean hasShakeSuccess = false;
    private boolean isShakeDetecting;
    private boolean isExpectingDpJump = false; // 是否期望dp跳转
    private boolean hasRealJumpedToOtherApp = false; // 是否真正跳转到外部App

    private Runnable showTipsRunnable = new Runnable() {
        @Override
        public void run() {
            if (mTipsAnimation != null) {
                mTipsAnimation.setVisibility(View.VISIBLE);
                mTipsAnimation.playAnimation();
            }
        }
    };

    public ImageAdFragment(RewardExtraParams extraParams, View.OnClickListener clickListener) {
        super(false, null);
        this.mClickListener = clickListener;
        this.mExtraParams = extraParams;
    }

    public static ImageAdFragment newInstance(long requestKey, RewardExtraParams extraParams, View.OnClickListener clickListener) {
        ImageAdFragment fragment = new ImageAdFragment(extraParams, clickListener);
        Bundle args = new Bundle();
        Advertis advertis = extraParams.getAdvertis();
        String positionName = extraParams.getPositionName();
        int rewardCountDownStyle = extraParams.getRewardCountDownStyle();
        args.putLong("requestKey", requestKey);
        args.putParcelable("ParamAdvertis", advertis);
        args.putString("ParamPosition", positionName);
        args.putInt("rewardCountDownStyle", rewardCountDownStyle);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        Bundle args = getArguments();
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        defaultUiVisibility = getWindow().getDecorView().getSystemUiVisibility();
        if (args != null) {
            mRequestKey = args.getLong("requestKey");
            mAdvertis = args.getParcelable("ParamAdvertis");
            mPositionName = args.getString("ParamPosition");
            mRewardCountDownStyle = args.getInt("rewardCountDownStyle", RewardExtraParams.REWARD_COUNT_DOWN_STYLE);
        }

        if (mExtraParams != null) {
            mAbstractThirdAd = mExtraParams.getAbstractThirdAd();
        }

        if (mAdvertis == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有数据"));
            finish();
            return;
        }

        //附加倒计时顶部布局
        if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2) {
            customViewByCountDownStyle = new RewardVideoClickStyleForFreeListenV2();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
            customViewByCountDownStyle = new RewardVideoClickStyleForSingleTrack();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT) {
            customViewByCountDownStyle = new RewardVideoClickStyleForPoint();
        }  else if (mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP) {
            customViewByCountDownStyle = new RewardVideoClickStyleForGetVip();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE) {
            customViewByCountDownStyle = new RewardVideoClickStyleForWelfareCash();
        } else if (mRewardCountDownStyle == RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD) {
            customViewByCountDownStyle = new RewardVideoClickStyleForDownload();
        }else if (mRewardCountDownStyle == REWARD_CLICK_STYLE_FOR_FREE_AD_NEW) {
            customViewByCountDownStyle = new RewardVideoClickStyleForFreeAd();
        }  else {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callBack -> callBack.onAdLoadError(IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "数据异常"));
            finish();
            return;
        }
        customViewByCountDownStyle.setCustomViewToActivity((ViewGroup) getView(), mExtraParams, mClickListener);
        initConfig();
        mRootLayout = findViewById(R.id.host_reward_image_ad_root_layout);
        mBgIv = findViewById(R.id.host_image_bg);
        mImageContainer = findViewById(R.id.host_image_container);
        mTipsAnimation  = findViewById(R.id.host_tips_animation);
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mImageContainer.getLayoutParams();
        if (mAdvertis.getShowstyle() == AdUnlockUtil.SHOW_STYLE_IMAGE_HORIZONTAL_CLICK) {
            lp.setMargins(BaseUtil.dp2px(getContext(), 16), BaseUtil.dp2px(getContext(), 70), BaseUtil.dp2px(getContext(), 16), 0);
        } else {
            lp.setMargins(BaseUtil.dp2px(getContext(), 74), BaseUtil.dp2px(getContext(), 70), BaseUtil.dp2px(getContext(), 74), 0);
        }
        mImageContainer.setLayoutParams(lp);
        initCashTaskView(mAdvertis, true);
        // 线索类广告
        mLandStartLayout = findViewById(R.id.host_video_ad_h5_layout_start);
        mLandStartCover = findViewById(R.id.host_video_ad_h5_layout_start_cover);
        mLandStartTitle = findViewById(R.id.host_video_ad_h5_layout_start_title);
        mLandStartContent = findViewById(R.id.host_video_ad_h5_layout_start_content);
        mLandStartBtn = findViewById(R.id.host_video_ad_h5_layout_start_btn);
        mLandStartTag = findViewById(R.id.host_video_ad_h5_layout_start_tag);
        mLandStartClose = findViewById(R.id.host_video_ad_h5_layout_start_close);

        mLandEndLayout = findViewById(R.id.host_video_ad_h5_layout_end);
        mLandEndCover = findViewById(R.id.host_video_ad_h5_layout_end_cover);
        mLandEndTitle = findViewById(R.id.host_video_ad_h5_layout_end_title);
        mLandEndContent = findViewById(R.id.host_video_ad_h5_layout_end_content);
        mLandEndBtn = findViewById(R.id.host_video_ad_h5_layout_end_btn);
        mLandEndTag = findViewById(R.id.host_video_ad_h5_layout_end_tag);
        mLandAdSource = findViewById(R.id.host_video_ad_h5_layout_start_source);


        // 下载类广告
        mAppStartLayout = findViewById(R.id.host_video_ad_app_layout_start);
        mAppStartCover = findViewById(R.id.host_video_ad_app_layout_start_cover);
        mAppStartTitle = findViewById(R.id.host_video_ad_app_layout_start_title);
        mAppStartContent = findViewById(R.id.host_video_ad_app_layout_start_content);
        mAppStartBtn = findViewById(R.id.host_video_ad_app_layout_start_btn);
        mAppStartClose = findViewById(R.id.host_video_ad_app_layout_start_close);
        mAppStartDeveloper = findViewById(R.id.host_video_ad_app_layout_start_developer);
        mAppStartVersion = findViewById(R.id.host_video_ad_app_layout_start_version);
        mAppStartPermission = findViewById(R.id.host_video_ad_app_layout_start_permission);
        mAppStartPermission.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        mAppStartPermission.getPaint().setAntiAlias(true);//抗锯齿
        mAppStartPolicy = findViewById(R.id.host_video_ad_app_layout_start_policy);
        mAppStartPolicy.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        mAppStartPolicy.getPaint().setAntiAlias(true);//抗锯齿
        mAppStartTag = findViewById(R.id.host_video_ad_app_layout_start_tag);

        mAppEndLayout = findViewById(R.id.host_video_ad_app_layout_end);
        mAppEndCover = findViewById(R.id.host_video_ad_app_layout_end_cover);
        mAppEndTitle = findViewById(R.id.host_video_ad_app_layout_end_title);
        mAppEndContent = findViewById(R.id.host_video_ad_app_layout_end_content);
        mAppEndBtn = findViewById(R.id.host_video_ad_app_layout_end_btn);
        mAppEndDeveloper = findViewById(R.id.host_video_ad_app_layout_end_developer);
        mAppEndVersion = findViewById(R.id.host_video_ad_app_layout_end_version);
        mAppEndPermission = findViewById(R.id.host_video_ad_app_layout_end_permission);
        mAppEndPolicy = findViewById(R.id.host_video_ad_app_layout_end_policy);
        mAppEndTag = findViewById(R.id.host_video_ad_app_layout_end_tag);
        mAppAdSource = findViewById(R.id.host_video_ad_app_layout_start_source);

        // 如果三方物料不为空，走三方曝光回调
        if (mAbstractThirdAd == null) {
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                    callback->callback.onAdLoad(XmNativeAd.createXmNativeAdByAdvertis(mAdvertis)));
        }
        startLoadTime = System.currentTimeMillis();
        RewardVideoReport.reportXmLoadStart(mAdvertis, startLoadTime);
        initView();
    }

    private AdRewardCashTasksView mAdRewardCashTasksView;
    private void initCashTaskView(Advertis advertis, boolean isClickRewardType) {
        if (advertis == null || mRewardCountDownStyle != RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE || AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL.equals(mPositionName)) {
            return;
        }
        mAdRewardCashTasksView = findViewById(R.id.reward_cash_task_view);
        RelativeLayout rlClickLayout  = mAdRewardCashTasksView.findViewById(R.id.rl_click_layout);
        if (rlClickLayout != null && isClickRewardType) {
            rlClickLayout.setOnClickListener(this);
        }
        mAdRewardCashTasksView.setVisibility(View.VISIBLE);
        mAdRewardCashTasksView.setCustomViewToActivity((ViewGroup) getView(), mExtraParams, mClickListener, () -> {});
        mAdRewardCashTasksView.setData(advertis,isClickRewardType);
    }

    private void initView() {
        String ImageUrl = mAbstractThirdAd != null ? mAbstractThirdAd.getImgUrl() : mAdvertis.getImageUrl();
        ImageManager.Options options =new ImageManager.Options();
        options.targetWidth = BaseUtil.getScreenWidth(getContext());
        ImageManager.from(getContext()).downloadBitmap(ImageUrl, options, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null) {
                    mImageContainer.setImageBitmap(bitmap);
                    LocalImageUtil.getDomainColor(bitmap, Color.BLACK, new LocalImageUtil.Callback() {
                        @Override
                        public void onMainColorGot(int color) {
                            int fixedColor = ColorUtil.covertColorToFixedSaturationAndLightness(color, 0.33f,0.23f);
                            mBgIv.setBackgroundColor(fixedColor);
                        }
                    });
                    RewardVideoReport.reportXmLoadEnd(mAdvertis, System.currentTimeMillis() - startLoadTime, true, false);
                }
            }
        });
        if (mAbstractThirdAd == null && AdManager.isDownloadAd(mAdvertis)) {
            initForAppAd();
        } else {
            initForLandAd();
        }
        updateButtonText(false);
        initClickListener();
        startAnimation();
    }

    private void initClickListener() {
        mLandStartClose.setOnClickListener(this);
        mAppStartClose.setOnClickListener(this);
        mLandStartLayout.setOnClickListener(this);
        mLandEndLayout.setOnClickListener(this);
        mAppStartLayout.setOnClickListener(this);
        mAppEndLayout.setOnClickListener(this);

        List<View> clickViews = new ArrayList<>();
        clickViews.add(mLandStartLayout);
        clickViews.add(mLandEndLayout);
        clickViews.add(mAppStartLayout);
        clickViews.add(mAppEndLayout);

        if (AdUnlockUtil.isFullScreenClick(mExtraParams)) {
            mBgIv.setOnClickListener(this);
            clickViews.add(mBgIv);
        }

        mAppStartPermission.setOnClickListener(permissionListener);
        mAppStartPolicy.setOnClickListener(privacyListener);
        mAppEndPermission.setOnClickListener(permissionListener);
        mAppEndPolicy.setOnClickListener(privacyListener);

        if (mAbstractThirdAd != null){
            mAbstractThirdAd.bindInteractionListener(mRootLayout, clickViews, new IThirdAdStatueCallBack() {
                @Override
                public void onADExposed() {
                    RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey,
                            callback->callback.onAdLoad(mAbstractThirdAd));
                }

                @Override
                public void onADClicked() {
                    doClick(1);
                }

                @Override
                public void onADError(int code, String msg) {

                }

                @Override
                public void onADStatusChanged() {

                }
            });
        }
    }

    private void initForLandAd() {
        mLandStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mLandEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        if (mAbstractThirdAd != null){
            ImageManager.from(getContext()).displayImage(mLandStartCover, mAbstractThirdAd.getAdIcon(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            ImageManager.from(getContext()).displayImage(mLandEndCover,mAbstractThirdAd.getAdIcon(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            mLandStartTag.setImageResource(R.drawable.host_ad_tag_style_baidu_1);
            mLandEndTag.setImageResource(R.drawable.host_ad_tag_style_baidu_1);
            mLandStartTitle.setText(mAbstractThirdAd.getTitle());
            mLandEndTitle.setText(mAbstractThirdAd.getTitle());
            mLandStartContent.setText(mAbstractThirdAd.getDesc());
            mLandEndContent.setText(mAbstractThirdAd.getDesc());
            if (!TextUtils.isEmpty(mAbstractThirdAd.getButtonText())) {
                mLandStartBtn.setText(mAbstractThirdAd.getButtonText());
                mLandEndBtn.setText(mAbstractThirdAd.getButtonText());
            }
        } else {
            ImageManager.from(getContext()).displayImage(mLandStartCover, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            ImageManager.from(getContext()).displayImage(mLandEndCover, mAdvertis.getLogoUrl(), -1,
                    BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
            mLandStartTitle.setText(mAdvertis.getName());
            mLandEndTitle.setText(mAdvertis.getName());
            mLandStartContent.setText(mAdvertis.getDescription());
            mLandEndContent.setText(mAdvertis.getDescription());
            if (mLandAdSource != null) {
                mLandAdSource.setAdvertis(mAdvertis);
            }
        }
        mLandStartLayout.setAlpha(0f);
        mLandStartLayout.setVisibility(View.VISIBLE);
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
        lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 28));
        mTipsAnimation.setLayoutParams(lp);
    }

    private void initForAppAd() {
        ImageManager.from(getContext()).displayImage(mAppStartCover, mAdvertis.getDownloadAppLogo(), -1,
                BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
        ImageManager.from(getContext()).displayImage(mAppEndCover, mAdvertis.getDownloadAppLogo(), -1,
                BaseUtil.dp2px(getContext(), 60), BaseUtil.dp2px(getContext(), 60));
        mAppStartTitle.setText(mAdvertis.getDownloadAppName());
        mAppEndTitle.setText(mAdvertis.getDownloadAppName());
        mAppStartContent.setText(mAdvertis.getDownloadAppDesc());
        mAppEndContent.setText(mAdvertis.getDownloadAppDesc());
        mAppStartBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        mAppEndBtn.setAdvertis(mAdvertis, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
        if (TextUtils.isEmpty(mAdvertis.getAppDeveloper())) {
            mAppStartDeveloper.setText("开发者: 未知");
            mAppEndDeveloper.setText("开发者: 未知");
        } else {
            mAppStartDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
            mAppEndDeveloper.setText("开发者:" + mAdvertis.getAppDeveloper());
        }
        if (TextUtils.isEmpty(mAdvertis.getAppVersion())) {
            mAppStartVersion.setText("版本号: 未知");
            mAppEndVersion.setText("版本号: 未知");
        } else {
            mAppStartVersion.setText("版本号:" + mAdvertis.getAppVersion());
            mAppEndVersion.setText("版本号:" + mAdvertis.getAppVersion());
        }
        mAppStartLayout.setAlpha(0f);
        mAppStartLayout.setVisibility(View.VISIBLE);
        RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
        lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 70));
        mTipsAnimation.setLayoutParams(lp);
        if (mAppAdSource != null) {
            mAppAdSource.setAdvertis(mAdvertis);
        }
    }


    private View.OnClickListener permissionListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (mAdvertis == null
                    || ((mAdvertis.getAppPermissions() == null || mAdvertis.getAppPermissions().isEmpty())
                    && (mAdvertis.getBusinessExtraInfo() == null || TextUtils.isEmpty(mAdvertis.getBusinessExtraInfo().getAppPermissionUrl())))) {
                return;
            }
            AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), mAdvertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PERMISSION);
            dialog.show();
            String positionName = AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId());
            DownloadAdvertisParams params = new DownloadAdvertisParams(mAdvertis, positionName);
            DownloadServiceManage.getInstance().recordDownloadDialogOkClick(mAdvertis.getRealLink(),
                    DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PERMISSION, params);
        }
    };

    private View.OnClickListener privacyListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (mAdvertis == null || TextUtils.isEmpty(mAdvertis.getAppPrivacyPolicy())) {
                return;
            }
            AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), mAdvertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PRIVACY);
            dialog.show();
            String positionName = AdPositionIdManager.getPositionNameByPositionId(mAdvertis.getAdPositionId());
            DownloadAdvertisParams params = new DownloadAdvertisParams(mAdvertis, positionName);
            DownloadServiceManage.getInstance().recordDownloadDialogOkClick(mAdvertis.getRealLink(),
                    DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PRIVACY, params);
        }
    };

    @Override
    public int getContainerLayoutId() {
        return R.layout.host_frame_image_ad_new;
    }

    @Override
    public void onPause() {
        super.onPause();
        VideoAdTrace.traceEvent(mAdvertis, "onPause");
        isVisibleToUser = false;
        ViewUtil.keepScreenOn(getActivity(), false);
        NavigationUtil.hideNavigationBar(getWindow(), false);
        stopShakeDetect();
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    public void onStop() {
        super.onStop();
        VideoAdTrace.traceEvent(mAdvertis, "onStop");
        // dp跳转优化：只有真正跳转外部App才开始计时
        if (isExpectingDpJump) {
            hasRealJumpedToOtherApp = true;
            // 只有此时才真正开始计时
            startJumpCountdown();
        }
    }

    public void onStart() {
        super.onStart();
        VideoAdTrace.traceEvent(mAdvertis, "onStart");
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        isVisibleToUser = true;
        ViewUtil.keepScreenOn(getActivity(), true);
        if (AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION.equals(mPositionName)
                && !MmkvCommonUtil.getInstance(getContext()).getBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, false)
                && !isFromPlayPageRewardVip()) {
            // 时长模式展示规则弹窗
            AdFreeListenProtocolDialog protocolDialog = new AdFreeListenProtocolDialog(BaseApplication.getMainActivity(), new AdFreeListenProtocolDialog.IDismissCallBack() {
                @Override
                public void onDismiss() {
                    NavigationUtil.hideNavigationBar(getWindow(), true);
                }
            });
            protocolDialog.show();
            MmkvCommonUtil.getInstance(getContext()).saveBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, true);
        } else {
            NavigationUtil.hideNavigationBar(getWindow(), true);
        }

        if (mExtraParams != null && mExtraParams.getRewardPageStatusCallBack() != null){
            mExtraParams.getRewardPageStatusCallBack().onPageResume(getActivity(),
                    RewardExtraParams.IRewardPageStatusCallBack.SOURCE_FROM_AD_PAGE);
        }
        // 站内iting的dp 会被FixLaunchModeActivity拦截，所以会多执行一次onResume
        VideoAdTrace.traceEvent(mAdvertis, "onMyResume");
        // dp跳转优化：判断是否真正跳转外部App
        if (isExpectingDpJump) {
            if (hasRealJumpedToOtherApp) {
                // 真正跳转外部App后回到页面，执行checkJump等逻辑
                VideoAdTrace.traceEvent(mAdvertis, "onResume_CancelJumpCount");
                checkJump();
            }
            isExpectingDpJump = false;
            hasRealJumpedToOtherApp = false;
        } else {
            // 站内iting的dp 会被FixLaunchModeActivity拦截，所以会多执行一次onResume
            if (isJumping && !AdUnlockUtil.isFixLaunchModeActivityShowed()) {
                VideoAdTrace.traceEvent(mAdvertis, "onResume_CancelJumpCount");
                checkJump();
            }
        }
        if (isFirstResume) {
            isFirstResume = false;
            return;
        }
        startShakeDetect();
        if (getWindow() != null) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    public boolean isFromPlayPageRewardVip() {
        //时长模式下，播放页免广告弹窗来源不展示规则弹窗，因为其公用的是时长模式的广告位，但本身不是时长模式的奖励而是免广时间的奖励
        return mExtraParams != null && (mExtraParams.getRewardCountDownStyle() == REWARD_CLICK_STYLE_FOR_FREE_AD_NEW || mExtraParams.getRewardCountDownStyle() == REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW);
    }

    private boolean isRecallDp(){
        if (mAdvertis == null) return false;
        return !TextUtils.isEmpty(mAdvertis.getDpRealLink()) &&
                mAdvertis.getDpRetrySecond() > 0 &&
                System.currentTimeMillis() - mJumpMillis < mAdvertis.getDpRetrySecond() * 1000L;
    }

    private void checkJump() {
        // 这里有可能会触发DP二跳
        if (!isRecallDp()) {
            isJumping = false;
        }
        if (!isJumpCountdownFinish && mAdRewardCashTasksView != null) {
            isJumpCountdownFinish = true;
            CustomToast.showSuccessToast("浏览未满足时长要求，请重试");
        }
        cancelJumpCountdown();
    }

    private void startJumpCountdown() {
        VideoAdTrace.traceEvent(mAdvertis, "onClick_StartJumpCount");
        if (jumpTimer != null) return;
        mJumpMillis = System.currentTimeMillis();
        isJumpCountdownFinish = false;
        jumpTimer = new CountDownTimer((long)mAdvertis.getActualStayTime() * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                Logger.i("JumpCountdown", "onTick = " + millisUntilFinished);
            }

            @Override
            public void onFinish() {
                Logger.i("JumpCountdown", "finish");
                VideoAdTrace.traceEvent(mAdvertis, "jumpCountDownFinish");
                isJumpCountdownFinish = true;
                updateButtonText(true);
                if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
                    ((ICustomViewToActivityExt) customViewByCountDownStyle).onAdCanReward();
                }
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.onAdCanReward();
                }
            }
        };
        jumpTimer.start();
    }

    private void cancelJumpCountdown() {
        if (jumpTimer != null) {
            jumpTimer.cancel();
            jumpTimer = null;
        }
    }

    @Override
    protected String getPageLogicName() {
        return "rewardVideoAd";
    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelShowTipsAnimation();
        stopShakeDetect();
        mShakeUtils = null;
        NavigationUtil.hideNavigationBar(getWindow(), false);
        if (getWindow() != null) {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
    }

    @Override
    public void finish() {
        willFinish = true;
        super.finish();
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    public void startAnimation() {
        View animationView = AdManager.isDownloadAd(mAdvertis) ? mAppStartLayout : mLandStartLayout;
        RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, IVideoAdStatueCallBack::onAdPlayStart);
        HandlerManager.postOnUIThread(() -> {
            ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(animationView,
                    "alpha", 0f, 1.0f);
            ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(animationView,
                    "translationY", animationView.getHeight(), 0);
            alphaAnimator.setDuration(500);
            translationYAnimator.setDuration(500);
            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(alphaAnimator, translationYAnimator);
            animatorSet.start();
        });
    }

    private void cancelShowTipsAnimation() {
        HandlerManager.removeCallbacks(showTipsRunnable);
        if (mTipsAnimation != null) {
            mTipsAnimation.cancelAnimation();
            mTipsAnimation.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean onBackPressed() {
        if (willFinish) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void onClick(View v) {
        if (!canUpdateUi() || v == null) {
            return;
        }
        if (v.getVisibility() != View.VISIBLE) {
            return;
        }
        if (!OneClickHelper.getInstance().onClick(v)) {
            return;
        }
        int id = v.getId();
        if (id == R.id.host_video_ad_h5_layout_start_close || id == R.id.host_video_ad_app_layout_start_close) {
            if (mTipsAnimation.getVisibility() == View.VISIBLE) {
                cancelShowTipsAnimation();
            }
            // 提示条还未展示的情况下改变提示条的布局
            RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mTipsAnimation.getLayoutParams();
            if (id == R.id.host_video_ad_h5_layout_start_close) {
                lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 17));
            } else {
                lp.setMargins(0, 0, BaseUtil.dp2px(getContext(), 10), BaseUtil.dp2px(getContext(), 62));
            }
            mTipsAnimation.setLayoutParams(lp);
            changeCard();
        } else {
            doClick(id == R.id.host_image_bg ? 1 : 2);
        }
    }

    private boolean isNeedDpJumpOptimize() {
        return mAdvertis != null && !TextUtils.isEmpty(mAdvertis.getDpRealLink()) && !mAdvertis.getDpRealLink().contains("iting")
                && ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "video_dp_jump_optimize", true);
    }

    private void doClick(int clickAreaType) {
        try {
            if (AdUnlockUtil.needCalculateJumpDuration(mAdvertis)) {
                isJumping = true;
                if (isNeedDpJumpOptimize()) {
                    // 此处不直接startJumpCountdown，等onStop真正跳转后再启动
                    isExpectingDpJump = true;
                    hasRealJumpedToOtherApp = false;
                } else {
                    startJumpCountdown();
                }
                if (mAdvertis != null){
                    mAdvertis.setDpRecordEnable(false);
                }
            } else if (!isAdClicked) {
                if (customViewByCountDownStyle instanceof ICustomViewToActivityExt) {
                    ((ICustomViewToActivityExt) customViewByCountDownStyle).onAdClicked();
                }
                if (mAdRewardCashTasksView != null) {
                    mAdRewardCashTasksView.onAdClick();
                }
                updateButtonText(true);
                isAdClicked = true;
            }
            RewardVideoAdManager.getInstance().notifyAdStatueCallBack(mRequestKey, callback -> callback.onAdVideoClick(false, clickAreaType));
            if (mAdvertis.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT) {
                // 订阅类型，先订阅
                doCollect(mAdvertis.getRealLink());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @SuppressLint("DefaultLocale")
    private void initConfig() {
        JSONObject config = ConfigureCenter.getInstance()
                .getJson(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_CLICK_CONFIG);
        if (config != null) {
            try {
                JSONObject tipsShowTimeConfig = config.optJSONObject("gestureShowTime");
                int positionId = mAdvertis.getPositionId();
                tipsShowTime = tipsShowTimeConfig.getInt(positionId + "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        clickButtonText = RewardVideoConfigManager.getInstance().getButtonText(
                RewardVideoConfigManager.getInstance().getRealConfigPositionName(mExtraParams),
                RewardVideoConfigManager.getInstance().getStyle(mExtraParams),
                mExtraParams != null ? mExtraParams.getRewardTime() : 0,
                AdUnlockUtil.needCalculateJumpDuration(mAdvertis) ? mAdvertis.getTipStayTime() : 0);
        if (tipsShowTime == 0) {
            tipsShowTime = 3;
        }
    }

    public boolean isCashWithdrawal() {
        if (mExtraParams != null) {
            String positionName = mExtraParams.getPositionName();
            return positionName.equals(AppConstants.AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL);
        }
        return false;
    }

    private void updateButtonText(boolean isRewarded) {
        if (!canUpdateUi()) return;
        if (isRewarded) {
            mLandStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mLandEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppStartBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            mAppEndBtn.setAdvertis(mAdvertis, mAbstractThirdAd, AdActionBtnView.ACTION_BTN_STYLE_REWARD_VIDEO_AD);
            if (mAbstractThirdAd != null && !TextUtils.isEmpty(mAbstractThirdAd.getButtonText())) {
                mLandStartBtn.setText(mAbstractThirdAd.getButtonText());
                mLandEndBtn.setText(mAbstractThirdAd.getButtonText());
                mAppStartBtn.setText(mAbstractThirdAd.getButtonText());
                mAppEndBtn.setText(mAbstractThirdAd.getButtonText());
            }
            if (mExtraParams.isShakeEnable()) {
                hideShakeView();
            } else {
                cancelShowTipsAnimation();
            }
            if (mExtraParams != null) {
                mExtraParams.setCanReward(true);
            }
            updateButtonWidth(false);
        } else {
            mLandStartBtn.setText(clickButtonText);
            mLandEndBtn.setText(clickButtonText);
            mAppStartBtn.setText(clickButtonText);
            mAppEndBtn.setText(clickButtonText);
            if (mExtraParams.isShakeEnable()) {
                showShakeView();
            } else {
                HandlerManager.postOnUIThreadDelay(showTipsRunnable, tipsShowTime * 1000);
            }
            updateButtonWidth(true);
        }
    }

    private void updateButtonWidth(boolean isLongWidth) {
        if (isLongWidth) {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mLandStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 12), lp.topMargin, BaseUtil.dp2px(getContext(), 12), lp.bottomMargin);
            mLandStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mLandEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mLandEndBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 12), lp.topMargin, BaseUtil.dp2px(getContext(), 12), lp.bottomMargin);
            mAppStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mAppEndBtn.setLayoutParams(lp);
        } else {
            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) mLandStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mLandStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mLandEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 44), lp.topMargin, BaseUtil.dp2px(getContext(), 44), lp.bottomMargin);
            mLandEndBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppStartBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 28), lp.topMargin, BaseUtil.dp2px(getContext(), 28), lp.bottomMargin);
            mAppStartBtn.setLayoutParams(lp);

            lp = (ViewGroup.MarginLayoutParams) mAppEndBtn.getLayoutParams();
            lp.setMargins(BaseUtil.dp2px(getContext(), 44), lp.topMargin, BaseUtil.dp2px(getContext(), 44), lp.bottomMargin);
            mAppEndBtn.setLayoutParams(lp);
        }
    }

    private void changeCard() {
        View startView = AdManager.isDownloadAd(mAdvertis) ? mAppStartLayout : mLandStartLayout;
        View endView = AdManager.isDownloadAd(mAdvertis) ? mAppEndLayout : mLandEndLayout;
        endView.setAlpha(0f);
        endView.setVisibility(View.VISIBLE);
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                ObjectAnimator alphaAnimator1 = ObjectAnimator.ofFloat(startView,
                        "alpha", 1f, 0f);
                alphaAnimator1.setDuration(100);

                ObjectAnimator alphaAnimator2 = ObjectAnimator.ofFloat(endView,
                        "alpha", 0f, 1f);
                alphaAnimator1.setDuration(200);
                ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(endView,
                        "translationY", endView.getHeight(), 0);
                translationYAnimator.setDuration(200);
                AnimatorSet animatorSet = new AnimatorSet();
                animatorSet.playTogether(alphaAnimator1, alphaAnimator2, translationYAnimator);
                animatorSet.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        startView.setVisibility(View.INVISIBLE);
                    }
                });
                animatorSet.start();
            }
        });
    }

    private void doCollect(String albumId) {
        AlbumM albumM = new AlbumM();
        albumM.setId(Long.parseLong(albumId));
        albumM.setFavorite(false);
        AlbumEventManage.doCollectActionV2(albumM, this, new ICollectStatusCallback() {
            @Override
            public void onCollectSuccess(int code, final boolean isCollected) {
                if (!canUpdateUi()) {
                    return;
                }
                if (isCollected) {
                    if (code == 0) {
//                        Toast toast = Toast.makeText(getContext(), "订阅成功~\r\n可在【我听】中查看", Toast.LENGTH_SHORT);
//                        TextView v = (TextView) toast.getView().findViewById(android.R.id.message);
//                        if (v != null) v.setGravity(Gravity.CENTER);
//                        toast.setGravity(Gravity.CENTER, 0, 0);
//                        toast.show();
                        ToastOption option = new ToastOption();
                        option.textGravity = Gravity.CENTER;
                        CustomToast.showToast("订阅成功~\r\n可在【我听】中查看",Toast.LENGTH_SHORT, option);
                    }
                    mLandStartBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mLandStartBtn.setSelected(true);
                    mLandStartBtn.setText("立即查看");

                    mLandEndBtn.setBackgroundResource(R.drawable.host_ad_action_btn_bg);
                    mLandEndBtn.setSelected(true);
                    mLandEndBtn.setText("立即查看");

                    mAdvertis.setClickType(1);
                    mAdvertis.setRealLink("iting://open?msg_type=13&album_id="+ albumId);
                }
            }

            @Override
            public void onError() {
            }
        });
    }

    private void showShakeView() {
        if (mExtraParams.canReward()) {
            return;
        }
        if (AdManager.isDownloadAd(mAdvertis)) {
            mAppStartBtn.showShakeView();
            mAppEndBtn.showShakeView();
        } else {
            mLandStartBtn.showShakeView();
            mLandEndBtn.showShakeView();
        }
        startShakeDetect();
    }

    private void hideShakeView() {
        if (AdManager.isDownloadAd(mAdvertis)) {
            mAppStartBtn.hideShakeView();
            mAppEndBtn.hideShakeView();
        } else {
            mLandStartBtn.hideShakeView();
            mLandEndBtn.hideShakeView();
        }
        stopShakeDetect();
    }

    private void startShakeDetect() {
        if (mExtraParams == null || !mExtraParams.isShakeEnable()) {
            return;
        }
        if (mExtraParams.canReward()) {
            return;
        }
        hasShakeSuccess = false;
        if (isShakeDetecting) {
            return;
        }
        if (mShakeUtils == null) {
            int shakeSpeed = ConfigureCenter.getInstance()
                    .getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_INCENTIVE_SHAKE_SPEED, 15);
            if (mAdvertis != null && mAdvertis.getShakeSpeed() != 0) {
                shakeSpeed = mAdvertis.getShakeSpeed();
            }
            mShakeUtils = new ShakeUtilsNew(getContext(), shakeSpeed);
            mShakeUtils.setOnShakeListener(new ShakeUtilsNew.OnShakeListener() {
                @Override
                public void onShake() {
                    if (hasShakeSuccess) {
                        return;
                    }
                    hasShakeSuccess = true;
                    try {
                        Vibrator vibrator = (Vibrator) getContext().getSystemService(Context.VIBRATOR_SERVICE);
                        if (vibrator != null) {
                            vibrator.vibrate(500);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    doClick(1);
                }
            });
        }
        mShakeUtils.onResume();
        isShakeDetecting = true;
    }

    private void stopShakeDetect() {
        if (mExtraParams == null || !mExtraParams.isShakeEnable()) {
            return;
        }
        if (!isShakeDetecting) {
            return;
        }
        if (mShakeUtils != null) {
            mShakeUtils.onPause();
        }
        isShakeDetecting = false;
    }
}
