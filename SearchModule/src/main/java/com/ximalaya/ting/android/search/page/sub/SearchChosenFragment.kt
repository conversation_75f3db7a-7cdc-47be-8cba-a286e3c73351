package com.ximalaya.ting.android.search.page.sub

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.os.TraceCompat
import androidx.core.view.ViewCompat
import androidx.fragment.app.Fragment
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.astuetz.AccessibilityClassNameUtil
import com.handmark.pulltorefresh.library.PullToRefreshBase
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.fragment.ManageFragment.StackChangeListener
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.pageerrormonitor.XmPageErrorMonitor
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.chatxmly.util.AIAgentSearchUtil
import com.ximalaya.ting.android.host.customhome.CustomHomeManager
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.listener.IGotoTop.IGotoTopBtnClickListener
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage.IFollowAnchorListener
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.search.SearchManager
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.search.SearchHotWord
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.satisfaction.SatisfactionSearchManager
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.search.SearchReactNativeSetting
import com.ximalaya.ting.android.host.util.server.NetworkUtils
import com.ximalaya.ting.android.host.util.server.PlayTools
import com.ximalaya.ting.android.host.util.view.setAccessibilityClassName
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis.IMG_SHOW_STYLE_SEARCH_BRAND_AD_IMMERSIVE
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList
import com.ximalaya.ting.android.opensdk.model.album.Album
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.SearchConstants
import com.ximalaya.ting.android.search.ad.SearchBrandAdBgManager
import com.ximalaya.ting.android.search.adapter.SearchChosenAdapter
import com.ximalaya.ting.android.search.adapter.SearchLabelOrSortFilterAdapter
import com.ximalaya.ting.android.search.adapter.SearchRecyclerViewMultiTypeAdapter2.OnCardLabelChangeListener
import com.ximalaya.ting.android.search.adapter.chosen.SearchExtendMoreProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchFilterAlbumGridProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchGiftForNewUserProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchLabelAlbumCardProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchQuestionAnswerCardProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchRecommendWordProvider
import com.ximalaya.ting.android.search.adapter.chosen.SearchTrackCardProvider
import com.ximalaya.ting.android.search.adapter.chosen.rn.ReactNativeAdapter
import com.ximalaya.ting.android.search.base.BaseSearchFragment
import com.ximalaya.ting.android.search.base.BaseSearchSubFragment
import com.ximalaya.ting.android.search.manager.SearchImmersiveBannerManager
import com.ximalaya.ting.android.search.model.AdapterProxyData
import com.ximalaya.ting.android.search.model.ExtraParam
import com.ximalaya.ting.android.search.model.MainLabel
import com.ximalaya.ting.android.search.model.SearchChosenPageContent
import com.ximalaya.ting.android.search.model.SearchChosenResponse
import com.ximalaya.ting.android.search.model.SearchItem
import com.ximalaya.ting.android.search.model.SearchItemModel
import com.ximalaya.ting.android.search.model.SearchLabel
import com.ximalaya.ting.android.search.model.SearchLabelData
import com.ximalaya.ting.android.search.model.SearchNewItem
import com.ximalaya.ting.android.search.model.SearchPaidFilterData
import com.ximalaya.ting.android.search.model.SearchReasonDocs
import com.ximalaya.ting.android.search.model.SearchResponseHeader
import com.ximalaya.ting.android.search.model.SearchSortFilterData
import com.ximalaya.ting.android.search.model.SearchTopUser
import com.ximalaya.ting.android.search.other.SearchPageLoadErrorConstants
import com.ximalaya.ting.android.search.request.SearchCommonRequest
import com.ximalaya.ting.android.search.request.SearchUrlConstants
import com.ximalaya.ting.android.search.utils.SearchTopAlbumColorUtils
import com.ximalaya.ting.android.search.utils.SearchTraceUtils
import com.ximalaya.ting.android.search.utils.SearchUiUtils
import com.ximalaya.ting.android.search.utils.SearchUtils
import com.ximalaya.ting.android.search.view.RefreshLoadMoreRecyclerView
import com.ximalaya.ting.android.search.view.SearchNormalFilterPopupWindow
import com.ximalaya.ting.android.search.wrap.StackChangeListenerWrapper
import com.ximalaya.ting.android.xmtrace.ManualExposureHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONException
import org.json.JSONObject
import java.io.UnsupportedEncodingException
import java.net.URLEncoder

class SearchChosenFragment : BaseSearchSubFragment<SearchChosenPageContent>(), IXmPlayerStatusListener,
        StackChangeListener, IXmAdsStatusListener, PullToRefreshRecyclerView.IRefreshLoadMoreListener,
    AlbumEventManage.CollectListener, IFollowAnchorListener {

    private var mRecyclerView: RefreshLoadMoreRecyclerView? = null
    private var mAdapter: SearchChosenAdapter? = null
    private var mFilterLayout: ViewGroup? = null
    private var mMask: View? = null

    private var mReturnTopBtnListener: IGotoTopBtnClickListener? = null
    private var mStackChangeListenerWrapper: StackChangeListenerWrapper? = null

    private var voiceAsInput = false
    private var condition = SearchConstants.CONDITION_RELATION
    private var conditionName = SearchConstants.CONDITION_RELATION_NAME
    private var paidType = SearchConstants.PAID_TYPE_NONE
    private var labelForQuery: String? = null

    private var mSearchFilterProxy: SearchFilterContentViewProxy? = null
    private var mContainerSliderView: ViewGroup? = null
    private var mSearchMeta: String? = null

    private var mNormalFilterPopupWindow: SearchNormalFilterPopupWindow? = null
    private var mTimeStamp: Long = 0
    private var isIntercept = false
    private val mTraceHelper = TraceHelper("搜索精选页b")
    private var mIsFirstLoad = true
    private var mIsVisibleToUser = false
    private var mNeedRefreshAfterLogin = false
    private var mNeedShowNewUserGiftToastIfNeed = false
    private var mIsAutoPlay = false
    private var mIsSimpleItemClick = false
    private var mIsPopupItemClick = false
    // 是否存在沉浸式item
    private var mHasBrandAdItem = false
    private var mIsDarkStatusBar = !BaseFragmentActivity.sIsDarkMode

    // 沉浸式来源
    private var mImmersiveFrom = SearchBrandAdBgManager.FROM_AD

    // 真实的上下滑动列表滚动偏移比例
    private var mRealListScrollOffset = -1f

    private val mLoginStatusListener = object : ILoginStatusChangeListener {
        override fun onLogout(olderUser: LoginInfoModelNew?) {}

        override fun onLogin(model: LoginInfoModelNew?) {
            if (mNeedRefreshAfterLogin) {
                mNeedRefreshAfterLogin = false
                mNeedShowNewUserGiftToastIfNeed = true
                if (canUpdateUi()) {
                    onRefresh()
                }
            }
        }

        override fun onUserChange(oldModel: LoginInfoModelNew?, newModel: LoginInfoModelNew?) {
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (SearchReactNativeSetting.isSearchResultBigPictureRnMode()) {
            mTraceHelper.setIsDynamic()
        }
        mTraceHelper.postPageStartNode()
        super.onCreate(savedInstanceState)
    }

    override fun initUi(savedInstanceState: Bundle?) {
        super.initUi(savedInstanceState)
        parseArgs(arguments)
        uiInit()
        initListener()
        showFeedBackIcon(true, SearchConstants.CORE_CHOSEN)
        mTraceHelper.postNode("初始化完成")
        SearchImmersiveBannerManager.addOnColorChangeListener(mOnColorChangeListener)
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.search_fra_feature
    }

    private fun parseArgs(bundle: Bundle?) {
        bundle?.apply {
            searchId = SearchTraceUtils.getSearchId()
            keyword = getString(SearchConstants.SEARCH_KEYWORD, "")
            typeFrom = getInt(SearchConstants.KEY_TYPE)
            spellCheck = getBoolean(SearchConstants.KEY_SPELL_CHECK, true)
            needSemantic = getBoolean(SearchConstants.SEARCH_NEED_SEMANTIC, true)
            inputKW = getString(SearchConstants.SEARCH_INPUT, null)
            voiceAsInput = getBoolean(BundleKeyConstants.KEY_SEARCH_VOICE, false)
            mIsAutoPlay = getBoolean(SearchConstants.KEY_AUTO_PLAY, false)
        }
    }

    override fun darkStatusBar(): Boolean {
        return mIsDarkStatusBar
    }

    private fun uiInit() {
        mRecyclerView = findViewById(R.id.host_id_stickynavlayout_innerscrollview)
        mRecyclerView?.mode = PullToRefreshBase.Mode.DISABLED

        mStackChangeListenerWrapper = StackChangeListenerWrapper(this)

        mFilterLayout = findViewById(R.id.search_filter_layout)
        mMask = findViewById(R.id.search_v_mask)
        mMask?.setOnClickListener {
            dismissNormalFilterWindow()
        }
        mMask?.setAccessibilityClassName(AccessibilityClassNameUtil.VIEW_TYPE_BUTTON)
        mMask?.contentDescription = "关闭"
        initHead()

        mAdapter = SearchChosenAdapter(searchDataContext, this)
        mAdapter?.setOnCardChangeListener(object :
            OnCardLabelChangeListener {
            override fun onLabelAlbumCardChangeListener(selectList: MutableList<SearchLabel>?, mainLabel: MainLabel?) {
                loadSearchForUniversalQuery(selectList, mainLabel)
            }

            override fun onHotPotTagChangeListener(extendClickTimes: Int, searchLabel: SearchLabel?, labelList: MutableList<SearchLabel>?) {
                loadSearchForHotPotQuery(extendClickTimes, searchLabel, labelList)
            }

            override fun onSaleCardClickListener(selected: Boolean, fq: String) {
                isSaleSelected = selected
                saleFq = if (!selected) {
                    null
                } else {
                    fq
                }
                onRefresh()
            }

            override fun onPoolLabelAlbumCardChangeListener(
                selectList: MutableList<SearchLabel>?,
                extendClickTimes: Int) {
                loadSearchForPoolLabel(selectList, extendClickTimes)
            }
        })
        mRecyclerView?.setAdapter(mAdapter)
        mAdapter?.setRecyclerView(mRecyclerView?.refreshableView)
        mRecyclerView?.refreshableView?.apply {
            layoutManager = LinearLayoutManager(mContext, RecyclerView.VERTICAL, false)
        }
    }

    private fun initHead() {
        if (showFilterView()) {
            SearchUiUtils.setVisible(View.INVISIBLE, mFilterLayout)
            mRecyclerView?.apply {
                val params = layoutParams as MarginLayoutParams
                params.topMargin = topViewHeight
            }
            mMask?.apply {
                val params = layoutParams as MarginLayoutParams
                params.topMargin = topViewHeight
            }
            mSearchFilterProxy = SearchFilterContentViewProxy(this, createSimpleSortFilterData(),
                    mContext, mFilterLayout, mContainerSliderView, "chosen")
        } else {
            SearchUiUtils.setVisible(View.GONE, mFilterLayout)
        }
    }

    fun traceItemViewed() {
        val manager = mRecyclerView?.refreshableView?.layoutManager ?: return
        if (manager is LinearLayoutManager) {
            val firstVisiblePosition = manager.findFirstVisibleItemPosition()
            val lastVisiblePosition = manager.findLastVisibleItemPosition()
            val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
            mRecyclerView?.apply {
                for (i in 0..visibleItemCount) {
                    mAdapter?.traceOnItemShow(i + firstVisiblePosition, refreshableView.getChildAt(i))
                }
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (mAdapter != null && canUpdateUi()) {
            mAdapter?.notifyDataSetChanged()
        }
    }

    var mRvScrollY = 0 // 列表纵向滑动的距离

    private fun initListener() {
        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusListener)
        mReturnTopBtnListener = IGotoTopBtnClickListener {
            // 这个方法不会触发onScrolled，所以直接重置一下mRvScrollY
            mRvScrollY = 0
            mRecyclerView?.refreshableView?.scrollToPosition(0)
        }
        mRecyclerView?.refreshableView?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceItemViewed()
                    mRecyclerView?.let {
                        ManualExposureHelper.exposureViewsByScroll(fraTag, it)
                    }
                    if (SearchReactNativeSetting.reactNativeCardShowed) {
                        SearchReactNativeSetting.notifyScroll(context)
                    }
                    SatisfactionSearchManager.scrollIdle(this@SearchChosenFragment, keyword, xmRequestId)
                } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    SatisfactionSearchManager.pageScroll()
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mRvScrollY += dy
                // Log.e("ZZB", "onScrolled dy = " + dy + ", rvScrollY = " + mRvScrollY)
                if (mRvScrollY < 0 || (mRvScrollY > 0 && !recyclerView.canScrollVertically(-1))) {
                    mRvScrollY = 0
                }
                handleScroll()
                if (mHasBrandAdItem) {
                    val startHeight = BaseUtil.dp2px(context, 10f)
                    val endHeight = BaseUtil.dp2px(context, 30f)
                    var offset = -1f
                    when {
                        mRvScrollY in startHeight..endHeight -> {
                            offset =
                                1 - (mRvScrollY.toFloat() - startHeight) / (endHeight - startHeight)
                        }
                        mRvScrollY <= startHeight -> {
                            offset = 1f
                        }
                        mRvScrollY <= 100.dp -> {
                            // 这里是为了保证有 offset=0 的状态
                            offset = 0f
                        }
                    }
                    mRealListScrollOffset = offset
                    if (!mIsPopupItemClick && !mIsSimpleItemClick && offset >= 0) {
                        SearchBrandAdBgManager.sendListScrollColorChangeBroad(
                            SearchBrandAdBgManager.topBgMainColor,
                            offset,
                            context,
                            mImmersiveFrom
                        )
                    }
                }
                SearchImmersiveBannerManager.onListScrolled(mRvScrollY)
            }
        })
        mRecyclerView?.setOnRefreshLoadMoreListener(this)

        AlbumEventManage.addListener(this)
        AnchorFollowManage.getSingleton().addFollowListener(this)

        val provider = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_EXTEND_MORE)
        if (provider is SearchExtendMoreProvider) {
            provider.setHandleClick(object : SearchExtendMoreProvider.IHandleClickListener {
                override fun onExpandMoreList(data: SearchItem, position: Int) {
                    if (!canUpdateUi()) return
                    val itemModels = SearchUtils.castSearchItemToItemModels(arrayListOf(data), false)
                    mAdapter?.apply {
                        if (!data.showAll) {
                            removeData(position, false)
                        }
                        addListData(itemModels, position)
                    }
                    postOnUiThread {
                        if (canUpdateUi()) {
                            traceItemViewed()
                            mRecyclerView?.let {
                                ManualExposureHelper.exposureViewsByScroll(fraTag, it)
                            }
                        }
                    }
                }

                override fun notifyItemRangeChanged(
                    data: SearchItem,
                    startPosition: Int,
                    itemCount: Int) {
                    if (!canUpdateUi()) return
                    mAdapter?.notifyItemRangeChanged(startPosition, itemCount)
//                    postOnUiThread {
//                        if (canUpdateUi()) {
//                            traceItemViewed()
//                            mRecyclerView?.let {
//                                ManualExposureHelper.exposureViewsByScroll(fraTag, it)
//                            }
//                        }
//                    }
                }
            })
        }

        val provider2 = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_RECOMMEND_WORD)
        if (provider2 is SearchRecommendWordProvider) {
            provider2.setHandleClick(object : SearchRecommendWordProvider.IHandleClickListener {
                override fun onHandleClick(v: View, hotWord: SearchHotWord, type: Int, pageId: Int, position: Int) {
                    if (!canUpdateUi()) return
                    searchDataContext?.onItemClick(v, hotWord, type, pageId, position)
                }
            })
        }

        val provider3 = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_TRACK)
        if (provider3 is SearchTrackCardProvider) {
            provider3.setHandleClick(object : SearchTrackCardProvider.IHandleClickListener {
                override fun onHandleClick(searchItem: SearchItem?, searchItemModel: SearchItemModel,
                                           trackId: Long, albumId: Long, position: Int) {
                    if (!canUpdateUi()) return
                    expandOrFoldTrackList(searchItem, searchItemModel, trackId, albumId, position)
                }
            })
        }

        val provider4 = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_QUESTION_ANSWER_CARD)
        if (provider4 is SearchQuestionAnswerCardProvider) {
            provider4.setHandleClick(object : SearchQuestionAnswerCardProvider.IHandleClickListener {
                override fun onHandleClick(v: View, hotWord: SearchHotWord, type: Int, pageId: Int, position: Int) {
                    if (!canUpdateUi()) return
                    searchDataContext?.onItemClick(v, hotWord, type, pageId, position)
                }
            })
        }

        val provider5 = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_GIFT_FOR_NEW_USER)
        if (provider5 is SearchGiftForNewUserProvider) {
            provider5.setHandleClick(object : SearchGiftForNewUserProvider.IHandleClickListener {
                override fun onHandleClick() {
                    onRefresh()
                }
            })
        }

        val filterGridAlbumProvider = mAdapter?.getProvider(SearchChosenAdapter.VIEW_TYPE_FILTER_ALBUM_GRID)
        (filterGridAlbumProvider as? SearchFilterAlbumGridProvider)?.mActionProvider = object : SearchFilterAlbumGridProvider.IActionProvider {
            override fun addItemModel(item: AdapterProxyData<*>, position: Int) {
                if (!canUpdateUi()) return
                mAdapter?.let {
                    val list = listOf(item)
                    it.addListData(list, position)
                }
            }

            override fun removeItemModel(position: Int) {
                if (!canUpdateUi()) return
                mAdapter?.removeData(position, false)
            }
        }
    }

    private fun setParentClipChildren(clipChildren: Boolean) {
        if (mRecyclerView?.clipChildren == clipChildren) {
            return
        }
        mRecyclerView?.refreshableView?.clipChildren = clipChildren
        mRecyclerView?.clipChildren = clipChildren
        var parent: ViewGroup? = mRecyclerView
        do {
            parent = parent?.parent as? ViewGroup
            parent?.clipChildren = clipChildren
            if (parent?.id == R.id.search_vg_root_container) {
                Logger.i(TAG, "setClipChildren find search root container")
                break
            }
        } while (parent != null)
    }

    private fun setRealRecyclerViewMarginTop(margin: Int) {
        val recyclerView = mRecyclerView?.refreshableView ?: return
        val params = recyclerView.layoutParams as? MarginLayoutParams ?: return
        if (params.topMargin != margin) {
            params.topMargin = margin
            recyclerView.layoutParams = params
        }
    }

    private fun expandOrFoldTrackList(searchItem: SearchItem?, searchItemModel: SearchItemModel,
                                      trackId: Long, albumId: Long, position: Int) {
        if (searchItem == null) {
            return
        }
        if (searchItemModel.isExpandSameAlbumTrack) {
            searchItemModel.isExpandSameAlbumTrack = false
            val size = searchItem.totalList.size
            val startPos = searchItemModel.indexOfList + 1
            val offset = searchItemModel.sameAlbumTrackItemModelList?.size ?: 0
            if (offset > 0 && size >= startPos + offset) {
                val subList = searchItem.totalList.subList(startPos + offset, size)
                for (itemModel in subList) {
                    itemModel.indexOfList -= offset
                }
                searchItem.totalList.subList(startPos, startPos + offset).clear()
                mAdapter?.removeListData(position + 1, offset)
            }
        } else {
            val sameAlbumTrackList = searchItemModel.sameAlbumTrackItemModelList
            if (sameAlbumTrackList.isNullOrEmpty()) {
                SearchCommonRequest.getSameAlbumTrackList(createSameAlbumTrackLoadParams(searchItem, searchItemModel, trackId, albumId),
                        searchItemModel.indexOfList + 1, object : IDataCallBack<List<SearchItemModel>> {
                    override fun onSuccess(data: List<SearchItemModel>?) {
                        if (!canUpdateUi()) {
                            return
                        }
                        if (data.isNullOrEmpty()) {
                            CustomToast.showToast("没有找到相关声音")
                            return
                        }
                        searchItemModel.sameAlbumTrackItemModelList = data
                        doExpandTrackList(searchItem, searchItemModel, data, position)
                    }

                    override fun onError(code: Int, message: String?) {
                        val msg = if (message.isNullOrEmpty()) "网络错误" else message
                        CustomToast.showToast(msg)
                    }
                })
            } else {
                doExpandTrackList(searchItem, searchItemModel, sameAlbumTrackList, position)
            }
        }
    }

    private fun doExpandTrackList(searchItem: SearchItem, searchItemModel: SearchItemModel,
                                  sameAlbumTrackItemModelList: List<SearchItemModel>, position: Int) {
        searchItemModel.isExpandSameAlbumTrack = true
        val size = searchItem.totalList.size
        val startPos = searchItemModel.indexOfList + 1
        val offset = sameAlbumTrackItemModelList.size
        if (size > startPos) {
            val subList = searchItem.totalList.subList(startPos, size)
            for (itemModel in subList) {
                itemModel.indexOfList += offset
            }
        }
        searchItem.totalList.addAll(startPos, sameAlbumTrackItemModelList)
        val itemModels = SearchUtils.getTrackItemModelList(searchItem, sameAlbumTrackItemModelList)
        mAdapter?.addListData(itemModels, position + 1)
        postOnUiThread {
            if (canUpdateUi()) {
                mRecyclerView?.let {
                    ManualExposureHelper.exposureViewsByScroll(fraTag, it)
                }
            }
        }
    }

    private fun createSameAlbumTrackLoadParams(searchItem: SearchItem, searchItemModel: SearchItemModel, trackId: Long, albumId: Long): HashMap<String, Any?> {
        val params = HashMap<String, Any?>()
        try {
            params["kw"] = URLEncoder.encode(keyword, "utf-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        params["needSemantic"] = needSemantic.toString()
        params["plan"] = "d"
        params["spellchecker"] = spellCheck.toString()
        params["search_version"] = SearchActionRouter.SEARCH_VERSION
        params["core"] = if (searchItem.moduleType == SearchItem.SEARCH_TYPE_PODCAST_CARD) "podcast" else "track"
        params["page"] = "1"
        params["paidFilter"] = "false"
        params["live"] = "true"
        params["condition"] = condition
        if (!labelForQuery.isNullOrEmpty()) {
            try {
                params["labelForQuery"] = URLEncoder.encode(labelForQuery, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        params["rows"] = "20"
        params["recall"] = SearchConstants.RECALL_STUFF
        params["voiceAsinput"] = voiceAsInput.toString()
        params["newDevice"] = SearchManager.isNewDevice.toString() + ""
        // 添加筛选项参数
//        val fqRequestStr = getFqRequestParamStr()
//        if (fqRequestStr.isNullOrEmpty()) {
//            params["fq"] = "albumId:${albumId},_id:${trackId}"
//        } else {
//            params["fq"] = fqRequestStr + ",albumId:${albumId},_id:${trackId}"
//        }
        params["searchId"] = SearchTraceUtils.getSearchId()
        params["sourceId"] = SearchTraceUtils.getSourceFromString(typeFrom)
        val foldTrackIds = (searchItemModel.item as? TrackM)?.foldTrackIds?.filterNotNull()
        if (!foldTrackIds.isNullOrEmpty()) {
            val extraParam = HashMap<String, Any>()
            extraParam["foldTrackIds"] = foldTrackIds
            params["extraParam"] = extraParam
        }
        return params
    }

    private fun handleScroll() {
        val manager = mRecyclerView?.refreshableView?.layoutManager ?: return
        if (manager is LinearLayoutManager) {
            val visibleItem = manager.findFirstVisibleItemPosition()
            showGoToTopIcon(visibleItem > SearchConstants.CONFIG_SHOW_GO_TO_TOP_ICON)
        }
    }

    override fun loadData() {
        SearchLabelAlbumCardProvider.needChangeColor = true
        loadSearch(keyword)
    }

    private fun loadSearch(searchWord: String?) {
        if (searchWord.isNullOrEmpty()) {
            CustomToast.showFailToast(R.string.search_input_search_kw)
            return
        }
        if (requestPageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        beginMonitor("SearchChosenFragment-loadSearch")
        mTraceHelper.postNode("准备请求数据")
        val params = HashMap<String, String?>()
        try {
            params["kw"] = URLEncoder.encode(searchWord, "utf-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        params["needSemantic"] = needSemantic.toString()
        params["plan"] = "d"
        params["spellchecker"] = spellCheck.toString()
        params["search_version"] = SearchActionRouter.SEARCH_VERSION
        params["core"] = "chosen3"
        params["page"] = requestPageId.toString()
        params["paidFilter"] = "false"
        params["live"] = "true"
        params["condition"] = condition
        if (!labelForQuery.isNullOrEmpty()) {
            try {
                params["labelForQuery"] = URLEncoder.encode(labelForQuery, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        params["rows"] = DTransferConstants.DEFAULT_PAGE_SIZE.toString() + ""
        params["recall"] = SearchConstants.RECALL_NORMAL
        params["voiceAsinput"] = voiceAsInput.toString()
        params["newDevice"] = SearchManager.isNewDevice.toString() + ""
        // 添加筛选项参数
        val fqRequestStr = getFqRequestParamStr()
        if (!fqRequestStr.isNullOrEmpty()) {
            params["fq"] = fqRequestStr
        }
        if (!TextUtils.isEmpty(saleFq)) {
            if (!fqRequestStr.isNullOrEmpty()) {
                params["fq"] = "$fqRequestStr,$saleFq"
            } else {
                params["fq"] = saleFq
            }
        }
        if (requestPageId > 1 && !mSearchMeta.isNullOrEmpty()) {
            try {
                params["searchMeta"] = URLEncoder.encode(mSearchMeta, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        params["searchId"] = SearchTraceUtils.getSearchId()
        params["sourceId"] = SearchTraceUtils.getSourceFromString(typeFrom)
        mTimeStamp = System.currentTimeMillis()
        loadData(UrlConstants.getInstanse().searchUrl, params, true, mTimeStamp)
    }

    private fun loadSearchForHotPotQuery(extendClickTimes: Int, searchLabel: SearchLabel?, selectList: MutableList<SearchLabel>?) {
        if (keyword.isNullOrEmpty()) {
            CustomToast.showFailToast(R.string.search_input_search_kw)
            return
        }
        if (requestPageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        beginMonitor("SearchChosenFragment-loadSearch")
        mTraceHelper.postNode("准备请求数据")

        val params = HashMap<String, Any?>()
        try {
            params["kw"] = URLEncoder.encode(keyword, "utf-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        params["spellchecker"] = spellCheck.toString()
        params["search_version"] = SearchActionRouter.SEARCH_VERSION
        params["page"] = "1"
        params["paidFilter"] = "false"
        params["condition"] = condition
        params["rows"] = DTransferConstants.DEFAULT_PAGE_SIZE.toString() + ""
        params["newDevice"] = SearchManager.isNewDevice.toString() + ""
        params["searchId"] = SearchTraceUtils.getSearchId()
        params["sourceId"] = SearchTraceUtils.getSourceFromString(typeFrom)
        // 添加筛选项参数
        val fqRequestStr = getFqRequestParamStr()
        if (!fqRequestStr.isNullOrEmpty()) {
            params["fq"] = fqRequestStr
        }
        if (!TextUtils.isEmpty(saleFq)) {
            if (!fqRequestStr.isNullOrEmpty()) {
                params["fq"] = "$fqRequestStr,$saleFq"
            } else {
                params["fq"] = saleFq
            }
        }
        //参数改动点
        params["core"] = "chosen3"
        params["recall"] = "hotspotExtend"
        val extraParam = ExtraParam()
        val labelList = ArrayList<ExtraParam.QueryLabelsBean>()
        selectList?.forEach {
            val queryLabelsBean = ExtraParam.QueryLabelsBean()
            queryLabelsBean.label = it.label
            queryLabelsBean.showName = it.showName
            queryLabelsBean.terms = it.terms
            queryLabelsBean.status = it.status
            labelList.add(queryLabelsBean)
        }
        extraParam.queryLabels = labelList
        extraParam.extendClickTimes = extendClickTimes
        params["extraParam"] = extraParam
        SearchCommonRequest.basePostRequestParmasToJsonNew(
            UrlConstants.getInstanse().searchUrl,
            params, object : IDataCallBack<Any?>{
                override fun onSuccess(data: Any?) {
                    onPageLoadingCompleted(LoadCompleteType.OK)
                }

                override fun onError(code: Int, message: String?) {
                    onPageLoadingCompleted(LoadCompleteType.OK)
                    CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_network_error)
                }
            },
            CommonRequestM.IRequestCallBack<Any?> { content -> parseAndRefreshHotPotCard(content)})
    }

    /**
     * 点击泛搜索卡片标签触发请求卡片数据
     */
    private fun loadSearchForUniversalQuery(searchLabels: MutableList<SearchLabel>?, mainLabel: MainLabel?) {
        if (keyword.isNullOrEmpty()) {
            CustomToast.showFailToast(R.string.search_input_search_kw)
            return
        }
        if (requestPageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        beginMonitor("SearchChosenFragment-loadSearch")
        mTraceHelper.postNode("准备请求数据")

        val params = HashMap<String, Any?>()
        try {
            params["kw"] = URLEncoder.encode(keyword, "utf-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        params["spellchecker"] = spellCheck.toString()
        params["search_version"] = SearchActionRouter.SEARCH_VERSION
        params["page"] = "1"
        params["paidFilter"] = "false"
        params["condition"] = condition
        params["rows"] = DTransferConstants.DEFAULT_PAGE_SIZE.toString() + ""
        params["newDevice"] = SearchManager.isNewDevice.toString() + ""
        params["searchId"] = SearchTraceUtils.getSearchId()
        params["sourceId"] = SearchTraceUtils.getSourceFromString(typeFrom)
        // 添加筛选项参数
        val fqRequestStr = getFqRequestParamStr()
        if (!fqRequestStr.isNullOrEmpty()) {
            params["fq"] = fqRequestStr
        }
        if (!TextUtils.isEmpty(saleFq)) {
            if (!fqRequestStr.isNullOrEmpty()) {
                params["fq"] = "$fqRequestStr,$saleFq"
            } else {
                params["fq"] = saleFq
            }
        }
        //参数改动点
        params["core"] = "album"
        params["recall"] = SearchConstants.RECALL_LABEL
        var extraParam = ExtraParam()
        var labelList = ArrayList<ExtraParam.QueryLabelsBean>()
        searchLabels?.forEach {
            val queryLabelsBean = ExtraParam.QueryLabelsBean()
            queryLabelsBean.label = it.label
            labelList.add(queryLabelsBean)
        }
        extraParam.queryLabels = labelList
        if (mainLabel != null) {
            extraParam.mainLabel = mainLabel
        }
        params["extraParam"] = extraParam
        SearchCommonRequest.basePostRequestParmasToJsonNew(
            UrlConstants.getInstanse().searchUrl,
            params, object : IDataCallBack<Any?>{
                override fun onSuccess(data: Any?) {
                    onPageLoadingCompleted(LoadCompleteType.OK)
                }

                override fun onError(code: Int, message: String?) {
                    CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_network_error)
                }
            },
            CommonRequestM.IRequestCallBack<Any?> { content -> parseAndRefreshLabelCard(content)})
    }

    /**
     * 点击内容池卡片标签触发请求卡片数据
     */
    private fun loadSearchForPoolLabel(searchLabels: MutableList<SearchLabel>?, extendClickTimes: Int) {
        if (keyword.isNullOrEmpty()) {
            CustomToast.showFailToast(R.string.search_input_search_kw)
            return
        }
        if (requestPageId == 1) {
            onPageLoadingCompleted(LoadCompleteType.LOADING)
        }
        beginMonitor("SearchChosenFragment-loadSearch")
        mTraceHelper.postNode("准备请求数据")

        val params = HashMap<String, Any?>()
        try {
            params["kw"] = URLEncoder.encode(keyword, "utf-8")
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        params["spellchecker"] = spellCheck.toString()
        params["search_version"] = SearchActionRouter.SEARCH_VERSION
        params["page"] = "1"
        params["paidFilter"] = "false"
        params["condition"] = condition
        params["rows"] = DTransferConstants.DEFAULT_PAGE_SIZE.toString() + ""
        params["newDevice"] = SearchManager.isNewDevice.toString() + ""
        params["searchId"] = SearchTraceUtils.getSearchId()
        params["sourceId"] = SearchTraceUtils.getSourceFromString(typeFrom)
        // 添加筛选项参数
        val fqRequestStr = getFqRequestParamStr()
        if (!fqRequestStr.isNullOrEmpty()) {
            params["fq"] = fqRequestStr
        }
        if (!TextUtils.isEmpty(saleFq)) {
            if (!fqRequestStr.isNullOrEmpty()) {
                params["fq"] = "$fqRequestStr,$saleFq"
            } else {
                params["fq"] = saleFq
            }
        }
        //参数改动点
        params["core"] = "chosen3"
        params["recall"] = "poolLabelAlbum"
        val extraParam = ExtraParam()
        val labelList = ArrayList<ExtraParam.QueryLabelsBean>()
        searchLabels?.forEach {
            val queryLabelsBean = ExtraParam.QueryLabelsBean()
            queryLabelsBean.label = it.label
            queryLabelsBean.id = it.id
            labelList.add(queryLabelsBean)
        }
        extraParam.queryLabels = labelList
        extraParam.extendClickTimes = extendClickTimes
        params["extraParam"] = extraParam
        SearchCommonRequest.basePostRequestParmasToJsonNew(
            UrlConstants.getInstanse().searchUrl,
            params, object : IDataCallBack<Any?>{
                override fun onSuccess(data: Any?) {
                    onPageLoadingCompleted(LoadCompleteType.OK)
                }

                override fun onError(code: Int, message: String?) {
                    CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_network_error)
                }
            }
        ) { content -> parseAndRefreshPoolLabelCard(content) }
    }

    private fun getFqRequestParamStr(): String? {
        return when (paidType) {
            SearchConstants.PAID_TYPE_PAID -> "is_paid:true"
            SearchConstants.PAID_TYPE_FREE -> "is_paid:false"
            SearchConstants.PAID_TYPE_VIP -> "is_paid:vip"
            else -> null
        }
    }

    fun getLabelForTrace(): String {
        return if (!labelForQuery.isNullOrEmpty()) labelForQuery!! else conditionName
    }

    override fun parse(json: String?, timeStamp: Long): SearchChosenPageContent? {
        if (json.isNullOrEmpty()) {
            return null
        }
        beginMonitor("SearchChosenFragment-parse")
        mTraceHelper.postNode("开始解析数据")
        var data: JSONObject? = null
        try {
            data = JSONObject(json)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        if (data == null) {
            endMonitor()
            return null
        }
        val searchChosenPageContent = SearchChosenPageContent()
        searchChosenPageContent.timeStamp = timeStamp
        if (timeStamp != 0L && timeStamp != mTimeStamp) {
            endMonitor()
            return searchChosenPageContent
        }
        //原因
        if (data.has(SearchConstants.REASON)) {
            searchChosenPageContent.reason = data.optString(SearchConstants.REASON)
            if ("risk:5" == searchChosenPageContent.reason) {
                // 业务风控统计  其他事件
                XMTraceApi.Trace()
                    .setMetaId(65748)
                    .setServiceId("others")
                    .put("source", "搜索-search_app")
                    .put("errorMessage", "") // 传错误码
                    .put("content", "") // 自定义内容
                    .put("msgType", searchChosenPageContent.reason) // 传原因
                    .createTrace()
            }
        }
        SearchItem.hasNoContent = false
        mHasBrandAdItem = false
        if (data.has(SearchConstants.MODULE_LIST) || data.has(SearchConstants.HEAD_LIST)) {
            val searchItems = SearchItem.parseSearchItemList(data, searchChosenPageContent.reason)
            searchChosenPageContent.searchItems = searchItems
            //大促+其他筛选项后，页面刷新结果，但是大促按钮筛选状态不变
            if (!searchItems.isNullOrEmpty() && searchItems[0].moduleType == SearchItem.SEARCH_TYPE_SALE) {
                searchChosenPageContent.searchItems[0].saleSelected = isSaleSelected
            }
            searchItems?.forEach { searchItem ->
                if (searchItem.moduleType == SearchItem.SEARCH_TYPE_AD_BRAND){
                    mImmersiveFrom = SearchBrandAdBgManager.FROM_AD
                    mHasBrandAdItem = true
                    return@forEach
                }
            }

            // 最佳匹配是否是沉浸色
            if (SearchTopAlbumColorUtils.isShowImmersiveBgColor(searchItems?.getOrNull(0))) {
                mImmersiveFrom = SearchBrandAdBgManager.FROM_TOP_ALBUM
                mHasBrandAdItem = true
            }

            if (mHasBrandAdItem) {
                // 点筛选弹窗的时候不刷新背景，否则点击重置时弹窗上方的背景会出现
                if (!SearchBrandAdBgManager.hasNoAdBg() && !mIsPopupItemClick) {
                    SearchBrandAdBgManager.sendListScrollColorChangeBroad(
                        SearchBrandAdBgManager.topBgMainColor,
                        1f,
                        context,
                        mImmersiveFrom
                    )
                }
            }
        }

        val searchChosenResponse = SearchChosenResponse.parse(data)
        searchChosenPageContent.response = searchChosenResponse
        mSearchMeta = if (searchChosenResponse != null) searchChosenResponse.searchMeta else ""

//        if (data.has(SearchConstants.CORRECTION_WORDS)) {
//            searchChosenPageContent.sq = data.optString(SearchConstants.CORRECTION_WORDS)
//        }

        if (data.has(SearchConstants.RESPONSE_HEADER)) {
            val responseHeader = SearchResponseHeader.parse(data.optString(SearchConstants.RESPONSE_HEADER))
            searchChosenPageContent.searchResponseHeader = responseHeader
        }

        if (data.has(SearchConstants.LABEL_FOR_QUERY_LIST)) {
            val jsonArray = data.optJSONArray(SearchConstants.LABEL_FOR_QUERY_LIST)
            val labelList: MutableList<SearchLabelData> = ArrayList()
            if (jsonArray != null && jsonArray.length() > 0) {
                for (i in 0 until jsonArray.length()) {
                    val label = jsonArray.optString(i)
                    if (!label.isNullOrEmpty()) {
                        val labelData = SearchLabelData(label, i == 0)
                        labelList.add(labelData)
                    }
                }
            }
            searchChosenPageContent.labelForQueryList = labelList
        }

        if (data.has(SearchConstants.REASON_DOCS)) {
            val reasonDocs = SearchReasonDocs.parse(data.optString(SearchConstants.REASON_DOCS))
            searchChosenPageContent.searchReasonDocs = reasonDocs
        }

        //头部无结果处理
        val numFound = searchChosenResponse?.numFound ?: 0
        val searchItems = searchChosenPageContent.searchItems
        val noData = isRefresh || isIntercept || mAdapter?.listData?.isEmpty() == true
        if (noData) {
            SearchUtils.resetAlbumStreamTitleShowStatus()
        }

        val itemModels = SearchUtils.castSearchItemToItemModels(searchItems, true)
        searchChosenPageContent.listItemModels = itemModels
        endMonitor()
        AIAgentSearchUtil.rnSearchResult = json
        mTraceHelper.postNode("数据解析完成")
        return searchChosenPageContent
    }

    private fun parseAndRefreshHotPotCard(json: String?) {
        if (json.isNullOrEmpty()) {
            return
        }
        beginMonitor("SearchChosenFragment-parse-universal-query")
        var data: JSONObject? = null
        try {
            data = JSONObject(json)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        if (data == null) {
            endMonitor()
            return
        }
        if (data.has(SearchConstants.MODULE_LIST) || data.has(SearchConstants.HEAD_LIST)) {
            val searchItems = SearchItem.parseSearchItemList(data, data.optString(SearchConstants.REASON))
            val itemModels = SearchUtils.castSearchItemToItemModels(searchItems, true)
            itemModels?.forEach {
                if (SearchChosenAdapter.VIEW_TYPE_HOT_POT_CARD == it.viewType) {
                    val index = mAdapter?.listData?.indexOfFirst { listItem -> SearchChosenAdapter.VIEW_TYPE_HOT_POT_CARD == listItem.viewType }
                    if (index != null && index >= 0) {
                        HandlerManager.postOnUIThread {
                            mAdapter?.listData?.set(index, it)
                            mRecyclerView?.refreshableView?.let { rcv ->
                                val layoutManager = rcv.layoutManager
                                if (layoutManager is LinearLayoutManager) {
                                    val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                    if (index in firstVisiblePosition..lastVisiblePosition) {
                                        mAdapter?.refreshHotPotCard(index, rcv.getChildAt(index))
                                    }
                                }
                            }
                        }
                    }
                    return@forEach
                }
            }
        }
        endMonitor()
    }

    private fun parseAndRefreshLabelCard(json: String?){
        if (json.isNullOrEmpty()) {
            return
        }
        beginMonitor("SearchChosenFragment-parse-universal-query")
        var data: JSONObject? = null
        try {
            data = JSONObject(json)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        if (data == null) {
            endMonitor()
            return
        }

//        val searchChosenResponse = SearchChosenResponse.parse(data)
//        mSearchMeta = if (searchChosenResponse != null) searchChosenResponse.searchMeta else ""

        if (data.has(SearchConstants.MODULE_LIST) || data.has(SearchConstants.HEAD_LIST)) {
            val searchItems = SearchItem.parseSearchItemList(data, data.optString(SearchConstants.REASON))
            val itemModels = SearchUtils.castSearchItemToItemModels(searchItems, true)
            itemModels?.forEach {
                if (SearchChosenAdapter.VIEW_TYPE_UNIVERSAL_QUERY_CARD == it.viewType){
                    val index = mAdapter?.listData?.indexOfFirst { listItem -> SearchChosenAdapter.VIEW_TYPE_UNIVERSAL_QUERY_CARD == listItem.viewType }
                    if (index !=null && index >= 0) {
                        HandlerManager.postOnUIThread {
                            mAdapter?.listData?.set(index, it)
                            mRecyclerView?.refreshableView?.let { rcv ->
                                val layoutManager = rcv.layoutManager
                                if (layoutManager is LinearLayoutManager) {
                                    val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                    if (index in firstVisiblePosition..lastVisiblePosition) {
                                        mAdapter?.refreshLabelAlbumCard(index, rcv.getChildAt(index))
                                    }
                                }
                            }
                        }
                    }
                    return@forEach
                }
            }
        }
        endMonitor()
    }

    private fun parseAndRefreshPoolLabelCard(json: String?){
        if (json.isNullOrEmpty()) {
            return
        }
        var data: JSONObject? = null
        try {
            data = JSONObject(json)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        if (data == null) {
            endMonitor()
            return
        }

//        val searchChosenResponse = SearchChosenResponse.parse(data)
//        mSearchMeta = if (searchChosenResponse != null) searchChosenResponse.searchMeta else ""

        if (data.has(SearchConstants.MODULE_LIST) || data.has(SearchConstants.HEAD_LIST)) {
            val searchItems = SearchItem.parseSearchItemList(data, data.optString(SearchConstants.REASON))
            val itemModels = SearchUtils.castSearchItemToItemModels(searchItems, true, false)
            itemModels?.forEach {
                if (SearchChosenAdapter.VIEW_TYPE_POOL_LABEL_ALBUM_CARD == it.viewType){
                    val index = mAdapter?.listData?.indexOfFirst { listItem -> SearchChosenAdapter.VIEW_TYPE_POOL_LABEL_ALBUM_CARD == listItem.viewType }
                    if (index !=null && index >= 0) {
                        HandlerManager.postOnUIThread {
                            mAdapter?.listData?.set(index, it)
                            mRecyclerView?.refreshableView?.let { rcv ->
                                val layoutManager = rcv.layoutManager
                                if (layoutManager is LinearLayoutManager) {
                                    val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                    if (index in firstVisiblePosition..lastVisiblePosition) {
                                        val view = rcv.getChildAt(index - firstVisiblePosition)
                                        mAdapter?.refreshPoolLabelAlbumCard(index, view)
                                    }
                                }
                            }
                        }
                    }
                    return@forEach
                }
            }
        }
    }

    private fun beginMonitor(behavior: String) {
        if (ConstantsOpenSdk.isDebug) {
            TraceCompat.beginSection(behavior)
        }
    }

    private fun endMonitor() {
        if (ConstantsOpenSdk.isDebug) {
            TraceCompat.endSection()
        }
    }

    private var xmRequestId = ""

    override fun updatePage(data: SearchChosenPageContent?): LoadCompleteType? {
        xmRequestId = XmRequestIdManager.getInstance(context).requestId
        endMonitor()
        mTraceHelper.postNode("数据加载完成")
        if (data != null) {
            if (data.timeStamp != 0L && data.timeStamp != mTimeStamp) {
                return null
            }
            this.data = data
            val chosenResponse = data.response
            val hasResult = chosenResponse?.numFound ?: 0 > 0
            val itemModelList = data.listItemModels
            var hasImmersiveBanner = itemModelList.find { it.viewType == SearchChosenAdapter.VIEW_TYPE_BLOCK_BANNER } != null
            val hasImmersiveBrandAd = hasImmersiveBrandAd(itemModelList)
            if (hasImmersiveBrandAd) {
                hasImmersiveBanner = true
            }
            if (isFirstLoadTab && showFilterView() && !hasImmersiveBanner && hasResult) {
                SearchUiUtils.setVisible(View.VISIBLE, mFilterLayout)
                val labelList = data.labelForQueryList
                mSearchFilterProxy?.apply {
                    updateLabelView(labelList, null, null, 0, isLoadMore)
                    createNormalFilterPopupWindow(labelList, sortFilterDataList)
                }
                if (!labelList.isNullOrEmpty()) {
                    labelForQuery = "全部"
                }
            } else if (isFirstLoadTab) {
                SearchUiUtils.setVisible(View.GONE, mFilterLayout)
                mRecyclerView?.apply {
                    val params = layoutParams as MarginLayoutParams
                    params.topMargin = 0
                }
            }
            if (!itemModelList.isNullOrEmpty()) {
                if (mNeedShowNewUserGiftToastIfNeed) {
                    mNeedShowNewUserGiftToastIfNeed = false
                    if (SearchUtils.hasNewUserGiftWithLoginModule()) {
                        CustomToast.showToast("领取成功")
                    }
                }
                updateHasMore(data.response)
                var hasBlockBanner = itemModelList.find { it.viewType == SearchChosenAdapter.VIEW_TYPE_BLOCK_BANNER } != null
                if (!hasBlockBanner) {
                    hasBlockBanner = mAdapter?.listData?.find { it.viewType == SearchChosenAdapter.VIEW_TYPE_BLOCK_BANNER } != null
                }
                var hasImmersiveBrandAd = hasImmersiveBrandAd(itemModelList)
                if (!hasImmersiveBrandAd) {
                    hasImmersiveBrandAd = hasImmersiveBrandAd(mAdapter?.listData)
                }
                if (hasImmersiveBrandAd) {
                    hasBlockBanner = true
                }
                if (hasBlockBanner) {
                    val location = IntArray(2)
                    mRecyclerView?.post {
                        mRecyclerView?.getLocationOnScreen(location)
                        setRealRecyclerViewMarginTop(-location[1])
                        setParentClipChildren(false)
                        val bannerHeight = SearchImmersiveBannerManager.placeHeight(context)
                        SearchImmersiveBannerManager.mListScrollMaxY = bannerHeight - location[1]
                    }
                    SearchImmersiveBannerManager.enterImmersiveMode()
                    if (hasImmersiveBrandAd) {
                        SearchImmersiveBannerManager.onListScrolled(0)
                    }
                } else {
                    setRealRecyclerViewMarginTop(0)
                    setParentClipChildren(true)
                    SearchImmersiveBannerManager.exitImmersiveMode()
                }
                updateListView(itemModelList)
                pageErrorMonitor(true)
                isIntercept = false
                if (view != null) {
                    mTraceHelper.postPageEndNodeAfterRenderComplete(view)
                } else {
                    notifyTraceFinish()
                }

                if (!isLoadMore && !SearchBrandAdBgManager.hasNoAdBg()
                    && immersiveFrom == SearchBrandAdBgManager.FROM_TOP_ALBUM
                ) { // 跳转回顶部(处理快速滑动导致的显示异常问题)
                    mRecyclerView?.refreshableView?.scrollToPosition(0)
                }

                return LoadCompleteType.OK
            } else {
                updateHasMore(false, hasResult)
            }
        } else {
            this.data = null
            updateHasMore(hasMore = false, hasResult = false)
        }
        pageErrorMonitor(false)
        if (isLoadMore) {
            isIntercept = false
        }
        notifyTraceFinish()
        return if (isLoadMore) LoadCompleteType.OK else noData()
    }

    private fun hasImmersiveBrandAd(itemModelList: List<AdapterProxyData<*>>?): Boolean {
        val itemModel =
            itemModelList?.find { it.viewType == SearchChosenAdapter.VIEW_TYPE_BRANDZONE }
        return ((itemModel?.data as? SearchItemModel)?.item as? SearchNewItem)?.brandAdModel?.advertis?.showstyle == IMG_SHOW_STYLE_SEARCH_BRAND_AD_IMMERSIVE
    }

    private fun pageErrorMonitor(isSuccess: Boolean) {
        if (!isLoadMore) {
            val result = if (isSuccess) SearchPageLoadErrorConstants.RESULT_SUCCESS else SearchPageLoadErrorConstants.RESULT_NO_DATA
            XmPageErrorMonitor.post(SearchPageLoadErrorConstants.SEARCH_CHOSEN_PAGE_B, result)
        }
    }

    override fun noData(): LoadCompleteType? {
        if (!isFirstLoadTab) {
            setNoFilterDataListView()
            isIntercept = false
            return LoadCompleteType.NOCONTENT
        }
        SearchUiUtils.setVisible(View.GONE, mFilterLayout)
        mRecyclerView?.apply {
            val params = layoutParams as MarginLayoutParams
            params.topMargin = 0
        }
        isIntercept = false
        return super.noData()
    }

    override fun onPageLoadingCompleted(loadCompleteType: LoadCompleteType?) {
        super.onPageLoadingCompleted(loadCompleteType)
        if (loadCompleteType != null && loadCompleteType == LoadCompleteType.NOCONTENT) {
            traceAfterLoad()
        }
    }

    private fun setNoFilterDataListView() {
        mRecyclerView?.apply {
            if (isRefresh || isIntercept) {
                mAdapter?.clear()
                mAdapter?.notifyDataSetChanged()
            }
            onRefreshComplete(false)
        }
    }

    private fun updateHasMore(searchChosenResponse: SearchChosenResponse?) {
        hasMore = searchChosenResponse != null && requestPageId < searchChosenResponse.totalPage
        val hasResult = searchChosenResponse?.numFound ?: 0 > 0
        updateHasMore(hasMore, hasResult)
    }

    private fun updateHasMore(hasMore: Boolean, hasResult: Boolean) {
        mRecyclerView?.onRefreshComplete(hasMore)
        mRecyclerView?.hideFootView()
        if (!hasMore && hasResult) {
            val color = ContextCompat.getColor(mContext, R.color.search_color_f6f7f8_000000)
            mRecyclerView?.setFootViewText(SearchConstants.NO_MORE_HINT, color)
        }
    }

    private fun notifyTraceFinish() {
        mTraceHelper.notifyPageFailed()
    }

    private fun updateListView(listData: List<AdapterProxyData<Any>>?) {
        if (isRefresh || isIntercept) {
            mAdapter?.clear()
            ManualExposureHelper.onPageDestroy(fraTag)
        }

        mAdapter?.apply {
            val isRefresh = getListData().isEmpty()
            setListData(listData, isRefresh)

            if (isFirstLoadTab && mIsAutoPlay && !listData.isNullOrEmpty()) {
                val trackOrAlbum = getFirstAlbumOrTrack(listData)
                if (trackOrAlbum is Album) {
                    PlayTools.playByAlbumByIdIfHasHistoryUseHistory(mContext, trackOrAlbum.id, null)
                } else if (trackOrAlbum is Track) {
                    PlayTools.playTrack(mContext, trackOrAlbum as Track, false, null)
                }
            }
        }
        postOnUiThread {
            if (canUpdateUi()) {
                traceItemViewed()
            }
        }
        traceAfterLoad()
    }

    private fun traceAfterLoad() {
        val isPullRefresh = !isLoadMore
        postOnUiThread {
            if (canUpdateUi()) {
                mRecyclerView?.let {
                    ManualExposureHelper.exposureViewsByRequest(fraTag, it, isPullRefresh)
                }
            }
        }
    }

    override fun onLoadError(code: Int, message: String?): LoadCompleteType? {
        endMonitor()
        SearchTraceUtils.traceSearchNetworkError(true)
        CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_network_error)
        isIntercept = false
        notifyTraceFinish()
        return if (isLoadMore) {
            isLoadMore = false
            isRefresh = false
            mRecyclerView?.onRefreshComplete(hasMore)
            LoadCompleteType.OK
        } else {
            //刷新时布局在筛选下面
            mRecyclerView?.onRefreshComplete(false)
            mAdapter?.clear()
            mAdapter?.notifyDataSetChanged()
            if (isFirstLoadTab && showFilterView()) {
                SearchUiUtils.setVisible(View.INVISIBLE, mFilterLayout)
            }
            val isNetworkAvailable = NetworkUtils.isNetworkAvaliable(mContext)
            val result = if (isNetworkAvailable) SearchPageLoadErrorConstants
                    .RESULT_API_REQUEST_FAILED else SearchPageLoadErrorConstants.RESULT_NO_NETWORK
            XmPageErrorMonitor.post(SearchPageLoadErrorConstants.SEARCH_CHOSEN_PAGE_B, result, null, message)
            super.onLoadError(code, message)
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        setWatchFragmentChangeState(isVisibleToUser)
        if (isVisibleToUser) {
            mNeedRefreshAfterLogin = false
            XmPlayerManager.getInstance(mActivity).addPlayerStatusListener(this)
            XmPlayerManager.getInstance(mActivity).addAdsStatusListener(this)
            resumeInnerDisplay()
            notifyAdapter()
        } else {
            XmPlayerManager.getInstance(mActivity).removePlayerStatusListener(this)
            XmPlayerManager.getInstance(mActivity).removeAdsStatusListener(this)
            pauseInnerDisplay()
            dismissDialogWindow()
        }
        if (isVisibleToUser && isResumed) {
            ManualExposureHelper.exposureViewsByResume(fraTag, mRecyclerView)
        }
        if (mIsFirstLoad) {
            mIsFirstLoad = false
        } else {
            if (!mIsVisibleToUser && isVisibleToUser && isResumed) {
                tracePageView()
            } else if (mIsVisibleToUser && !isVisibleToUser) {
                tracePageExit()
            }
            mIsVisibleToUser = isVisibleToUser
        }
    }

    override fun onResume() {
        super.onResume()
        // 这里是为了解决精选tab从第一个位置变成第二个位置 初始化onMyResume 没走导致的监听不生效问题
        val enable = ConfigureCenter.getInstance().getBool(
            CConstants.Group_ad.GROUP_NAME,
            CConstants.Group_ad.ITEM_SEARCH_BRAND_FILTER_COLOR_ENABLE,
            true
        )
        if (enable) {
            registerTopColorChangeReceiver()
        }
    }

    override fun onMyResume() {
        super.onMyResume()
        mNeedRefreshAfterLogin = false
        registerTopColorChangeReceiver()
        setWatchFragmentChangeState(true)
        XmPlayerManager.getInstance(mActivity).addPlayerStatusListener(this)
        XmPlayerManager.getInstance(mActivity).addAdsStatusListener(this)
        resumeInnerDisplay()
        notifyAdapter()
        tracePageView()
        postRunnable {
            ManualExposureHelper.exposureViewsByResume(fraTag, mRecyclerView)
        }

        CustomHomeManager.checkShowGuideDialog(this, getSearchWord()) {
            mRecyclerView?.refreshableView?.smoothScrollToPosition(0)

            HandlerManager.postOnUIThreadDelay({
                val intent = Intent(CustomHomeManager.ACTION_CUSTOM_HOME_SHAKE)
                LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext()).sendBroadcast(intent)
                CustomHomeManager.log("sendBroadcast after smoothScrollToPosition")
            }, 700)
        }
    }

    override fun onPause() {
        super.onPause()
        unregisterTopColorChangeReceiver()
        setWatchFragmentChangeState(false)
        XmPlayerManager.getInstance(mActivity).removePlayerStatusListener(this)
        XmPlayerManager.getInstance(mActivity).removeAdsStatusListener(this)
        pauseInnerDisplay()
        dismissDialogWindow()
        tracePageExit()
        mTraceHelper.abandon()

        SatisfactionSearchManager.pagePause()
    }

    override fun onDestroy() {
        setParentClipChildren(true)
        SearchImmersiveBannerManager.exitImmersiveMode()
        SearchImmersiveBannerManager.removeOnColorChangeListener(mOnColorChangeListener)
        super.onDestroy()
        ManualExposureHelper.onPageDestroy(fraTag)
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginStatusListener)
        ReactNativeAdapter.releaseBusinessData(this);
        if (SearchReactNativeSetting.reactNativeCardShowed) {
            SearchReactNativeSetting.notifyDestroy(context);
        }
        AlbumEventManage.removeListener(this)
        AnchorFollowManage.getSingleton().removeFollowListener(this)

        SatisfactionSearchManager.release()
        destroyInnerDisplay()
    }

    private fun tracePageView() {
        // 新精选tab页  页面展示
        XMTraceApi.Trace()
                .pageView(43628, "新精选tab页")
                .put("searchWord", keyword)
                .put("searchId", SearchTraceUtils.getSearchId())
                .put("currPage", "新精选tab页")
                .createTrace()
    }

    private fun tracePageExit() {
        // 新精选tab页  页面离开
        XMTraceApi.Trace()
                .pageExit2(43629)
                .put("searchWord", keyword)
                .put("searchId", SearchTraceUtils.getSearchId())
                .createTrace()
    }

    private fun setWatchFragmentChangeState(watch: Boolean) {
        manageFragment?.apply {
            if (watch) {
                addStackChangeListener(mStackChangeListenerWrapper)
            } else {
                removeStackChangeListener(mStackChangeListenerWrapper)
            }
        }
    }

    private fun resumeInnerDisplay() {
        if (mAdapter?.itemCount ?: 0 <= 0) {
            return
        }
        postOnUiThread {
            mRecyclerView?.refreshableView?.apply {
                val layoutManager = layoutManager
                if (layoutManager is LinearLayoutManager) {
                    val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                    val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                    val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
                    for (i in 0..visibleItemCount) {
                        mAdapter?.onResume(i + firstVisiblePosition, getChildAt(i))
                    }
                }
            }
            //埋点优化
            traceItemViewed()
        }
    }

    private fun pauseInnerDisplay() {
        if (mAdapter?.itemCount ?: 0 <= 0) {
            return
        }
        mRecyclerView?.refreshableView?.apply {
            val layoutManager = layoutManager
            if (layoutManager is LinearLayoutManager) {
                val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
                for (i in 0..visibleItemCount) {
                    mAdapter?.onPause(i + firstVisiblePosition, getChildAt(i))
                }
            }
        }
    }

    private fun destroyInnerDisplay() {
        if (mAdapter?.itemCount ?: 0 <= 0) {
            return
        }
        mRecyclerView?.refreshableView?.apply {
            val layoutManager = layoutManager
            if (layoutManager is LinearLayoutManager) {
                val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
                val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
                for (i in 0..visibleItemCount) {
                    mAdapter?.onDestroy(i + firstVisiblePosition, getChildAt(i))
                }
            }
        }
    }

    private fun createNormalFilterPopupWindow(
        labelList: List<SearchLabelData>?,
        sortFilterDataList: List<SearchSortFilterData>?
    ) {
        if (mNormalFilterPopupWindow != null) {
            return
        }
        mNormalFilterPopupWindow = SearchNormalFilterPopupWindow(mContext,
            null,
            null,
            3,
            labelList,
            sortFilterDataList,
            if (showPaidTypeView()) createPaidFilterData() else null,
            object : SearchNormalFilterPopupWindow.IOnItemClickListener {
                override fun onLabelItemClick(labelData: SearchLabelData, position: Int) {
                    labelForQuery = labelData.labelName
                    isIntercept = true
                    mIsPopupItemClick = true
                    SearchTopAlbumColorUtils.mFilterPopupItemClick = true
                    onRefresh()
                    mSearchFilterProxy?.updateLabelOrFilterStatus(
                        position,
                        SearchLabelOrSortFilterAdapter.TYPE_LABEL
                    )
                    resetFilterTv()
                }

                override fun onSearchLabelItemClick(
                    searchLabels: MutableList<SearchLabel>?,
                    mainLabel: MainLabel?
                ) {
                }

                override fun onSortItemClick(sortFilterData: SearchSortFilterData, position: Int) {
                    condition = sortFilterData.value
                    conditionName = sortFilterData.displayName
                    isIntercept = true
                    mIsPopupItemClick = true
                    SearchTopAlbumColorUtils.mFilterPopupItemClick = true
                    onRefresh()
                    mSearchFilterProxy?.updateLabelOrFilterStatus(
                        position,
                        SearchLabelOrSortFilterAdapter.TYPE_SORT_FILTER
                    )
                    resetFilterTv()
                }

                override fun onPaidItemClick(paidFilterData: SearchPaidFilterData) {
                    paidType = paidFilterData.paidType
                    isIntercept = true
                    mIsPopupItemClick = true
                    SearchTopAlbumColorUtils.mFilterPopupItemClick = true
                    onRefresh()
                    mSearchFilterProxy?.updateLabelOrFilterStatus(
                        -1,
                        SearchLabelOrSortFilterAdapter.TYPE_NONE
                    )
                    resetFilterTv()
                }

                override fun onResetClick() {
                    if (!labelForQuery.isNullOrEmpty()) {
                        labelForQuery = "全部"
                    }
                    condition = SearchConstants.CONDITION_RELATION
                    conditionName = SearchConstants.CONDITION_RELATION_NAME
                    paidType = SearchConstants.PAID_TYPE_NONE
                    isIntercept = true
                    mIsPopupItemClick = true
                    SearchTopAlbumColorUtils.mFilterPopupItemClick = true
                    onRefresh()
                    mSearchFilterProxy?.updateLabelOrFilterStatus(
                        0,
                        SearchLabelOrSortFilterAdapter.TYPE_NONE
                    )
                    resetFilterTv()
                }

                override fun onDismiss() {
                    mMask?.visibility = View.GONE
                    mRecyclerView?.apply {
                        ViewCompat.setImportantForAccessibility(
                            this,
                            ViewCompat.IMPORTANT_FOR_ACCESSIBILITY_YES
                        )
                    }
                    mSearchFilterProxy?.resetFilterTvStr(false)
                    searchDataContext?.setViewPagerSlideStatus(true)
                    if (mHasBrandAdItem) {
                        SearchBrandAdBgManager.sendListScrollColorChangeBroad(
                            SearchBrandAdBgManager.topBgMainColor,
                            mRealListScrollOffset,
                            context,
                            mImmersiveFrom
                        )
                    }
                    mIsPopupItemClick = false
                    SearchTopAlbumColorUtils.mFilterPopupItemClick = false
                }
            },
            "chosen"
        )
    }

    private fun resetFilterTv() {
        mSearchFilterProxy?.resetFilterTv(paidType != SearchConstants.PAID_TYPE_NONE
                || !labelForQuery.isNullOrEmpty() && "全部" != labelForQuery
                || condition.isNotEmpty() && SearchConstants.CONDITION_RELATION != condition)
    }

    private fun showNormalFilterPopupWindow(labelList: List<SearchLabelData>?, sortFilterDataList: List<SearchSortFilterData>?) {
        if (mNormalFilterPopupWindow?.isShowing == true) {
            dismissNormalFilterWindow()
            return
        } else if (mNormalFilterPopupWindow == null) {
            createNormalFilterPopupWindow(labelList, sortFilterDataList)
        }
        mMask?.visibility = View.VISIBLE
        mRecyclerView?.apply {
            ViewCompat.setImportantForAccessibility(this, ViewCompat.IMPORTANT_FOR_ACCESSIBILITY_NO)
        }
        mFilterLayout?.apply {
            mNormalFilterPopupWindow?.show(this)
        }
        mSearchFilterProxy?.resetFilterTvStr(true)
        searchDataContext?.setViewPagerSlideStatus(false)
    }

    private fun createSimpleSortFilterData(): List<SearchSortFilterData> {
        val filter1 = SearchSortFilterData(SearchConstants.CONDITION_RELATION, SearchConstants.CONDITION_RELATION_NAME, true)
        val filter2 = SearchSortFilterData(SearchConstants.CONDITION_PLAY, SearchConstants.CONDITION_PLAY_NAME)
        val filter3 = SearchSortFilterData(SearchConstants.CONDITION_RECENT, SearchConstants.CONDITION_RECENT_NAME)
        return listOf(filter1, filter2, filter3)
    }

    private fun createPaidFilterData(): List<SearchPaidFilterData> {
        val filter1 = SearchPaidFilterData(SearchConstants.PAID_TYPE_NONE, SearchConstants.PAID_TYPE_NONE_NAME, true)
        val filter2 = SearchPaidFilterData(SearchConstants.PAID_TYPE_PAID, SearchConstants.PAID_TYPE_PAID_NAME)
        val filter3 = SearchPaidFilterData(SearchConstants.PAID_TYPE_FREE, SearchConstants.PAID_TYPE_FREE_NAME)
        val filter4 = SearchPaidFilterData(SearchConstants.PAID_TYPE_VIP, SearchConstants.PAID_TYPE_VIP_NAME)
        return listOf(filter1, filter2, filter3, filter4)
    }

    private fun dismissDialogWindow() {
        dismissNormalFilterWindow()
    }

    private fun dismissNormalFilterWindow() {
        mNormalFilterPopupWindow?.apply {
            if (isShowing) {
                dismiss()
            }
        }
    }

    private fun notifyAdapter() {
        mAdapter?.notifyDataSetChanged()
    }

    override fun onPrepareNoContentView(): Boolean {
        setNoContentImageView(com.ximalaya.ting.android.host.R.drawable.host_no_search_result)
        return false
    }

    override fun setNoContentTitleLayout(titleView: View) {
        if (titleView is TextView) {
            titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
            titleView.setLineSpacing(0f, 1.2f)
            titleView.gravity = Gravity.CENTER
            val hasTipMsg = data != null && data.searchReasonDocs != null && !data.searchReasonDocs.tipMsg.isNullOrEmpty()
            val noContentHint = if (hasTipMsg) SearchUtils.getSearchNoContentString(data.searchReasonDocs.tipMsg) else SearchUtils.getSearchCommonNoContentHint()
            titleView.text = noContentHint
            titleView.setVisibility(View.VISIBLE)
        }
    }

    override fun dismissSortDialog() {
        dismissNormalFilterWindow()
    }

    override fun isNeedTimeStamp(): Boolean {
        return true
    }

    override fun onRefresh() {
        if (isRefresh && !isIntercept) return
        isRefresh = true
        requestPageId = 1
        // 跳转回顶部
        mRecyclerView?.refreshableView?.scrollToPosition(0)
        resetColorChange()
        loadData()
    }

    override fun onMore() {
        if (!hasMore || isLoadMore) {
            return
        }
        isLoadMore = true
        requestPageId = currentPageId + 1
        loadData()
    }

    override fun getFraTag(): String {
        return "SearchChosenFragment"
    }

    override fun onFeedBackClicked(v: View?) {
        super.onFeedBackClicked(v)

        val mainActivity = (mActivity as? MainActivity) ?: return

        if (isShowFeedbackAi()) {
            val url = SearchUrlConstants.getInstance().getSearchFeedbackAiUrl(keyword)
            ToolUtil.clickUrlAction(mainActivity, url, null)

            if (ConstantsOpenSdk.isDebug) {
                CustomToast.showToast(url)
            }

        } else {
            NativeHybridFragment.start(
                mainActivity,
                SearchUrlConstants.getInstance().getSearchFeedbackUrl(keyword), true
            )
        }
    }

    override fun onGoToTopClicked(v: View?) {
        super.onGoToTopClicked(v)
        mReturnTopBtnListener?.onClick(v)
    }

    override fun createLoadParams(): MutableMap<String, String>? {
        return null
    }

    override fun showFilterView(): Boolean {
        return true
    }

    override fun showPaidTypeView(): Boolean {
        return true
    }

    override fun onSimpleSortFilterClicked(data: SearchSortFilterData, position: Int) {
        condition = data.value
        conditionName = data.displayName
        isIntercept = true
        mIsSimpleItemClick = true
        onRefresh()
        mNormalFilterPopupWindow?.updateSelectedSortFilterItem(position)
        resetFilterTv()
    }

    override fun onLabelClicked(data: SearchLabelData, position: Int) {
        labelForQuery = data.labelName
        isIntercept = true
        onRefresh()
        mNormalFilterPopupWindow?.updateSelectedLabelItem(position)
        resetFilterTv()
    }

    override fun onLabelFilterClicked(labelList: MutableList<SearchLabelData>?, sortFilterList: MutableList<SearchSortFilterData>?, searchLabels: List<SearchLabel>?, mainLabel: MainLabel?) {
        showNormalFilterPopupWindow(labelList, sortFilterList)
    }

    override fun onSearchLabelClicked(labelList: MutableList<SearchLabel>) {
    }

    override fun getImmersiveFrom(): String {
        return mImmersiveFrom
    }

    override fun getTopViewHeight(): Int {
        return 46.dp
    }

    override fun onPlayStart() {
        notifyAdapter()
    }

    override fun onPlayPause() {
        notifyAdapter()
    }

    override fun onPlayStop() {
        notifyAdapter()
    }

    override fun onSoundPlayComplete() {
        notifyAdapter()
    }

    override fun onSoundPrepared() {
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
    }

    override fun onBufferingStart() {
        notifyAdapter()
    }

    override fun onBufferingStop() {
    }

    override fun onBufferProgress(percent: Int) {
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
        mAdapter?.onPlayProgressChanged(currPos)
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        notifyAdapter()
        return false
    }

    override fun onStartGetAdsInfo(playMethod: Int, duringPlay: Boolean, isPaused: Boolean) {
    }

    override fun onGetAdsInfo(ads: AdvertisList?) {
    }

    override fun onAdsStartBuffering() {
    }

    override fun onAdsStopBuffering() {
    }

    override fun onStartPlayAds(ad: Advertis?, position: Int) {
        notifyAdapter()
    }

    override fun onCompletePlayAds() {
        notifyAdapter()
    }

    override fun onError(what: Int, extra: Int) {
    }

    override fun onEntryAdd(fragment: Fragment?) {
        hasItemClicked = true
    }

    override fun onEntryRemove(fragment: Fragment?) {
    }

    override fun onGetPageItem(position: Int, fragment: BaseSearchFragment<*>?) {
        super.onGetPageItem(position, fragment)
        if (position == 0 && mContainerSliderView == null) {
            mContainerSliderView = searchDataContext?.containerSlideView
        }
    }

    //最佳匹配卡片需要监控订阅状态
    override fun onCollectChanged(collect: Boolean, id: Long) {
        mAdapter?.listData?.forEach {
            if (it.data is SearchItem ){
                val item = (it.data as SearchItem).searchItemModel?.item
                if (item is AlbumM && item.id == id){
                    item.isFavorite = collect
                    return@forEach
                }
            }
        }
    }

    override fun onFollow(uid: Long, follow: Boolean) {
        mAdapter?.listData?.forEach {
            if (it.data is SearchItem ){
                val item = (it.data as SearchItem).searchItemModel?.item
                if (item is SearchTopUser && item.anchor!= null && item.anchor.id == uid){
                    item.anchor.followingStatus = if (follow) 1 else 3
                    return
                }
            } else if (it.data is SearchItemModel){
                val item = (it.data as SearchItemModel).item
                if (item is SearchTopUser && item.anchor!= null && item.anchor.id == uid){
                    item.anchor.followingStatus = if (follow) 1 else 3
                    return
                }
            }
        }
    }

    //精选页得到第一张专辑或第一条声音（只包含专辑意图、声音意图、专辑流、声音流）
    private fun getFirstAlbumOrTrack(models: List<AdapterProxyData<*>>?): Any? {
        if (!models.isNullOrEmpty()) {
            for (index in models.indices) {
                val item = (models[index].data as? SearchItemModel)?.item
                if (item is Track || item is Album) {
                    return item
                }

                val searchItemModel = (models[index].data as? SearchItem)?.searchItemModel?.item
                if (searchItemModel is Album || searchItemModel is Track) {
                    return searchItemModel
                }
            }
        }
        return null
    }

    private fun resetColorChange() {
        mRvScrollY = 0
        mRealListScrollOffset = 1f
        // 点了筛选条件，背景清除，同时透明度设为0，防止切换到非精选tab，出现背景色
        if (mIsSimpleItemClick) {
            SearchBrandAdBgManager.sendListScrollColorChangeBroad(
                Color.TRANSPARENT,
                0f,
                context,
                mImmersiveFrom
            )
            HandlerManager.postOnUIThreadDelay({ mIsSimpleItemClick = false }, 500)
        } else if (mIsPopupItemClick) {
            SearchBrandAdBgManager.sendListScrollColorChangeBroad(
                Color.TRANSPARENT,
                0f,
                context,
                mImmersiveFrom
            )
        }
    }


    private var mHasRegisterTopColorChangeReceiver = false

    private fun unregisterTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            mHasRegisterTopColorChangeReceiver = false
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mTopColorChangeReceiver)
        }
    }

    private fun registerTopColorChangeReceiver() {
        if (mHasRegisterTopColorChangeReceiver) {
            return
        }
        mHasRegisterTopColorChangeReceiver = true
        val filter = IntentFilter()
        filter.addAction(SearchBrandAdBgManager.ACTION_AD_CHANGE_COLOR)
        filter.addAction(SearchBrandAdBgManager.ACTION_REMOVE_AD_CHANGE_COLOR)
        filter.addAction(SearchBrandAdBgManager.ACTION_TAB_SCROLL_CHANGE_COLOR)
        filter.addAction(SearchBrandAdBgManager.ACTION_LIST_SCROLL_CHANGE_COLOR)
        filter.addAction(SearchBrandAdBgManager.ACTION_FILTER_BTN_CHANGE_COLOR)
        LocalBroadcastManager.getInstance(mContext)
            .registerReceiver(mTopColorChangeReceiver, filter)
    }

    fun getSearchWord(): String? {
        return keyword
    }

    private val mTopColorChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            intent?.let{
                val mainColor = it.getIntExtra("color", Color.TRANSPARENT)
                val offset = it.getFloatExtra("offset", 1f)

                // 只有广告和最佳匹配才有这个字段 注意
                val from = intent.getStringExtra("from")

                SearchBrandAdBgManager.printLog("chose fragment mainColor: $mainColor, offset:$offset")

                if (mainColor != Color.TRANSPARENT) {
                    mRecyclerView?.background = null
                }
                mSearchFilterProxy?.changeColor(getContext(), mainColor, offset)

                // 只处理最佳匹配的状态栏字体颜色值  广告直接沿用线上的逻辑(可能改动了反倒出问题)
                if (SearchBrandAdBgManager.FROM_TOP_ALBUM == from) {
                    // 夜间模式一直用白色的没问题 代码在SearchFragmentNew.hideHotSearchDetailFra中统一处理了
                    if (!BaseFragmentActivity.sIsDarkMode) {
                        // 为啥用0.3f  因为changeColor也是用的这个  同一个时间点一起变化
                        val darkStatusBar = mainColor == Color.TRANSPARENT || offset <= 0.3f
                        mIsDarkStatusBar = darkStatusBar
                        StatusBarManager.setStatusBarColor(window, darkStatusBar)
                    }
                }

            }
        }
    }

    private val mOnColorChangeListener = object : SearchImmersiveBannerManager.IOnColorChangeListener {
        override fun onColorChange(color: Int, tabScrollOffset: Float, listScrollOffset: Float, listScrollY: Int) {
            val offset = tabScrollOffset * (1 - listScrollOffset)
            if (!BaseFragmentActivity.sIsDarkMode) {
                val darkStatusBar = color == Color.TRANSPARENT || offset <= 0.1f
                mIsDarkStatusBar = darkStatusBar
                StatusBarManager.setStatusBarColor(window, darkStatusBar)
            }
        }
    }

    fun getListFirstVisiblePosition(): Int {
        mRecyclerView?.refreshableView?.let { rcv ->
            val layoutManager = rcv.layoutManager
            if (layoutManager is LinearLayoutManager) {
                return layoutManager.findFirstVisibleItemPosition()
            }
        }
        return -1
    }
}