package com.ximalaya.ting.android.live.common.lib.base.request;

import android.text.TextUtils;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

import java.net.URLEncoder;

/**
 * 直播接口清单
 *
 * <AUTHOR>
 */
public class LiveUrlConstants extends UrlConstants {

    /**
     * 生产环境直播 H5 旧版本兼容 HTTP 接口域名
     */
    private static final String SERVER_LIVE_H5_HTTPS_LEGACY = "https://liveroom.ximalaya.com/";

    public static LiveUrlConstants getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        private static final LiveUrlConstants INSTANCE = new LiveUrlConstants();
    }

    private String getFrozenServiceBaseUrlV2() {
        return getFrozenServiceBaseUrl() + "/v2";
    }

    /**
     * 获取直播H5接口域名，兼容老版本
     */
    private String getLiveServerH5HttpsHostLegacy() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return SERVER_LIVE_H5_HTTPS_LEGACY;
        } else {
            return "https://m.test.ximalaya.com/";
        }
    }

    private String getLiveServerH5RankUrl() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "https://mlive.ximalaya.com/";
        } else {
            return "https://mlive.test.ximalaya.com/";
        }
    }

    private String getMicSettingServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "rm-mic-web/";
    }

    public static String getSuggestH5Url() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "https://m.ximalaya.com/cs-bridge-web/page/feedback/submit?feedbackChannel=broadcast&_fix_keyboard=1";
        } else {
            return "https://m.test.ximalaya.com/cs-bridge-web/page/feedback/submit?feedbackChannel=broadcast&_fix_keyboard=1";
        }
    }

    /**
     * 贵族H5域名接口
     */
    private String getLiveServerNobleH5HttpsHost() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return SERVER_LIVE_H5_HTTPS_LEGACY;
        } else {
            // 旧的项目，服务端使用的大多数是m.live，改动较大，暂时测试环境保留使用m.live，新项目皆为mlive
            return "https://m.live.test.ximalaya.com/";
        }
    }

    // 批量查询个人直播商品列表
    public final String getPersonalLiveGoodsListByIdsUrl() {
        return getFrozenServiceBaseUrlV2() + "/user/query/goods";
    }

    // 查询课程直播商品列表
    public final String getCourseLiveGoodsListUrl() {
        return getFrozenServiceBaseUrlV1() + "/course/user/goods/list";
    }

    // 查询个人直播商品列表
    public final String getPersonalLiveGoodsListUrl() {
        return getFrozenServiceBaseUrlV2() + "/user/goods/list";
    }

    // 个人直播购买点击统计
    public final String getPersonalLiveGoShoppingUrl() {
        return getFrozenServiceBaseUrlV2() + "/user/live/shopping";
    }

    // 课程直播购买点击统计
    public final String getCourseLiveGoShoppingUrl() {
        return getFrozenServiceBaseUrlV1() + "/course/user/live/shopping";
    }

    // 批量查询课程直播商品列表
    public final String getCourseLiveGoodsListByIdsUrl() {
        return getFrozenServiceBaseUrlV1() + "/course/user/query/goods";
    }

    // 获取主播店铺地址
    public final String getAnchorShopUrl(long anchorId) {
        return UrlConstants.getInstanse().getMNetAddressHostS() + "anchor-sell/userCenter/shop/"
                + anchorId + "?_full_with_transparent_bar=1";
    }

    /**
     * 幸运礼物 http 接口
     */
    private String getLiveLuckyGiftBaseUrl() {
        return getLiveServerMobileHttpHost() + "lucky-star-web";
    }

    /**
     * asr
     */
    public String getLiveAsrAnchorPermissionUrl() {
        return getLiveLamiaServiceBaseUrl() + "/asr/white/list";
    }

    /**
     * 请求互动玩法区域接口信息
     *
     * @return asr, 专辑卡， 主题卡
     */
    public String getAreaInfoUrl() {
        return getLiveLamiaServiceBaseUrl() + "/view/area/show";
    }

    /**
     * 请求互动玩法区域 专辑信息
     * @return
     */
    public String getAreaAlbumInfoUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/rec/album";
    }

    /**
     * asr观众第一次进房获取历史记录
     *
     * @return
     */
    public String getLiveAsrHistoryUrl() {
        return getLiveLamiaServiceBaseUrl() + "/asr/content/history";
    }

    public String getThemeQueryUrl() {
        return getLiveLamiaServiceBaseUrl() + "/live/theme/query";
    }

    public String getPgcThemeQueryUrl() {
        return getLiveDoomServiceBaseUrl() + "/entertain/theme/query";
    }

    public String getAreaConfig() {
        return getLiveLamiaServiceBaseUrl() + "/view/area/config";
    }

    public String changeAreaConfig() {
        return getLiveLamiaServiceBaseUrl() + "/view/area/config/change";
    }

    public String switchPgcTheme() {
        return getLiveDoomServiceBaseUrl() + "/entertain/theme/switch";
    }

    public String savePgcThemeUrl() {
        return getLiveDoomServiceBaseUrl() + "/entertain/theme/save";
    }

    public String getThemeSaveUrl() {
        return getLiveLamiaServiceBaseUrl() + "/live/theme/save";
    }

    public final String getCheckLiveCouponDisplayUrlInCourse() {
        return getFrozenServiceBaseUrlV1() + "/course/user/coupons/check/display";
    }

    //查询主播本场直播是否还有优惠券可以领
    public final String getCheckLiveCouponDisplayUrl() {
        return getFrozenServiceBaseUrlV1() + "/user/coupons/check/display";
    }

    public final String getLiveCouponListUrlInCourse() {
        return getFrozenServiceBaseUrlV1() + "/course/user/coupons/list";
    }

    //查询个人优惠券列表
    public final String getLiveCouponListUrl() {
        return getFrozenServiceBaseUrlV1() + "/user/coupons/list";
    }

    /**
     * 幸运礼物 h5
     */
    private String getLiveLuckyGiftH5BaseUrl() {
        return getLiveServerH5RankUrl() + "lucky-star-web";
    }

    /**
     * 直播基础项目服务
     */
    public String getLiveLamiaServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "lamia";
    }

    /**
     * 直播数据上包项目服务
     */
    public String getLiveDataServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "live-data-web";
    }

    /**
     * 课程直播项目服务
     */
    public String getDiabloServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "diablo-web";
    }

    /**
     * 直播AI提供话题反馈地址
     */
    public String getLiveChatAiHelperBaseUrl() {
        return getLiveServerMobileHttpsHost() + "rm-ai-web";
    }

    public String getLiveOpenGameServiceUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return getLiveServerMobileHttpHost() + "opengame";
        } else {
            return getLiveServerMobileHttpHost() + "open-game-web";
        }
    }

    /**
     * MyClub ChitChat
     */
    protected String getMyClubChitChatBaseUrl() {
        return getMyClubMobileHttpHost() + "chitchat-mobile-web";
    }

    /**
     * 课程直播基础服务
     */
    private String getCourseLiveServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "diablo-web";
    }

    /**
     * 语音房基础项目服务
     */
    public String getLiveDoomServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "doom-web";
    }

    /**
     * 直播基础项目服务-挂件
     */
    protected String getLiveTaliyahWebServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "taliyah-web";
    }


    /**
     * 直播带货服务
     */
    protected String getFrozenServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "frozen-goods-web";
    }

    private String getFrozenServiceBaseUrlV1() {
        return getFrozenServiceBaseUrl() + "/v1";
    }

    /**
     * 直播标签项目服务
     */
    protected String getLiveLamiaTagServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "lamia-tags-web";
    }

    /**
     * 直播鉴权服务
     */
    private String getLiveLamiaAuthorizeServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "lamia-authorize-web";
    }

    /**
     * 直播礼物项目服务
     */
    private String getLiveGiftServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "gift";
    }

    /**
     * 直播ares-web服务
     */
    private String getAresWebService() {
        return getLiveServerMobileHttpHost() + "ares-web";
    }

    /**
     * 直播礼物排名项目服务
     */
    private String getLiveGiftRankServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "gift-rank";
    }

    /**
     * 语音房守护服务
     */
    protected String getLiveDoomDaemonServiceBaseUrl() {
        return getLiveServerMobileHttpsHost() + "doom-daemon-web";
    }

    /**
     * 直播红包项目服务
     */
    private String getLiveMammonServiceBaseUrl() {
        return getLiveServerMobileHttpsHost() + "mammon";
    }

    /**
     * 直播夺宝商场项目服务
     */
    protected String getLiveTreasureServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "treasure";
    }

    /**
     * 直播主播任务服务
     */
    private String getFirstRechargeServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "sprint-web";
    }

    /**
     * prediction-web服务
     *
     * @return
     */
    private String getPredictionWeb() {
        return getLiveServerMobileHttpHost() + "prediction-web";
    }

    /**
     * 直播粉丝团服务
     */
    private String getLiveFansServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "live-fans-web";
    }

    /**
     * 直播关注服务
     */
    private String getLiveFollowServiceBaseUrl() {
        return getServerNetAddressHost() + "fans";
    }

    /**
     * 直播Pk服务
     */
    protected String getLivePkServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "live-pk";
    }

    /**
     * 直播用户召回服务
     */
    public String getLiveMetisServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "metis";
    }

    /**
     * 直播贵族服务
     */
    private String getLiveNobelServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "noble-web";
    }

    /**
     * 直播贵族 H5 服务，https 请求
     */
    private String getLiveNobleH5ServiceBaseUrl() {
        return getLiveServerNobleH5HttpsHost() + "noble-web/page/";
    }

    /**
     * 直播勋章 H5 服务
     */
    private String getLiveMedalH5ServiceBaseUrlLegacy() {
        return getLiveServerH5HttpsHostLegacy() + "medal-web";
    }

    /**
     * 直播Pk H5 服务
     */
    public String getLivePkH5ServiceBaseUrlLegacy() {
        return getLiveServerH5RankUrl() + "gatekeeper/live-pk-ranking-list/";
    }

    /**
     * 直播礼物排名 H5 服务
     */
    private String getLiveGiftRankH5ServiceBaseUrl() {
        return getLiveServerH5RankUrl() + "live-toc-ssr";
    }

    /**
     * 心愿单服务
     */
    public String getLiveWishServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "dazzle-web";
    }

    /**
     * 财富等级服务
     */
    public String getLivGiftWealthServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "gift-wealth-web";
    }

    /**
     * 直播福袋服务
     */
    private String getLiveLotteryServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "gift-lottery-business";
    }

    /**
     * 直播关系服务
     */
    private String getLiveRelationServiceBaseUrl() {
        return getLiveServerMobileHttpHost() + "live-relation-web";
    }

    /**
     * 查询自己的直播间信息
     */
    public final String queryMyRoomInfo() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/room/query";
    }

    /**
     * 查询myclub配置信息
     */
    public final String queryMyClubRoomInfo() {
        return getMyClubChitChatBaseUrl() + "/api/v1/sdk_chat/create_chat";
    }

    /**
     * 查询myclub配置信息
     */
    public final String queryMyClubRoomInfoV2() {
        return getMyClubChitChatBaseUrl() + "/api/v2/sdk_chat/create_chat";
    }

    /**
     * 查询天选红包列表信息
     */
    public String getRoomRedPacketHeavenlyListUrl() {
        return getLiveLotteryServiceBaseUrl() + "/v1/chosen-red-pack/id/list/get";
    }

    /*
     * 创建直播POST
     */
    public final String createPersonLive() {
        return getLiveLamiaServiceBaseUrl() + "/v6/live/record/create";
    }

    /*
     * 创建直播预告 POST
     */
    public final String createPersonLivePreview() {
        return getLiveLamiaServiceBaseUrl() + "/v1/preview/live/record/create";
    }

    /*
     * 创建直播预告 POST
     */
    public final String updatePersonLivePreview() {
        return getLiveLamiaServiceBaseUrl() + "/v1/preview/live/record/update";
    }


    /*
     * 获取直播封面信息
     */
    public final String getCoverInfo() {
        return getLiveLamiaServiceBaseUrl() + "/v1/room/photo/selected";
    }

    /**
     * 创建直播服务协议
     */
    public final String createLiveRuleAgreement() {
        return getLiveServerH5RankUrl() + "gatekeeper/live-app-h5/agreement";
    }

    /**
     * 创建直播隐私协议
     */
    public final String createLivePrivatePolicy() {
        return getServerPassportAddressHostS() + "page/privacy_policy";
    }

    /**
     * 开播优质封面介绍
     */
    public final String getExcellentCoverUrl() {
        return "https://mlive.ximalaya.com/live-anchor-web/v1/anchor/train/view/1/#/first/questionThree";
    }

    /**
     * 主播任务勋章图标
     */
    public final String getAllAnchorPilotLevelIconUrl() {
        return getServerNetSAddressHost() + "live-anchor-web/navigation/medal/all";
    }

    /**
     * 获取主播任务
     */
    public final String getAnchorTaskUrl() {
        return getLiveWishServiceBaseUrl() + "/v2/multi/anchor/tasks/progress";
    }


    /*
     * 编辑个人直播 POST
     */
    public final String updatePersonLiveById() {
        return getLiveLamiaServiceBaseUrl() + "/v4/live/record/update";
    }


    /*
     * 删除个人直播 POST
     */
    public final String deletePersonLiveById() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/record/delete";
    }

    /**
     * 开始个人直播场次 POST
     */
    public final String startPersonLiveById() {
        return getLiveLamiaServiceBaseUrl() + "/v4/live/record/start";
    }

    /**
     * 停止个人直播  POST
     */
    public final String stopPersonLiveById() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/record/stop";
    }

    /**
     * 根据直播间id查询直播详情 V13
     */
    public final String queryPersonLiveRoomDetailByRoomIdV13() {
        return getLiveLamiaServiceBaseUrl() + "/v13/live/room";
    }

    public final String getBusinessPushUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/common/business/push";
    }

    /**
     * 获取首映室信息
     */
    public final String getPremiereInfo() {
        return getLiveLamiaServiceBaseUrl() + "/premiere/room";
    }

    /**
     * 首映室试听上报
     */
    public final String reportPremiereTrial() {
        return getLiveLamiaServiceBaseUrl() + "/premiere/room/trial/report";
    }

    /**
     * 获取共创信息
     */
    public final String getCooperatorInfo() {
        return getLiveLamiaServiceBaseUrl() + "/premiere/room/cooperators";
    }

    /**
     * 获取AI助手 展示总开关
     *
     * @return string
     */
    public final String getAiEnableStatus() {
        return getLiveChatAiHelperBaseUrl() + "/ai/query/enable/status";
    }

    /**
     * 获取AI配置信息
     *
     * @return string
     */
    public final String getAiConfig() {
        return getLiveChatAiHelperBaseUrl() + "/ai/query/ai/config";
    }

    /**
     * 上传大礼物截图调用次数
     *
     * @return string
     */
    public final String setScreenShotConfig() {
        return getLiveChatAiHelperBaseUrl() + "/ai/incr/gift/screenshot/use";
    }

    public final String getPKSettingInfo() {
        return getLivePkServiceBaseUrl() + "/pk/config/basis";
    }

    public final String mutePkAnchor() {
        return getLivePkServiceBaseUrl() + "/pk/config/mute";
    }

    public final String queryOnlineNoble() {
        return getLiveNobelServiceBaseUrl() + "/v1/noble/room/list";
    }

    /**
     * 查询是否建议主播重启直播
     */
    public final String suggestRestartLiveOrNot() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/record/checkstop";
    }

    /**
     * 查询是否有预告或者正在直播的场次
     */
    public final String queryMyNoticeOrLivingRecord() {
        return getLiveLamiaServiceBaseUrl() + "/v5/record/living";
    }

    /**
     * 查询推荐直播间
     */
    public final String getQueryRecommendLiveRoom() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/room/recommend";
    }

    /**
     * 查询我的直播信息
     */
    public final String getMyLive() {
        return getLiveLamiaServiceBaseUrl() + "/v3/live/mylive";
    }

    /**
     * 红点消除接口
     */
    public final String setDot() {
        return getLiveLamiaServiceBaseUrl() + "/v1/common/red/dot/set";
    }

    public final String getLivesByCategoryIdV7() {
        return getLiveLamiaServiceBaseUrl() + "/v7/live/record/category";
    }

    /**
     * 个播领取优惠券
     */
    public final String getLiveCouponReceiveUrl() {
        return getFrozenServiceBaseUrlV1() + "/user/coupons/receive";
    }

    /**
     * 课程直播领取优惠券
     */
    public final String getLiveCouponReceiveUrlInCourse() {
        return getFrozenServiceBaseUrlV1() + "/course/user/coupons/receive";
    }

    /**
     * 个播查询单张优惠券
     */
    public final String getSingleLiveCouponInfoUrl() {
        return getFrozenServiceBaseUrlV1() + "/user/coupon";
    }

    /**
     * 课程直播查询单张优惠券
     */
    public final String getSingleLiveCouponInfoUrlInCourse() {
        return getFrozenServiceBaseUrlV1() + "/course/user/coupon";
    }

    /*
     * 查询主播直播用户详情 V8
     */
    public final String getUserDetailByUidAndRoomIdV8() {
        return getLiveLamiaServiceBaseUrl() + "/v8/live/userinfo";
    }

    public final String getUserDetailByUid() {
        return getLiveLamiaServiceBaseUrl() + "/v2/user/card";
    }

    public String getBizUserInfoUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/user/roominfo";
    }

    /**
     * 个播：禁言用户
     */
    public final String forbiddenUserByUidAndRecordId() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/chat/forbidden";
    }

    /**
     * 个播：取消禁言用户
     */
    public final String unForbiddenUserByUidAndRecord() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/chat/unforbidden";
    }

    /**
     * 查询所有个人直播分类
     */
    public final String getAllPersonLivesCategory() {
        return getLiveLamiaServiceBaseUrl() + "/v4/live/category";
    }

    /**
     * 设置匿名直播间
     */
    public final String getAnonymityUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/anonymous/room/set";
    }

    /**
     * 查询直播父级分类
     */
    public final String getAllLivesParentCategory() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/parentcategory";
    }

    /**
     * 创建个人直播管理员
     */
    public final String createPersonLiveAdminsByUidAndRoomId() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/admin/create";
    }

    /**
     * 删除个人直播管理员
     */
    public final String deletePersonLiveAdminsByUidAndRoomId() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/admin/delete";
    }

    /**
     * 个播：查询管理员列表
     */
    public final String getAllPersonLivesAdminsByRoomId() {
        return getLiveLamiaServiceBaseUrl() + "/v3/live/admin/list";
    }

    /**
     * 查询用户权限及角色
     **/
    public final String getTargetUserPermission() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/admin/user/permission";
    }

    /*
     * 创建直播时查询关注的人
     */
    public final String queryMyFollowings() {
        return getLiveLamiaServiceBaseUrl() + "/v3/following/search";
    }

    /**
     * 保存直播至专辑
     */
    public final String saveLiveToAlbum() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/record/demand/save";
    }

    /**
     * 查询个人直播开始时间
     */
    public final String queryPersonalLiveRealtime() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/stat/realtime";
    }

    /**
     * 分享回调接口地址
     */
    public final String getShareCallbackUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/room/share/callback";
    }


    public final String getLiveAddFansClubFriendShipUrl() {
        return getLiveFansServiceBaseUrl() + "/v1/friendship/add";
    }

    /**
     * 个人直播发布话题
     */
    public final String getLivePublishTopicUrlV4() {
        return getLiveLamiaServiceBaseUrl() + "/v4/live/topic/publish";
    }

    //课程直播直播发布话题
    public final String getUpdateDescriptionUrl() {
        return getCourseLiveServiceBaseUrl() + "/v1/live/record/update";
    }

    /**
     * 个人直播查询话题
     */
    public final String getLiveQueryTopicUrlV3() {
        return getLiveLamiaServiceBaseUrl() + "/v3/live/topic/detail";
    }

    /**
     * 直播首页
     */
    public String getLiveHomeRecordList() {
        return getLiveLamiaServiceBaseUrl() + "/v17/live/homepage";
    }

    /**
     * 个播获取禁言列表
     */
    public String getLamiaForbiddenList() {
        return getLiveLamiaServiceBaseUrl() + "/v3/live/chat/forbiddenedlist";
    }

    /**
     * 课程直播获取用户禁言列表
     */
    public final String getDiableForbiddenUserList() {
        return getDiabloServiceBaseUrl() + "/v1/live/user/forbiddenedlist";
    }

    /**
     * 贵族权益信息展示 H5 页面
     * 默认定位到子爵grade=5
     */
    public String getNobleInfoUrl(long roomId, long anchorUid, long chatId) {
        return getNobleInfoUrl() + "?roomid=" + roomId + "&anchorUid=" + anchorUid + "&chatId=" + chatId + "&gradeId=5";
    }

    /**
     * 贵族与权益信息展示 H5 页面
     */
    public String getNobleInfoUrl() {
        return getLiveNobleH5ServiceBaseUrl() + "nobleInfo";
    }

    /**
     * 我的贵族 H5 页面
     */
    public String getMyNobleUrl() {
        return getLiveNobleH5ServiceBaseUrl() + "myNoble";
    }

    /**
     * 贵族常见问题
     */
    public String getNobleFAQ() {
        return getLiveNobleH5ServiceBaseUrl() + "nobleFAQ";
    }

    /**
     * 音视频观众端，直播结束状态的推荐直播列表
     */
    public String getRecommendLiveRecordListForAudience() {
        return getLiveLamiaServiceBaseUrl() + "/v10/live/stop/recommend";
    }

    /**
     * 直播间进场通知 v5 版本，请求头新增风控 ticket 字段
     */
    public String getUserEntryRoomUrlV3() {
        return getLiveLamiaServiceBaseUrl() + "/v5/live/user_entry";
    }

    /**
     * 查询直播间运营位信息，在直播间 WebView 和底部小挂件中轮播展示，音视频直播、PGC聊天室使用
     * <p>
     * 接收查询参数包括：bizType，roomId，recordId，types
     */
    public String getLiveRoomOperationActivityInfo() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v4/query/operation/tab";
    }

    /**
     * 查询课程直播间运营位信息
     * 接受参数包括id
     */
    public String getCourseRoomOperationActivityInfo() {
        return getCourseLiveServiceBaseUrl() + "/v1/live/record/operationtab";
    }

    /**
     * 推流地址
     */
    public final String getPersonLivePushUrls() {
        return getLiveLamiaAuthorizeServiceBaseUrl() + "/v2/broadcast";
    }

    /**
     * 视频预览需要appKey，提前初始化推拉流SDK
     */
    public final String getPersonLivePushAppKey() {
        return getLiveLamiaAuthorizeServiceBaseUrl() + "/v1/broadcast/appkey";
    }

    /**
     * 课程直播观众连麦申请获取zego appId和appKey
     */
    public final String getCourseLivePushAppKey() {
        return getCourseLiveServiceBaseUrl() + "/v1/live/broadcast/appkey";
    }

    /**
     * 查询音视频直播连麦推流配置信息
     */
    public final String getMicStreamInfoUrls() {
        return getLiveLamiaAuthorizeServiceBaseUrl() + "/v1/zego/join/mic";
    }

    /**
     * 查询课程直播连麦推流配置信息
     */
    public final String getCourseLiveMicStreamInfoUrls() {
        return getLiveLamiaAuthorizeServiceBaseUrl() + "/v1/diablo/zego/join/mic";
    }

    /**
     * 直播本场榜
     */
    public String getRoomGiftRankNew() {
        return getLiveGiftRankServiceBaseUrl() + "/v3/live/fans/room/rank";
    }

    /**
     * 直播本场榜
     */
    public String getAudioVideoRoomOnline() {
        return getLiveLamiaServiceBaseUrl() + "/v2/online/user/list";
    }

    /**
     * 直播间粉丝榜，进直播间第一次请求，后续长连接消息获取
     */
    public String getChatRoomAnchorRank() {
        return getLiveGiftRankServiceBaseUrl() + "/v1/gift/rank/anchor/live";
    }

    /**
     * 直播间主播短收入记录-送礼记录
     */
    public String getChatRoomAnchorReceiveGiftRecord() {
        return getLiveGiftRankServiceBaseUrl() + "/v1/gift/live/record";
    }

    /**
     * 请求主播端喜爱值列表
     *
     * @return
     */
    public String getFavoriteListUrl() {
        return getLiveGiftRankServiceBaseUrl() + "/v2/gift/rank/favorite/live";
    }

    /**
     * 音视频直播赠送礼物
     */
    public String getSendLiveGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v4/sendGift/live";
    }

    /**
     * 音视频直播间背包送礼接口，详见<a herf="https://alidocs.dingtalk.com/i/nodes/YMyQA2dXW793xlpjhy24Nwk6JzlwrZgb?utm_scene=team_space">技术文档</a>
     */
    public String getSendPackageItemUrl() {
        return getLiveGiftServiceBaseUrl() + "/v1/sendGift/package";
    }

    /**
     * 娱乐派对赠送礼物
     */
    public String getSendHallGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v1/sendGift/hall";
    }

    /**
     * 课程直播间赠送礼物
     */
    public String getCourseSendVideoGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v1/sendGift/lessonVideo";
    }

    /**
     * 发送礼物  宝箱礼物送礼
     */
    public String getSendBoxGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/box";
    }

    /**
     * 娱乐厅发送礼物  宝箱礼物送礼
     */
    public String getSendBoxGiftUrlForEnt() {
        return getLiveGiftBaseUrl() + "v1/sendGift/hall/box";
    }

    /**
     * 发送礼物 点播
     *
     * @return url
     */
    public String getSendTrackGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/track";
    }

    /**
     * 发送礼物 点播
     *
     * @return url
     */
    public String getSendHomePageGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/common";
    }

    /**
     * 交友模式
     */
    public String getSendFriendsGiftUrl() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/entertainment";
    }

    public String getHitGiftTerminateHttp() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/consecution/terminate";
    }

    /**
     * 课程直播连击结束
     */
    public String getCourseLiveHitGiftTerminateHttp() {
        return getLiveGiftServiceBaseUrl() + "/v1/lessonVideo/sendGift/consecution/terminate";
    }


    /**
     * 课程直播连击结束
     */
    public String getPGCLiveHitGiftTerminateHttp() {
        return getLiveGiftServiceBaseUrl() + "/v1/sendGift/hall/consecution/terminate";
    }

    public String getFriendGiftHitTerminateHttp() {
        return getLiveGiftServiceBaseUrl() + "/v3/sendGift/entertainment/combo/over";
    }

    public String getLiveGiftListBySendTypeUrl() {
        return getLiveGiftServiceBaseUrl() + "/v10/gift/category";
    }

    /**
     * 获取关联礼物的文案接口
     */
    public String getLiveRelationTipsUrl() {
        return getLiveGiftServiceBaseUrl() + "/tips";
    }

    /**
     * 根据礼物ids查询礼物信息接口
     */
    public String getGiftInfoByGiftIdsUrl() {
        return getLiveGiftServiceBaseUrl() + "/giftInfos";
    }

    /**
     * 获取 CP 关系礼物信息接口
     */
    public String getLiveCpRelationGiftTipsUrl() {
        return getLiveRelationServiceBaseUrl() + "/relation/giftTip";
    }

    /**
     * 获取用户关系动画信息接口
     */
    public final String getUserRelationshipAnimationUrl() {
        return getLiveRelationServiceBaseUrl() + "/relation/animation/show";
    }

    /**
     * 获取用户关系动画信息接口
     */
    public final String getRelationBondAnimationUrl() {
        return getLiveRelationServiceBaseUrl() + "/relation/bond/animation/show";
    }

    /**
     * 礼物解锁进度接口地址
     *
     * @return url
     */
    public String getGiftUnlockProgressUrl() {
        return getLiveGiftServiceBaseUrl() + "/unlock/progress";
    }

    public String getBrocadeBagInfoUrl() {
        return getLiveTreasureServiceBaseUrl() + "/room/package/v1/bindInfo";
    }

    public String postBindPackageUrl() {
        return getLiveTreasureServiceBaseUrl() + "/room/package/v1/bind";
    }

    /**
     * 亲密关系礼物邀请
     */
    public String postRelationBondInviteUrl() {
        return getLiveRelationServiceBaseUrl() + "/relation/bond/invite";
    }

    /**
     * 珊瑚海礼物url
     */
    public String getCoralGiftInfoUrl() {
        return getAresWebService() + "/coral/v1/gift/info";
    }

    /**
     * 信息流收到消息确认
     */
    public String chatConfirmUrl() {
        return getAresWebService() + "/surprised-bag/chat/confirm";
    }


    /**
     * 年度环游世界礼物
     *
     * @return url
     */
    public String getAnnualGiftUrl() {
        return getAresWebService() + "/travel/v1/toast/info";
    }

    public String getLiveLuckySilkBagGiftPrizeUrl() {
        return getLiveGiftServiceBaseUrl() + "/lucky-parcel/prize-list";
    }

    public String batchQueryPackageItemsInfoUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package/v1/batchIds/" + System.currentTimeMillis();
    }

    /**
     * 查询背包物品，升级到 v4 接口，支持业务分类查询
     */
    public String getPackageInfoUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package/v4/list";
    }

    public String getRoomPackageInfoUrl() {
        return getLiveTreasureServiceBaseUrl() + "/room/package/v1/list";
    }

    /**
     * 获取背包物品tips内容接口
     * doc: <a href="https://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/treasure/v1/treasure-package-web-api.md#%E8%8E%B7%E5%8F%96%E8%83%8C%E5%8C%85%E7%89%A9%E5%93%81tips%E5%86%85%E5%AE%B9%E6%8E%A5%E5%8F%A3">...</a>
     */
    public String getPackageTipsUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package/tips";
    }

    /**
     * 获取亲密关系tips Url
     */
    public String getRelationTipsUrl() {
        return getLiveGiftServiceBaseUrl() + "/relation/tip";
    }

    /**
     * 背包使用明细
     */
    public String getPackageUseListUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package-order/list/" + System.currentTimeMillis();
    }

    /**
     * 直播间使用背包物品
     */
    public String getUsePackageItemUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package/v3/use/live/" + System.currentTimeMillis();
    }

    /**
     * 娱乐厅使用背包物品
     */
    public String getUsePackageItemInHallUrl() {
        return getLiveTreasureServiceBaseUrl() + "/package/v2/use/hall/" + System.currentTimeMillis();
    }

    /**
     * 直播间外使用背包物品，如装扮中心
     */
    public String getUsePackageItemCommonUrl() {
        return this.getServerNetSAddressHost() + "treasure/package/v2/use/common/" + System.currentTimeMillis();
    }

    /**
     * 查询喜钻余额
     */
    private String getAccountBaseUrl() {
        return getMemberAddressHost() + "xmacc/mysubaccount";
    }

    /**
     * 查询喜钻余额 v4 接口
     */
    public String getXiBeanAndXiDiamondAmount() {
        return this.getAccountBaseUrl() + "/v4";
    }

    /**
     * 停留时长上报接口
     */
    public String getStayUploadUrl() {
        return getLiveServerMobileHttpsHost() + "metis/backAward/v1/report/stay/time";
    }

    /**
     * 挂播检测数据上报接口
     */
    public String getLeaveUploadUrl() {
        return getLiveLamiaServiceBaseUrl() + "/anchor/device/report";
    }

    public String getUnLoginUserUrl() {
        return getLiveServerMobileHttpsHost() + "metis/guest/listen/report";
    }

    public String getGiftPanelAd() {
        return getLiveLamiaServiceBaseUrl() + "/v3/gift/operationtab";
    }

    public String getChargeNotice() {
        return getLiveLamiaServiceBaseUrl() + "/v1/charge/notice";
    }

    public String getLiveHomeLoopRankUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v3/live/rank_list";
    }

    public String getLiveHomeMyAttention() {
        return getLiveLamiaServiceBaseUrl() + "/v1/follow/home";
    }

    public String getLiveHomeMyContent() {
        return getLiveLamiaServiceBaseUrl() + "/v1/follow/content";
    }

    public String getLiveDialogMoreLiveChannel() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/more/channel";
    }

    public String getLiveRecommendLiveAnchor() {
        return getLiveLamiaServiceBaseUrl() + "/v1/follow/recommend";
    }

    public String getLiveDialogMoreLive() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/more/records";
    }


    /**
     * 音频直播交友模式权限校验
     */
    public String getLiveFriendModePassportCheckUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/friendshipMode/check";
    }

    /**
     * 音频直播交友模式权限申请页面
     */
    public String getLiveFriendModeRequestUrl() {
        return getLiveServerH5RankUrl() + "gatekeeper/friends-mode-auth?_default_share=0";
    }

    /**
     * 主播榜单 H5 页面
     */
    public String getH5AnchorRankUrl() {
        return getLiveGiftRankH5ServiceBaseUrl() + "/anchorRank";
    }

    /**
     * pk 预选赛白名单入口地址
     *
     * @return url
     */
    public String getPkQualifierUrl() {
        return getLivePkServiceBaseUrl() + "/annual/pk/qualifier/enter";
    }

    /**
     * 排位pk入口配置查询
     *
     * @return true: 新版排位pk，false: 旧版排位pk
     */
    public String getPkRankConfigUrl() {
        return getLivePkServiceBaseUrl() + "/pk/config/v1";
    }

    /**
     * ai 惩罚声音地址
     */
    public String getPkVoicePenaltyUrl() {
        return getLivePkServiceBaseUrl() + "/annual/pk/voice/penalty";
    }

    /**
     * 获取合规文案
     *
     * @return url
     */
    public String getComplianceContent() {
        return getLivePkServiceBaseUrl() + "/v1/compliance/restriction/info";
    }

    public String getScrollLivePlayRecords() {
        return getLiveLamiaServiceBaseUrl() + "/v4/live/exchange";
    }

    public String getScrollLiveRecordsStatus() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/exchange/room/check";
    }

    /**
     * 上下滑上报
     */
    public String getLiveScrollUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/exchange/surprise";
    }

    /**
     * 获取上下滑状态
     */
    public String getLiveScrollStatus() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/exchange/user/status";
    }

    /**
     * PK 排行榜 H5 页面
     */
    public String getPkRankH5Url() {
        return getLivePkH5ServiceBaseUrlLegacy() + "home?defaultKey=record";
    }

    public String getPkRankV2H5Url() {
        return getLivePkH5ServiceBaseUrlLegacy() + "homeV2?defaultKey=record";
    }

    /**
     * 个性推荐直播接口
     */
    public String getRecommendRoomId() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/room/push/recommend";
    }

    /**
     * 勋章墙 H5 网页
     */
    public String getMedalWallH5Url(long uid) {
        return getLiveMedalH5ServiceBaseUrlLegacy() + "/v1/medal/wall?uid=" + uid;
    }

    public String getPetH5Url(long uid, int bizType, long anchorUid, long roomId) {
        return getLiveServerH5RankUrl() + "gatekeeper/fairy-pet?petUserId=" + uid + "&bizType=" + bizType + "&anchorUid=" + anchorUid + "&roomId=" + roomId;
    }

    /**
     * CP 关系空间 H5 页面 iting 地址
     *
     * @param bizType   业务类型
     * @param roomId    房间 id
     * @param anchorUid 主播 id
     */
    public String getCpSpaceItingUrl(int bizType, long roomId, long anchorUid) {
        return "iting://open?msg_type=184&position=bottom&height=533&animationFrom=bottom&corner=10&transparent=0&showClose=0&extraUrl=" + getCpSpaceH5Url(bizType, roomId, anchorUid);
    }

    /**
     * CP 关系空间 H5 页面 url
     *
     * @param bizType   业务类型
     * @param roomId    房间 id
     * @param anchorUid 主播 id
     */
    private String getCpSpaceH5Url(int bizType, long roomId, long anchorUid) {
        String domain;
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            domain = "mlive.ximalaya.com";
        } else {
            domain = "mlive.test.ximalaya.com";
        }

        if (anchorUid > 0 && anchorUid == UserInfoMannage.getUid()) {
            return "https%3A%2F%2F" + domain + "%2Flive-business-ssr%2Fcp-relationship%2Frelationship-history-list%3Fself%3Dtrue";
        }
        return "https%3A%2F%2F" + domain + "%2Flive-business-ssr%2Fcp-relationship%3FbizType%3D" + bizType + "%26roomId%3D" + roomId + "%26anchorUid%3D" + anchorUid;
    }

    /**
     * 排位pk战报接口
     */
    public String getPkReportUrl() {
        return getLivePkServiceBaseUrl() + "/v3/ranking/pk_report";
    }

    /**
     * 排位pk玩法介绍接口
     */
    public String getPkFAQUrl() {
        return getLivePkServiceBaseUrl() + "/v1/ranking/faq";
    }

    /**
     * PK规则说明页
     */
    public String getPkRuleUrl() {
        return getLivePkServiceBaseUrl() + "/v1/pk/ranking/rule";
    }

    /**
     * 查询道具或者 buff 信息
     */
    public String getPkBuffedAndPropInfo() {
        return getLivePkServiceBaseUrl() + "/v1/ranking/buffAndProp";
    }

    /**
     * 星际补给箱领取
     */
    public String getPkStarCraftBoxTakeUrl() {
        return getLivePkServiceBaseUrl() + "/v1/starcraft/award/take";
    }

    /**
     * 星际补给箱状态
     */
    public String getPkStarCraftBoxStatusUrl() {
        return getLivePkServiceBaseUrl() + "/v1/starcraft/award/status";
    }

    public String getMineCenterUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/anchor/center/menus";
    }

    /**
     * 个人直播收听时长上报接口
     */
    public String getLamiaReportDurationUrl() {
        return getLiveMetisServiceBaseUrl() + "/backAward/v2/report/duration";
    }

    /**
     * 娱乐厅（聊天室，电台房）收听时长上报接口
     */
    public String getEntReportDurationUrl() {
        return getLiveMetisServiceBaseUrl() + "/backAward/v1/doom/report/duration";
    }

    /**
     * 课程直播收听时长上报接口
     */
    public String getCourseReportDurationUrl() {
        return getLiveMetisServiceBaseUrl() + "/backAward/v1/diablo/report/duration";
    }

    /**
     * 课程直播收听回放时长上报接口
     */
    public String getCoursereReportDurationUrl() {
        return getCourseLiveServiceBaseUrl() + "/replay/report";
    }

    /**
     * 查询所有表情
     */
    public String getAllEmojiTemplateUrl() {
        return getLiveLamiaTagServiceBaseUrl() + "/v3/template/expression/all";
    }

    /**
     * 查询我的表情
     */
    public String getMyEmojiUrl() {
        return getLiveTreasureServiceBaseUrl() + "/v2/emoticons/" + System.currentTimeMillis();
    }

    public String getCheckEmojiUrl() {
        return getLiveTreasureServiceBaseUrl() + "/v2/emoticons/send";
    }

    public String getStopReportUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/record/stopReport";
    }

    public String getChatRoomPictureClipperUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/chatroom/picture";
    }

    public String getPersonalInfo() {
        return getLiveServerMobileHttpHost() + "doom-web/entertain/appUserinfo/v1";
    }

    public String getHotWordUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/hot/word";
    }

    public String getHotWordReportUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/hot/word/report";
    }

    public String getFansSetDotUrl() {
        return getLiveFansServiceBaseUrl() + "/v1/club/redpoint/set";
    }

    public String getBookLiveUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/book";
    }

    /**
     * 聚合查询普通弹幕，粉丝团弹幕，贵族弹幕
     */
    public String getQueryBulletBalance() {
        return getLiveLamiaTagServiceBaseUrl() + "/v2/query/bullet/balance";
    }

    /**
     * 发送弹幕
     */
    public String getSendBulletUrl() {
        return getLiveLamiaTagServiceBaseUrl() + "/v3/send/bullet";
    }

    /**
     * 收听奖励 H5 地址
     */
    @Deprecated
    public String getListenRewardH5Url() {
        return getLiveMetisServiceBaseUrl() + "/hearAwardView/v1/index/" + System.currentTimeMillis();
    }

    /**
     * 开屏广告获取直播间roomId
     */
    public String getAdvertiseRoomIdUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/room/opening/recommend";
    }

    /**
     * 幸运礼物进度, v2
     */
    public String getLuckyGiftProgressUrl() {
        return getLiveLuckyGiftBaseUrl() + "/lucky/progress/v2";
    }

    /**
     * 幸运礼物中奖名单
     */
    public String getLuckyGiftUserListUrl() {
        return getLiveLuckyGiftH5BaseUrl() + "/index/list";
    }

    /**
     * 幸运礼物规则页
     */
    public String getLuckyGiftRuleUrl() {
        return getLiveLuckyGiftH5BaseUrl() + "/index/rule";
    }

    /**
     * 上传分享成功
     */
    public String getShareSuccessUrl() {
        return getLiveServerMobileHttpHost() + "doom-web/room/share/v1/callback";
    }

    /**
     * 流量卡
     */
    public String getFlowCardUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/trafficCard/query/room/card";
    }

    /**
     * 开播标题随机
     */
    public String getLiveTitleUrlV2() {
        return getLiveLamiaServiceBaseUrl() + "/v2/random/live/title";
    }

    /**
     * 主播解约、续约接口
     */
    public String getLiveAnchorTodoList() {
        return getLiveLamiaServiceBaseUrl() + "/v1/anchor/todo";
    }

    /**
     * 财富值进度条
     */
    public String getLiveWealthInfoUrl() {
        return getLivGiftWealthServiceBaseUrl() + "/v1/progress";
    }

    /**
     * 更多菜单
     */
    public String getLiveMenuListUrl() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/room/menus";
    }

    /**
     * 更多菜单红点
     */
    public String getLiveMoreMenuRedDotUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/room/menus/set";
    }

    /**
     * 更多按钮，
     */
    public String getLiveTaLiYaMoreMenuRedDotUrl() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/room/menus/set";
    }

    /**
     * 取消提示信息接口
     */
    public String getLiveMoreMenuTipsUrl() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/room/tip/set";
    }

    /**
     * 查询是否有个人直播带货权限
     */
    public String getPersonalLiveGoodsSellAuthUrl() {
        return getFrozenServiceBaseUrl() + "/v1/user/goods/auth";
    }

    public String getCreateLiveBanner() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/query/livingResource";
    }

    /**
     * 带货资格申请审核h5地址
     */
    public String getSellAuthUrl() {
        return getLiveServerH5RankUrl() + "gatekeeper/good-auth";
    }

    /**
     * 带货教学h5地址
     */
    public String getSellEducationUrl() {
        return getLiveServerH5RankUrl() + "gatekeeper/good-auth/good-guide";
    }

    /**
     * 查询个人直播是否是带货直播间
     */
    public String getQueryIsGoodsLiveUrl() {
        return getFrozenServiceBaseUrl() + "/v1/live/goods/query";
    }

    /**
     * 弹窗-web-api
     */
    public String getCommonDialogUrl() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/query/popup";
    }

    public String getMagicProgress() {
        return getLiveLuckyGiftBaseUrl() + "/mana/progress";
    }

    /**
     * 通过FM号搜索主播
     */
    public String getSearchHostUrl() {
        return getLivePkServiceBaseUrl() + "/v1/pk/manual/search";
    }

    /**
     * 搜索历史记录
     *
     * @return url
     */
    public String getSearchHistory() {
        return getLivePkServiceBaseUrl() + "/v1/pk/search/history";
    }

    public String postSearchHistoryClear() {
        return getLivePkServiceBaseUrl() + "/v1/pk/search/history/clear";
    }

    /**
     * 不再接收PK邀请
     */
    public String getRejectInvitePkUrl() {
        return getLivePkServiceBaseUrl() + "/v1/pk/invitation/reject";
    }

    /**
     * 查询pk贡献榜
     */
    public String getPkContributeListUrl() {
        return getLivePkServiceBaseUrl() + "/v2/ranking/round/list";
    }

    /**
     * 切换小额礼物按钮弹窗开关
     */
    public String getSwitchPopupUrl() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/toggle/little/gift/button/popup";
    }

    /**
     * 获取发言红包
     */
    public String queryWordRedPacketUrl() {
        return getLiveMammonServiceBaseUrl() + "/speak/packet/id/get";
    }

    public String queryMultiWordRedPacketUrl() {
        return getLiveMammonServiceBaseUrl() + "/speak/packet/batch/get";
    }

    /**
     * 上头条头条主播及粉丝列表
     */
    public String getHeadlinesH5Url(long anchorUid, long roomId, long chatId, long liveId) {
        return getLiveServerH5RankUrl() + "gatekeeper/live-headlines/headlines?anchorUid=" + anchorUid
                + "&roomId=" + roomId + "&chatId=" + chatId + "&liveId=" + liveId;
    }

    /**
     * 直播间踢人
     */
    public String queryKickOutUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/kick/out";
    }

    /**
     * 用户端打赏榜页面以及在线列表页面
     */
    public String getAudienceOnlineListH5Url() {
        return getLiveServerH5RankUrl() + "gatekeeper/online-list/rewardlist";
    }

    /**
     * 用户端打赏榜页面以及在线列表页面 ，新版，客户端版本 9.2.9.21开始
     */
    public String getAudienceOnlineListH5UrlNew() {
        return getLiveServerH5RankUrl() + "live-toc-ssr/userRank";
    }

    /**
     * 预言记录页面
     */
    public String getPkPredictRecordH5Url() {
        return getLiveServerH5RankUrl() + "gatekeeper/live-prediction/record";
    }

    /**
     * 预言结果页面
     */
    public String getPkPredictResultH5Url() {
        return getLiveServerH5RankUrl() + "gatekeeper/live-prediction/result";
    }

    /**
     * 预言页
     */
    public String getPkPredictPageH5Url() {
        return getLiveServerH5RankUrl() + "gatekeeper/live-prediction/home";
    }


    /**
     * pk赏金页面
     */
    public String getPkBountyH5Url(long anchorUid) {
        return getLiveServerH5RankUrl() + "gatekeeper/live-reward/reward?anchorUid=" + anchorUid;
    }

    /**
     * 获取生日场信息
     */
    public String getBirthDayUrl() {
        return getLiveServerMobileHttpHost() + "metis/api/v1/birthdayParty/lotteryDetail";
    }

    /**
     * 获取官方直播间
     */
    public String getOfficialLiveRoomUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/official/room";
    }

    /**
     * 官方直播间,主播选择
     */
    public String getOfficialLiveHostChooseUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/dialog/confirm";
    }

    /**
     * 主播查询：连麦dialog的在线列表
     */
    public String getMicAudienceListUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/mic/online/user/list";
    }

    /**
     * 是否显示礼物墙弹窗
     */
    public String getLiveCategoryInfoUrl() {
        return getLiveServerHost() + "lamia/v1/live/channel";
    }

    /**
     * 查询讲解商品挂件
     *
     * @return 查询讲解商品挂件接口
     */
    public final String getExplainGoodsUrl() {
        return getFrozenServiceBaseUrlV1() + "/user/speaking/goods";
    }

    /**
     * 获取视频混流配置
     *
     * @return 获取视频混流配置接口
     */
    public final String getVideoMixConfig() {
        return getMicSettingServiceBaseUrl() + "v1/getvideomixconfig";
    }

    /**
     * 获取主播连麦权限设置
     *
     * @return 获取主播连麦权限设置接口
     */
    public final String getUserMicAuthConfig() {
        return getMicSettingServiceBaseUrl() + "v1/getusermicauthconfig";
    }

    /**
     * 修改主播连麦权限设置
     *
     * @return 修改主播连麦权限设置接口
     */
    public final String getModifyUserMicAuthConfig() {
        return getMicSettingServiceBaseUrl() + "v1/setusermicauthconfig";
    }

    /**
     * 获取单个fm搜索
     */
    public final String postMicPkSearchUrl() {
        return getLivePkServiceBaseUrl() + "/mic/pk/v1/search";
    }

    // 查询连麦搜索记录url
    public final String getMicPkHistoryUrl() {
        return getLivePkServiceBaseUrl() + "/mic/pk/v1/search/history";
    }

    // 清除连麦搜索记录url
    public final String clearMicPkHistoryUrl() {
        return getLivePkServiceBaseUrl() + "/mic/pk/v1/search/history/clear";
    }

    // 查询PK状态信息
    public final String getPkStatusInfoUrl() {
        return getLivePkServiceBaseUrl() + "/mic/pk/v1/status/info";
    }

    /**
     * 连麦PK入口白名单
     */
    public final String getMicPkWhiteListUrl() {
        return getLivePkServiceBaseUrl() + "/mic/pk/v1/whitelist";
    }

    /**
     * 音频连麦入口白名单
     */
    public final String getMicWhiteListUrl() {
        return getMicSettingServiceBaseUrl() + "/v1/multi/groupmic/white";
    }

    //设置场次主播群麦黑名单
    public final String getSetInviterBlackListUrl() {
        return getMicSettingServiceBaseUrl() + "v1/setinviterblacklist";
    }

    //获取用户连麦认证判断
    public final String getLiveMicNeedCheckVerifyUrl() {
        return getMicSettingServiceBaseUrl() + "v1/getusermiclimit";
    }

    //查询个人直播间是否开启带货
    public final String getQuerySellStatusUrl() {
        return getFrozenServiceBaseUrlV1() + "/live/goods/query";
    }

    //带货开关
    public final String geToggleSellUrl() {
        return getFrozenServiceBaseUrlV1() + "/goods/onOff";
    }

    private String getXimaMobileBaseUrl() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "http://m.ximalaya.com/";
        } else {
            return "http://m.test.ximalaya.com/";
        }
    }

    /**
     * 音视频整合--底部左侧常用按钮
     */
    public String getLiveRoomBottomButtons() {
        return getLiveTaliyahWebServiceBaseUrl() + "/v1/room/frequently/functions";
    }

    /**
     * 娱乐直播间，查询更多直播
     */
    public final String getQueryMoreLiveRecordUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/more/records";
    }


    /**
     * 直播间左上角查询是否有福袋
     */
    public String getRoomLucyBagUrl() {
        return getLiveLotteryServiceBaseUrl() + "/v1/gift/lottery/show";
    }

    /**
     * 直播间查询是否有投票功能
     *
     * @return
     */
    public String getRoomVoteIsShow() {
        return getPredictionWeb() + "/vote/show/v1";
    }

    /**
     * 官方直播间查询是否有投票功能
     *
     * @return 官方直播间查询是否有投票功能API
     */
    public String getOfficialRoomVoteIsShow() {
        return getLiveWishServiceBaseUrl() + "/v1/official/rank/status";
    }

    /**
     * C端获取满意度问卷信息
     */
    public final String getSatisfactionUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/csflow/usercheck";
    }

    /**
     * 用户已购直播列表
     */
    public final String getPaidLiveListUrl() {
        return getCourseLiveServiceBaseUrl() + "/v1/paid/list";
    }

    /**
     * 获取用户和主播的关注关系
     */
    public String getHostFollowStatusUrl() {
        return getLiveFollowServiceBaseUrl() + "/check_follow";
    }

    /**
     * 获取 PGC 房间权限
     */
    public String getPGCRoomAuthUrl() {
        return getLiveServerMobileHttpHost() + "doom-web/entertain/room/user/v1/room/auth";
    }


    /**
     * 获取弹幕模版
     */
    public String getUserBulletTemplateUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v2/live/getUserBulletTemplate";
    }

    /**
     * 获取推荐话题
     */
    public String getLiveRecommendTopicUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/topic/list";
    }

    /**
     * 获取图文公告详情
     */
    public String getLiveAnnouncementDetailUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v5/live/topic/detail";
    }

    /**
     * 保存图文公告草稿
     */
    public String getLiveAnnouncementDraftSaveUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/topic/draft/save";
    }

    /**
     * 发布图文公告
     */
    public String getLiveAnnouncementPublishUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v6/live/topic/publish";
    }

    /**
     * 直播间通用枚举接口,用于获取各个贵族和守护等级对应的信息
     * 如：国王贵族的名称、icon、id，月神守护的名称、icon、id
     */
    public String getLiveCommonEnumsUrl() {
        return getLiveLamiaServiceBaseUrl() + "/common/live-enums";
    }

    /**
     * pk合规弹窗
     */
    public String getPkComplianceHintUrl() {
        return getLivePkServiceBaseUrl() + "/v1/compliance/restriction/hint";
    }

    //未关注人消息均设为已读 post
    public String getClearNoCareUnreadNumUrl() {
        return getServerNetAddressHost() + "chaos-notice-web/v1/himsg/unreadcount/reset";
    }

    /**
     * 是否弹窗提示领取过指定头像框
     */
    public String getPopupAvatarInfoUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/popup/avatar/info";
    }

    /**
     * 是否弹窗显示提示可领取广告奖励礼物
     */
    public String getPopupAdvertiseUrl() {
        return getFirstRechargeServiceBaseUrl() + "/popup/v1/ad/room";
    }

    /**
     * 添加收藏房间
     */
    public String addFavoriteRoom(long mRoomId) {
        return getLiveDoomServiceBaseUrl() + "/entertain/favorite/" + mRoomId + "/add/v1";
    }

    /**
     * 移除收藏房间
     */
    public String removeFavoriteRoom(long mRoomId) {
        return getLiveDoomServiceBaseUrl() + "/entertain/favorite/" + mRoomId + "/remove/v1";
    }

    /**
     * 装扮数据
     */
    public String getDecorateByType() {
        return getLiveTreasureServiceBaseUrl() + "/v2/query/dress/type";
    }

    public String getPostBgDecorateByType() {
        return getLiveTreasureServiceBaseUrl() + "/v1/dress/self/upload";
    }

    public String getDeleteBgDecorateByType() {
        return getLiveTreasureServiceBaseUrl() + "/v1/dress/self/delete";
    }

    /**
     * 装扮数据
     */
    public String selectDecorate() {
        return getLiveTreasureServiceBaseUrl() + "/v1/dress/selected";
    }

    public String gameConfigUrl() {
        return getLiveOpenGameServiceUrl() + "/game/config";
    }

    /**
     * 个播音视频通用上报接口
     */
    public String getLamiaBizReportUrl() {
        return getLiveServerMobileHttpHost() + "lamia/v1/common/business/report";
    }

    /**
     * 直播聊天室登录服务，生产环境
     */
    public static String getRoomLoginProductUrl() {
        if (ConstantsOpenSdk.isDebug) {
            return "http://live.ximalaya.com/rm-login/v4/login";
        }

        return "https://live.ximalaya.com/rm-login/v4/login";
    }

    /**
     * 直播聊天室登录服务，测试环境
     */
    public static String getRoomLoginTestUrl() {
        return "http://live.test.ximalaya.com/rm-login/v4/login";
    }

    /**
     * 首映室试听上报
     */
    public final String getRoomCommonConfig() {
        return getLiveLamiaServiceBaseUrl() + "/live/config";
    }

    /**
     * 新推荐首页 - 热门直播落地页直播列表地址
     */
    public final String getRecommendHotLiveLandPageUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/app/live/more/records";
    }

    /**
     * 打开赞助榜和在线榜聚合页面
     *
     * @param h5Url 页面链接
     * @param appId 业务id
     * @param tab   打开页面后定位到指定 tab
     * @return iting 184 链接
     */
    public final String getGiftAndRank184Iting(String h5Url, int appId, int tab) {
        if (TextUtils.isEmpty(h5Url)) {
            return "";
        }

        try {
            String encodeH5Url = URLEncoder.encode(h5Url, "UTF-8");
            int dialogHeightPx = BaseUtil.getScreenHeight(MainApplication.getMyApplicationContext()) / 3 * 2;
            int heightDp = BaseUtil.px2dip(MainApplication.getMyApplicationContext(), dialogHeightPx);

            StringBuilder sb = new StringBuilder();
            sb.append("iting://open?msg_type=184&width=375&position=bottom&animationFrom=bottom&showClose=0&realTransparent=1");
            sb.append("&height=").append(heightDp);
            sb.append("&appId=").append(appId);
            sb.append("&tab=").append(tab);
            sb.append("&extraUrl=").append(encodeH5Url);
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }


    /**
     * 直播首页关注列表排序开关
     */
    public final String getLiveHomeFollowSortSwitchUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/follow/sort/switch";
    }

    public final String getGiftPanelActivityItemInfoUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/table/info";
    }

    public final String getRoomQuitInterceptorUrl() {
        return getLiveLamiaServiceBaseUrl() + "/v1/live/quit";
    }

    /**
     * 直播间推广中挂件点击事件上报
     */
    public final String getReportAdPromotionClickEventUrl() {
        return getLiveDataServiceBaseUrl() + "/v1/product/click";
    }

    /**
     * 巨量引擎站外投放上报
     */
    public final String reportNewInstallUseActivateAppEventUrl() {
        return getLiveDataServiceBaseUrl() + "/new/user/live/iting";
    }
}
