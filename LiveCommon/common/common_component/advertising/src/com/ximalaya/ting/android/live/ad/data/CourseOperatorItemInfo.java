package com.ximalaya.ting.android.live.ad.data;

import androidx.annotation.Keep;

/**
 * 视频直播广告数据
 *
 * <AUTHOR>
 */
@Keep
public class CourseOperatorItemInfo {

    /**
     * 挂件名称
     */
    public String name;

    /**
     * 类型 0：小挂件上
     * 类型 1：小挂件下
     * 不论是小挂件上还是小挂件下，统一当作小挂件处理
     */
    public int type;

    /**
     * 是否独占0,独占、1不独占
     */
    public int position;

    /**
     * 0:图片挂件,使用imageUrl+targetUrl,1:H5挂件,使用webViewUrl
     */
    public int urlType;
    /**
     * imageUrl
     */
    public String imageUrl = "";

    /**
     * 图片点击跳转链接
     */
    public String targetUrl = "";

    /**
     * 站内iting跳
     */
    public String itingUrl = "";

    /**
     * WebView 挂件 H5 链接
     */
    public String webViewUrl = "";

    /**
     * 本地生成的埋点去重请求id
     */
    public String xmRequestId;

}
