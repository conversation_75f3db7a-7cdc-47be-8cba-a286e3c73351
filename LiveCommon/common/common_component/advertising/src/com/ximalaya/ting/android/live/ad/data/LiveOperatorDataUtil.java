package com.ximalaya.ting.android.live.ad.data;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程直播挂件数据转换
 *
 * @email <EMAIL>
 * @phone 15026804470
 */
public class LiveOperatorDataUtil {

    public static OperationInfo convertCourseAdsResToComm(List<CourseOperatorItemInfo> courseAds) {
        if (courseAds == null || courseAds.isEmpty()) {
            return null;
        }

        OperationInfo res = new OperationInfo();

        int topAdIndex = 0;
        int bottomAdIndex = 0;

        List<OperationInfo.OperationItemInfo> topList = new ArrayList<>();
        List<OperationInfo.OperationItemInfo> bottomList = new ArrayList<>();

        for (int i = 0; i < courseAds.size(); i++) {
            CourseOperatorItemInfo itemInfo = courseAds.get(i);
            if (itemInfo.type == 0) {
                OperationInfo.OperationItemInfo topItem = convertToCommItem(itemInfo, topAdIndex);
                topList.add(topItem);
                topAdIndex++;
            }

            if (itemInfo.type == 1) {
                OperationInfo.OperationItemInfo topItem = convertToCommItem(itemInfo, bottomAdIndex);
                bottomList.add(topItem);
                bottomAdIndex++;
            }

        }

        res.setRollSecond(3);
        res.setLargePendants(null);
        res.setLittlePendants(null);

        ArrayList<OperationInfo.OperationItemInfo> littlePendants = new ArrayList<>();
        if (!bottomList.isEmpty()) {
            littlePendants.addAll(bottomList);
        }
        if (!topList.isEmpty()) {
            littlePendants.addAll(topList);
        }
        res.setLittlePendants(littlePendants);

        return res;
    }

    public static OperationInfo.OperationItemInfo convertToCommItem(CourseOperatorItemInfo item, int pos) {
        OperationInfo.OperationItemInfo res = new OperationInfo.OperationItemInfo();

        res.setId(-1);
        res.setImageUrl(item.imageUrl);
        res.setUrlType(item.urlType);
        res.setTargetUrl(!TextUtils.isEmpty(item.itingUrl) ? item.itingUrl : item.targetUrl);
        res.setWebViewUrl(item.webViewUrl);
        res.setPosition(pos);
        res.setXmRequestId(item.xmRequestId);

        return res;
    }


    public static void reportAdPromotionViewClickEvent(String cid, String adPayload, long liveId, long anchorUid) {
        CommonRequestForRoomAd.reportAdPromotionClickEvent(
                cid,
                adPayload,
                liveId,
                anchorUid,
                new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean data) {
                        boolean ret = data != null && data;
                        Logger.d("LiveOperatorDataUtil", "reportAdPromotionViewClickEvent ret = " + ret);
                    }

                    @Override
                    public void onError(int code, String message) {
                        Logger.d("LiveOperatorDataUtil", "reportAdPromotionViewClickEvent code = " + code + " message = " + message);
                    }
                });
    }

}
