package com.ximalaya.ting.android.live.common.dialog.web;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Looper;
import android.webkit.JavascriptInterface;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.reflect.TypeToken;
import com.tencent.smtt.sdk.WebView;
import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.BaseScrollConstant;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILiveFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.live.BookBean;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.extension.ResourceExt;
import com.ximalaya.ting.android.live.common.dialog.web.bean.JsAlbumSubscribeData;
import com.ximalaya.ting.android.live.common.lib.base.bean.JsPreviewMediaData;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.CommonRequestForCommon;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.base.util.uppermodulehook.HookFromUpperLayerModule;
import com.ximalaya.ting.android.live.common.lib.base.util.uppermodulehook.ISubscribeRoomHook;
import com.ximalaya.ting.android.live.common.lib.entity.PropInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.manager.LiveFollowInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.broadcast.LiveLocalBroadcastManager;
import com.ximalaya.ting.android.live.common.lib.utils.CollectionsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveListenUtil;
import com.ximalaya.ting.android.live.common.lib.utils.LiveXdcsUtil;
import com.ximalaya.ting.android.live.common.lib.utils.gift.SendGiftHelper;
import com.ximalaya.ting.android.live.common.view.dialog.NewAudienceAcquireDialog;
import com.ximalaya.ting.android.live.lib.liveroomalbum.bean.MediaPreviewData;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.AsyncGson;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直播 Native 和 JS 通讯接口 JSInterface。
 * 请注意：
 * 1、新增 JS 方法能力时，默认在个播、PGC聊天室、UGC聊天室和课程直播都实现支持。
 *
 * <AUTHOR>
 * @wiki <a href="https://alidocs.dingtalk.com/i/nodes/ROGpvEna5YQWmXdnmngz84ykmK3zoB27">直播业务Native和JS方法（liveJsBridge）说明</a>
 */
public class CommonXmlObjcJsCall implements IFragmentFinish,
        SendGiftHelper.IRechargeDialogShowListener,
        SendGiftHelper.IRechargeDialogCancelListener {

    private static final String TAG = "CommonXmlObjcJsCall";

    /**
     * interfaceName
     */
    public static final String XML_OBJC = "xmlObjc";
    /**
     * interfaceName
     */
    public static final String XML_POPVIEW = "popview";

    public static final String LOCAL_BROADCAST_ACTION_SEND_MESSAGE = "action_send_message_XmlObjcJsCall";
    public static final String LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE = "action_close_current_page_xmlobjcjscall";
    public static final String KEY_SEND_MESSAGE = "key_send_message_XmlObjcJsCall";

    /**
     * 本地广播 隐藏AI小红点 UI
     */
    public static final String LOCAL_BROADCAST_ACTION_DISMISS_AI_DOT = "com.ximalaya.ting.android.liveanchor.components.bottom.DISMISS_AI_DOT";
    /**
     * 用户判断打开的是否为 ai助手弹窗
     */
    public static final String KEY_AI_DOT_URL = "com.ximalaya.ting.android.liveanchor.components.bottom.DISMISS_AI_DOT_URL";

    /**
     * 原生调用h5方法
     */
    private final String NATIVE_CALL_JS_NAME_PRE = "javascript:window.liveNativeCall.";


    /**
     * 关闭弹窗 （打企鹅用）
     */
    private static final String METHOD_QUIT_GAME = "quitGame";
    /**
     * 关闭弹窗
     */
    private static final String METHOD_CLOSEPOP = "closePop";
    /**
     * 送礼，params 参数说明：giftId 为礼物 id，amount 为礼物数量
     */
    private static final String METHOD_SEND_GIFTS = "sendGifts";

    /**
     * 直播间内发送消息  param参数说明：IM消息内容字符串
     */
    private static final String METHOD_SEND_MESSAGE = "sendMessage";
    /**
     * 关闭当前native页面
     */
    private static final String METHOD_CLOSE_CURRENT_PAGE = "closeCurrentPage";
    /**
     * 关注用户
     */
    private static final String METHOD_FOLLOW = "follow";
    /**
     * 关注房间 pgc使用较多
     */
    private static final String METHOD_FOLLOW_ROOM = "subscribeRoom";

    /**
     * 预约直播
     */
    private static final String METHOD_BOOK_LIVE = "bookLive";

    /**
     * 生成签名方法
     */
    private static final String METHOD_GENERATE_SIGNATURE = "generateSignature";
    /**
     * 新手福利-领取奖励弹窗
     */
    public static final String METHOD_SHOW_PACKAGE_ITEM = "showPackageItem";
    /**
     * 唤起键盘
     */
    public static final String METHOD_SHOW_KEYBOARD = "showKeyBoard";
    /**
     * 唤起键盘打开弹幕开关
     */
    public static final String METHOD_OPEN_KEYBOARD_DANMU = "showKeyBoardAndOpenBullet";
    /**
     * 长连接支持活动类消息推送
     */
    public static final String METHOD_PUSH_MESSAGE = "pushMessage";

    /**
     * 更新容器高度
     */
    public static final String METHOD_UPDATE_CONTAINERHEIGHT = "updateContainerHeight";
    /**
     * key 容器高度
     */
    public static final String JSON_PARAM_HEIGHT = "height";

    public static final String METHOD_SHARELIVEROOM = "shareLiveRoom";
    /**
     * 获取背景图推荐颜色
     */
    public static final String METHOD_BACKGROUND_COLOR = "getBackgroundColor";

    /**
     * 根据参数活动 id 定位到底部运营位对应活动 (停止轮播)，指定倒计时结束后恢复挂件轮播，同时回调 H5 直播间底部运营位挂件中心锚点坐标
     */
    public static final String METHOD_FREEZE_BOTTOM_BANNER = "freezeBottomBanner";

    /**
     * 更新节目单状态
     */
    public static final String METHOD_UPDATE_OFFICIAL_STATUS = "updateOfficialFollowStatus";

    /**
     * 预览媒体文件，支持图片视频混合预览
     */
    public static final String METHOD_PREVIEW_MEDIA = "previewMedia";

    /**
     * 更新专辑订阅状态
     */
    public static final String METHOD_UPDATE_ALBUM_SUBSCRIBE_STATUS = "albumSubscribeStatus";

    /**
     * 获取一次上报收听时长的时间距离当前时间的间隔
     * 返回时长单位：毫秒 liveNativeCall.sendLastReportInterval(millisecond)，
     */
    public static final String METHOD_GET_LAST_REPORT_INTERVAL = "getLastReportInterval";

    private ICallback mCallback;

    @Override
    public void onFinishCallback(Class<?> cls, int fid, Object... params) {

    }

    @Override
    public void reChargeDialogShow() {
        if (mCallback != null) {
            mCallback.closeDialog();
        }
    }

    @Override
    public void cancelRechargeDialog() {
        notifyJsSendGiftResult(false);
    }

    public interface ICallback {
        void closeDialog();

        void updateContainerHeight(int height);

        void shareLiveRoom();

        void previewMedia(List<MediaPreviewData> previewData, int position);
    }

    public void setCallback(ICallback callback) {
        this.mCallback = callback;
    }

    private WeakReference<WebView> mWeakWebViewRef;

    public void setWebView(WebView webView) {
        mWeakWebViewRef = new WeakReference<>(webView);
    }


    private long mAnchorUid;

    public void setExtraData(long anchorUid) {
        this.mAnchorUid = anchorUid;
    }

    public void destroy() {
        if (mCallback != null) {
            mCallback = null;
        }
        SendGiftHelper.getInstance().removeRechargeDialogShowListener(this);
        SendGiftHelper.getInstance().removeRechargeDialogCancelListener(this);
    }

    @JavascriptInterface
    public void postMessage(String params) {
        try {
            JSONObject jsonObject = new JSONObject(params);
            String method = jsonObject.optString("method");
            String methodParam = jsonObject.optString("param");


            if (TextUtils.isEmpty(method)) {
                return;
            }

            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    handlePossMessage(method, methodParam);
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handlePossMessage(String method, String methodParam) {
        if (TextUtils.isEmpty(method)) {
            return;
        }

        switch (method) {
            case METHOD_QUIT_GAME:
            case METHOD_CLOSEPOP:
                if (mCallback != null) {
                    mCallback.closeDialog();
                }
                break;
            case METHOD_SEND_GIFTS:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallSendGifts(methodParam);
                }
                break;
            case METHOD_SEND_MESSAGE:
                if (TextUtils.isEmpty(methodParam)) {
                    return;
                }

                Activity activity = BaseApplication.getMainActivity();
                if (activity == null) {
                    return;
                }
                Intent intent = new Intent(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_SEND_MESSAGE);
                intent.putExtra(CommonXmlObjcJsCall.KEY_SEND_MESSAGE, methodParam);
                LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
                break;
            case METHOD_CLOSE_CURRENT_PAGE:
                handleJSCallCloseFragment();
                break;
            case METHOD_FOLLOW:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallFollow(methodParam);
                }
                break;
            case METHOD_FOLLOW_ROOM:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallFollowRoom(methodParam);
                }
                break;
            case METHOD_BOOK_LIVE:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallBookLive(methodParam);
                }
                break;
            case METHOD_GENERATE_SIGNATURE:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallGenerateSignature(methodParam);
                }
                break;
            case METHOD_SHOW_PACKAGE_ITEM:
                if (!TextUtils.isEmpty(methodParam)) {
                    handleJsCallShowNewAudienceDialog(methodParam);
                }
                break;
            case METHOD_SHOW_KEYBOARD:
                handleJsCallShowKeyBoard();
                break;
            case METHOD_OPEN_KEYBOARD_DANMU:
                handleOpenKeyBoardDanMu(methodParam);
                break;
            case METHOD_PUSH_MESSAGE:
                handleJsCallPushMessage(methodParam);
                break;
            case METHOD_UPDATE_CONTAINERHEIGHT:
                handleContainerHeight(methodParam);
                break;
            case METHOD_SHARELIVEROOM:
                shareLiveRoom();
                break;
            case METHOD_BACKGROUND_COLOR:
                getBackgroundColor();
                break;
            case METHOD_UPDATE_OFFICIAL_STATUS:
                updateOfficialStatus(methodParam);
                break;
            case METHOD_FREEZE_BOTTOM_BANNER:
                handleFreezeBottomBanner(methodParam);
                break;
            case METHOD_PREVIEW_MEDIA:
                goPreviewMedia(methodParam);
                break;
            case METHOD_UPDATE_ALBUM_SUBSCRIBE_STATUS:
                handleUpdateAlbumSubscribeStatus(methodParam);
                break;
            case METHOD_GET_LAST_REPORT_INTERVAL:
                handleGetLastReportInterval();
                break;
            default:
                break;
        }
    }

    /**
     * 关闭当前页
     */
    private void handleJSCallCloseFragment() {
        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }
        Intent intent = new Intent(CommonXmlObjcJsCall.LOCAL_BROADCAST_ACTION_CLOSE_CURRENT_PAGE);
        LocalBroadcastManager.getInstance(activity).sendBroadcast(intent);
    }

    private void updateOfficialStatus(String json) {
        boolean follow = false;
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                follow = jsonObject.optBoolean("follow", false);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().updateOfficialFollowStatus(BaseApplication.getTopActivity(), follow);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleFreezeBottomBanner(String json) {
        int actId = 0;
        int countdown = 0;

        try {
            if (!TextUtils.isEmpty(json)) {
                JSONObject jsonObject = new JSONObject(json);
                actId = jsonObject.optInt("actId", 0);
                countdown = jsonObject.optInt("countdown", 0);
            }

            if (actId <= 0 || countdown <= 0) {
                // 参数错误
                handleInvalidBottomBannerAnchorCallback();
                return;
            }

            LiveActionRouter router = Router.getActionRouter(Configure.BUNDLE_LIVE);
            if (router == null) {
                handleInvalidBottomBannerAnchorCallback();
                return;
            }

            Activity topActivity = BaseApplication.getTopActivity();
            Point anchor = router.getFunctionAction().getRoomBottomBannerAnchor(topActivity);
            if (anchor == null) {
                handleInvalidBottomBannerAnchorCallback();
                return;
            }

            // 调用房间方法，定位并冻结 (滚动) 对应的底部运营位，x 秒后自动解冻
            boolean valid = router.getFunctionAction().freezeRoomBottomBanner(
                    topActivity, actId, countdown
            );
            if (!valid) {
                handleInvalidBottomBannerAnchorCallback();
                return;
            }

            // 计算出坐标，单位 dp，H5 全屏容器没有延伸到状态栏，需要减去状态栏高度
            int statusBarHeight = BaseUtil.getStatusBarHeight(topActivity);
            int xInDp = ResourceExt.getPx2dp(anchor.x);
            int yInDp = ResourceExt.getPx2dp(anchor.y - statusBarHeight);
            handleBottomBannerAnchorCallback(xInDp, yInDp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 直接回调原点坐标不做动画
     */
    private void handleInvalidBottomBannerAnchorCallback() {
        handleBottomBannerAnchorCallback(0, 0);
    }

    private void handleBottomBannerAnchorCallback(int x, int y) {
        String call = NATIVE_CALL_JS_NAME_PRE + "getBottomBannerAnchorCallback(" + x + ", " + y + ")";
        WebView mWebView = mWeakWebViewRef.get();
        if (mWebView != null) {
            //必须在主线程调用
            mWebView.evaluateJavascript(call, null);
        }
    }

    private void goPreviewMedia(String json) {
        try {
            new AsyncGson<JsPreviewMediaData>().fromJson(
                    json, JsPreviewMediaData.class,
                    new AsyncGson.IResult<JsPreviewMediaData>() {
                        @Override
                        public void postResult(JsPreviewMediaData result) {
                            if (result != null && !CollectionsUtil.isEmpty(result.getList())) {
                                if (mCallback != null) {
                                    mCallback.previewMedia(
                                            result.formatToPreviewData(),
                                            result.getPreviewPosition()
                                    );
                                }
                            }
                        }

                        @Override
                        public void postException(Exception e) {
                            e.printStackTrace();
                        }
                    }
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleUpdateAlbumSubscribeStatus(String json) {
        try {
            new AsyncGson<JsAlbumSubscribeData>().fromJson(
                    json, JsAlbumSubscribeData.class,
                    new AsyncGson.IResult<JsAlbumSubscribeData>() {
                        @Override
                        public void postResult(JsAlbumSubscribeData result) {
                            if (result != null && result.getAlbumId() > 0) {
                                AlbumEventManage.sendWidgetBroadCast(
                                        result.getAlbumId(), result.getSubscribed()
                                );
                            }
                        }

                        @Override
                        public void postException(Exception e) {
                            e.printStackTrace();
                        }
                    }
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getBackgroundColor() {
        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
            return;
        }
        try {
            int backgroundColor = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().getBackgroundColor(BaseApplication.getTopActivity());
            String red = Integer.toHexString(Color.red(backgroundColor));
            String green = Integer.toHexString(Color.green(backgroundColor));
            String blue = Integer.toHexString(Color.blue(backgroundColor));
            String color = "#" + red + "" + green + "" + blue;
            WebView webView = mWeakWebViewRef.get();
            if (webView != null) {
                //必须在主线程调用
                webView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "getBackgroundColorCallback('" + color + "')", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleOpenKeyBoardDanMu(String json) {
        String text = "";
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                text = jsonObject.optString("text");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().showLiveKeyBoardAndDanMu(BaseApplication.getTopActivity(), text);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleJsCallShowKeyBoard() {
        try {
            Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction().showLiveKeyBoard(BaseApplication.getTopActivity());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void shareLiveRoom() {
        if (mCallback != null) {
            mCallback.shareLiveRoom();
        }
    }

    private void handleContainerHeight(String methodParam) {
        if (TextUtils.isEmpty(methodParam)) {
            return;
        }
        int height = 0;

        try {
            JSONObject jsonObject = new JSONObject(methodParam);
            height = jsonObject.optInt(JSON_PARAM_HEIGHT);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (height <= 0) {
            return;
        }

        if (mCallback != null) {
            mCallback.updateContainerHeight(height);
        }
    }

    private void handleJsCallShowNewAudienceDialog(String methodParam) {
        if (TextUtils.isEmpty(methodParam)) {
            return;
        }

        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }

        long id = 0;

        try {
            JSONObject jsonObject = new JSONObject(methodParam);
            id = jsonObject.optLong("packageId");
        } catch (Exception e) {
            e.printStackTrace();
        }

        final long packageId = id;
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("packageItemIds", String.valueOf(packageId));
        CommonRequestForCommon.batchQueryPackageInfo(params, new IDataCallBack<List<PropInfo>>() {
            @Override
            public void onSuccess(@Nullable List<PropInfo> object) {
                if (ToolUtil.isEmptyCollects(object)) {
                    return;
                }
                PropInfo propInfo = object.get(0);
                if (propInfo == null || TextUtils.isEmpty(propInfo.getAvatar())) {
                    return;
                }

                String packageCoverPath = propInfo.getAvatar();
                NewAudienceAcquireDialog dialog = new NewAudienceAcquireDialog((FragmentActivity) activity, packageCoverPath, packageId);
                dialog.show();

                if (mCallback != null) {
                    mCallback.closeDialog();
                }
            }

            @Override
            public void onError(int code, String message) {
                NewAudienceAcquireDialog dialog = new NewAudienceAcquireDialog((FragmentActivity) activity, "", packageId);
                dialog.show();
            }
        });
    }

    private void handleJsCallGenerateSignature(String jsonMap) {
        String signature = "";

        if (TextUtils.isEmpty(jsonMap)) {
            return;
        }

        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }

        try {
            Map<String, String> params = LiveGsonUtils.sGson.fromJson(jsonMap,
                    new TypeToken<Map<String, String>>() {
                    }.getType());


            if (params == null) {
                return;
            }

            signature = EncryptUtil.getInstance(activity).getSignatureV2(activity, params);
            if (!TextUtils.isEmpty(signature)) {

                if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                    return;
                }

                WebView mWebView = mWeakWebViewRef.get();
                if (mWebView != null) {
                    //必须在主线程调用
                    mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "signatureCallback('" + signature + "')", null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void handleJsCallFollow(String paramsJson) {
        if (TextUtils.isEmpty(paramsJson)) {
            return;
        }

        boolean follow = false;
        long uid = -1;
        int subBizType = 0;

        try {
            JSONObject jsonObject = new JSONObject(paramsJson);
            follow = jsonObject.optBoolean("follow");
            uid = jsonObject.optLong("uid");
            subBizType = jsonObject.optInt("subBizType");
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (uid < 0) {
            return;
        }

        boolean finalFollow = follow;
        long finalUid = uid;


        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }

        String specificParams = LiveFollowInfoManager.getInstance().getFollowParams();
        AnchorFollowManage.setFollowRequestV3(
                activity, finalUid, !finalFollow,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM,
                subBizType != 0 ? subBizType : AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_PROMOTION_ACTIVITY,
                specificParams, true, new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {

                        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                            return;
                        }

                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateFollowStatus(true)", null);
                        }
                    }

                    @Override
                    public void onError(int i, String s) {
                        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                            return;
                        }

                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateFollowStatus(false)", null);
                        }
                    }
                }
        );
    }


    private void handleJsCallBookLive(String paramsJson){
        if (TextUtils.isEmpty(paramsJson)) {
            return;
        }

        Logger.i("xm_live1","handleJsCallBookLive "+paramsJson);
        boolean book = false;
        int bizType = 0;
        long liveId = 0;

        try {
            JSONObject jsonObject = new JSONObject(paramsJson);
            bizType = jsonObject.optInt("bizType");
            liveId = jsonObject.optLong("liveId");
            book = jsonObject.optBoolean("book");
            Logger.i("xm_live1","handleJsCallBookLive bizType "+bizType + " liveId "+liveId +" book "+book);
        } catch (Exception e) {
            Logger.i("xm_live1","handleJsCallBookLive Exception "+e);
        }
        try {
            MainActivity mainActivity  = (MainActivity) BaseApplication.getMainActivity();
            if(mainActivity == null || mainActivity.getManageFragment() == null){
                CustomToast.showDebugFailToast("handleJsCallBookLive mainActivity null");
                return;
            }
            Fragment fragment= mainActivity.getManageFragment().getCurrentFragment();
            if(fragment == null){
                CustomToast.showDebugFailToast("handleJsCallBookLive fragment null");
                return;
            }
            boolean finalBook = book;
            ISubscribeRoomHook hook = HookFromUpperLayerModule.getUpperHookImpl();
            if(hook != null){
                Logger.i("xm_live1","handleJsCallBookLive subscribe hook "+hook);
                Map<String,Object> arg = new HashMap<>();
                arg.put("book",finalBook);
                arg.put("liveId",liveId);
                arg.put("bizType",bizType);
                hook.sendRequest((BaseFragment2) fragment, new IDataCallBack<BookBean>() {
                    @Override
                    public void onSuccess(@Nullable BookBean data) {
                        Logger.i("xm_live1","handleJsCallBookLive requestBookLive onSuccess ");
                        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                            return;
                        }
                        if(data == null){
                            return;
                        }
                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateBookLiveStatus(true)", null);
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateBookLiveStatus(false)", null);
                        }
                    }
                },arg);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 关注房间
     * @param paramsJson params
     */
    private void handleJsCallFollowRoom(String paramsJson) {
        if (TextUtils.isEmpty(paramsJson)) {
            return;
        }

        boolean follow = false;
        long uid = -1;

        try {
            JSONObject jsonObject = new JSONObject(paramsJson);
            follow = jsonObject.optBoolean("isSubscribe");
            uid = jsonObject.optLong("uid");
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (uid < 0) {
            return;
        }

        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }
        String url;
        if (follow) {
            url = LiveUrlConstants.getInstance().addFavoriteRoom(LiveFollowInfoManager.getInstance().getRoomId());
        } else {
            url = LiveUrlConstants.getInstance().removeFavoriteRoom(LiveFollowInfoManager.getInstance().getRoomId());
        }
        boolean finalFollow = follow;
        CommonRequestM.basePostRequestWithStr(url, LiveGsonUtils.sGson.toJson("{}"),
                new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean data) {
                        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                            return;
                        }
                        if(data == null || !data){
                            return;
                        }
                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateSubscribeRoomStatus(true)", null);
                        }
                        // 发送本地广播，通知 UI 更新
                        Intent intent = new Intent(LiveLocalBroadcastManager.ACTION.UPDATE_FAVORITE_STATE);
                        intent.putExtra(LiveLocalBroadcastManager.EXTRA.FAVORITE, finalFollow);
                        LiveLocalBroadcastManager.send(intent);
                    }

                    @Override
                    public void onError(int code, String message) {
                        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                            return;
                        }
                        WebView mWebView = mWeakWebViewRef.get();
                        if (mWebView != null) {
                            //必须在主线程调用
                            mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "updateSubscribeRoomStatus(false)", null);
                        }
                    }
                },
                content -> {
                    if (android.text.TextUtils.isEmpty(content)) {
                        return null;
                    }

                    try {
                        JSONObject jsonObject = new JSONObject(content);
                        if (!jsonObject.has("ret") || jsonObject.optInt("ret") != 0) {
                            return false;
                        }
                        return true;
                    } catch (Exception e) {
                        return null;
                    }
                });
    }

    /**
     * JS 调用 Native 送礼，目前进支持个播给主播送礼、娱乐派对送礼、课程直播送礼，不支持个播交友送礼和 UGC 聊天室送礼。
     *
     * @param json 送礼参数
     */
    public void handleJsCallSendGifts(String json) {
        long giftId;
        int amount;
        String toastMessage;
        try {
            JSONObject jsonObject = new JSONObject(json);
            amount = jsonObject.optInt("amount");
            giftId = jsonObject.optLong("giftId");
            toastMessage = jsonObject.optString("toastMessage");

            // 获取送礼服务
            int bizType = LiveRecordInfoManager.getInstance().getLiveRoomType();
            ILiveFunctionAction f = Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE).getFunctionAction();
            BaseGiftLoader giftLoader = (BaseGiftLoader) f.getGiftLoaderByRoomType(bizType, false);

            // 送礼场景
            int sendGiftType = getSendGiftType(bizType);

            // 执行送礼
            internalSendGift(giftLoader, giftId, amount, toastMessage, sendGiftType);
        } catch (Exception e) {
            e.printStackTrace();

            // 执行失败，上报错误信息
            String report = "handleJsCallSendGifts," +
                    " params:" + (json == null ? "" : json) +
                    " exception:" + e.getMessage();
            LiveXdcsUtil.doXDCS("LiveGiftSend", report);
        }
    }

    @ParamsConstantsInLive.SendGiftType
    private static int getSendGiftType(@BaseScrollConstant.LiveRoomBizType int bizType) {
        int sgt;
        if (bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_AUDIO
                || bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_VIDEO) {
            sgt = ParamsConstantsInLive.SEND_GIFT_TYPE_LIVE;
        } else if (bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_PGC) {
            sgt = ParamsConstantsInLive.SEND_GIFT_TYPE_HALL;
        } else if (bizType == BaseScrollConstant.LiveRoomBizType.LIVE_TYPE_COURSE) {
            sgt = ParamsConstantsInLive.SEND_GIFT_TYPE_COURSE_VIDEO_LIVE;
        } else {
            // 默认为个播送礼
            sgt = ParamsConstantsInLive.SEND_GIFT_TYPE_LIVE;
        }
        return sgt;
    }

    /**
     * 执行内部送礼逻辑
     *
     * @param giftLoader   送礼服务
     * @param giftId       礼物id
     * @param amount       礼物数量
     * @param toastMessage 送礼成功提示信息
     * @param sendType     送礼类型
     */
    private void internalSendGift(BaseGiftLoader giftLoader,
                                  long giftId,
                                  final int amount,
                                  String toastMessage,
                                  @ParamsConstantsInLive.SendGiftType int sendType) {
        SendGiftHelper.getInstance().addRechargeDialogShowListener(this);
        SendGiftHelper.getInstance().addRechargeDialogCancelListener(this);
        SendGiftHelper.getInstance().sendGift(giftId, amount, mAnchorUid, giftLoader.getClass(),
                sendType,
                new BaseGiftLoader.GiftSendSessionCallback() {
                    @Override
                    public void onSendSuccess(int currentRank, double contribution) {
                        if (!TextUtils.isEmpty(toastMessage)) {
                            CustomToast.showSuccessToast(toastMessage);
                        }
                        notifyJsSendGiftResult(true);
                    }

                    @Override
                    public void onSendFail(int code, String message) {
                        notifyJsSendGiftResult(false);
                        // 执行失败，上报错误信息
                        String report = "internalSendGift," +
                                " giftLoader:" + giftLoader +
                                " giftId:" + giftId +
                                " amount:" + amount +
                                " toastMessage:" + toastMessage +
                                " sendType:" + sendType +
                                " errorCode:" + code +
                                " errorMsg:" + message;
                        XDCSCollectUtil.statErrorToXDCS("LiveGiftSend", report);
                    }
                });
    }

    /**
     * 将送礼结果通知 JS
     *
     * @param isSuccess true 送礼成功，false 送礼失败
     */
    private void notifyJsSendGiftResult(boolean isSuccess) {
        // 送礼失败，回调失败结果
        if (null == mWeakWebViewRef) {
            return;
        }

        WebView webView = mWeakWebViewRef.get();
        if (null == webView) {
            return;
        }

        //必须在主线程调用
        if (Looper.getMainLooper() == Looper.myLooper()) {
            webView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "sendGiftsCallback(" + isSuccess + ")", null);
        } else {
            HandlerManager.postOnUIThread(() -> webView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "sendGiftsCallback(" + isSuccess + ")", null));
        }
    }

    public void handleJsCallPushMessage(String json) {
        if (TextUtils.isEmpty(json)) {
            return;
        }

        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }

        try {
            if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                return;
            }

            WebView mWebView = mWeakWebViewRef.get();
            if (mWebView != null) {
                //必须在主线程调用
                if (Looper.getMainLooper() == Looper.myLooper()) {
                    mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "pushMessage('" + json + "')", null);
                } else {
                    HandlerManager.postOnUIThread(() -> mWebView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "pushMessage('" + json + "')", null));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 通知 H5 更新专辑订阅状态
     *
     * @param albumId    专辑 id
     * @param subscribed 专辑是否订阅
     */
    public void updateAlbumSubscribeStatus(long albumId, boolean subscribed) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity == null) {
            return;
        }

        try {
            if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
                return;
            }

            WebView mWebView = mWeakWebViewRef.get();
            if (mWebView != null) {
                JsAlbumSubscribeData data = new JsAlbumSubscribeData(subscribed, albumId);
                String json = LiveGsonUtils.toJson(data);

                // 必须在主线程调用
                HandlerManager.postOnMainAuto(() -> {
                    mWebView.evaluateJavascript(
                            NATIVE_CALL_JS_NAME_PRE + "updateSubscribeAlbumStatus(" + json + ")",
                            null
                    );
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取一次上报收听时长的时间距离当前时间的间隔
     * 返回时长单位：毫秒 liveNativeCall.sendLastReportInterval(millisecond)，
     */
    private void handleGetLastReportInterval() {
        if (mWeakWebViewRef == null || mWeakWebViewRef.get() == null) {
            return;
        }
        try {
            WebView webView = mWeakWebViewRef.get();
            long lastReportInterval = LiveListenUtil.getLastReportIntervalInMs();
            if (webView != null) {
                //必须在主线程调用
                webView.evaluateJavascript(NATIVE_CALL_JS_NAME_PRE + "sendLastReportInterval(" + lastReportInterval + ")", null);
                Logger.d(TAG, "handleGetLastReportInterval, lastReportInterval = " + lastReportInterval);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
