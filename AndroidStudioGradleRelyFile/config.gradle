apply from: 'util.gradle'
ext {
    xmDependencies = [
            androidxCollection      : 'androidx.collection:collection:1.1.0',
            androidxAnnotations    : 'androidx.annotation:annotation:1.2.0',
            androidxAppcompat      : 'androidx.appcompat:appcompat:1.2.0',
            paletteV7              : 'androidx.palette:palette:1.0.0',
            supportV4              : 'androidx.legacy:legacy-support-v4:1.0.0',
            glide                  : 'com.github.bumptech.glide:glide:4.11.0',
            androidxFragment       : 'androidx.fragment:fragment:1.2.5',
            material               : 'com.google.android.material:material:1.4.0',
            recyclerView           : 'androidx.recyclerview:recyclerview:1.2.1',
            cardview               : 'androidx.cardview:cardview:1.0.0',
            percent                : 'androidx.percentlayout:percentlayout:1.0.0',
            gson                   : 'com.google.code.gson:gson:2.8.5',
            junit                  : 'junit:junit:4.12',
            multiDex               : 'androidx.multidex:multidex:2.0.0',
            okHttp                 : 'com.squareup.okhttp3:okhttp:3.12.11',
            okio                   : 'com.squareup.okio:okio:1.17.4',
            wechatSdkVersion       : 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0',
            blockcanaryVersion     : 'com.github.markzhai:blockcanary-android:1.5.0',
            blockcanaryNoOpVersion : 'com.github.markzhai:blockcanary-no-op:1.5.0',
            okHttpUrlConnect       : 'com.squareup.okhttp3:okhttp-urlconnection:3.12.1',
            picasso                : 'com.squareup.picasso:picasso:2.6.0',
            gifDrawable            : 'pl.droidsonroids.gif:android-gif-drawable:1.2.10',
            lottie                 : 'com.airbnb.android:lottie:3.4.2',
            xmutil                 : 'com.ximalaya.ting.android.xmutil:xmutil-' + NEW_FRAMEWORK_REPO_BRANCH + ':' + XMUTIL_REPO_VERSION,
            constraintLayout       : 'androidx.constraintlayout:constraintlayout:2.0.4',
            rebound                : 'com.facebook.rebound:rebound:0.3.8',
            stetho                 : 'com.facebook.stetho:stetho:1.6.0',
            stethoNoOp             : 'com.facebook.stetho:stetho-no-op:1.0.0',
            zxing                  : 'com.google.zxing:core:3.3.3',
            logView                : 'com.ximalaya.reactnative:LogView:0.0.5-SNAPSHOT',
            aspectJ                : 'org.aspectj:aspectjrt:1.9.6',
            work_manager           : 'androidx.work:work-runtime:2.3.4',
            xm_apptoolbox          : 'com.xiamlaya.ting.android.apptoolbox:xm_app_tool_box-developer:2.0.9',
            xm_apptoolbox_no_op    : 'com.xiamlaya.ting.android.apptoolbox:xm_app_tool_box-no_op:1.0.0',
            xmprocessor            : 'com.ximalaya.ting.android.xmprocessor:xmprocessor-dev-ksp:2.0.7',
            xmTypeAdapter          : 'com.ximalaya.ting.android.typeadapter:generator-dev-ksp:1.0.2',
            xmTransition           : 'androidx.transition:transition:1.3.0',
            androidXMedia          : 'androidx.media:media:1.0.0',
            androidXLocalBraoadCast: 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0',
            videoCache             : 'com.danikula:videocache:2.7.1',
            asynclayoutinflater    : 'androidx.asynclayoutinflater:asynclayoutinflater:1.0.0',
            xmgrowth               : 'com.ximalaya.ting.android.xmgrowth:xmgrowth-' + XM_GROWTH_BRANCH + ':' + XM_GROWTH_VERSION
    ]
    //编译控制参数
    /**
     ALL_COMPILE_SRC  是否生产全量包
     3. BUILD_PLUGIN_APK  是否生产包含插件的apk
     */

    packageName = "com.ximalaya.ting.android"
    createHostPatch = false
    openLintCheck = false //是否开启lint检查
    javaCompileVersion = JavaVersion.VERSION_1_8

    versionCode = "994"
    versionName = "********.dev"
    compileSdkVersion = 30
    buildToolsVersion = "30.0.3"
    minSdkVersion = "21"
    targetSdkVersion = "30"

    openBlockCanary = "false"  //是否开启BlockCanary
    generate_public_xml = false
    openLogView = "true"    //是否开启log日志展示
    //extChannels = "and-d3"//不能命名test/debug等字段开头
    channel = "and-f5-ocpa"// 开发时渠道改为 ceshi
    needChannel = "false"  //生成的apk中是否需要加入渠道信息
    modifyApplicationName = "false"  //是否需要修改应用名称
    applicationName = "喜马拉雅"
    //pChannels shell 输入参数
    shallReadChannelfromFile = "false"
    isProguard = "false"
    realObfuscate = true // 是否是真实的混淆
    isReleaseDebug = "true"
    openBTrace = "false"  //是否开启抓取 trace（上架包暂不打开，只用于本地性能分析）
    isBuildLeakCanaryInDebug = "false" //对release环境是否buildin没有影响，release环境固定不会打入LeakCanary
    isDebugGradle = "false"  //控制是否输出gradle 调试信息
    isBundleDevMode = "false"

    isOpenAutoTrace = "true" // 是否打开自动埋点
    isOpenPointSdk = "false" // 是否打入 可视化埋点sdk （一般不需要打开,打发布包时 务必关闭）
    serverType = "2" // 埋点服务器类型，用于选择不同的埋点服务器类型（2 正式服，1 uat （稳定测试服），3 测试服；）

    isOpenBootMonitor = "false" // 是否记录应用开启阶段的网络请求
    isCheckResourceConflict = "false" //是否导入CheckResourceConflict

    isDemo = "false"

    openLogSpirit = "false" // 是否打开日志回捞编译时操作

    isOpenXlogDecoder = "false"
//打开后，编译打入xlogdecode的包，在setting打开开关，日志会被解压存放到 files/xlog_decoded_file 目录

    if (ALL_COMPILE_SRC.toBoolean()) {
        openStetho = false
    } else {
        openStetho = false
    }

    createBaseJar = "true"
    createPatchJar = "false"
    //资源混淆mapping路径，打patch使用
    resProguardMapping = null

//    proguardLableDir = "XAndroidFramework"// 混淆标志文件，在有多级transform编译时
    commResPath = project(':TingMainHost:TingMainApp').getProjectDir().absolutePath + File.separator + "constant-resource"
    println "==========================================>commResPath:" + commResPath
    //只有引用了providedhost才需要把configurationType的值设置为archives，这样可以分析依赖，否则直接留空，正常项目如果使用了archives会导致libs里面的jar重复引用
    if (BUILD_PLUGIN_APK.toBoolean()) {
        configurationType = 'archives'
    } else {
        configurationType = ''
    }
    //是否应用以前的混淆mapping文件
    shouldApplyLastMapping = false
    // 打包时是否使用mapping文件，上面那个看起来没用了，但为了不和老的设置混了，新加个参数
    useMappings = "false"
    //是否应用public.xml
    shouldApplyPublicXml = false
    // 是否保持资源id固定
    keepResourceId=false
    //是否将application生成的Mapping拷贝到bundle project中
    isCopyApplicationMappingToBundleProject = false
    isBuildCommonRes = false
    //全量编译整个项目，生成混淆的mapping文件，用来支持各个bundle独立编译保持混淆的一致性，此开关打开后，Gradle脚本会忽略一些和生成混淆无关的task,从而加快编译速度
    isBuildAllProjectOnlyForProguardMapping = false
    hostPackageName = "com.ximalaya.ting.android"
    // 不要随便改动名称
    hostBundleVersion = dispatch_bundle.version

    buildNumber = "LocalBuild"

    abis = ABIS.split(",")

    openApm = false
    openAppToolBox = false
    cpPluginSoToSd = 'false'
    useZ7 = "false"
    resGuard = "false"
    includeRobust = "false"
    patchRobust = "false"


    closeDialog = false

    buildTimestamp = new Date().time


    aspectJExclude = [
            'com.ximalaya.ting.android.object.monitor',
            'com.ximalaya.xiaoya.sdk',
            'com.ximalaya.ting.android.xmloader',
            'com.alipay.apmobilesecuritysdk.common',
            'com.ximalaya.ting.android.xmevilmethodmonitor',
            'com.ximalaya.ting.android.xmuimonitorbase',
            'module-info',
            "com.ksad",   //快手
            "com.kwad",   //快手
            "com.kwai",   //快手
            "com.leto",
            "com.ledong",
            "com.mgc",
            "com.kymjs",
            "com.maplehaze",
            "com.ximalaya.ting.android.adsdk",
            "com.ccbsdk"
    ]
    // 私密key
    privacyStrKey = [
            'key_telecom_listen_card_app_secret': 'Xny86Rw4vBWkc9zZp24y6bZzSf7WAcLm',
            'key_telecom_auth_signature'        : 'iCuJqQeXomh9',
            'disabled_verify'                   : 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQUuWT5WD9c5k6JI7tOumQQtiq2TQ1V3N/EAg4Wb00QAthHes/wPZ1Ei+0J5aBDBuV6mf0RWmSbjedDyW/NLP9q3U+SF6N7Fab4UPd2HeyjF8pY8yuDYQyTYtN+KVZd65L7WDDKtrRP3cfPmrbyF68qEMUUZukq+a3M6NwHLFf4wIDAQAB',
            "cmcc_private_key"                  : "MIIBSwIBADCCASsGByqGSM44BAEwggEeAoGBAM0jcRKGfOOAN3NoF2Zhk065s2KrO/y7Zyk8FXa+i6gnMoFG/titJdTP+R5NoaSUP56SZOgoNp0tceYu1Gw+iv+kvoA4zXaA3XhUazKaP6TmxamDNlKig+wezVHwau3DyRpavXYr1kDBLllsRtlwpr7VN21tnoKAkn4rAkrqgqMDAhUA2WOvowZ3+uXOI42vBNl9gzVj16UCgYArpEc0EwlmZ/fVJ8+DKf28lmWisrhUdlwDhh8JwVXUOwQ0yAhboPnX1WtgK+zp8MUd9XjVyArZOS4yFHTG7M2ofsse+/rWfA7+fAukl3X5AIAUwFMbMe7EQXYKDhsO1Yi7aKtBJxmNRebUw/P25/xlbG2Gy51eQXGhyNukVhuk4wQXAhUAj7w7JvanaPsRNtqRUwhc6fIGv9U=",
            "cmcc_private_key_debug"            : "MIIBSwIBADCCASwGByqGSM44BAEwggEfAoGBAKmPJb6HHJkovn5vEOQxK/0ChWdMaKW7lJhrA8YGVudCTsxvHKCCLK5Rapx530Dlso/ctWL9p2xPMp5O0cAK+vKy7q62i6s7IWAzq747b5XAqrJ2eNL5hygCI92Vg9QhMNT0/MjnDQublnjvGnDdu3QowIP1YKWeAGYEAPO4y6j/AhUAz1bhJuKe+iYzfThrr6KVDPvKSjECgYEAkAsoGiJsZFEUp9rXXz3dtEOTeHTkRNXFL9pA21MPjhkVcebmozCoXYgAZAPbjTgB88dH+EYlGQVG9pkq8I1HCfJLrWTeSgOqlFKAmPdBfJM520om4da7E7mP+ec8zsZMzjQ//ZC+MNDfMJbsnuPJs3AQwHwP0Y95DivZQNK7cq8EFgIUbrc18FCk2+ETR1nmaIlWHqJJLSc=",
            "walle_recharge_key"                : "047369a778fc490e8cdbbf6a44111644",
            'read_xhtml_key'                    : 'xmly&epub&book&2020@#$%^&',
            'read_asr_track_key'                : 'xmly&asr&track&2022@#$%^&',
            'play_url_key'                      : '5776f21b9e9911388aacfe448068f16a',
            'unicom_phone_dec_key'              : '15658381932293211080438072168077',
            'unicom_phone_dec_offset'           : '12345678',
            'telecom_user_id_sercet_key'        : '92a51b6dff661b9c',
            'telecom_user_id_sercet_iv_key'     : '410960532bdad5dc',
            "vi_record_key"                     : 'XM2A18E522-5CF8-47E6-B2B4-0FD0ED70934F404LY',
            "doc_read_rsa_public_key"           : 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANMBnEoKPMiwJEHPPY+RYeYP+MkxEdFdM4PgeC4ijn6oYNIwYLv3/H9nFTxJkpuMj0jUORyu6ISE09+jcUVVhIECAwEAAQ==',
            "mc_user_tip_api_salt"              : '9fdba3d603411310678a6a9d341d6261'
    ]

    /*********/
    Properties localProperties = new Properties()
    localProperties.load(rootProject.file('local.properties').newDataInputStream())
    def version = localProperties.getProperty("APP_VERSION", null)
    def vCode = localProperties.getProperty("APP_VERSION_CODE", null)
    if (version != null) {
        versionName = version
    }
    if (vCode != null) {
        versionCode = vCode
    }
//    def openlkc = localProperties.getProperty("OPEN_LEAKCANARY", null)
//    if (openlkc != null) {
//        openLeakCanary = openlkc
//    }
    def bundleVersion = localProperties.getProperty("BUNDLE_VERSION", null)
    if (bundleVersion != null) {
        hostBundleVersion = bundleVersion
    }
    def localAbis = localProperties.getProperty("ABIS")
    if (localAbis != null) {
        abis = localAbis.split(",")
    }
    def cpSo = localProperties.get("CP_SO")
    if (cpSo != null) {
        cpPluginSoToSd = cpSo
    }
    def ro = localProperties.getProperty("REALOBFUSCATE")
    if (ro != null) {
        println("real hunxiao: " + ro)
        realObfuscate = ro.toBoolean()
    }
}

if (rootProject.isDebugGradle.toBoolean()) {
    println "=====================set ext property start====================="
}

if (project.hasProperty('pIsBuildCommonRes')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsBuildCommonRes")
    }
    rootProject.isBuildCommonRes = project.property('pIsBuildCommonRes')
}

if (project.hasProperty('pIsBuildAllProjectOnlyForProguardMapping')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsBuildAllProjectOnlyForProguardMapping")
    }
    rootProject.isBuildAllProjectOnlyForProguardMapping = project.property('pIsBuildAllProjectOnlyForProguardMapping')
}

if (project.hasProperty('pIsCopyApplicationMappingToBundleProject')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsCopyApplicationMappingToBundleProject")
    }
    rootProject.isCopyApplicationMappingToBundleProject = project.property('pIsCopyApplicationMappingToBundleProject')
}

if (project.hasProperty('pShouldApplyLastMapping')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pShouldApplyLastMapping")
    }
    rootProject.shouldApplyLastMapping = project.property('pShouldApplyLastMapping')
}

// 上面那个参数看起来没用了，不过为了跟以前的设置混了，还是再重新加一个参数
// 打包的时候是否使用指定的mapping文件
if (project.hasProperty("pUseMappings")) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pUseMappings")
    }
    rootProject.useMappings = project.property('pUseMappings')
}

if (project.hasProperty("pKeepResourceId")) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pKeepResourceId")
    }
    rootProject.keepResourceId = project.property('pKeepResourceId')
}

if (project.hasProperty('pConfigurationType')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pConfigurationType")
    }
    rootProject.configurationType = project.property('pConfigurationType')
}

if (project.hasProperty('pChannels')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pChannels")
    }
    rootProject.channel = project.property('pChannels')
}

if (project.hasProperty('pVersionCode')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pVersionCode")
    }
    rootProject.versionCode = project.property('pVersionCode')
}

if (project.hasProperty('pVersionName')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pVersionName")
    }
    rootProject.versionName = project.property('pVersionName')
}

if (project.hasProperty('pIsProguard')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsProguard")
    }
    rootProject.isProguard = project.property('pIsProguard')
}

if (project.hasProperty('pIsReleaseDebug')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsReleaseDebug")
    }
    rootProject.isReleaseDebug = project.property('pIsReleaseDebug')
}

if (project.hasProperty('pOpenBTrace')) {
    if (rootProject.pOpenBTrace.toBoolean()) {
        println("set pOpenBTrace")
    }
    rootProject.openBTrace = project.property('pOpenBTrace')
}

if (project.hasProperty('pNeedChannel')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pNeedChannel")
    }
    rootProject.needChannel = project.property('pNeedChannel')
}

if (project.hasProperty('pModifyApplicationName')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pModifyApplicationName")
    }
    rootProject.modifyApplicationName = project.property('pModifyApplicationName')
}

if (project.hasProperty('pApplicationName')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pApplicationName")
    }
    rootProject.applicationName = project.property('pApplicationName')
}

if (project.hasProperty("pOpenBlockCanary")) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pOpenBlockCanary")
    }
    rootProject.openBlockCanary = project.property('pOpenBlockCanary')
}

if (project.hasProperty('pCreateBaseJar')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pCreateBaseJar")
    }
    rootProject.createBaseJar = project.property('pCreateBaseJar')
}

if (project.hasProperty('pCreatePatchJar')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pCreatePatchJar")
    }
    rootProject.createPatchJar = project.property('pCreatePatchJar')
}

if (project.hasProperty('pShouldApplyPublicXml')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pShouldApplyPublicXml")
    }
    rootProject.shouldApplyPublicXml = project.property('pShouldApplyPublicXml')
}

if (rootProject.isDebugGradle.toBoolean()) {
    println "=====================set ext property end====================="
}

if (project.hasProperty('pIsOpenPointSdk')) {
    println(" point trace sdk----------------------------------")
    rootProject.isOpenPointSdk = project.property('pIsOpenPointSdk')
}

if (project.hasProperty("pServerType")) {
    println(" trace server type ---------------------------------- " + rootProject.serverType.toInteger())
    rootProject.serverType = project.property("pServerType")
}

if (project.hasProperty("pCreateHostPatch")) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pCreateHostPatch")
    }
    rootProject.createHostPatch = project.property("pCreateHostPatch")
}

if (project.hasProperty('pIsBundleDevMode')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsBundleDevMode")
    }
    rootProject.isBundleDevMode = project.property('pIsBundleDevMode')
}

if (project.hasProperty('pIsOpenAutoTrace')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsOpenAutoTrace")
    }
    rootProject.isOpenAutoTrace = project.property('pIsOpenAutoTrace')
}

if (project.hasProperty('pOpenLogSpirit')) {
    rootProject.openLogSpirit = project.property('pOpenLogSpirit')
}

if (project.hasProperty('pHostPackageName')) {
    rootProject.hostPackageName = project.property('pHostPackageName')
    println("set pHostPackageName")
}

if (project.hasProperty('pBuildNumber')) {

    rootProject.buildNumber = project.property('pBuildNumber')
    println("set pBuildNumber " + rootProject.buildNumber)
}

if (project.hasProperty('pOpenApm')) {

    rootProject.openApm = project.property('pOpenApm')
    println("set pOpenApm " + rootProject.openApm)
}

if (project.hasProperty('pOpenAppToolBox')) {

    rootProject.openAppToolBox = project.property('pOpenAppToolBox')
    println("set pOpenAppToolBox " + rootProject.openAppToolBox)
}

if (project.hasProperty('pOpenLintCheck')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pOpenLintCheck")
    }
    rootProject.openLintCheck = project.property('pOpenLintCheck')
}

if (project.hasProperty('pIsOpenBootMonitor')) {
    println(" boot monitor----------------------------------")
    rootProject.isOpenBootMonitor = project.property('pIsOpenBootMonitor')
}

if (project.hasProperty("pIsCheckResourceConflict")) {
    rootProject.isCheckResourceConflict = project.property("pIsCheckResourceConflict")
}

if (project.hasProperty('pResProguardMapping')) {
    println("set resProguradMapping")
    rootProject.resProguardMapping = project.property('pResProguardMapping')
}

if (rootProject.hasProperty("pBuildDebugPackage")) {
    def bdp = rootProject.property("pBuildDebugPackage")
    if (bdp.toBoolean() && rootProject.hasProperty("pSysBuildNum")) {
        // debug 打包 使用本地版本号 + 打包number 作为插件版本号。
        def num = rootProject.property("pSysBuildNum").toString()
        num = Integer.valueOf(num) % 10000
        String[] ps = hostBundleVersion.split("\\.")
        if (ps.length != 2) {
            println("paramVersion: " + paramVersion)
            throw new RuntimeException("bundle 版本好格式不正确，必须是两段式")
        }
        def mn = ps[0]
        hostBundleVersion = mn + "." + num
        if (rootProject.hasProperty("HOST_BUNDLE_VERSION")) {
            rootProject.HOST_BUNDLE_VERSION = hostBundleVersion
            println "new bundle Version = ${hostBundleVersion}  ${HOST_BUNDLE_VERSION}"
        }
        println "new bundle Version = ${hostBundleVersion}"
    } else {
        if (rootProject.hasProperty("HOST_BUNDLE_VERSION")) {
            println("set bundle verison")
            String v = rootProject.property("HOST_BUNDLE_VERSION").toString()
            checkBundleVersion(v, dispatch_bundle.version)
            rootProject.hostBundleVersion = v
        }
    }
} else {
    if (rootProject.hasProperty("HOST_BUNDLE_VERSION")) {
        println("set bundle verison")
        String v = rootProject.property("HOST_BUNDLE_VERSION").toString()
        checkBundleVersion(v, dispatch_bundle.version)
        rootProject.hostBundleVersion = v
    }
}

//if (rootProject.hasProperty("HOST_BUNDLE_VERSION")) {
//    println("set bundle verison")
//    String v = rootProject.property("HOST_BUNDLE_VERSION").toString()
//    checkBundleVersion(v, dispatch_bundle.version)
//    rootProject.hostBundleVersion = v
//}

if (rootProject.hasProperty("pCpPluginSoToSd")) {
    rootProject.cpPluginSoToSd = rootProject.property("pCpPluginSoToSd").toString()
}

if (project.hasProperty("pUseZ7")) {
    rootProject.useZ7 = project.property("pUseZ7").toString()
}


if (project.hasProperty("pResGuard")) {
    rootProject.resGuard = project.property("pResGuard").toString()
}

//if (rootProject.hasProperty("HOST_BUNDLE_VERSION")) {
//    println("set bundle verison")
//    String v = rootProject.property("HOST_BUNDLE_VERSION").toString()
//    checkBundleVersion(v, dispatch_bundle.version)
//    rootProject.hostBundleVersion = v
//}

if (project.hasProperty("pCloseDialog")) {
    rootProject.closeDialog = project.property("pCloseDialog").toBoolean()
}
if (project.hasProperty("pCpuType")) {
    abis = project.property("pCpuType").toString().split(",")
}

if (project.hasProperty("pProguardType")) {
    def t = project.property("pProguardType").toString()
    if (t == "1") {
        realObfuscate = false
        isProguard = "false"
    } else if (t == "2"){
        realObfuscate = false
        isProguard = "true"
    } else {
        realObfuscate = true
        isProguard = "true"
    }
}

if (project.hasProperty("pAppAbis")) {
    def a = project.property("pAppAbis").toString()
    abis = a.split(",")
}

if (project.hasProperty('pIsBuildLeakCanaryInDebug')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIsBuildLeakCanaryInDebug")
    }
    rootProject.isBuildLeakCanaryInDebug = project.property('pIsBuildLeakCanaryInDebug')
}

if (project.hasProperty('pIncludeRobust')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pIncludeRobust")
    }
    rootProject.includeRobust = project.property('pIncludeRobust')
}

if (project.hasProperty('pPatchRobust')) {
    if (rootProject.isDebugGradle.toBoolean()) {
        println("set pPatchRobust")
    }
    rootProject.patchRobust = project.property('pPatchRobust')
}

if (project.hasProperty("pMinSdkVersion")) {
    rootProject.minSdkVersion = project.property("pMinSdkVersion").toString()
} else {
    def buildFast
    if (rootProject.hasProperty("pBuildFast")) {
        buildFast = rootProject.property("pBuildFast").toString()
    } else {
        Properties properties = new Properties()
        properties.load(project.rootProject.file('local.properties').newDataInputStream())
        buildFast = properties.getProperty('buildFast', 'false')
    }

    if (buildFast.toBoolean()) {
        rootProject.minSdkVersion = "23"
    }
}

task printExtProperties doLast {
    println "============================"
    println "channel: " + channel
    println "versionCode: " + versionCode
    println "versionName: " + versionName
    println "minSdkVersion: " + minSdkVersion
    println "isProguard: " + isProguard
    println "isReleaseDebug: " + isReleaseDebug
    println "needChannel: " + needChannel
    println "modifyApplicationName: " + modifyApplicationName
    println "applicationName: " + applicationName
    println "openBlockCanary: " + openBlockCanary
    println "createHostPatch: " + createHostPatch
    println "isBundleDevMode: " + isBundleDevMode
    println "isOpenAutoTrace: " + isOpenAutoTrace
    println "isBuildLeakCanaryInDebug: " + isBuildLeakCanaryInDebug
    println "============================"
}