<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="49dp"
    android:paddingTop="@dimen/host_y5"
    android:paddingRight="@dimen/universal_custom_album_module_padding_horizontal"
    android:paddingLeft="@dimen/universal_custom_album_module_padding_horizontal"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/universal_color_transparent"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/universal_color_transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@drawable/universal_bg_gradient_lfffff2ea_rffffe9dc">

        </View>

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/universal_id_logo"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="1dp"
        android:layout_marginRight="70dp"
        android:scaleType="fitXY"
        android:src="@drawable/universal_ic_vip_logo_2"
        app:layout_constraintDimensionRatio="61:42"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="@id/universal_id_sub_title"/>

    <TextView
        android:id="@+id/universal_id_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/universal_color_ff732f06"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        tools:text="VIP免费听此专辑"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/universal_id_sub_title"
        />

    <TextView
        android:id="@+id/universal_id_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:gravity="center"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="85dp"
        android:textSize="13sp"
        android:textStyle="bold"
        android:textColor="@color/universal_color_ffffffff"
        android:background="@drawable/universal_bg_rect_f09068_radius_16"
        android:layout_marginRight="14dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="108元买年卡" />


</androidx.constraintlayout.widget.ConstraintLayout>
