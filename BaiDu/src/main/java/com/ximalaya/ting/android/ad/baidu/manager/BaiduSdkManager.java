package com.ximalaya.ting.android.ad.baidu.manager;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.baidu.mobads.sdk.api.BaiduNativeManager;
import com.baidu.mobads.sdk.api.NativeResponse;
import com.baidu.mobads.sdk.api.RequestParameters;
import com.baidu.mobads.sdk.api.RewardVideoAd;
import com.baidu.mobads.sdk.api.ShakeViewContainer;
import com.baidu.mobads.sdk.api.XNativeView;
import com.ximalaya.ting.android.ad.baidu.model.BaiduNativeRewardVideoAd;
import com.ximalaya.ting.android.ad.baidu.model.BaiduNativeThirdAd;
import com.ximalaya.ting.android.ad.baidu.model.BaiduRewardTemplateVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.CsjRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.GdtRewardVideoAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.NoLoadAd;
import com.ximalaya.ting.android.adsdk.bridge.util.sdkinit.BaiduSDKInitHelper;
import com.ximalaya.ting.android.adsdk.external.IBaseLoadListener;
import com.ximalaya.ting.android.adsdk.external.IMediationType;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.manager.ad.AdLogger;
import com.ximalaya.ting.android.host.manager.ad.AdSDKManager;
import com.ximalaya.ting.android.host.manager.ad.BaseAdSDKManager;
import com.ximalaya.ting.android.host.manager.ad.CSJAdManager;
import com.ximalaya.ting.android.host.manager.ad.ILoadNativeAdHandler;
import com.ximalaya.ting.android.host.manager.ad.ThirdAdLoadParams;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBackExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;

public class BaiduSdkManager extends BaseAdSDKManager {

    public int getMediationType() {
        return IMediationType.BAIDU;
    }

    public boolean checkInit(IBaseLoadListener loadListener) {
        return checkInit(1500, loadListener);
    }

    public boolean checkInit(int timeOut, IBaseLoadListener loadListener) {
        BaiduSDKInitHelper.getInstance().checkSDKInit(ToolUtil.getCtx(), timeOut, AdSDKManager.getBaiduInitParams(), null);

        return BaiduSDKInitHelper.getInstance().checkInitSuccessAndNotifyLoadError(new IBaseLoadListener() {
            @Override
            public void onLoadError(int code, String message) {
                Logger.log("BaiduSdkManager : SDK 初始化失败 code=" + code + "  message=" + message);
                if (loadListener != null) {
                    loadListener.onLoadError(code, message);
                }
            }
        });
    }

    @Override
    public void preloadInit(Context context) {
        BaiduSDKInitHelper.getInstance().preloadSDKAsync(context, AdSDKManager.getBaiduInitParams());
    }

    // 加载信息流广告
    @Override
    public void loadNativeAd(Context context, Advertis advertis , ThirdAdLoadParams thirdAdLoadParams,
                             ILoadNativeAdHandler adHandler , String posId) {
        if (thirdAdLoadParams == null || TextUtils.isEmpty(thirdAdLoadParams.getPositionName())
                || advertis == null || adHandler == null) {
            if (adHandler != null) {
                adHandler.loadNextGDT();
            }
            return;
        }

        if (!checkInit(null)) {
            adHandler.loadNextGDT();
            return;
        }
        Logger.log("BaiduSdkManager : loadNativeBaidu 开始请求 " + advertis);
        BaiduNativeManager adManager = new BaiduNativeManager(BaseApplication.getTopActivity(), posId);
        // 构建请求参数
        final RequestParameters requestParameters = new RequestParameters.Builder().build();

        final BaiduNativeManager.FeedAdListener feedAdListener = new BaiduNativeManager.FeedAdListener() {
            @Override
            public void onNoAd(int code, String msg, NativeResponse nativeResponse) {
                Logger.log("BaiduSdkManager : loadNativeBaidu 无广告返回 code = " + code + " msg="+ msg);
                adHandler.loadNextGDT();
            }

            @Override
            public void onNativeLoad(List<NativeResponse> list) {
                Logger.log("BaiduSdkManager : loadNativeBaidu 请求成功");
                if (ToolUtil.isEmptyCollects(list)) {
                    adHandler.loadNextGDT();
                } else {
                    adHandler.loadNativeAds(new BaiduNativeThirdAd(advertis, list.get(0), posId));
                }
            }

            @Override
            public void onNativeFail(int errorCode, String message, NativeResponse nativeResponse) {
                Logger.log("BaiduSdkManager : loadNativeBaidu 请求失败 code = " + errorCode + " msg="+ message);
                adHandler.loadNextGDT();
            }

            @Override
            public void onVideoDownloadSuccess() {

            }

            @Override
            public void onVideoDownloadFailed() {

            }

            @Override
            public void onLpClosed() {
            }
        };
        // 【可选】【Bidding】设置广告的底价，单位：分
//        adManager.setBidFloor(100);
        // 发起信息流广告请求
        adManager.loadFeedAd(requestParameters, feedAdListener);
    }


    public void loadNativeAdForReward(Advertis advertis,
                                      RewardVideoAdManager.IMultiThirdRewardAdLoadCallback adCallBack, String posId) {
        if (advertis == null || adCallBack == null) {
            if (adCallBack != null) {
                adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "参数错误");
            }
            return;
        }

        if (!checkInit(null)) {
            adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "sdk初始化失败");
            return;
        }
        Logger.log("BaiduSdkManager : loadNativeForReward 开始请求 " + advertis);
        BaiduNativeManager adManager = new BaiduNativeManager(BaseApplication.getTopActivity(), posId);
        // 构建请求参数
        final RequestParameters requestParameters = new RequestParameters.Builder().build();

        final BaiduNativeManager.FeedAdListener feedAdListener = new BaiduNativeManager.FeedAdListener() {
            @Override
            public void onNoAd(int code, String msg, NativeResponse nativeResponse) {
                Logger.log("BaiduSdkManager : loadNativeForReward 无广告返回 code = " + code + " msg=" + msg);
                adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), code, msg);
            }

            @Override
            public void onNativeLoad(List<NativeResponse> list) {
                Logger.log("BaiduSdkManager : loadNativeForReward 请求成功");
                if (ToolUtil.isEmptyCollects(list)) {
                    adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "没有广告返回");
                } else {
                    adCallBack.loadThirdNativeAdFinish(new BaiduNativeRewardVideoAd(advertis, list.get(0), posId), 0, "");
                }
            }

            @Override
            public void onNativeFail(int errorCode, String message, NativeResponse nativeResponse) {
                Logger.log("BaiduSdkManager : loadNativeForReward 请求失败 code = " + errorCode + " msg=" + message);
                adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, message);
            }

            @Override
            public void onVideoDownloadSuccess() {

            }

            @Override
            public void onVideoDownloadFailed() {

            }

            @Override
            public void onLpClosed() {
            }
        };
        // 【可选】【Bidding】设置广告的底价，单位：分
//        adManager.setBidFloor(100);
        // 发起信息流广告请求
        adManager.loadFeedAd(requestParameters, feedAdListener);
    }

    private RewardVideoAd mRewardVideoAd = null;
    private boolean isAdLoadOverTime;
    private boolean hasShowVideo;
    private CountDownTimer countDownTimer;
    @Override
    public void loadRewardVideoTempAd(@NonNull Activity activity, String posId, Advertis advertis,
                                  @NonNull RewardExtraParams extraParams, IVideoAdStatueCallBack callback) {
        if (advertis == null || callback == null) {
            return;
        }
        if (!checkInit(new IBaseLoadListener() {
            @Override
            public void onLoadError(int i, String s) {
                if (callback != null) {
                    callback.onAdLoadError(i, s);
                }
            }
        })) {
            return;
        }
        if (!checkInit(null)) {
            return;
        }

        Logger.log("BaiduSdkManager : loadNativeForReward 开始请求 " + advertis);

        isAdLoadOverTime = false;
        countDownTimer = null;
        hasShowVideo = false;
        countDownTimer = new CountDownTimer(RewardExtraParams.getMaxLoadTime(extraParams.getRewardCountDownStyle()), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未加载完成，认为此次广告加载失败
                isAdLoadOverTime = true;
                AdLogger.log("BaiduSdkManager loadRewardVideoTempAd : adLoadOverTime ");
                if (callback != null) {
                    callback.onAdLoadError(
                            IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME,
                            "广告加载超时");
                }
            }
        };
        countDownTimer.start();

        mRewardVideoAd = new RewardVideoAd(activity, posId,  new RewardVideoAd.RewardVideoAdListener(){

            @Override
            public void onAdShow() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdShow");
                //激励视频广告曝光
                if(callback != null) {
                    callback.onAdPlayStart();
                }
            }

            @Override
            public void onAdClick() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdClick");
                if (callback != null) {
                    callback.onAdVideoClick(false, 0);
                }
            }

            @Override
            public void onAdClose(float v) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdClose");
                //激励视频广告被关闭
                if (callback != null) {
                    callback.onAdClose(false);
                }
            }

            @Override
            public void onAdFailed(String message) {
                Logger.log("BaiduSdkManager : onAdFailed 无广告返回   msg = " + message);
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                if (callback != null) {
                    callback.onAdLoadError(-1, message);
                }
            }

            @Override
            public void onVideoDownloadSuccess() {
                if (hasShowVideo) {
                    return;
                } else {
                    hasShowVideo = true;
                }
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                if(!ToolUtil.activityIsValid(activity)) {
                    if (callback != null) {
                        callback.onAdLoadError(
                                IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                "Activity 已不能展示广告");
                    }
                    return;
                }
                Logger.log("BaiduSdkManager : loadRewardVideoAd onVideoDownloadSuccess,isReady="+(mRewardVideoAd != null && mRewardVideoAd.isReady()));
                if (mRewardVideoAd != null && mRewardVideoAd.isReady()) {
                    if (callback != null) {
                        callback.onAdLoad(new BaiduRewardTemplateVideoAd(advertis, mRewardVideoAd, posId));
                    }
                    mRewardVideoAd.show();
                }
            }

            @Override
            public void onVideoDownloadFailed() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onVideoDownloadFailed");
            }

            @Override
            public void playCompletion() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd playCompletion");
                //激励视频播放完毕
                if (callback != null) {
                    callback.onAdPlayComplete();
                }
            }

            @Override
            public void onAdLoaded() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdLoaded");
                if (hasShowVideo) {
                    return;
                } else {
                    hasShowVideo = true;
                }
                if (countDownTimer != null) {
                    if (isAdLoadOverTime) {
                        return;
                    } else {
                        countDownTimer.cancel();
                    }
                }
                if(!ToolUtil.activityIsValid(activity)) {
                    if (callback != null) {
                        callback.onAdLoadError(
                                IVideoAdStatueCallBack.ERROR_CODE_ACTIVITY_IS_NO_VALID,
                                "Activity 已不能展示广告");
                    }
                    return;
                }
                Logger.log("BaiduSdkManager : loadRewardVideoAd onVideoDownloadSuccess,isReady="+(mRewardVideoAd != null && mRewardVideoAd.isReady()));
                if (mRewardVideoAd != null && mRewardVideoAd.isReady()) {
                    if (callback != null) {
                        callback.onAdLoad(new BaiduRewardTemplateVideoAd(advertis, mRewardVideoAd, posId));
                    }
                    mRewardVideoAd.show();
                }
            }

            @Override
            public void onAdSkip(float v) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdSkip");
            }

            @Override
            public void onRewardVerify(boolean b) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onRewardVerify");
                if (callback != null && callback instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) callback).onRewardVerify();
                }
            }
        });

        // 构建请求参数
        final RequestParameters requestParameters = new RequestParameters.Builder().build();
        // 传参设置
        mRewardVideoAd.setRequestParameters(requestParameters);
        //设置低价，单位：分
        //mRewardVideoAd.setBidFloor(100);
        mRewardVideoAd.load();
    }


    /**
     * 并行请求框架(带竞价)，只请求sdk，先不渲染
     */
    @Override
    public void loadRewardVideoTempAdNew(Advertis advertis, String posId, boolean isTemp,
                                         IVideoAdStatueCallBack callback,
                                         RewardVideoAdManager.IMultiThirdRewardAdLoadCallback adCallBack) {
        if (advertis == null || adCallBack == null) {
            if (adCallBack != null) {
                adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "参数错误");
            }
            return;
        }
        if (!checkInit(null)) {
            adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), IVideoAdStatueCallBack.ERROR_CODE_NO_AD, "sdk初始化失败");
            return;
        }

        Logger.log("BaiduSdkManager : loadNativeForReward 开始请求 " + advertis);

        mRewardVideoAd = new RewardVideoAd(BaseApplication.getMyApplicationContext(), posId, new RewardVideoAd.RewardVideoAdListener() {
            boolean hasCallBackFinish = false;

            @Override
            public void onAdShow() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdShow");
                //激励视频广告曝光
                if (callback != null) {
                    callback.onAdPlayStart();
                }
            }

            @Override
            public void onAdClick() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdClick");
                if (callback != null) {
                    callback.onAdVideoClick(false, 0);
                }
            }

            @Override
            public void onAdClose(float v) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdClose");
                //激励视频广告被关闭
                if (callback != null) {
                    callback.onAdClose(false);
                }
            }

            @Override
            public void onAdFailed(String message) {
                Logger.log("BaiduSdkManager : onAdFailed ");
                if (hasCallBackFinish) {
                    return;
                }
                adCallBack.loadThirdNativeAdFinish(new NoLoadAd(advertis, advertis, posId), -101, message);
                hasCallBackFinish = true;
            }

            @Override
            public void onVideoDownloadSuccess() {
                Logger.log("BaiduSdkManager : onVideoDownloadSuccess  ");
                if (hasCallBackFinish) {
                    return;
                }
                adCallBack.loadThirdNativeAdFinish(new BaiduRewardTemplateVideoAd(advertis, mRewardVideoAd, posId), 0, "");
                hasCallBackFinish = true;
            }

            @Override
            public void onVideoDownloadFailed() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onVideoDownloadFailed");
            }

            @Override
            public void playCompletion() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd playCompletion");
                //激励视频播放完毕
                if (callback != null) {
                    callback.onAdPlayComplete();
                }
            }

            @Override
            public void onAdLoaded() {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdLoaded");
            }

            @Override
            public void onAdSkip(float v) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onAdSkip");
            }

            @Override
            public void onRewardVerify(boolean b) {
                Logger.log("BaiduSdkManager : loadRewardVideoAd onRewardVerify");
                if (callback != null && callback instanceof IVideoAdStatueCallBackExt) {
                    ((IVideoAdStatueCallBackExt) callback).onRewardVerify();
                }
            }
        });

        // 构建请求参数
        final RequestParameters requestParameters = new RequestParameters.Builder().build();
        // 传参设置
        mRewardVideoAd.setRequestParameters(requestParameters);
        //设置低价，单位：分
        //mRewardVideoAd.setBidFloor(100);
        mRewardVideoAd.load();
    }
    /**
     * 添加视频播放控件
     * @param relativeLayout
     * @param videoCover
     * @return
     */
    @SuppressLint("ResourceType")
    @Nullable
    public static XNativeView addMediaViewToView(RelativeLayout relativeLayout, ImageView videoCover) {
        if(relativeLayout == null || videoCover == null) {
            return null;
        }

        if(videoCover.getId() <= 0 || relativeLayout.indexOfChild(videoCover) < 0) {
            ToolUtil.throwIllegalNoLogicException();
        }

        relativeLayout.setVisibility(View.VISIBLE);

        FrameLayout baiduMediaLay = relativeLayout.findViewById(com.ximalaya.ting.android.host.R.id.host_baidu_ad_media_view);
        if(baiduMediaLay != null) {
            baiduMediaLay.removeAllViews();
            baiduMediaLay.setVisibility(View.VISIBLE);
        } else {
            baiduMediaLay = new FrameLayout(relativeLayout.getContext());
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.addRule(RelativeLayout.ALIGN_LEFT, videoCover.getId());
            params.addRule(RelativeLayout.ALIGN_RIGHT, videoCover.getId());
            params.addRule(RelativeLayout.ALIGN_TOP, videoCover.getId());
            params.addRule(RelativeLayout.ALIGN_BOTTOM, videoCover.getId());
            baiduMediaLay.setLayoutParams(params);
            baiduMediaLay.setId(com.ximalaya.ting.android.host.R.id.host_baidu_ad_media_view);
            relativeLayout.addView(baiduMediaLay, relativeLayout.indexOfChild(videoCover));
        }

        XNativeView mediaView = new XNativeView(relativeLayout.getContext());
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        mediaView.setLayoutParams(params);
        mediaView.setShowProgress(false);
        mediaView.setUseDownloadFrame(true);
        baiduMediaLay.addView(mediaView);
        return mediaView;
    }


    @Override
    public void renderNativeShakeView(ViewGroup parentLayout, ViewGroup shakeView, IAbstractAd ad) {
        if (shakeView == null || ad == null) {
            Log.d("baiduShakeViewHelper", "baiduShakeViewHelper null");
            return;
        }
        if (ad instanceof BaiduNativeThirdAd) {
            BaiduNativeThirdAd baiduNativeThirdAd = (BaiduNativeThirdAd) ad;
            baiduNativeThirdAd.renderNativeShakeView(parentLayout, shakeView, ad);
        }
    }

}
