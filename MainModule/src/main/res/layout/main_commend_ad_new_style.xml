<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_comment_top_container_new"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="22dp"
    android:clipChildren="false">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        xmlns:makeramen="http://schemas.android.com/apk/res-auto"
        android:id="@+id/main_comment_image_new"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="12dp"
        android:scaleType="fitXY"
        android:src="@drawable/host_default_avatar_88"
        makeramen:border_color="@color/main_color_e8e8e8_2a2a2a"
        makeramen:border_width="0.5px"
        makeramen:corner_radius="72dp"
        makeramen:pressdown_shade="true" />

    <TextView
        android:id="@+id/main_comment_name_new"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/main_comment_image_new"
        android:layout_marginRight="8dp"
        android:layout_toLeftOf="@id/main_ad_close_container_new"
        android:layout_toRightOf="@+id/main_comment_image_new"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/host_color_cc8d8d91"
        android:textSize="12sp"
        tools:text="西瓜啊啊啊" />

    <TextView
        android:id="@+id/main_commend_ad_title_new"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_comment_name_new"
        android:layout_alignLeft="@+id/main_comment_name_new"
        android:layout_marginTop="6dp"
        android:layout_marginRight="16dp"
        android:ellipsize="end"
        android:maxLines="4"
        android:includeFontPadding="false"
        android:text="广告内容主标题测试啊啊啊啊啊啊啊啊啊测试啊啊啊啊啊啊啊啊啊啊测试啊啊啊啊啊啊啊啊啊啊测试啊啊啊啊啊啊啊啊啊啊测试啊啊啊啊啊啊啊啊啊啊"
        android:textColor="@color/main_color_131313_dcdcdc"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/main_ad_close_container_new"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_alignTop="@+id/main_comment_name_new"
        android:layout_alignParentRight="true"
        android:layout_marginRight="16dp"
        android:gravity="center"
        tools:background="@color/host_red">

        <ImageView
            android:id="@+id/main_ad_tag_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            tools:src="@drawable/host_ad_tag_style_11" />

        <ImageView
            android:id="@+id/main_ad_close_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginLeft="1dp"
            android:layout_marginRight="2dp"
            android:gravity="center_vertical"
            android:src="@drawable/main_commend_ad_close_icon_new" />
    </LinearLayout>
    <!--广告关闭的真实点击区域-->
    <View
        android:id="@+id/main_ad_close_real_new"
        android:layout_width="60dp"
        android:layout_height="42dp"
        android:layout_alignParentRight="true"
        android:contentDescription="关闭广告" />

    <com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
        android:id="@+id/main_layout_cover_new"
        android:layout_width="173dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_commend_ad_title_new"
        android:layout_alignLeft="@id/main_commend_ad_title_new"
        android:layout_marginTop="6dp"
        app:corner="all"
        app:corner_radius="4dp"
        app:layout_ratio="1.778"
        tools:background="@color/host_blue_b34990e2"
        tools:layout_height="100dp">

        <RelativeLayout
            android:id="@+id/main_cover_container_horizontal_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_cover_horizontal_new"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="广告封面"
                android:scaleType="fitXY"
                app:corner_radius="4dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_alignBottom="@+id/main_cover_horizontal_new"
                android:background="@drawable/main_comment_ad_bottom_bg" />

            <LinearLayout
                android:id="@+id/main_ad_download_info_horizontal_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/main_cover_horizontal_new"
                android:layout_marginLeft="8dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/main_ad_download_info_policy_horizontal_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="隐私"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />

                <TextView
                    android:id="@+id/main_ad_download_info_permission_horizontal_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:includeFontPadding="false"
                    android:text="权限"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />

                <TextView
                    android:id="@+id/main_ad_download_info_introduction_horizontal_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:includeFontPadding="false"
                    android:text="介绍"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/main_cover_icon_horizontal_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/main_cover_horizontal_new"
                android:layout_alignParentRight="true"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="8dp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/main_cover_container_vertical_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_cover_vertical_new"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="广告封面"
                android:scaleType="fitXY"
                app:corner_radius="4dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_alignBottom="@+id/main_cover_vertical_new"
                android:background="@drawable/main_comment_ad_bottom_bg" />

            <RelativeLayout
                android:id="@+id/main_ad_download_info_vertical_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/main_cover_vertical_new"
                android:layout_marginLeft="8dp"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/main_ad_download_info_policy_vertical_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="隐私"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />

                <TextView
                    android:id="@+id/main_ad_download_info_permission_vertical_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_ad_download_info_policy_vertical_new"
                    android:layout_marginTop="6dp"
                    android:includeFontPadding="false"
                    android:text="权限"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />

                <TextView
                    android:id="@+id/main_ad_download_info_introduction_vertical_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/main_ad_download_info_policy_vertical_new"
                    android:layout_marginLeft="8dp"
                    android:layout_marginTop="6dp"
                    android:layout_toRightOf="@id/main_ad_download_info_permission_vertical_new"
                    android:includeFontPadding="false"
                    android:text="介绍"
                    android:textColor="#ffffff"
                    android:textSize="9sp" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/main_cover_icon_vertical_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/main_cover_vertical_new"
                android:layout_alignParentRight="true"
                android:layout_marginRight="8dp"
                android:layout_marginBottom="8dp"
                tools:src="@drawable/main_ic_download_n_n_line_regular_24" />
        </RelativeLayout>


    </com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_layout_cover_new"
        android:layout_alignLeft="@id/main_layout_cover_new"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="16dp">

        <TextView
            android:id="@+id/main_create_time_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="刚刚"
            tools:visibility="invisible"
            android:textColor="@color/host_color_cc8d8d91"
            android:textSize="12sp" />

        <FrameLayout
            android:id="@+id/main_layout_like_hate_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true">

            <LinearLayout
                android:id="@+id/main_layout_like_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/main_iv_like_new"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/host_album_rate_like_selector_new"
                    android:visibility="visible" />

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_lottie_like_new"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:scaleType="centerInside"
                    android:visibility="gone"
                    app:lottie_autoPlay="false"
                    app:lottie_fileName="lottie/host_lottie_for_like_action.json" />

                <TextView
                    android:id="@+id/main_tv_like_count_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:paddingTop="1dp"
                    android:textColor="@color/main_8d8d91_selector"
                    android:textSize="12sp"
                    tools:text="99" />

            </LinearLayout>

            <ImageView
                android:id="@+id/main_iv_hate_new"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="58dp"
                android:contentDescription="踩"
                android:paddingLeft="2dp"
                android:src="@drawable/host_ic_standard_thumb_down" />

        </FrameLayout>

        <!-- 开发者信息和版本号组件 -->
        <LinearLayout
            android:id="@+id/main_developer_info_container_new"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_alignParentLeft="true"
            android:layout_toLeftOf="@id/main_layout_like_hate_new">

            <TextView
                android:id="@+id/main_developer_info_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxEms="9"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/host_color_cc8d8d91"
                android:textSize="12sp"
                tools:text="开发者信息啊啊啊啊啊啊啊啊啊啊啊啊" />

            <View
                android:layout_width="2dp"
                android:layout_height="2dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:background="@drawable/main_8d8d91_radius_2" />

            <TextView
                android:id="@+id/main_app_version_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/host_color_cc8d8d91"
                android:textSize="12sp"
                tools:text="V6666666" />
        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>