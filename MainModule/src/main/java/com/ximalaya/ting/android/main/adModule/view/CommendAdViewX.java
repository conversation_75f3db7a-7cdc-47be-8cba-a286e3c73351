package com.ximalaya.ting.android.main.adModule.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.HapticFeedbackConstants;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.qq.e.ads.nativ.widget.NativeAdContainer;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.ad.model.thirdad.VideoParamModel;
import com.ximalaya.ting.android.adsdk.InnerHelper;
import com.ximalaya.ting.android.adsdk.XmAdSDK;
import com.ximalaya.ting.android.adsdk.base.log.AdLogger;
import com.ximalaya.ting.android.adsdk.base.util.AdUtil;
import com.ximalaya.ting.android.adsdk.bridge.inner.download.IDownloadTaskListener;
import com.ximalaya.ting.android.adsdk.bridge.inner.download.ITaskImpl;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.AdSDKAdapterModel;
import com.ximalaya.ting.android.adsdk.bridge.viewcheck.ViewStateCheckUtil;
import com.ximalaya.ting.android.adsdk.external.bean.XmDownloadInfo;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.service.DownloadAdvertisParams;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.framework.view.image.RoundImageView;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.data.model.ad.thirdad.IHaveVideoThirdAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.AdConversionUtil;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.AdViewUtil;
import com.ximalaya.ting.android.host.manager.ad.FeedAdWrapper;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadAdRecordManager;
import com.ximalaya.ting.android.host.manager.downloadapk.DownloadServiceManage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.ad.AdCollectData;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.util.PlayPageStyleUtil;
import com.ximalaya.ting.android.host.util.common.PackageUtil;
import com.ximalaya.ting.android.host.util.constant.AdUrlConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.host.view.ad.AdActionBtnView;
import com.ximalaya.ting.android.host.view.ad.AdCommonShakeView;
import com.ximalaya.ting.android.host.view.ad.AdDownloadPermissionAndPrivacyDialog;
import com.ximalaya.ting.android.host.view.ad.AdSourceFromView;
import com.ximalaya.ting.android.host.view.ad.IClickIntercept;
import com.ximalaya.ting.android.host.view.image.RatioImageView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.manager.AdCloseManager;
import com.ximalaya.ting.android.main.adapter.play.IFragmentLifecircle;
import com.ximalaya.ting.android.main.fragment.find.child.FeedAdHelper;
import com.ximalaya.ting.android.main.manager.comment.CommentOperationUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.CreativeAdManager;
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException;
import com.ximalaya.ting.android.opensdk.util.NewPlayPageUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新版播放页评论广告
 */
public class CommendAdViewX extends LinearLayout implements IFragmentLifecircle,
        IXmPlayerStatusListener {

    private NativeAdContainer mAdContainer;
    @Nullable
    private LinearLayout mTrackCommentLay;
    private View mAdNewStyleLayout;
    @Nullable
    private RoundImageView mCommentImage;
    @Nullable
    private TextView mCommentName;
    @Nullable
    private TextView mCreateTime;

    @Nullable
    private RatioImageView mLayoutCover;
    @Nullable
    private AdActionBtnView mCommendAdAboutBtn;
    @Nullable
    private TextView mCommendAdTitle;
    @Nullable
    private TextView mVolumnBtn;
    @Nullable
    private ImageView mAdTag;
    private ImageView mThirdAdTag;
    private View mAdClose;
    private ImageView mAdCloseIcon;
    @Nullable
    private RelativeLayout mVideoFrameLayout;
    @NonNull
    private View mDivider;

    private IAbstractAd mAbstractAd;

    private Advertis mAdvertis;

    private int mCurrentProgress = 0;

    private TextView mCommendAdTitleNew;

    @Nullable
    private VideoParamModel videoParamModel;

    private XmLottieAnimationView moneyRainView;
    private XmLottieAnimationView popupView;
    private RelativeLayout downloadInfoFloatView;
    private TextView mAppName;
    private TextView mAppVersion;
    private TextView mAppSize;
    private TextView mAppDeveloper;
    private TextView mAppPermission;
    private TextView mAppPolicy;
    private TextView mAppIntroduction;

    private View mTopContainer;
    private AdCommonShakeView commonShakeView;

    private AdSourceFromView mAdSourceFromView;

    private View mAdCloseContainer;

    private int adSequence;
    private int adRank;

    private boolean isDetached;

    private boolean currentAdIsThirdAd; // 当前广告，是否是三方广告

    private boolean videoShowSuccess;

    private boolean playMute;   // 播放是否静音

    private boolean mHasAddScrollListener;

    private CommendAdCloseLister closeListener;

    @Nullable
    private IScrollableView mScrollableView;

    private boolean isYPlayPage;
    private boolean isFloatTrack;

    @Nullable
    private LinearLayout mTitleLinkContainer;

    private int lastCutIndex; // 标题超长时的截断位置

    public CommendAdViewX(Context context,boolean isFloatTrack) {
        super(context);
        this.isFloatTrack = isFloatTrack;
        init(context);
    }

    public CommendAdViewX(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CommendAdViewX(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    @SuppressLint("MissingInflatedId")
    private void init(Context context) {
        checkIsYPlayPage();
        final View view = LayoutInflater.from(getContext()).inflate(getContentLayoutId(),
                this, true);

        mAdContainer = view.findViewById(R.id.main_commend_ad_root_lay);
        mTopContainer = view.findViewById(R.id.main_comment_top_container);
        mAdNewStyleLayout = view.findViewById(R.id.main_commend_ad_new_style);
        mTrackCommentLay = (LinearLayout) view.findViewById(R.id.main_track_comment_lay);
        mCommentImage = (RoundImageView) view.findViewById(R.id.main_comment_image);
        mCommentName = (TextView) view.findViewById(R.id.main_comment_name);
        mCreateTime = (TextView) view.findViewById(R.id.main_create_time);
        mLayoutCover = (RatioImageView) view.findViewById(R.id.main_layout_cover);
        mCommendAdAboutBtn = (AdActionBtnView) view.findViewById(R.id.main_commend_ad_about_btn);
        mCommendAdTitle = (TextView) view.findViewById(R.id.main_commend_ad_title);
        mVideoFrameLayout = view.findViewById(R.id.main_commend_ad_video_lay);
        mVolumnBtn = view.findViewById(R.id.main_video_volumn);
        mAdTag = view.findViewById(R.id.main_ad_tag);
        mThirdAdTag = view.findViewById(R.id.main_third_ad_tag);

        mAdClose = view.findViewById(R.id.main_ad_close_real);
        mAdCloseIcon = view.findViewById(R.id.main_ad_close);
        moneyRainView = view.findViewById(R.id.main_play_money_rain_view);
        popupView = view.findViewById(R.id.main_play_pop_up_view);
        downloadInfoFloatView = view.findViewById(R.id.main_ad_download_float_view);
        mDivider = view.findViewById(R.id.main_divider);
        commonShakeView = view.findViewById(R.id.main_common_shake_view);

        mAppName = findViewById(R.id.main_ad_download_info_name);
        mAppVersion = findViewById(R.id.main_ad_download_info_version);
        mAppDeveloper = findViewById(R.id.main_ad_download_info_developer);
        mAppPermission = findViewById(R.id.main_ad_download_info_permission);
        mAppPolicy = findViewById(R.id.main_ad_download_info_policy);
        mAppIntroduction = findViewById(R.id.main_ad_download_info_introduction);
        mAdCloseContainer = findViewById(R.id.main_ad_close_container);
        mAdSourceFromView = findViewById(R.id.main_source_from);

        if (mVolumnBtn != null) {
            mVolumnBtn.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if(!OneClickHelper.getInstance().onClick(v)) {
                        return;
                    }

                    if(mAbstractAd == null) {
                        return;
                    }

                    mVolumnBtn.setSelected(!mVolumnBtn.isSelected());
                    onVideoVolumnChange(videoParamModel);
                }
            });
        }
    }

    private void checkIsYPlayPage() {
        isYPlayPage = NewPlayPageUtil.getNewPageParams().equals("2");
    }

    private boolean isYCommentAdUI() {
        return isYPlayPage && mIsNewCommentParent && !parentIsNoTab();
    }

    @LayoutRes
    protected int getContentLayoutId() {
        //必须要是y版本播放页面的评论列表  并且  不是悬浮评论列表才使用 main_commend_ad_view_y
        if (isYPlayPage && !isFloatTrack) {
            return R.layout.main_commend_ad_view_y;
        } else {
            return R.layout.main_commend_ad_view_x;
        }
    }

    protected  int getMargin() {
        return BaseUtil.dp2px(getContext(), 32f);
    }

    public void setDividerVisible(boolean visible) {
        mDivider.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setDividerColor(int color) {
        mDivider.setBackgroundColor(color);
    }

    private void onVideoVolumnChange(VideoParamModel videoParamModel) {
        if (videoParamModel == null || videoParamModel.getSetPlayMute() == null) {
            return;
        }

        if (videoParamModel.getSetPlayMute() != null) {
            videoParamModel.getSetPlayMute().setPlayMute(!videoParamModel.isPlayMute(), false);
        }
        playMute = !playMute;

        if (!playMute) {
            onVideoHasVolumnStart();
        } else {
            onVideoHideVolumeOrPlayPause();
        }
        XmPlayerManager.getInstance(getContext()).setCommendAdPlayStatus(!playMute);
    }

    private void record(IAbstractAd abstractAd,int showStyle, int adSequence,int adRank) {
        if(abstractAd != null && abstractAd.getAdvertis() != null && !abstractAd.getAdvertis().isShowedToRecorded()) {
            abstractAd.getAdvertis().setShowedToRecorded(true);
            AdManager.adRecord(getContext(),abstractAd.getAdvertis(),
                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_SHOW ,
                                    AppConstants.AD_POSITION_NAME_PLAY_COMMENT).showType(showStyle)
                            .sequence(adSequence).rank(adRank).build());
        }

    }

    //广告容器是否是新的评论列表容器 ,这种情况下不需要展示音按钮，
    private boolean mIsNewCommentParent;
    public void setNewCommentParent(boolean isNewCommentParent){
        mIsNewCommentParent = isNewCommentParent;
    }

    public void setData(FeedAdWrapper adWrapper, int inListPosition, IScrollableView scrollableView,
                        int adSequence, int adRank, FeedAdHelper.IFeedAdShowedCallBack mShowedCallBack,
                        CommendAdCloseLister closeListener) {
        this.closeListener = closeListener;
        videoParamModel = null;
        popupView.setVisibility(GONE);
        popupView.cancelAnimation();
        moneyRainView.setVisibility(GONE);
        moneyRainView.cancelAnimation();

        if(FeedAdHelper.onAdSetDataToView(adWrapper, mShowedCallBack)) {
            if (mTrackCommentLay != null) {
                mTrackCommentLay.setVisibility(View.GONE);
            }
            if (mAdNewStyleLayout != null) {
                mAdNewStyleLayout.setVisibility(View.GONE);
            }
            return;
        }

        AdManager.removeGdtAdMask(mAdContainer);

        setOnClickListener(null);

        AbstractThirdAd abstractAd = adWrapper.getAbstractAd();

        if(abstractAd == null || mTrackCommentLay == null) {
            return;
        }

        if(!AdManager.isBaiduAd(abstractAd.getAdvertis()) && mAbstractAd == abstractAd && !isDetached) {
            return;
        }

        isDetached = false;
        mAbstractAd = abstractAd;
        mAdvertis = abstractAd.getAdvertis();
        this.adSequence = adSequence;
        this.adRank = adRank;
        currentAdIsThirdAd = AdManager.isThirdAd(abstractAd);
        lastCutIndex = 0;

        int showStyle = abstractAd.getAdvertis() != null ? abstractAd.getAdvertis().getShowstyle() : -1;
        if ((showStyle == Advertis.IMG_SHOW_TYPE_COMMEND_NATIVE_HORIZONTAL || showStyle == Advertis.IMG_SHOW_TYPE_COMMEND_NATIVE_VERTICAL)
                && mAdNewStyleLayout != null) {
            // 展示新广告样式
            mAdNewStyleLayout.setVisibility(View.VISIBLE);
            mTrackCommentLay.setVisibility(View.GONE);
            setDataForNewStyle(abstractAd);
            return;
        }
        // 展示老样式
        if (mAdNewStyleLayout != null) mAdNewStyleLayout.setVisibility(View.GONE);
        if (mAdClose != null) {
            AdCloseManager.bindAdClose(null, mAdClose, mAbstractAd, mAbstractAd.getAdvertis(), AppConstants.AD_POSITION_NAME_PLAY_COMMENT,
                    new AdCloseManager.IAdCloseCallBack() {
                        @Override
                        public void onAdClose(boolean isAdCloseByFeedBack) {
                            if (mAbstractAd != null && mAbstractAd.getAdvertis() != null
                                    && mAbstractAd.getAdvertis().getShowstyle() == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO
                                    && videoParamModel != null
                                    && videoParamModel.getAdVideoCloseHandler() != null) {
                                videoParamModel.getAdVideoCloseHandler().close();
                            }
                            popupView.cancelAnimation();
                            moneyRainView.cancelAnimation();
                            if (closeListener != null) {
                                AdLogger.i("closeCommendAd", "CommendAdViewX onAdClose   -----");
                                closeListener.onAdClose();
                            }
                        }
                    }, this::setReportFinishSeconds);
            AdCloseManager.bindAdCloseViewForABTest(mAdCloseIcon, AppConstants.AD_POSITION_NAME_PLAY_COMMENT);
        }
        setCommonData(abstractAd);
        setVideoData(abstractAd);
        setShakeData(abstractAd);
        HandlerManager.postOnUIThreadDelay(() -> {
            try {
                checkViewAndReport();
            } catch (Exception e) {
                Log.e("CommendAdViewX", "checkViewAndReport error==" + e.getMessage());
            }
        }, 300);
        if (mTrackCommentLay != null) {
            mTrackCommentLay.setVisibility(View.VISIBLE);
        }

        if (mVolumnBtn != null && mIsNewCommentParent) {
            mVolumnBtn.setVisibility(View.INVISIBLE);
        }
    }

    private void setDataForNewStyle(AbstractThirdAd abstractAd) {
        Advertis advertis =  abstractAd.getAdvertis();
        if (advertis == null) {
            return;
        }
        boolean isVertical = advertis.getShowstyle() == Advertis.IMG_SHOW_TYPE_COMMEND_NATIVE_VERTICAL;
        boolean isDownload = AdManager.isDownloadAd(advertis);

        RoundImageView commentImage = mAdNewStyleLayout.findViewById(R.id.main_comment_image_new);
        TextView commentName = mAdNewStyleLayout.findViewById(R.id.main_comment_name_new);
        TextView commendAdTitle = mAdNewStyleLayout.findViewById(R.id.main_commend_ad_title_new);
        mCommendAdTitleNew = commendAdTitle;

        View adCloseContainer = mAdNewStyleLayout.findViewById(R.id.main_ad_close_container_new);
        ImageView adTag = mAdNewStyleLayout.findViewById(R.id.main_ad_tag_new);
        ImageView adCloseIcon = mAdNewStyleLayout.findViewById(R.id.main_ad_close_new);
        View adClose = mAdNewStyleLayout.findViewById(R.id.main_ad_close_real_new);

        RatioCornerRelativeLayout coverContainer = mAdNewStyleLayout.findViewById(R.id.main_layout_cover_new);
        View appDeveloperContainer = mAdNewStyleLayout.findViewById(R.id.main_developer_info_container_new);
        TextView appDeveloper = mAdNewStyleLayout.findViewById(R.id.main_developer_info_new);
        TextView appVersion = mAdNewStyleLayout.findViewById(R.id.main_app_version_new);
        TextView createTime = mAdNewStyleLayout.findViewById(R.id.main_create_time_new);

        RelativeLayout adCoverContainerHorizontal = mAdNewStyleLayout.findViewById(R.id.main_cover_container_horizontal_new);
        ImageView adCoverHorizontal = mAdNewStyleLayout.findViewById(R.id.main_cover_horizontal_new);
        View appPolicyContainerHorizontal = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_horizontal_new);
        TextView appPolicyHorizontal = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_policy_horizontal_new);
        appPolicyHorizontal.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appPolicyHorizontal.getPaint().setAntiAlias(true);//抗锯齿
        TextView appPermissionHorizontal = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_permission_horizontal_new);
        appPermissionHorizontal.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appPermissionHorizontal.getPaint().setAntiAlias(true);//抗锯齿
        TextView appIntroductionHorizontal = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_introduction_horizontal_new);
        appIntroductionHorizontal.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appIntroductionHorizontal.getPaint().setAntiAlias(true);//抗锯齿
        ImageView coverIconHorizontal = mAdNewStyleLayout.findViewById(R.id.main_cover_icon_horizontal_new);

        RelativeLayout adCoverContainerVertical = mAdNewStyleLayout.findViewById(R.id.main_cover_container_vertical_new);
        ImageView adCoverVertical = mAdNewStyleLayout.findViewById(R.id.main_cover_vertical_new);
        View appPolicyContainerVertical = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_vertical_new);
        TextView appPolicyVertical = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_policy_vertical_new);
        appPolicyVertical.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appPolicyVertical.getPaint().setAntiAlias(true);//抗锯齿
        TextView appPermissionVertical = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_permission_vertical_new);
        appPermissionVertical.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appPermissionVertical.getPaint().setAntiAlias(true);//抗锯齿
        TextView appIntroductionVertical = mAdNewStyleLayout.findViewById(R.id.main_ad_download_info_introduction_vertical_new);
        appIntroductionVertical.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG); //下划线
        appIntroductionVertical.getPaint().setAntiAlias(true);//抗锯齿
        ImageView coverIconVertical = mAdNewStyleLayout.findViewById(R.id.main_cover_icon_vertical_new);

        if (isVertical) {
            coverContainer.getLayoutParams().width = BaseUtil.dp2px(getContext(), 96);
            coverContainer.setRatio(9.0f / 16f);
            adCoverContainerVertical.setVisibility(View.VISIBLE);
            adCoverContainerHorizontal.setVisibility(View.GONE);
        } else {
            coverContainer.getLayoutParams().width = BaseUtil.dp2px(getContext(), 173);
            coverContainer.setRatio(16.0f / 9f);
            adCoverContainerVertical.setVisibility(View.GONE);
            adCoverContainerHorizontal.setVisibility(View.VISIBLE);
        }

        AdCloseManager.bindAdClose(null, adClose, mAbstractAd, mAbstractAd.getAdvertis(), AppConstants.AD_POSITION_NAME_PLAY_COMMENT,
                new AdCloseManager.IAdCloseCallBack() {
                    @Override
                    public void onAdClose(boolean isAdCloseByFeedBack) {
                        // 添加高度变小和渐隐动画
                        playAdCloseAnimation(mAdNewStyleLayout);
                    }
                }, this::setReportFinishSeconds);
        AdCloseManager.bindAdCloseViewForABTest(adCloseIcon, AppConstants.AD_POSITION_NAME_PLAY_COMMENT);
        AdViewUtil.bindViewData(abstractAd, isVertical ? adCoverVertical : adCoverHorizontal, com.ximalaya.ting.android.host.R.drawable.host_image_default_f3f4f5,
                false, null, commendAdTitle, new ArrayList<View>() {
                    {
                        add(commendAdTitle);
                        add(coverContainer);
                        add(commentImage);
                        add(commentName);
                    }
                }, new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // 过滤下频繁点击
                        if (!OneClickHelper.getInstance().onClick(v)) {
                            return;
                        }
                        //如果是iting跳转则需要关闭掉播放评论页
                        if (abstractAd != null && abstractAd.getAdvertis() != null && abstractAd.getAdvertis().isItingSoundSwich()) {
                            IPlayFragmentService iPlayFragmentService = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
                            if (iPlayFragmentService != null) {
                                iPlayFragmentService.removeAllTopFragments();
                            }
                        }

                        if(abstractAd != null) {
                            AdManager.updateWebVideoModel(abstractAd.getAdvertis(), videoParamModel, playMute);
                        }

                        AdReportModel.Builder builder = AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        AppConstants.AD_POSITION_NAME_PLAY_COMMENT).
                                showType(Advertis.SHOW_TYPE_STATIC)
                                .rank(adRank).sequence(adSequence);
                        setReportFinishSeconds(builder);
                        if (AdManager.isAdSupportDowloadInfoFloat(abstractAd.getAdvertis())) {
                            builder.downloadDirect(true);
                        }
                        AdManager.handlerAdClick(getContext(), mAbstractAd.getAdvertis(), builder.build());
                    }
                }, null, adTag, com.ximalaya.ting.android.host.R.drawable.host_ad_tag_style_11, null, null, null);
        if (adTag != null) {
            int color = ContextCompat.getColor(getContext(), R.color.main_color_aaaaaa_8d8d91);
            adTag.setColorFilter(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN));
        }

        ImageManager.from(getContext()).displayImage(commentImage, abstractAd.getAdIcon(),
                com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);

        if (isDownload) {
            commentName.setText(advertis.getDownloadAppName());
        } else {
            commentName.setText(TextUtils.isEmpty(advertis.getProviderName()) ? advertis.getName() : advertis.getProviderName());
        }

        if (isDownload) {
            initDownLoadListenerForSdk(advertis);
            coverIconHorizontal.setImageResource(R.drawable.main_ic_download_n_n_line_regular_24);
            coverIconVertical.setImageResource(R.drawable.main_ic_download_n_n_line_regular_24);
            if (!AdManager.isAdSupportDowloadInfoFloat(advertis)) {
                createTime.setVisibility(VISIBLE);
                appDeveloperContainer.setVisibility(GONE);
                appPolicyContainerHorizontal.setVisibility(GONE);
                appPolicyContainerVertical.setVisibility(GONE);
            } else {
                if (android.text.TextUtils.isEmpty(advertis.getAppVersion())) {
                    appVersion.setText("版本: 未知");
                } else {
                    appVersion.setText("V" + advertis.getAppVersion());
                }
                if (android.text.TextUtils.isEmpty(advertis.getAppDeveloper())) {
                    appDeveloper.setText("开发者: 未知");
                } else {
                    appDeveloper.setText(advertis.getAppDeveloper());
                }
                appPolicyContainerHorizontal.setVisibility(VISIBLE);
                appPolicyContainerVertical.setVisibility(VISIBLE);
                TextView appPolicy = isVertical ? appPolicyVertical : appPolicyHorizontal;
                TextView appPermission = isVertical ? appPermissionVertical : appPermissionHorizontal;
                TextView appIntroduction = isVertical ? appIntroductionVertical : appIntroductionHorizontal;
                appPermission.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (advertis == null
                                || ((advertis.getAppPermissions() == null || advertis.getAppPermissions().isEmpty())
                                && (advertis.getBusinessExtraInfo() == null || android.text.TextUtils.isEmpty(advertis.getBusinessExtraInfo().getAppPermissionUrl())))) {
                            return;
                        }
                        AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PERMISSION);
                        dialog.show();
                        String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                        DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                        DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                                DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PERMISSION, params);
                    }
                });
                appPolicy.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (advertis == null || android.text.TextUtils.isEmpty(advertis.getAppPrivacyPolicy())) {
                            return;
                        }
                        AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PRIVACY);
                        dialog.show();
                        String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                        DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                        DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                                DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PRIVACY, params);
                    }
                });
                appIntroduction.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (advertis == null) {
                            return;
                        }
                        AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(
                                MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_INTRODUCTION);
                        dialog.show();
                    }
                });

                createTime.setVisibility(GONE);
                appDeveloperContainer.setVisibility(VISIBLE);
            }
        } else {
            createTime.setVisibility(VISIBLE);
            appDeveloperContainer.setVisibility(GONE);
            appPolicyContainerHorizontal.setVisibility(GONE);
            appPolicyContainerVertical.setVisibility(GONE);
            if (AdManager.isOperationAd(advertis) || AdManager.isRTBAd(advertis)) {
                coverIconHorizontal.setImageResource(0);
                coverIconVertical.setImageResource(0);
            } else {
                coverIconHorizontal.setImageResource(R.drawable.main_ic_link_n_n_line_regular_24);
                coverIconVertical.setImageResource(R.drawable.main_ic_link_n_n_line_regular_24);
            }
        }

        View likeLayout = mAdNewStyleLayout.findViewById(R.id.main_layout_like_new);
        TextView likeCount = mAdNewStyleLayout.findViewById(R.id.main_tv_like_count_new);
        ImageView likeIv = mAdNewStyleLayout.findViewById(R.id.main_iv_like_new);
        LottieAnimationView likeAnimation = mAdNewStyleLayout.findViewById(R.id.main_lottie_like_new);
        bindLike(advertis, likeLayout, likeIv, likeAnimation, likeCount);
        likeLayout.setOnClickListener(v -> {
            onLikeStateChange(advertis, likeLayout, likeIv, likeAnimation, likeCount);
        });

        ImageView hate = mAdNewStyleLayout.findViewById(R.id.main_iv_hate_new);
        hate.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_standard_thumb_down);
        hate.setColorFilter(ContextCompat.getColor(getContext(), com.ximalaya.ting.android.host.R.color.host_color_8f8f8f_8d8d91));
        AdCloseManager.bindAdClose(hate, null, mAbstractAd, mAbstractAd.getAdvertis(), AppConstants.AD_POSITION_NAME_PLAY_COMMENT,
                new AdCloseManager.IAdCloseCallBack() {
                    @Override
                    public void onAdClose(boolean isAdCloseByFeedBack) {
                        playAdCloseAnimation(mAdNewStyleLayout);
                    }
                }, this::setReportFinishSeconds);
        // 设置带链接的标题文本
        if (AdManager.isOperationAd(advertis) || AdManager.isRTBAd(advertis)) {
            setupTitleWithLinkText(commendAdTitle, advertis.getDescription() + " ", advertis.getClickTitle(),
                    PlayPageDataManager.getInstance().getCommentHighlightColor(), 0, false);
        } else {
            setupTitleWithLinkText(commendAdTitle, advertis.getDescription() + " ", advertis.getClickTitle(),
                    PlayPageDataManager.getInstance().getCommentHighlightColor(), isDownload ? 2 : 1, false);
        }

        HandlerManager.postOnUIThreadDelay(() -> {
            try {
                checkViewAndReport();
            } catch (Exception e) {
                Log.e("CommendAdViewX", "checkViewAndReport error==" + e.getMessage());
            }
        }, 300);

    }

    private void initDownLoadListenerForSdk(Advertis advertis) {
        if (advertis == null) {
            return;
        }
        String btnTxt = "";
        if (advertis != null && PackageUtil.isAppInstalled(getContext(), advertis.getAppPackageName())) {
            btnTxt = "立即打开";
        } else {
            if (advertis != null) {
                AdSDKAdapterModel adSDKAdapterModel = AdConversionUtil.conversionModel(advertis, advertis.getPositionName());
                ITaskImpl downloadTaskManager = InnerHelper.getInstance().getDownloadTaskManager();
                if (adSDKAdapterModel != null && downloadTaskManager != null) {
                    XmDownloadInfo downloadInfoByAdModel = downloadTaskManager.createDownloadInfoByAdModel(adSDKAdapterModel);
                    if (downloadInfoByAdModel != null) {
                        XmDownloadInfo downloadInfoByOnlyKey = downloadTaskManager.getDownloadInfoByOnlyKey(downloadInfoByAdModel.onlyKey());
                        if (downloadInfoByOnlyKey != null) {
                            int status = downloadInfoByOnlyKey.status;
                            if (status == XmDownloadInfo.Status.DOWNLOADING) {
                                btnTxt = "下载中";
                            } else if (status == XmDownloadInfo.Status.FINISHED) {
                                btnTxt = "立即安装";
                            } else if (status == XmDownloadInfo.Status.STOPPED) {
                                btnTxt = "继续下载";
                            } else {
                                btnTxt = "";
                            }
                        }
                    }
                    downloadTaskManager.addTaskListener(sdkDownLoadListener);
                }
            }
        }
        if (!TextUtils.isEmpty(btnTxt)) {
            updateDownloadButton(btnTxt,false);
        }
    }

    private IDownloadTaskListener sdkDownLoadListener = new IDownloadTaskListener() {
        @Override
        public void onStart(XmDownloadInfo xmDownloadInfo, boolean b) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    if (mCurrentProgress == 0) {
                        updateDownloadButton("下载中", false);
                    } else {
                        if (mCurrentProgress < 10) {
                            updateDownloadButton("下载中: " + mCurrentProgress + "%" + "\u00A0", true);
                        } else {
                            updateDownloadButton("下载中: " + mCurrentProgress + "%", true);
                        }
                    }
                }
            });
        }

        @Override
        public void onPause(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    updateDownloadButton("继续下载", false);
                }
            });
        }

        @Override
        public void onRemove(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            mCurrentProgress = 0;
        }

        @Override
        public void onProgress(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            mCurrentProgress = xmDownloadInfo.progress;
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    if (mCurrentProgress == 0) {
                        updateDownloadButton("下载中", false);
                    } else {
                        if (mCurrentProgress < 10) {
                            updateDownloadButton("下载中: " + mCurrentProgress + "%" + "\u00A0", true);
                        } else {
                            updateDownloadButton("下载中: " + mCurrentProgress + "%", true);
                        }
                    }
                }
            });
        }

        @Override
        public void onError(XmDownloadInfo xmDownloadInfo) {
        }

        @Override
        public void onSuccess(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    updateDownloadButton("立即安装", false);
                }
            });
        }

        @Override
        public void onInstallBegin(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    updateDownloadButton("立即安装", false);
                }
            });
        }

        @Override
        public void onInstallSuccess(XmDownloadInfo xmDownloadInfo) {
            if (mAdvertis == null || !TextUtils.equals(xmDownloadInfo.url, mAdvertis.getRealLink())) {
                return;
            }
            HandlerManager.postOnUIThread(new Runnable() {
                @Override
                public void run() {
                    updateDownloadButton("立即打开", false);
                }
            });
        }

        @Override
        public void onOpenApk(XmDownloadInfo xmDownloadInfo, boolean b) {
        }
    };

    private void updateDownloadButton(String btnTxt,boolean isDownloading) {
        if (mCommendAdTitleNew == null || mAdvertis == null) {
            return;
        }
        setupTitleWithLinkText(mCommendAdTitleNew, mAdvertis.getDescription() + " ", btnTxt,
                PlayPageDataManager.getInstance().getCommentHighlightColor(), 2, isDownloading);
    }

    private ViewTreeObserver.OnScrollChangedListener mOnScrollChangedListener =
            () -> checkViewAndReport();
    private long lastVisibleTime;

    private void checkViewAndReport() {
        if (mAbstractAd == null || mAbstractAd.getAdvertis() == null) {
            return;
        }
        if (System.currentTimeMillis() - lastVisibleTime < 200) {
            return;
        }
        lastVisibleTime = System.currentTimeMillis();
        boolean localVisibleRect = ViewStateCheckUtil.checkIsVisibility(this, 1);
        //滑动中检测非三方的静态图片广告是否展示并上报
        if (!currentAdIsThirdAd && localVisibleRect) {
            if (!(mAbstractAd.getAdvertis().getShowstyle() == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO) && !mAbstractAd.getAdvertis().isShowedToRecorded()) {
                record(mAbstractAd, Advertis.SHOW_TYPE_STATIC, adSequence, adRank);
                mAbstractAd.getAdvertis().setShowedToRecorded(true);
            }
        }
    }

    @Override
    public void setVisibility(int visibility) {
        super.setVisibility(visibility);
        if (mAdContainer != null) {
            mAdContainer.setVisibility(visibility);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        listenerViewTree();
    }

    public void listenerViewTree() {
        ViewTreeObserver viewTreeObserver = getViewTreeObserver();
        if(viewTreeObserver != null) {
            viewTreeObserver.removeOnScrollChangedListener(mOnScrollChangedListener);
            viewTreeObserver.addOnScrollChangedListener(mOnScrollChangedListener);
        }
    }

    public void removeListenerViewTree() {
        ViewTreeObserver viewTreeObserver = getViewTreeObserver();
        if(viewTreeObserver != null) {
            viewTreeObserver.removeOnScrollChangedListener(mOnScrollChangedListener);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        removeListenerViewTree();
        super.onDetachedFromWindow();
    }

    private void setShakeData(AbstractThirdAd abstractAd) {
        if (abstractAd == null || commonShakeView == null) return;
        commonShakeView.bindData(abstractAd.getAdvertis());
    }

    private void setVideoData(IAbstractAd abstractAd) {
        if(mVideoFrameLayout == null) {
            return;
        }

        List<View> views = new ArrayList<View>();
        views.add(mTrackCommentLay);
        views.add(mCommentImage);

        AdManager.CustomFrameLayoutLayoutParams logoParams =
                AdManager.createCustomLayoutParamsForGdt();
        logoParams.gravity = Gravity.RIGHT | Gravity.TOP;
        int imageBottomHeight;
        if (mTopContainer != null) {
            mTopContainer.measure(MeasureSpec.makeMeasureSpec(BaseUtil.getScreenWidth(getContext()), MeasureSpec.EXACTLY),
                    MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
            imageBottomHeight =  (int) ((BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 60 + 16)) / 1.78f + getMargin() + mTopContainer.getMeasuredHeight());
        } else {
            imageBottomHeight = (int) ((BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 60 + 16)) / 1.78f + BaseUtil.dp2px(getContext(), 68));
        }
        logoParams.topMargin = imageBottomHeight - AdManager.dspMarkHeight() - BaseUtil.dp2px(getContext(), 4);
        logoParams.rightMargin = -BaseUtil.dp2px(getContext(), 46);

        Advertis advertis = mAbstractAd.getAdvertis();
        if (advertis != null && (advertis.getVolume() == 0 || !advertis.isSoundEnabled())) {
            playMute = true;
            mVolumnBtn.setSelected(false);
        } else {
            playMute = false;
            mVolumnBtn.setSelected(true);
        }

        checkVolumnIsVis();

        videoParamModel = new VideoParamModel(mVideoFrameLayout, mLayoutCover, playMute);
        videoParamModel.setSetVideoState(false);
        videoParamModel.setOnlyRelayShowToPlay(true);
        videoParamModel.setListenScrollAndCheckViewState(true);
        videoParamModel.setRegisterVideoStatue(false);
        videoParamModel.setOnlySetVolume(true);

        boolean isVideoAd = abstractAd != null && abstractAd.getAdvertis() != null && abstractAd.getAdvertis().getShowstyle() ==  Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO;
        isVideoAd = isVideoAd ||
                (abstractAd != null && AdManager.isBaiduAd(abstractAd.getAdvertis()) && abstractAd.getImageMode() == IAbstractAd.ITEM_VIEW_TYPE_VIDEO); // 百度可能会出现视频图片混投

        if(!AdManager.isThirdAd(abstractAd) && isVideoAd
                && !android.text.TextUtils.isEmpty(abstractAd.getAdvertis().getVideoCover())) {
            videoParamModel.setVideoPath(AdManager.wrapVideoPlayUrl(abstractAd.getAdvertis().getVideoCover()));
            videoParamModel.setPlayLooper(true);
        }

        if (isVideoAd) {
            mLastIsPlaying = XmPlayerManager.getInstance(getContext()).isPlaying();
        }

        videoParamModel.setSetVideoState(false);
        videoParamModel.setUseAudioFocusChangeState(true);
        videoParamModel.setOnPagePauseVideoPause(true);
        videoParamModel.setOnPauseAbandonAudioFocus(false);

        abstractAd.bindAdToView(getContext(), mAdContainer, views, logoParams, videoParamModel,
                new IHaveVideoThirdAdStatueCallBack() {
                    @Override
                    public void onTimeOutNoRecord(boolean adViewIsRealShowing) {

                    }

                    @Override
                    public void onVideoAudioStart(AbstractThirdAd thirdAd) {

                    }

                    @Override
                    public void onVideoStart(AbstractThirdAd thirdAd) {
                        checkVolumnIsVis();

                        if(mVolumnBtn != null && mVolumnBtn.isSelected()) {
                            CommendAdViewX.this.onVideoHasVolumnStart();
                        }

                        record(mAbstractAd,Advertis.SHOW_TYPE_VIDEO,adSequence,adRank);
                        videoShowSuccess = true;

                        if (mLayoutCover != null) {
                            mLayoutCover.setVisibility(View.INVISIBLE);
                        }
                    }

                    @Override
                    public void onVideoReady(AbstractThirdAd thirdAd) {

                    }

                    @Override
                    public void onVideoPause(AbstractThirdAd thirdAd) {
                    }

                    @Override
                    public void onVideoResume(AbstractThirdAd thirdAd) {
                        checkVolumnIsVis();

                        if(mVolumnBtn != null && mVolumnBtn.isSelected()) {
                            CommendAdViewX.this.onVideoHasVolumnStart();
                        }
                    }

                    @Override
                    public void onVideoComplete(AbstractThirdAd thirdAd) {
                    }

                    @Override
                    public void onVideoPlayError(int errorCode, String errorMsg) {
                        onVideoHideVolumeOrPlayPause();

                        if(mAbstractAd != null && !TextUtils.isEmpty(mAbstractAd.getImgUrl())) {
                            ImageManager.from(getContext()).displayImage(mLayoutCover,
                                    mAbstractAd.getImgUrl(),
                                    com.ximalaya.ting.android.host.R.drawable.host_image_default_f3f4f5);
                            if (mLayoutCover != null) {
                                mLayoutCover.setVisibility(View.VISIBLE);
                            }
                        }

                        record(mAbstractAd,Advertis.SHOW_TYPE_STATIC,adSequence,adRank);
                    }

                    @Override
                    public void onVideoPlayMuteStateChange(boolean playMute) {
                        if (mVolumnBtn != null) {
                            mVolumnBtn.setSelected(!playMute);
                        }
                    }

                    @Override
                    public void onADExposed() {
                        record(mAbstractAd, 0, adSequence, adRank);
                    }

                    @Override
                    public void onADClicked() {
                        if (AdManager.isThirdAd(abstractAd) && abstractAd instanceof AbstractThirdAd) {
                            AdManager.handlerGDTAd((AbstractThirdAd) abstractAd, abstractAd.getAdvertis(),
                                    MainApplication.getTopActivity(), null,
                                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                                    AppConstants.AD_POSITION_NAME_PLAY_COMMENT)
                                            .rank(adRank).sequence(adSequence)
                                            .build());
                        }
                    }

                    @Override
                    public void onADError(int code, String msg) {

                    }

                    @Override
                    public void onADStatusChanged() {
                        adStatueChange(abstractAd);
                    }
                });

        adStatueChange(abstractAd);

    }

    public void adStatueChange(IAbstractAd abstractAd) {
        if (abstractAd == mAbstractAd && currentAdIsThirdAd && abstractAd != null && mCommendAdAboutBtn != null) {
            mCommendAdAboutBtn.setText(AdManager.getProgressText(abstractAd));
        }
    }

    @Override
    public void onResume() {
        mLastIsPlaying = XmPlayerManager.getInstance(getContext()).isPlaying();
    }

    @Override
    public void onPause() {
        if(!playMute) {
            if (videoParamModel != null && videoParamModel.getVideoControl() != null) {
                // pause 换成 reset是因为在评论广告的场景，页面pause ——>remuse 会请求新的广告
                // 因此前面的广告不必要暂停直接重置即可，如不这样处理，新旧播放评论页都会有bug，
                // 具体表现为前后台切换即使广告没展示，也会继续播放广告上次的广告声音。
                if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_COMMENT_AD_PAUSE_RESET_ENABLE, true)) {
                    videoParamModel.getVideoControl().reset();
                } else {
                    videoParamModel.getVideoControl().pause();
                }
            }
            onVideoHideVolumeOrPlayPause();
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser, boolean isResume) {
        if (isResume) {
            if (!isVisibleToUser && !playMute) {
                onVideoHideVolumeOrPlayPause();
            } else {
                mLastIsPlaying = XmPlayerManager.getInstance(getContext()).isPlaying();
            }
        }
    }

    public interface IScrollableView {
        void addOnScrollListener(AbsListView.OnScrollListener onScrollListener);

        void removeOnScrollListener(AbsListView.OnScrollListener onScrollListener);

        ListView getListView();
    }

    private void setCommonData(IAbstractAd abstractAd) {
        videoShowSuccess = false;
        if (mCreateTime != null) {
            mCreateTime.setText(StringUtil.convertTime(System.currentTimeMillis()));
        }

        if (mCommendAdAboutBtn != null) {
            mCommendAdAboutBtn.setTag(com.ximalaya.ting.android.host.R.id.host_action_btn_style, parentIsNoTab() ? AdActionBtnView.ACTION_BTN_STYLE_COMMON_NO_TAB : AdActionBtnView.ACTION_BTN_STYLE_COMMON_Y_PLAY);
        }

        AdViewUtil.bindViewData(abstractAd, mLayoutCover, com.ximalaya.ting.android.host.R.drawable.host_image_default_f3f4f5,
                false, null, mCommendAdTitle, new ArrayList<View>() {
                    {
                        add(mTrackCommentLay);
                        add(mCommentImage);
                    }
                }, new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // 过滤下频繁点击
                        if (!OneClickHelper.getInstance().onClick(v)) {
                            return;
                        }
                        //如果是iting跳转则需要关闭掉播放评论页
                        if (abstractAd != null && abstractAd.getAdvertis() != null && abstractAd.getAdvertis().isItingSoundSwich()) {
                            IPlayFragmentService iPlayFragmentService = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
                            if (iPlayFragmentService != null) {
                                iPlayFragmentService.removeAllTopFragments();
                            }
                        }

                        if(abstractAd != null) {
                            AdManager.updateWebVideoModel(abstractAd.getAdvertis(), videoParamModel, playMute);
                        }

                        AdReportModel.Builder builder = AdReportModel.newBuilder(
                                        AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                        AppConstants.AD_POSITION_NAME_PLAY_COMMENT).
                                showType(videoShowSuccess ?
                                        Advertis.SHOW_TYPE_VIDEO : Advertis.SHOW_TYPE_STATIC)
                                .rank(adRank).sequence(adSequence);
                        setReportFinishSeconds(builder);
                        if (v instanceof AdActionBtnView && abstractAd != null && AdManager.isAdSupportDowloadInfoFloat(abstractAd.getAdvertis())) {
                            builder.downloadDirect(true);
                        }
                        AdManager.handlerAdClick(getContext(), mAbstractAd.getAdvertis(), builder.build());
                    }
                }, mCommendAdAboutBtn, mAdTag, com.ximalaya.ting.android.host.R.drawable.host_ad_tag_style_11, mAdSourceFromView, null, null);

        if (abstractAd != null && mAdTag != null) {
            if (AdManager.isGdtAd(abstractAd.getAdvertis())) {
                int resource = isYCommentAdUI() ? R.drawable.host_gdt_ad_tag_withe_y : R.drawable.host_gdt_ad_tag_withe_x;
                mAdTag.setImageResource(resource);
                mAdTag.setVisibility(View.VISIBLE);
            } else if (AdManager.isCSJAd(abstractAd.getAdvertis())) {
                int resource = isYCommentAdUI() ? R.drawable.host_csj_ad_tag_withe_y : R.drawable.host_csj_ad_tag_withe_x;
                mAdTag.setImageResource(resource);
            } else if (AdManager.isBaiduAd(abstractAd)) {
                mAdTag.setImageResource(R.drawable.host_ad_tag_style_11);
            }
        }
        if (mThirdAdTag != null && abstractAd != null) {
            if (AdManager.isBaiduAd(abstractAd.getAdvertis()) && !TextUtils.isEmpty(abstractAd.getLogoUrl())) {
                ImageManager.from(getContext()).displayImage(mThirdAdTag, abstractAd.getLogoUrl(), -1);
                mThirdAdTag.setVisibility(VISIBLE);
            } else {
                mThirdAdTag.setVisibility(GONE);
            }
        }
        if (isYCommentAdUI()) {
            setAdMarkBackgroundY(abstractAd, mAdCloseContainer, mAdCloseIcon);
        } else {
            setAdMarkBackground(abstractAd, mAdCloseContainer, mAdCloseIcon);
        }
        if (isYCommentAdUI() || parentIsNoTab()) {
            if (mDivider != null) {
                mDivider.setVisibility(View.INVISIBLE);
            }
        }
        if (mAdTag != null && !AdManager.isThirdAd(abstractAd)) {
            boolean sIsDarkMode = BaseFragmentActivity.sIsDarkMode;
            if (isYCommentAdUI()) {
                sIsDarkMode = true;
            }
            int color = sIsDarkMode ? ContextCompat.getColor(getContext(), R.color.main_color_4dffffff) : ContextCompat.getColor(getContext(), R.color.main_color_aaaaaa);
            mAdTag.setColorFilter(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN));
        }

        if (mCommendAdAboutBtn != null) {
            mCommendAdAboutBtn.setClickIntercept(new IClickIntercept() {
                @Override
                public void onClickIntercept(AdReportModel.Builder builder) {
                    if (builder != null) {
                        builder.rank(adRank);
                        builder.sequence(adSequence);
                    }
                }
            });
        }

        ImageManager.from(getContext()).displayImage(mCommentImage, abstractAd.getAdIcon(),
                com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);

        if (mVolumnBtn != null) {
            mVolumnBtn.setSelected(false);
            mVolumnBtn.setVisibility(View.INVISIBLE);
        }


        if (mCommentName != null) {
            mCommentName.setText(AdManager.isThirdAd(abstractAd) ? abstractAd.getTitle() :
                    abstractAd.getAdvertis().getProviderName());
        }
        bindDownloadData(abstractAd);
        loadCreativeAd(abstractAd);
    }

    private void setAdMarkBackground(IAbstractAd abstractAd, View adCloseContainer, ImageView adCloseIcon) {
        if (adCloseContainer == null) {
            return;
        }
        try {
            if (abstractAd != null && AdManager.isOperationAd(abstractAd.getAdvertis())) {
                adCloseContainer.setBackground(null);
                adCloseIcon.setImageDrawable(getContext().getDrawable(R.drawable.xm_ad_close_operation_style_2));
                if (adCloseIcon.getLayoutParams() instanceof LinearLayout.LayoutParams) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mAdCloseIcon.getLayoutParams();
                    layoutParams.rightMargin = 0;
                    adCloseIcon.setLayoutParams(layoutParams);
                }
            } else {
                if (adCloseIcon.getLayoutParams() instanceof LinearLayout.LayoutParams) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mAdCloseIcon.getLayoutParams();
                    layoutParams.rightMargin = AdUtil.dp2px(XmAdSDK.getContext(), 2);
                    adCloseIcon.setLayoutParams(layoutParams);
                }
                adCloseIcon.setImageDrawable(getContext().getDrawable(R.drawable.xm_ad_arrow_style_2_bg));
                if (parentIsNoTab()) {
                    adCloseContainer.setBackground(getContext().getDrawable(R.drawable.main_ad_close_bg_ffffff_radius_4));
                } else {
                    adCloseContainer.setBackground(getContext().getDrawable(R.drawable.xm_ad_close_bg_f6f7f8_radius_2));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setAdMarkBackgroundY(IAbstractAd abstractAd, View adCloseContainer, ImageView adCloseIcon) {
        if (adCloseContainer == null) {
            return;
        }
        try {
            if (abstractAd != null && AdManager.isOperationAd(abstractAd.getAdvertis())) {
                adCloseContainer.setBackground(null);
                adCloseIcon.setImageDrawable(getContext().getDrawable(R.drawable.main_ad_close_operation_style_y));
                if (adCloseIcon.getLayoutParams() instanceof LinearLayout.LayoutParams) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) adCloseIcon.getLayoutParams();
                    layoutParams.rightMargin = 0;
                    adCloseIcon.setLayoutParams(layoutParams);
                }
            } else {
                if (adCloseIcon.getLayoutParams() instanceof LinearLayout.LayoutParams) {
                    LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) mAdCloseIcon.getLayoutParams();
                    layoutParams.rightMargin = AdUtil.dp2px(XmAdSDK.getContext(), 2);
                    adCloseIcon.setLayoutParams(layoutParams);
                }
                adCloseIcon.setImageDrawable(getContext().getDrawable(R.drawable.main_ad_arrow_commom_y_bg));
                adCloseContainer.setBackground(getContext().getDrawable(R.drawable.main_ad_bg_14ffffff_radius_2));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean parentIsNoTab(){
        return this instanceof CommendAdViewXNoTab;
    }
    private void bindDownloadData(IAbstractAd abstractAd) {
        if(abstractAd == null || abstractAd.getAdvertis() == null || downloadInfoFloatView == null){
            return;
        }
        Advertis advertis = abstractAd.getAdvertis();
        if (!AdManager.isAdSupportDowloadInfoFloat(advertis)) {
            downloadInfoFloatView.setVisibility(GONE);
            return;
        }
        downloadInfoFloatView.setVisibility(VISIBLE);
        if (mCommendAdTitle != null) {
            mCommendAdTitle.setLines(1);
        }
        if (android.text.TextUtils.isEmpty(advertis.getDownloadAppName())) {
            mAppName.setVisibility(GONE);
        } else {
            mAppName.setText(advertis.getDownloadAppName());
            mAppName.setVisibility(VISIBLE);
        }
        if (android.text.TextUtils.isEmpty(advertis.getAppVersion())) {
            mAppVersion.setText("版本: 未知");
        } else {
            mAppVersion.setText("V" + advertis.getAppVersion());
        }

        if (android.text.TextUtils.isEmpty(advertis.getAppDeveloper())) {
            mAppDeveloper.setText("开发者: 未知");
        } else {
            mAppDeveloper.setText(advertis.getAppDeveloper());
        }
        mAppPermission.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (advertis == null
                        || ((advertis.getAppPermissions() == null || advertis.getAppPermissions().isEmpty())
                        && (advertis.getBusinessExtraInfo() == null || android.text.TextUtils.isEmpty(advertis.getBusinessExtraInfo().getAppPermissionUrl())))) {
                    return;
                }
                AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PERMISSION);
                dialog.show();
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PERMISSION, params);
            }
        });
        mAppPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (advertis == null || android.text.TextUtils.isEmpty(advertis.getAppPrivacyPolicy())) {
                    return;
                }
                AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_PRIVACY);
                dialog.show();
                String positionName = AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId());
                DownloadAdvertisParams params = new DownloadAdvertisParams(advertis, positionName);
                DownloadServiceManage.getInstance().recordDownloadDialogOkClick(advertis.getRealLink(),
                        DownloadAdRecordManager.DOWNLOAD_EVENT_CLICK_PRIVACY, params);
            }
        });
        mAppIntroduction.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (advertis == null) {
                    return;
                }
                AdDownloadPermissionAndPrivacyDialog dialog = new AdDownloadPermissionAndPrivacyDialog(
                        MainApplication.getTopActivity(), advertis, AdDownloadPermissionAndPrivacyDialog.DIALOG_TYPE_INTRODUCTION);
                dialog.show();
            }
        });
    }

    private void setReportFinishSeconds(AdReportModel.Builder builder) {
        if (videoShowSuccess && videoParamModel != null && videoParamModel.getVideoControl() != null) {
            int curPos = videoParamModel.getVideoControl().getCurPos();
            if (curPos > 0) {
                builder.setFinishSeconds(curPos / 1000);
            }
        }
    }

    private void loadCreativeAd(IAbstractAd abstractAd) {
        if (abstractAd == null || abstractAd.getAdvertis() == null) {
            return;
        }
        Advertis advertis = abstractAd.getAdvertis();
        if (advertis.getAnimationType() == Advertis.ANIMATION_TYPE_NONE) {
            return;
        }
        CreativeAdManager.loadCreativePics(abstractAd.getAdvertis(), new CreativeAdManager.PicLoadCallBack() {
            @Override
            public void loadSuccess(List<Bitmap> bitmaps) {
                if (advertis.getAnimationType() == Advertis.ANIMATION_TYPE_POP_UP) {
                    CreativeAdManager.playPopUpAnimation(getContext(), popupView, bitmaps, false, false, false, mCommendAdAboutBtn, 90);
                } else if (advertis.getAnimationType() == Advertis.ANIMATION_TYPE_MONEY_RAIN) {
                    CreativeAdManager.playMoneyRainAnimation(getContext(), moneyRainView, bitmaps, false);
                }
            }
        });
    }

    private boolean mLastIsPlaying;

    private void onVideoHasVolumnStart() {
        Logger.log("CommendAdViewX : mLastIsPlaying " + mLastIsPlaying);
        PlayTools.pause(getContext(), PauseReason.Business.CommendAdViewX);
        XmPlayerManager.getInstance(getContext()).addPlayerStatusListener(this);

        if(mVolumnBtn != null) {
            if (mIsNewCommentParent) {
                return;
            }
            mVolumnBtn.setVisibility(View.VISIBLE);
        }
    }

    private void onVideoHideVolumeOrPlayPause() {
        Logger.log("CommendAdViewX : onVideoHideVolumeOrPlayPause " + mLastIsPlaying);
        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(this);
        if(mLastIsPlaying) {
            PlayTools.play(getContext());
        }
    }

    @Override
    public void onStartTemporaryDetach() {
        super.onStartTemporaryDetach();
        isDetached = true;

        if (mAbstractAd != null
                && mAbstractAd.getAdvertis() != null
                && mAbstractAd.getAdvertis().getShowstyle() == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO
                && !playMute) {
            onVideoHideVolumeOrPlayPause();
        }
        XmPlayerManager.getInstance(getContext()).removePlayerStatusListener(this);
        Logger.log("CommendAdViewX : onStartTemporaryDetach");
    }

    @Override
    public void onPlayStart() {
        if (videoParamModel != null && !videoParamModel.isPlayMute() && videoParamModel.getSetPlayMute() != null) {
            videoParamModel.getSetPlayMute().setPlayMute(true, false);
        }

        if(mVolumnBtn != null) {
            mVolumnBtn.setSelected(false);
        }

        playMute = true;
    }

    @Override
    public void onPlayPause() {

    }

    @Override
    public void onPlayStop() {

    }

    @Override
    public void onSoundPlayComplete() {

    }

    @Override
    public void onSoundPrepared() {

    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {

    }

    @Override
    public void onBufferingStart() {

    }

    @Override
    public void onBufferingStop() {

    }

    @Override
    public void onBufferProgress(int percent) {

    }

    @Override
    public void onPlayProgress(int currPos, int duration) {

    }

    @Override
    public boolean onError(XmPlayerException exception) {
        return false;
    }

    private void checkVolumnIsVis() {
        if(mVolumnBtn == null) {
            return;
        }

        if (mAbstractAd != null && mAbstractAd.getAdvertis() != null
                && mAbstractAd.getAdvertis().getShowstyle() == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO
                && (AdManager.isGdtAd(mAbstractAd.getAdvertis()) || !AdManager.isThirdAd(mAbstractAd))) {
            if (mIsNewCommentParent) {
                return;
            }
            mVolumnBtn.setVisibility(View.VISIBLE);
        } else {
            mVolumnBtn.setVisibility(View.INVISIBLE);
        }
    }

    public interface CommendAdCloseLister {
        void onAdClose();
    }

    private void bindLike(Advertis advertis, View layoutLike, ImageView ivLike,
                         LottieAnimationView lottieLike,
                         TextView tvLikeCount) {
        lottieLike.setAnimation("lottie/host_lottie_for_like_action.json");
        lottieLike.setTag("");
        ivLike.setImageResource(R.drawable.host_album_rate_like_selector_new);
        setLikeCount(tvLikeCount, advertis.getLikeCount());
        tvLikeCount.setSelected(advertis.isLiked());
        lottieLike.setVisibility(View.GONE);
        ivLike.setVisibility(View.VISIBLE);
        ivLike.setSelected(advertis.isLiked());
        updateContentDescriptionForLike(layoutLike, advertis.isLiked(), advertis.getLikeCount());
    }

    private void onLikeStateChange(Advertis advertis, View layoutLike,
                                          ImageView ivLike, LottieAnimationView lottieLike,
                                          TextView tvLikeCount) {
        advertis.setLiked(!advertis.isLiked());
        if (advertis.isLiked()) {
            advertis.setLikeCount(advertis.getLikeCount() + 1);
        } else {
            advertis.setLikeCount(advertis.getLikeCount()- 1);
        }

        tvLikeCount.setSelected(advertis.isLiked());
        setLikeCount(tvLikeCount, advertis.getLikeCount());
        updateContentDescriptionForLike(layoutLike, advertis.isLiked(), advertis.getLikeCount());

        if (advertis.isLiked()) {
            if (lottieLike.getTag() != null && lottieLike.getComposition() != null) {
                ivLike.setVisibility(View.GONE);
                ivLike.setSelected(true);
                lottieLike.setVisibility(View.VISIBLE);
                lottieLike.playAnimation();
                lottieLike.addAnimatorListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {
                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        lottieLike.setVisibility(View.GONE);
                        ivLike.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                        lottieLike.setVisibility(View.GONE);
                        ivLike.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {
                    }
                });
            } else {
                lottieLike.setVisibility(View.GONE);
                ivLike.setVisibility(View.VISIBLE);
                ivLike.setSelected(true);
            }
            layoutLike.performHapticFeedback(HapticFeedbackConstants.VIRTUAL_KEY);
            layoutLike.announceForAccessibility("已点赞");
            Map<String, String> params = new HashMap<>();
            params.put("adId", advertis.getAdid() + "");
            params.put("positionId", advertis.getAdPositionId());
            JSONObject jsonObject = new JSONObject(params);
            String postJsonStr = jsonObject.toString();
            MainCommonRequest.basePostRequest(AdUrlConstants.getInstanse().getLikeReportUrl(), null, null, null, postJsonStr);
            AdManager.postRecord(()-> {
                AdCollectData adCollectData = AdManager.thirdAdToAdCollect(MainApplication.getMyApplicationContext(),
                        advertis,
                        new AdReportModel.Builder(AppConstants.AD_LOG_TYPE_LIKE,
                                AdPositionIdManager.getPositionNameByPositionId(advertis.getAdPositionId()))
                                .playPageRevision(PlayPageStyleUtil.getNewPageStyle() + "")
                                .newPlayPageVersion(NewPlayPageUtil.getNewPageParams())
                                .isDisplayedInScreen(1)
                                .adPlayVersion(AdManager.getAdPlayVersion()).build());
                CommonRequestM.statOnlineAd(adCollectData);
            });
        } else {
            lottieLike.setVisibility(View.GONE);
            ivLike.setSelected(false);
            ivLike.setVisibility(View.VISIBLE);
            layoutLike.announceForAccessibility("已取消点赞");
        }
    }

    private void setLikeCount(TextView view, long likes) {
        view.setText(likes > 0 ? StringUtil.getFriendlyNumStr((likes)) : "赞");
    }

    private void updateContentDescriptionForLike(View view, boolean liked, long likes) {
        view.setContentDescription(CommentOperationUtil.getLikeContentDescription(liked, likes));
    }

    /**
     * 设置标题末尾嵌入链接文本
     */
    private void setupTitleWithLinkText(final TextView textView, String titleText, String linkText, int color, int iconStyle, boolean isDownloading) {
        try {
            // 设置基本设置
            textView.setEllipsize(TextUtils.TruncateAt.END);
            textView.setMaxLines(4);

            final SpannableString linkSpan;
            if (iconStyle == 0) {
                linkSpan = new SpannableString(linkText);
                linkSpan.setSpan(new ForegroundColorSpan(color), 0, linkText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            } else {
                // 获取默认图标并设置大小
                Drawable originalDrawable = ContextCompat.getDrawable(getContext(), iconStyle == 1 ?
                        R.drawable.main_ic_link_n_n_line_regular_24_with_color : R.drawable.main_ic_download_n_n_line_regular_24_with_color);
                Drawable drawable = originalDrawable.mutate(); // 创建可变副本
                int iconSize = (int) textView.getTextSize();
                drawable.setBounds(0, 0, iconSize, iconSize);
                drawable.setColorFilter(new PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN));

                // 创建图标+链接的文本部分
                // 使用不间断空格\u00A0替代普通空格，确保图标和链接文本不被分开显示在不同行
                final String iconPlaceholder = "\u00A0"; // 不间断空格作为图标占位符
                linkSpan = new SpannableString(iconPlaceholder + linkText);
                linkSpan.setSpan(new ImageSpan(drawable, ImageSpan.ALIGN_CENTER), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                linkSpan.setSpan(new ForegroundColorSpan(color), 1, linkSpan.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }

            if (isDownloading && lastCutIndex > 0) {
                // 如果正在下载，并且有上次截断的索引,则直接使用上次截断的索引
                String finalTitle = titleText.substring(0, lastCutIndex) + "... ";
                SpannableString finalContent = new SpannableString(finalTitle);
                textView.setText(finalContent);
                textView.append(linkSpan);
                return;
            } else {
                // 先设置全部内容
                SpannableString fullContent = new SpannableString(titleText);
                textView.setText(fullContent);
                textView.append(linkSpan);
            }

            // 监听布局完成事件，确保图标+链接总是完整显示
            textView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    try {
                        // 移除监听器，防止重复调用
                        textView.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                        Layout layout = textView.getLayout();
                        if (layout == null) return;

                        int lineCount = layout.getLineCount();
                        if (lineCount <= 0) return;

                        // 只有当总行数超过4行时才处理
                        if (lineCount <= 4) {
                            return;
                        }

                        // 如果超过4行或最后一行不包含完整链接文本，需要截断
                        // 使用二分查找法找到最佳截断点，使标题尽可能多显示
                        int left = 0;
                        int right = titleText.length();
                        int best = 0;

                        while (left <= right) {
                            int mid = (left + right) / 2;
                            if (mid > titleText.length()) break;

                            String truncatedTitle = titleText.substring(0, mid) + "... ";
                            SpannableString testContent = new SpannableString(truncatedTitle);
                            textView.setText(testContent);
                            textView.append(linkSpan);

                            // 强制重新布局
                            textView.measure(
                                    MeasureSpec.makeMeasureSpec(textView.getWidth(), MeasureSpec.EXACTLY),
                                    MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
                            );
                            textView.layout(textView.getLeft(), textView.getTop(), textView.getRight(), textView.getBottom());

                            Layout newLayout = textView.getLayout();
                            if (newLayout == null) continue;

                            // 检查是否满足条件：不超过4行且最后一行包含完整的链接文本
                            int newLineCount = newLayout.getLineCount();
                            boolean valid = newLineCount <= 4;

                            if (valid && newLineCount > 0) {
                                int newLastLine = newLineCount - 1;
                                int newLineStart = newLayout.getLineStart(newLastLine);
                                int newLineEnd = newLayout.getLineEnd(newLastLine);
                                String newLastLineText = textView.getText().subSequence(newLineStart, newLineEnd).toString();
                                valid = newLastLineText.contains(linkText);
                            }

                            if (valid) {
                                best = mid;
                                left = mid + 1; // 尝试更长的标题
                            } else {
                                right = mid - 1; // 标题太长，需要缩短
                            }
                        }

                        // 应用找到的最佳截断点
                        if (best > 0) {
                            if (isDownloading) {
                                lastCutIndex = best;
                            }
                            String finalTitle = titleText.substring(0, best) + "... ";
                            SpannableString finalContent = new SpannableString(finalTitle);
                            textView.setText(finalContent);
                            textView.append(linkSpan);
                        } else {
                            // 如果找不到合适的截断点，使用最小长度
                            String minTitle = titleText.length() > 10 ? titleText.substring(0, 10) + "... " : titleText + "... ";
                            SpannableString minContent = new SpannableString(minTitle);
                            textView.setText(minContent);
                            textView.append(linkSpan);
                        }
                    } catch (Exception e) {
                        Logger.d(CommendAdViewX.class.getSimpleName(), "Adjust title for link layout error: " + e.getMessage());
                        // 出现异常时，提供基本显示
                        textView.setText(titleText);
                        textView.append(linkSpan);
                    }
                }
            });
        } catch (Exception e) {
            // 出现异常，至少设置基本的文本
            textView.setText(titleText + " " + linkText);
            Logger.d("CommendAdViewX", "Setup title with link text error: " + e.getMessage());
        }
    }

    /**
     * 执行广告视图的关闭动画（高度变小和渐隐）
     * @param view 需要执行动画的视图
     */
    private void playAdCloseAnimation(final View view) {
        if (view == null || view.getVisibility() != View.VISIBLE) {
            return;
        }
        
        int originalHeight = view.getHeight();
        ValueAnimator heightAnimator = ValueAnimator.ofInt(originalHeight, 0);
        heightAnimator.addUpdateListener(new android.animation.ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(android.animation.ValueAnimator valueAnimator) {
                int val = (Integer) valueAnimator.getAnimatedValue();
                android.view.ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                layoutParams.height = val;
                view.setLayoutParams(layoutParams);
            }
        });
        
        view.animate()
            .alpha(0f)
            .setDuration(500)
            .setListener(new android.animation.AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(android.animation.Animator animation) {
                    super.onAnimationEnd(animation);
                    if (closeListener != null) {
                        closeListener.onAdClose();
                    }
                }
            });
            
        heightAnimator.setDuration(500);
        heightAnimator.start();
    }
}
