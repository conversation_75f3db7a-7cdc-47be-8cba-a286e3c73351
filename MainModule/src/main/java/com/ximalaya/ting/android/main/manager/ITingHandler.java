package com.ximalaya.ting.android.main.manager;

import static com.ximalaya.ting.android.host.constants.CommentConstants.FROM_PAGE_SHOW_NOTES_PAGE;
import static com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router.getActionRouter;
import static com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager.TAB_FINDING;
import static com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager.TAB_HOME_PAGE;
import static com.ximalaya.ting.android.host.manager.track.AlbumEventManage.FROM_DESKTOP_SHORTCUT;
import static com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo.FROM_TOB;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.net.Uri;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.core.widget.ImageViewCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.earn.statistics.PushArrivedTraceManager;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.fragment.ManageFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.IntentUtil;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.activity.web.WebActivity;
import com.ximalaya.ting.android.host.ccb.CcbSdkManager;
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager;
import com.ximalaya.ting.android.host.chatxmly.ChatXmlySettingFragment;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.constant.ResPositionConstant;
import com.ximalaya.ting.android.host.constant.SharedConstant;
import com.ximalaya.ting.android.host.constants.BusinessTypeEnum;
import com.ximalaya.ting.android.host.constants.CommentConstants;
import com.ximalaya.ting.android.host.constants.LoginByConstants;
import com.ximalaya.ting.android.host.constants.MyListenTabEnum;
import com.ximalaya.ting.android.host.constants.TingListConstants;
import com.ximalaya.ting.android.host.constants.TrackPageFromSourceEnum;
import com.ximalaya.ting.android.host.constants.TrackPageFromSourceUtil;
import com.ximalaya.ting.android.host.data.model.BaseResponseData;
import com.ximalaya.ting.android.host.dialog.HalfWebVIewDialog;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenTimeDialogManager;
import com.ximalaya.ting.android.host.drivemode.DriveModeActivityV3;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.other.LoginEventBridge;
import com.ximalaya.ting.android.host.fragment.play.XPlayPageRef;
import com.ximalaya.ting.android.host.fragment.rn.ReactNativeDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.hybrid.utils.ComponentSecuritySignUtils;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.listener.IVoiceWakeInstructionCallback;
import com.ximalaya.ting.android.host.manager.AccessibilityModeManager;
import com.ximalaya.ting.android.host.manager.ArriveTraceManager;
import com.ximalaya.ting.android.host.manager.ChildProtectDialogManager;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.LastAudioPlayListCache;
import com.ximalaya.ting.android.host.manager.NewAlbumReserveManager;
import com.ximalaya.ting.android.host.manager.NewShowNotesManager;
import com.ximalaya.ting.android.host.manager.PlayCompleteRecommendManager;
import com.ximalaya.ting.android.host.manager.QuickListenTabAbManager;
import com.ximalaya.ting.android.host.manager.ReadBundleHelper;
import com.ximalaya.ting.android.host.manager.TempDataManager;
import com.ximalaya.ting.android.host.manager.ToListenManager;
import com.ximalaya.ting.android.host.manager.account.LoginDeferredActionHelper;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.TouTiaoAdManager;
import com.ximalaya.ting.android.host.manager.ad.download.center.AdDownloadListFragment;
import com.ximalaya.ting.android.host.manager.ad.thirdgamead.ThirdGameAdConstants;
import com.ximalaya.ting.android.host.manager.ad.thirdgamead.view.ThirdGameWebViewActivity;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockPaidManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.ItingFreeListenRewardManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoTaskUtil;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.chat.IChatNewsCenterBuzType;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.mylisten.IMyListenFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.speechrecognition.ISpeechRecognitionFragmentAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ChatActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.FeedActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.HybridViewActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.KidActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LiveActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.LoginActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MiniDramaActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MyListenActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MyclubActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RadioActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RecordActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SearchActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ShootActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.SmartDeviceActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VipActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ZoneActionRouter;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.firework.FireworkManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.iting.HomePageSwitchManager;
import com.ximalaya.ting.android.host.manager.iting.ITingExtraUriParamsManager;
import com.ximalaya.ting.android.host.manager.iting.ITingOutsideSourceManager;
import com.ximalaya.ting.android.host.manager.iting.ItingManager;
import com.ximalaya.ting.android.host.manager.kidmode.CategoryRecommendKidEntryManager;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.host.manager.pay.PayManager;
import com.ximalaya.ting.android.host.manager.play.PlayerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.rn.RnMobDebugUtil;
import com.ximalaya.ting.android.host.manager.router.XmUriRouter;
import com.ximalaya.ting.android.host.manager.share.VipGiftShareManager;
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.vip.UserVipInfoManager;
import com.ximalaya.ting.android.host.model.OutSiteWebLoginModel;
import com.ximalaya.ting.android.host.model.TingListTracksInfoModel;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.album.AlbumPgcRouteResult;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.childprotect.ChildProtectInfo;
import com.ximalaya.ting.android.host.model.feed.community.CommunityHomeParam;
import com.ximalaya.ting.android.host.model.feed.community.TopicDetailParam;
import com.ximalaya.ting.android.host.model.listenlist.TingListInfoModel;
import com.ximalaya.ting.android.host.model.onekeylisten.OneKeyListenTabModel;
import com.ximalaya.ting.android.host.model.play.CommentModel;
import com.ximalaya.ting.android.host.model.play.DubTransferModel;
import com.ximalaya.ting.android.host.model.play.PlayPageTab;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.postbox.Mail;
import com.ximalaya.ting.android.host.model.push.PushModel;
import com.ximalaya.ting.android.host.model.share.ShareAssistData;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.model.track.TrackPushInfo;
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlUtil;
import com.ximalaya.ting.android.host.shoot.ShootCallback;
import com.ximalaya.ting.android.host.util.MyListenRouterUtil;
import com.ximalaya.ting.android.host.util.RouteServiceUtil;
import com.ximalaya.ting.android.host.util.ShortVideoItingUtil;
import com.ximalaya.ting.android.host.util.SpeechRecognitionRouterUtil;
import com.ximalaya.ting.android.host.util.XimaTenDataUtil;
import com.ximalaya.ting.android.host.util.commercial.CommercialHostUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.host.util.feed.DyncAbTestUtil;
import com.ximalaya.ting.android.host.util.onekey.DailyNewsLogicManager;
import com.ximalaya.ting.android.host.util.other.BookUtil;
import com.ximalaya.ting.android.host.util.other.VideoPlayParamsBuildUtil;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.host.util.vip.VipHostUtil;
import com.ximalaya.ting.android.host.view.CustomTipsView;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.fragment.SplashShakeSettingFragment;
import com.ximalaya.ting.android.main.albumModule.album.AlbumRateDetailFragment;
import com.ximalaya.ting.android.main.albumModule.album.CreateAlbumRateFragment;
import com.ximalaya.ting.android.main.albumModule.album.UncommentedAlbumFragment;
import com.ximalaya.ting.android.main.albumModule.album.album2.AlbumFragmentNew2;
import com.ximalaya.ting.android.main.albumModule.album.monthTicket.MonthTicketContributeRankFragment;
import com.ximalaya.ting.android.main.albumModule.other.AlbumBookShareDialogFragment;
import com.ximalaya.ting.android.main.albumModule.other.AlbumListFragment;
import com.ximalaya.ting.android.main.anchorModule.AnchorQrCodeFragmentNew;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryContentFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryDetailFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryListFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryListV2Fragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.CategoryMetadataFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.HistoryPreferredFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.NewCategoryRecommendFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.NewProductFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.NewPublishFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.PreferredContentFragment;
import com.ximalaya.ting.android.main.categoryModule.fragment.WeeklyDramaFragment;
import com.ximalaya.ting.android.main.categoryModule.page.ChannelFragment;
import com.ximalaya.ting.android.main.categoryModule.page.tab.AllCategoryFragment;
import com.ximalaya.ting.android.main.categoryModule.page.tab.ChannelChildCategoryFragment;
import com.ximalaya.ting.android.main.categoryModule.page.tab.HomePageTabAndChannelListNewFragmentDialog;
import com.ximalaya.ting.android.main.commentModule.SelectedHotCommentFragment;
import com.ximalaya.ting.android.main.commentModule.fragment.FloatingTrackCommentFragment;
import com.ximalaya.ting.android.main.commentModule.util.FloatingCommentManager;
import com.ximalaya.ting.android.main.constant.BundleKeyConstantsInMain;
import com.ximalaya.ting.android.main.constant.MainUrlConstants;
import com.ximalaya.ting.android.main.dialog.RecommendBottomDialogCaller;
import com.ximalaya.ting.android.main.dialog.WeChatReserveDialog;
import com.ximalaya.ting.android.main.dialog.commercial.PlatinumVipCouponConsumeDialog;
import com.ximalaya.ting.android.main.dialog.reward.RewardDianGiftDialog;
import com.ximalaya.ting.android.main.doc.DocReadFragment;
import com.ximalaya.ting.android.main.downloadModule.other.BatchDownloadFragmentNew;
import com.ximalaya.ting.android.main.dubbingModule.fragment.DubbingUserInfoFragment;
import com.ximalaya.ting.android.main.findModule.fragment.DubbingRecommendFragment;
import com.ximalaya.ting.android.main.fragment.feedback.FeedBackOrderDetailFragment;
import com.ximalaya.ting.android.main.fragment.feedback.FeedBackOrderFragment;
import com.ximalaya.ting.android.main.fragment.find.child.PodCastCategoryListFragment;
import com.ximalaya.ting.android.main.fragment.find.child.PodCastFragmentV2;
import com.ximalaya.ting.android.main.fragment.find.child.PodCastImmersiveAudioListFragment;
import com.ximalaya.ting.android.main.fragment.find.child.PodCastRankFragment;
import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendChannelITing;
import com.ximalaya.ting.android.main.fragment.find.other.CustomizeFragment;
import com.ximalaya.ting.android.main.fragment.find.other.NewAlbumTimeLimitFreeListFragment;
import com.ximalaya.ting.android.main.fragment.find.other.anchor.AnchorListFragment;
import com.ximalaya.ting.android.main.fragment.find.other.anchor.FindFriendFragmentNew;
import com.ximalaya.ting.android.main.fragment.find.other.city.CityListFragment;
import com.ximalaya.ting.android.main.fragment.find.other.fantasy.BoutiqueFraSelectManager;
import com.ximalaya.ting.android.main.fragment.find.other.rank.CustomizeRankFragment;
import com.ximalaya.ting.android.main.fragment.find.other.rank.NewUserRankFragment;
import com.ximalaya.ting.android.main.fragment.find.other.rank.TrackListFragment;
import com.ximalaya.ting.android.main.fragment.find.other.recommend.ReportFragment;
import com.ximalaya.ting.android.main.fragment.find.vip.PayAlbumRankFragment;
import com.ximalaya.ting.android.main.fragment.myspace.MineModuleEntranceListFragment;
import com.ximalaya.ting.android.main.fragment.myspace.OutSiteWebLoginFragment;
import com.ximalaya.ting.android.main.fragment.myspace.QRCodeScanFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.AnchorSkillSettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ChildPlatformAcceptFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.ListenPermissionFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.MyAllAlbumCommentsFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.SettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.child.XiMaNoticeFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.disabledverify.DisabledVerifyPostFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.disabledverify.DisabledVerifyResultFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.family.FamilyRecommendAlbumFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.friend.FindFriendFragmentV2;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.AddOrEditAlarmFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.AlarmManagerFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.CommentSettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.LockScreenSettingFragment;
import com.ximalaya.ting.android.main.fragment.myspace.util.InterestUtil;
import com.ximalaya.ting.android.main.fragment.other.child.EditChildInfoFragment;
import com.ximalaya.ting.android.main.fragment.recommend.NewDailyRecommendFragment;
import com.ximalaya.ting.android.main.fragment.recommend.NewUserListenFragment;
import com.ximalaya.ting.android.main.fragment.recommend.RecommendCalabashFragment;
import com.ximalaya.ting.android.main.fragment.share.MedalPosterFragment;
import com.ximalaya.ting.android.main.fragment.tinglist.TingListGroupFragment;
import com.ximalaya.ting.android.main.kachamodule.fragment.post.KachaPostFragment;
import com.ximalaya.ting.android.main.kachamodule.produce.utils.KachaHintHelper;
import com.ximalaya.ting.android.main.manager.comment.CommentOperationUtil;
import com.ximalaya.ting.android.main.manager.comment.PicCropUtil;
import com.ximalaya.ting.android.main.manager.playPage.PlayListAndHistoryDialogManager;
import com.ximalaya.ting.android.main.mine.fragment.AllServiceFragment;
import com.ximalaya.ting.android.main.mine.util.MineTopUtil;
import com.ximalaya.ting.android.main.model.album.MainAlbumMList;
import com.ximalaya.ting.android.main.model.city.CityModel;
import com.ximalaya.ting.android.main.model.disabledverify.DisabledVerifyBean;
import com.ximalaya.ting.android.main.model.vip.MemberInfo;
import com.ximalaya.ting.android.main.payModule.refund.AlbumRefundInfoFragment;
import com.ximalaya.ting.android.main.payModule.refund.RefundFragment;
import com.ximalaya.ting.android.main.playModule.dailyNews4.DailyNewsFragment4;
import com.ximalaya.ting.android.main.playModule.soundEffect.ChooseTrackSoundEffectDialog;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageSubscribeFirstManager;
import com.ximalaya.ting.android.main.playpage.dialog.ListenTimeDialogManager;
import com.ximalaya.ting.android.main.playpage.dialog.PlanTerminalNewDialog;
import com.ximalaya.ting.android.main.playpage.fragment.CardAggregationFragment;
import com.ximalaya.ting.android.main.playpage.fragment.CardPublishFragment;
import com.ximalaya.ting.android.main.playpage.fragment.CommentLotteryThemeResultFragment;
import com.ximalaya.ting.android.main.playpage.fragment.CommentThemePageFragment;
import com.ximalaya.ting.android.main.playpage.fragment.PlayFragmentNew;
import com.ximalaya.ting.android.main.playpage.fragment.PlayManuscriptNotInTabFragment;
import com.ximalaya.ting.android.main.playpage.internalservice.IPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayFriendListenedManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDocAndCoverHelper;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.playx.XUtils;
import com.ximalaya.ting.android.main.playpage.playy.YUtils;
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew;
import com.ximalaya.ting.android.main.playpage.playy.tabs.biz.comment.YPlayCommentIndependentFragment;
import com.ximalaya.ting.android.main.playpage.util.CommentThemeUtil;
import com.ximalaya.ting.android.main.playpage.util.InteractiveCardUtil;
import com.ximalaya.ting.android.main.playpage.util.PlayManuscriptTimeStampUtil;
import com.ximalaya.ting.android.main.playpage.vote.dialog.VoteDialogFragment;
import com.ximalaya.ting.android.main.playpage.vote.list.MonthlyVoteListFragment;
import com.ximalaya.ting.android.main.postbox.fragment.MailBoxMailListFragment;
import com.ximalaya.ting.android.main.postbox.fragment.MailBoxWriteMailFragment;
import com.ximalaya.ting.android.main.postbox.fragment.MailDetailFragment;
import com.ximalaya.ting.android.main.postbox.fragment.MyMailListFragment;
import com.ximalaya.ting.android.main.rankModule.AggregateRankUtil;
import com.ximalaya.ting.android.main.rankModule.fragment.CategoryAggregateRankFragment;
import com.ximalaya.ting.android.main.rankModule.fragment.CategoryAggregateRankFragmentNew;
import com.ximalaya.ting.android.main.rankModule.fragment.RecommendNewUserRankFragment;
import com.ximalaya.ting.android.main.rankModule.fragment.SingleRankFragment;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.roleModule.fragment.RoleDetailFragment;
import com.ximalaya.ting.android.main.share.dialog.ShareAssistDialogFragment;
import com.ximalaya.ting.android.main.tagModule.fragment.TagChannelContentFragment;
import com.ximalaya.ting.android.main.tagModule.fragment.TagChannelContentFragmentV2;
import com.ximalaya.ting.android.main.tagModule.fragment.TagContentFragment;
import com.ximalaya.ting.android.main.tagModule.fragment.TopicDetailFragment;
import com.ximalaya.ting.android.main.util.ChooseLikeUtil;
import com.ximalaya.ting.android.main.util.MyListenAbUtil;
import com.ximalaya.ting.android.main.util.MyListenUtil;
import com.ximalaya.ting.android.main.util.PaidRetentionUtil;
import com.ximalaya.ting.android.main.util.mine.ModeSwitchUtil;
import com.ximalaya.ting.android.main.util.other.LoanFunctionUtil;
import com.ximalaya.ting.android.main.util.other.ShareUtilsInMain;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.SharedModelConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.album.SubordinatedAlbum;
import com.ximalaya.ting.android.opensdk.model.live.radio.Radio;
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.opensdk.player.statistics.manager.UserInteractivePlayStatistics;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.pay.log.PayActionLog;
import com.ximalaya.ting.android.pay.log.PayActionManager;
import com.ximalaya.ting.android.pay.log.PayConstants;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.bocpay.IBocPayManager;
import com.ximalaya.ting.android.routeservice.service.history.IHistoryManagerForMain;
import com.ximalaya.ting.android.routeservice.service.pay.PayResult;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import kotlin.jvm.functions.Function1;

/**
 * iTIng接口的处理类
 *
 * <AUTHOR> on 2016/9/6.
 * 新加的接口，请迁移到 BundleRouterHandler 的子类
 */
public class ITingHandler {
    interface ITing {
        boolean handleITing(final Activity activity, final PushModel pm);
    }

    public static final String ITING_NOT_SUPPORT_TIP = "发现新功能哦，请升级App";
    private final static String TAG = ITingHandler.class.getSimpleName();
    boolean isOuterLink = false;
    private Uri mData;

    private long mLastHandleTrackTime;
    private long mOpenSpeechRecognitionTime;


    private static List<Track> parseTrackList(String tracks) {
        if (TextUtils.isEmpty(tracks))
            return null;
        if (tracks.contains("["))
            tracks.replace("[", "");
        if (tracks.contains("]"))
            tracks.replace("]", "");
        String[] trackIds = tracks.split(",");

        List<Track> trackList = new ArrayList<>();
        for (int i = 0; i < trackIds.length; i++) {
            Track sound = new Track();
            sound.setDataId(Long.valueOf(trackIds[i]));
            sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);
            trackList.add(sound);
        }
        return trackList;
    }

    private static int parseInt(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Integer.parseInt(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static long parseLong(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Long.parseLong(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static double parseDouble(String number) {
        if (TextUtils.isEmpty(number))
            return 0;

        try {
            return Double.parseDouble(number);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static boolean parseBoolean(String bool) {
        if (TextUtils.isEmpty(bool))
            return false;

        return Boolean.parseBoolean(bool);
    }

    public boolean handleITing(final Activity activity, Uri data) {
        mData = data;
        if (XmUriRouter.isXmRouteUri(data)) {
            XmUriRouter.route(activity, data.toString());
            return true;
        }

        return handleITing(activity, getPushModelFromUri(data));
    }

    public boolean handleITing(final Activity activity, Uri data, String resPosition) {
        mData = data;
        if (XmUriRouter.isXmRouteUri(data)) {
            XmUriRouter.route(activity, data.toString());
            return true;
        }
        PushModel pushModel = getPushModelFromUri(data);
        pushModel.resPosition = resPosition;
        return handleITing(activity, pushModel);
    }


    private boolean handleITing(Activity activity, Uri uri, PushModel oldPm) {
        mData = uri;

        PushModel newPm = getPushModelFromUri(uri);
        if (newPm != null && oldPm != null) {
            newPm.msgId = oldPm.msgId;
            newPm.isPush = oldPm.isPush;
            newPm.pushUrl = oldPm.pushUrl;
        }
        return handleITing(activity, newPm);
    }


    public boolean handleITing(final Activity activity, final PushModel pm) {
        Logger.d("MainActivity", "handleITing messageType " + pm.messageType
                + ", " + pm.recSrc + ", " + pm.msgId);
        final Bundle bundle;
        MainActivity mainActivity = null;
        HashMap<String, String> xdcsParams = pm.xdcsParams;
        if (activity instanceof MainActivity) {
            mainActivity = (MainActivity) activity;
        }
        if (!pm.keepWeb && null != mainActivity) {
//            mainActivity.closeWebFragment();
            //增加isNeedForbidCloseWebFragment判断解决选中底部tab页面透明页重叠，选中底部tab
            // 的关闭页面可以使用clearAllFragmentFromStacks
            if (!isNeedForbidCloseWebFragment(pm.messageType)) {
                // 如果是从播放页启动的h5页面，现在要跳转iting，那么关闭h5时不能把播放页显示出来，不然就把跳转的页面盖住了
                mainActivity.closeWebFragment(true);
            }
        }
        if (toWebComponent(activity, pm.schema)) {
            return true;
        }
        if (pm.channelJumpOver) {
            XmPlayerManager.getInstance(activity).setChannelJumpOver(true);
        }

        if (!TextUtils.isEmpty(pm.deliveryChannelid) && pm.deliveryChannelid.startsWith("hm_intention_")) {
            TempDataManager.getInstance().saveLong(PreferenceConstantsInHost.KEY_FROM_HM_INTENTION_OPEN, System.currentTimeMillis());
        }
        ITingOutsideSourceManager.checkOutsideSourceParams(activity, pm);

        // 有点恶心,后面可能会改为原生的
        QuickListenTabAbManager.handleDialogHide();
        try {
            switch (pm.messageType) {
                case AppConstants.PAGE_TO_PAY_BACK_NONE:
                    //原生拉起支付宝支付returnUrl回调打开iting 不做任何处理
                    UserVipInfoManager.loopCheckUserVipInfo();
                    break;
                case AppConstants.PAGE_FANS:                    // 打开粉丝页
                case AppConstants.PAGE_FRIEND:                  // 打开找好友页
                case AppConstants.PAGE_HOMEPAGE:                // 打开他人页
                case AppConstants.PAGE_SOUND:                   // 打开声音详情页
                case AppConstants.PAGE_BIND_PHONE:              // 绑定手机页
                case AppConstants.PAGE_ALBUM_PLAY:              // 打开专辑并播放
                case AppConstants.PAGE_MINE_CENTER:             // 打开主播管理中心
                case AppConstants.PAGE_SEARCH_RESULT:           // 打开搜索结果页面
                case AppConstants.PAGE_TO_BOUGHT:               // 已购页面
                case AppConstants.PAGE_TO_EDIT:                 // 个人资料编辑页面
                case AppConstants.PAGE_TO_WALLET:               // 钱包页
                case AppConstants.PAGE_TO_RECHARGE_ITEM:        // 充值页
                case AppConstants.PAGE_TO_DAKA:                 // 大咖读书会
                case AppConstants.PAGE_TO_TINGYOU:              // 听友圈
                case AppConstants.PAGE_TO_FEEDBACK:             // 反馈页
                case AppConstants.PAGE_TO_RADIO_LIST:           // 打开广播列表
                case AppConstants.PAGE_TO_RADIO_PROVINCE_LIST:  // 打开广播省市台列表
                case AppConstants.PAGE_TO_COMMENT_LIST:         // 全部评论页
                case AppConstants.PAGE_TO_OPEN_SEARCH_TRACK:         // 搜索专辑声音
                case AppConstants.PAGE_TO_DOWNLOADED:         // 下载页面
                case AppConstants.PAGE_TO_HISTORY:         // 历史页面
                case AppConstants.PAGE_TO_LIKE_PAGE:         // 打开听单-我喜欢的声音页面
                case AppConstants.PAGE_TO_COMMENT_DETAIL:
                case AppConstants.PAGE_TO_TINGLIST:
                case AppConstants.PAGE_TO_MY_TINGLIST:
                case AppConstants.PAGE_TO_FREE_ALBUM_RATE_DETAIL:
                case AppConstants.PAGE_TO_PAID_ALBUM_RATE_DETAIL:
                case AppConstants.PAGE_TO_ALBUM_RATE:
                case AppConstants.PAGE_TO_ANCHOR_MY_ALL_ALBUMS:
                case AppConstants.PAGE_TO_XIMALAYA_HOT_COMMENT:
                case AppConstants.PAGE_TO_MODIFY_PWD:
                case AppConstants.PAGE_TO_CHILD_PROTECT_FORGET_PED:
                case AppConstants.PAGE_TO_DOWNLOAD_CACHE:
                case AppConstants.PAGE_TO_UNLOCK_PAID_ALBUM:
                case AppConstants.PAGE_TO_KACHA_POST:
                case AppConstants.PAGE_TO_TRACK_PROGRESS:
                case AppConstants.PAGE_TO_PODCAST:
                case AppConstants.PAGE_TO_PODCAST_RANK:
                case AppConstants.PAGE_TO_PODCAST_ALL_CATEGORY:
                case AppConstants.PAGE_TO_COMMENT_THEME_PAGE:
                case AppConstants.PAGE_TO_MY_DETAIL:
                case AppConstants.PAGE_TO_RECOMMEND_BOTTOM_DIALOG:
                case AppConstants.PAGE_TO_REWARD_DIAN_DIALOG:
                case AppConstants.PAGE_TO_SWITCH_ACCOUNT:
                case AppConstants.PAGE_TO_PLAY_ALBUM_LAST_TRACK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoMainBundleAsync(pm, mainActivity);
                    break;
//                case AppConstants.PAGE_IN_COMMENT:// 打开收到的评论页
//                    if (null == mainActivity) {
//                        sendToMainActivityToHandle(activity);
//                        return false;
//                    }
//                    if (UserInfoMannage.hasLogined()) {
//                        AppConstants.MsgCount.sCount_Comments = 0;
//
//                        gotoChatBundleAsync(pm, mainActivity);
//                    }
//                    break;
                case AppConstants.PAGE_LETTER:// 打开私信页（即消息中心）(打开私信 iting://open?msg_type=10) 和
                    // 打开给用户发私信页(给用户发私信 iting://open?msg_type=10&uid=48646617)
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (UserInfoMannage.hasLogined()) {
                        AppConstants.MsgCount.sCount_Letter = 0;

                        if (!DyncAbTestUtil.canShowMessageTab() || pm.uid > 0) {
                            gotoChatBundleAsync(pm, mainActivity);
                        } else {
                            handleITing(activity, Uri.parse("iting://open?msg_type=125&tab=message"));
                        }
                    } else {
                        UserInfoMannage.gotoLogin(mainActivity);
                    }

                    break;
                case AppConstants.PAGE_APP:// 打开主应用
                case 0:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (isOuterLink) {

                        XDCSCollectUtil.getInstanse().statIting(
                                XDCSCollectUtil.APP_NAME_ITING,
                                XDCSCollectUtil.SERVICE_ITING, xdcsParams);
                    }
                    return false;
                case AppConstants.PAGE_ALBUM:// 打开专辑详情页
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm != null && !TextUtils.isEmpty(pm.deliveryChannelid)) {
                        RecommendChannelITing.INSTANCE.getChannelItems(pm, pm.schema, pm.deliveryChannelid);
                    }

                    if (isOuterLink) {

                        XDCSCollectUtil.getInstanse().statIting(
                                XDCSCollectUtil.APP_NAME_ITING,
                                XDCSCollectUtil.SERVICE_ITING, xdcsParams);
                    }
                    //这里默认是免费专辑页
                    final PushModel copy = pm;
                    final MainActivity finalMainActivity = mainActivity;
                    AlbumEventManage.AlbumFragmentOption option =
                            new AlbumEventManage.AlbumFragmentOption();
                    option.schema = pm.schema;
                    option.isOuterIting = pm.isOuterIting;
                    option.activityParams = pm.albumActivityParams;
                    option.fromLiveParams = pm.fromLiveParams;
                    option.tab = pm.tabName;
                    option.materialId = pm.materialId;
                    option.isShowSharePopup = pm.showSharePopup;
                    option.selfShareFrom = pm.self_share_from;
                    option.giftKey = pm.gift_key;
                    option.bookAlbumCover = pm.bookAlbumCover;
                    option.bookPrice = pm.book_price;
                    option.paidPageCode = pm.paidPageCode;
                    option.autoReserve = pm.autoReserve;
                    option.isIgnoreShowPlayPage = pm.isIgnoreShowPlayPage;
                    option.pgcAlbumPageRouteResult = AlbumPgcRouteResult.parse(pm.pgcAlbumPageRouteResult);

                    if (pm.trackId > 0) {
                        option.isAutoPlay = pm.autoPlay;
                        option.trackId = pm.trackId;
                    }
                    option.isAutoPlayForce = pm.isAutoPlayForce;
                    int from = AlbumEventManage.FROM_UNDEFINED;
                    if (!TextUtils.isEmpty(pm.source)) {
                        try {
                            Integer iSource = Integer.valueOf(pm.source);
                            if (iSource != null && iSource > 0) {
                                from = iSource;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if (!TextUtils.isEmpty(pm.vipExtraUrl)) {
                        if (null == option.extraInfo) {
                            option.extraInfo = new HashMap<>();
                        }
                        option.extraInfo.put(BundleKeyConstants.KEY_VIP_EXTRA_URL, pm.vipExtraUrl);
                    }
                    option.isPush = pm.isPush;
                    option.sourceCover = pm.albumCover;
                    option.sourceTitle = pm.albumTitle;
                    option.highlightedComment = pm.highlightedComment;
                    option.highlightedCommentId = pm.commentid;
                    if (pm.openMonthlyTicketDialog) {
                        option.openMonthlyTicketDialog = true;
                        option.initialMonthlyTicketTab = pm.initialMonthlyTicketDialogTab;
                        option.anchorId = pm.anchorId;
                    }
                    int playSource = ConstantsOpenSdk.PLAY_FROM_PUSH;
                    if (pm.playSource > 0) {
                        playSource = pm.playSource;
                    }

                    int finalFrom = from;
                    int finalPlaySource = playSource;
                    AlbumEventManage.startMatchAlbumFragment(pm.albumId, finalFrom, finalPlaySource,
                            pm.mRecSrc, pm.mRecTrack
                            , -1, activity, option);
                    if (option.isFromBook()) {
                        handleAlbumBookShare(activity, option, pm.albumId + "");
                    }
                    VipGiftShareManager.INSTANCE.receiveVipShareGift(pm);
                    break;
                case AppConstants.PAGE_URL:// 带url的，打开wap页
                    if (pm.openElderlyMode && !ElderlyModeManager.getInstance().isElderlyMode()) {
                        if (!TextUtils.isEmpty(pm.tab)) {
                            ElderlyModeManager.getInstance().setSelectTabFromIting(pm.tab);
                        }
                        ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                    } else if (isOuterLink) {
                        XDCSCollectUtil.getInstanse().statIting(
                                XDCSCollectUtil.APP_NAME_ITING,
                                XDCSCollectUtil.SERVICE_ITING, xdcsParams);
                    } else {
                        if (pm.openElderlyMode && !TextUtils.isEmpty(pm.tab) && ElderlyModeManager.getInstance().isElderlyMode()) {
                            ElderlyModeManager.getInstance().setSelectTabFromIting(pm.tab);
                        }
                        if (pm.inWebActivity || null == mainActivity) {
                            //FIXME 用WebActivity跳登录页后用微信登陆,返回异常
                            Intent intent = new Intent(activity, WebActivity.class);
                            intent.putExtra(BundleKeyConstants.KEY_EXTRA_URL, pm.url);
                            activity.startActivity(intent);
                        } else {
                            Bundle bd = null;
                            if (!TextUtils.isEmpty(pm.pageStackTag)) {
                                bd = new Bundle();
                                bd.putString(PushModel.PAGE_STACK_TAG, pm.pageStackTag);
                            }
                            if (!TextUtils.isEmpty(pm.resPosition)) {
                                if (bd == null) {
                                    bd = new Bundle();
                                }
                                bd.putString(ResPositionConstant.RES_POSITION, pm.resPosition);
                            }
                            BaseFragment frag = new NativeHybridFragment.Builder().url(pm.url).showTitle(true).bundle(bd).builder();
                            mainActivity.startFragment(frag);
                        }

                    }
                    break;
                case AppConstants.PAGE_ACTIVITY: // 打开活动页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (isOuterLink) {

                        XDCSCollectUtil.getInstanse().statIting(
                                XDCSCollectUtil.APP_NAME_ITING,
                                XDCSCollectUtil.SERVICE_ITING, xdcsParams);
                    }
                    String url = UrlConstants.getInstanse().getActivitiesHost()
                            + "activity-web/activity/" + pm.activityId;
                    if (pm.trackId > 0) {
                        url = url + "/detail/" + pm.trackId;
                    }

                    // /detail/
                    Intent intent2 = new Intent(mainActivity, WebActivity.class);
                    intent2.putExtra(BundleKeyConstants.KEY_EXTRA_URL, url);
                    mainActivity.startActivity(intent2);
                    break;
                case AppConstants.PAGE_UPLOAD:                  // 20 打开录音上传页面
                case AppConstants.PAGE_SHARE_MY_TRACK:          // 37 分享声音
                case AppConstants.PAGE_TO_RECORD_FRA:           // 48 打开录音页面
                case AppConstants.PAGE_IMPROVE_CLICK_RATE:      // 91 提高页面点击率
                case AppConstants.PAGE_RECORD_DUB_SHOW:         // 87 配音秀选择图片
                case AppConstants.PAGE_EDIT_ALBUM:              // 95 打开专辑编辑页'
                case AppConstants.PAGE_VIDEO_DUB:               // 113 打开双人合作配音
                case AppConstants.PAGE_TO_ANCHOR_CHILD_PROTECT_PAGE: // 115 打开主播下的青少年拦截页
                case AppConstants.PAGE_TO_DUB_MATERIAL:         // 112 打开有特定内容的趣配音素材列表
                case AppConstants.PAGE_TO_AUDIO_COMIC_MATERIAL: // 145 打开ugc我的素材页
                case AppConstants.PAGE_TO_UGC_MY_MATERIAL:      // 150 打开有声漫画素材广场页
                case AppConstants.PAGE_TO_COPYRIGHT_BOOK_RECORD: // 175 打开版权书库录音
                case AppConstants.PAGE_TO_AUDITION:
                case AppConstants.PAGE_TO_OPEN_RECORD_HOME_PAGE:
                case AppConstants.PAGE_TO_RECORD_JOIN_CHAT_ROOM:
                case AppConstants.PAGE_TO_RECORD_TOPIC_PAGE:    // 220 话题录制页
                case AppConstants.PAGE_TO_RECORD_AUDIO_COMIC:   // 222 有声漫画录制页
                case AppConstants.PAGE_TO_CREATE_ALBUM:   // 227 专辑创建页
                case AppConstants.PAGE_TO_UPLOAD_AUDIO_BY_UPLOADID:   // 294 微信音频上传
                case AppConstants.PAGE_TO_RECORD_CREATE_CHAT_ROOM:   // 316 创建录音语聊房
                case AppConstants.PAGE_TO_RECORD_AI_ALBUM_LIST:   // 355 AI文稿专辑列表页
                case AppConstants.PAGE_TO_RECORD_AI_ALBUM:   // 356 AI文稿专辑详情页
                case AppConstants.PAGE_TO_MY_WORKS:
                case AppConstants.PAGE_TO_RECORD_INTERACTIVE_RECORD:
                case AppConstants.PAGE_TO_RECORD_EDIT:
                case AppConstants.PAGE_TO_TTS_PARSE_VOICE:
                case AppConstants.PAGE_TO_AI_BROKER_TOOLS:
                case AppConstants.PAGE_TO_RECORD_UGC_HOME_PAGE:
                case AppConstants.PAGE_TO_RECORD_UGC_READ_PAGE:
                case AppConstants.PAGE_TO_RECORD_FROM_CLOUD_EDIT:
                case AppConstants.PAGE_TO_UGC_STORY:
                case AppConstants.PAGE_IN_COMMENT:// 打开收到的评论页

                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoRecordBundleAsync(pm, mainActivity);
                    break;
                case AppConstants.PAGE_TO_LOGIN_FROM_OUT_SITE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    OutSiteWebLoginFragment outSiteWebLoginFragment = OutSiteWebLoginFragment.newInstance(new OutSiteWebLoginModel(pm.loginCode, pm.title, pm.loginScheme));
                    mainActivity.startFragment(outSiteWebLoginFragment);
                    break;
                case AppConstants.PAGE_TO_DAILY_NEWS_PLAY_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    DailyNewsLogicManager.INSTANCE.getCustomDailyNewsData(pm.toTrackId, pm.channelGroupId, pm.toChannelId, null, new IDataCallBack<CommonTrackList<Track>>() {
                        @Override
                        public void onSuccess(@Nullable CommonTrackList<Track> data) {
                            if (data != null) {
                                int playIndex = 0;
                                if (pm.toTrackId > 0 && data != null && data.getTracks() != null && data.getTracks().size() > 0) {
                                    for (int i = 0; i < data.getTracks().size(); i++) {
                                        if (data.getTracks().get(i).getDataId() == pm.toTrackId) {
                                            playIndex = i;
                                            break;
                                        }
                                    }
                                }
                                int openStatus = pm.openStatus;
                                //  0-直接肚脐眼起播    1-打开播放页  2-打开播放页并打开播放列表
                                if (openStatus == 2) {
                                    PlayListAndHistoryDialogManager.Companion.setAutoOpenStatus(true);
                                }
                                PlayTools.playCommonList(BaseApplication.getMyApplicationContext(), data,
                                        playIndex, openStatus > 0, null);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                    break;
                case AppConstants.PAGE_TO_LIVE_HOME_PAGE:// 52 打开直播首页
                case AppConstants.PAGE_TO_LIVE_HOME_PAGE_SELECTED_CATEGORY_TAB:// 151 打开娱乐派对
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm.segmentId <= 0 && pm.open_type == 0) {
                        // 把主站的 tab 切换到 直播
                        gotoMainBundleAsync(pm, mainActivity);
                    } else {
                        gotoLiveBundleAsync(pm, mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_LIVE_BY_ROOM_ID:
                case AppConstants.PAGE_TO_LIVE_CATEGORY:
                case AppConstants.PAGE_TO_LIVE_ADMIN_LIST:
                case AppConstants.PAGE_TO_LIVE_CREATE:
                case AppConstants.PAGE_TO_RECOMMEND_LIVE:
                case AppConstants.PAGE_TO_ENT_HALL_ROOM_FRAGMENT:
                case AppConstants.PAGE_TO_OPEN_PK_RESULT_DIALOG:
                case AppConstants.PAGE_TO_OPEN_LISTEN_AWARD_DIALOG:
                case AppConstants.PAGE_TO_OPEN_LIVE_FROM_ADVERTISEMENT:
                case AppConstants.PAGE_TO_LIVE_MY_LIVES_FRAGMENT:
                case AppConstants.PAGE_TO_LIVE_PROVIDE_FOR_H5_CUSTOMER_DIALOG:
                case AppConstants.PAGE_TO_OPEN_MY_JOINED_GUARDIAN_PAGE:
                case AppConstants.PAGE_TO_OPEN_GIFT_PANEL:
                case AppConstants.PAGE_TO_OPEN_GIFT_PACKAGE_ITEM:
                case AppConstants.PAGE_TO_OPEN_LIVE_USER_CARD:
                case AppConstants.PAGE_TO_VIDEO_LIVE_ROOM:
                case AppConstants.PAGE_TO_VIDEO_LIVE_LIST:
                case AppConstants.PAGE_TO_CLOSE_VIDEO_FLOAT_WINDOW:
                case AppConstants.PAGE_TO_LIVE_RECHARGE_MODEL:
                case AppConstants.PAGE_TO_CHATROOM_LIVE_ROOM:
                case AppConstants.PAGE_TO_LIVE_GIFT_PACKAGE:
                case AppConstants.PAGE_TO_QUERY_RECOMMEND_LIVE_ROOM:
                case AppConstants.PAGE_TO_PIA_SCRIPT_DETAIL:
                case AppConstants.PAGE_TO_PIA_AUTHOR_DETAIL:
                case AppConstants.PAGE_TO_LIVE_MINE_FUNCTION:
                case AppConstants.PAGE_TO_LIVE_MINE_PAID_LIST:
                case AppConstants.PAGE_TO_SELL_TOGGLE:
                case AppConstants.PAGE_TO_NEW_CHAT_ROOM_CREATE_PAGE:
                case AppConstants.PAGE_TO_LIVE_TYPE_OFFICIAL:
                case AppConstants.PAGE_TO_LIVE_CHANNEL_MORE:
                case AppConstants.PAGE_TO_LIVE_ROOM_LOCATE_PACKAGE:
                case AppConstants.PAGE_TO_LIVE_FOLLOW_LIST:
                case AppConstants.PAGE_TO_LIVE_PGC_ROOM_EDIT:
                case AppConstants.PAGE_TO_LIVE_DISCOVER_PAGE:
                case AppConstants.PAGE_TO_RECOMMEND_HOT_LIVE_LANDING_PAGE: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoLiveBundleAsync(pm, mainActivity);
                    break;
                }
                case AppConstants.PAGE_LOGIN: // 打开登录页面
                    if (pm != null && "1".equals(pm.type)) {
                        UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_FULL_SCREEN
                                , false, pm.openChannel);
                    } else if (pm != null && "2".equals(pm.type)) {
                        UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_HALF_SCREEN
                                , false, pm.openChannel);
                    } else {
                        UserInfoMannage.gotoLogin(activity, LoginByConstants.LOGIN_BY_DEFUALT,
                                false, pm.openChannel);
                    }
                    break;

                case AppConstants.PAGE_MIN: { // 应用最小化
                    activity.moveTaskToBack(true);
                    break;
                }

                case AppConstants.PAGE_APP_PLAY: { // 打开应用并播放
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.playLastPlayedSoundAndCheckIsConnected();
                    break;
                }
                case AppConstants.PAGE_MINE_HOME: { // 打开我的页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.hidePlayFragment(null);
                    if (mainActivity.getManageFragment() != null) {
                        Bundle bundleMine = new Bundle();
                        bundleMine.putBoolean(BundleKeyConstants.KEY_TAB_MINE_TO_MY_SPACE, true);
                        if (pm.toSpacePoint) {
                            bundleMine.putBoolean(BundleKeyConstants.KEY_MY_SPACE_TO_POINT, true);
                            bundleMine.putInt(BundleKeyConstants.KEY_TAB_MINE_MY_TO_SPACE_FROM, pm.sourceFrom);
                        }
                        mainActivity.gotoMySpacePage(bundleMine);
                    }
                    break;
                }
                case AppConstants.PAGE_TO_PERSONAL_QR_CODE_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.uid > 0) {
                        mainActivity.startFragment(AnchorQrCodeFragmentNew.Companion.newInstance(pm.uid));
                    } else if (UserInfoMannage.hasLogined()) {
                        mainActivity.startFragment(AnchorQrCodeFragmentNew.Companion.newInstance(UserInfoMannage.getUid()));
                    }
                    break;
                case AppConstants.PAGE_FEEDBACK: { // 打开反馈页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    break;
                }
                case AppConstants.PAGE_CATEGORY: // 打开分类页
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    final int categoryId = pm.categoryId;
                    final String tagName = pm.tagName;

                    if (categoryId == 0) {
                        return true;
                    }

                    if (categoryId == 33) {
                        mainActivity.startFragment(BoutiqueFraSelectManager.getBoutiqueFragmentH5Version(true));
                    } else {
                        final CategoryContentFragment fragment = CategoryContentFragment
                                .newInstance(categoryId, null, null, null);

                        mainActivity.startFragment(fragment);
                        if (!TextUtils.isEmpty(tagName)) {// 如果传分类标签信息，则跳转到指定的tag标签下
                            fragment.setAction(new CategoryContentFragment.IOpenTagFragment() {

                                @Override
                                public void open() {
                                    fragment.changeTagFragment(tagName);
                                }
                            });
                        }
                    }
                    break;

                case AppConstants.PAGE_OPEN_FLOW: // 打开流量激活页面
                    ToolUtil.gotoFlowActivityPage(activity, true);
                    break;

                case AppConstants.PAGE_TING_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.tingListId > 0 && pm.contentType > 0) {
                        mainActivity.startFragment(NativeHybridFragment.newInstance(MainUrlConstants.getInstanse().getSubjectDetailPageUrl(pm.tingListId + ""), true));
                    } else {
                        gotoMainBundleAsync(pm, mainActivity);
                    }
                    break;
                case AppConstants.PAGE_FIND:    // Find页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.getManageFragment().clearAllFragmentFromStacks();
                    mainActivity.checkRadio(TAB_HOME_PAGE, null);
                    break;
                case AppConstants.PAGE_RANK_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (PushModel.TYPE_GROUP_RANK.equals(pm.type)) {
                        mainActivity.startFragment(CategoryAggregateRankFragmentNew.Companion.newInstance("", pm.clusterId));
                    } else {
                        if (!TextUtils.isEmpty(pm.type)) {
                            mainActivity.checkRank(pm.rankingListId, pm.type, pm.title);
                        }
                    }
                    break;


                case AppConstants.PAGE_TO_AGGREGATE_RANK_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (!TextUtils.isEmpty(pm.schema)) {
                        if (pm.toHome) {
                            AggregateRankUtil.startAggregateRankHomePage();
                        } else {
                            int clusterID = getClusterFromSchema(pm.schema, 0);
                            AggregateRankUtil.startRankDetailPage(clusterID, pm.categoryId, pm.rankingListId);
                        }
                    }

                    break;

                case AppConstants.PAGE_TO_RADIO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoRadioContentFragment(mainActivity);
                    break;
                case AppConstants.PAGE_TO_MEMBER:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    MemberInfo.checkMemberType(activity, null, pm.tingListId);
                    break;
                case AppConstants.PAGE_TO_FEEDBACK_CHAT:// 打开用户反馈页交谈页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    FeedBackManager.initXiaoNeng(mainActivity);
                    FeedBackManager.loginXiaoNeng();
                    FeedBackManager.jumpToChat(mainActivity);
                    break;
                case AppConstants.PAGE_TO_FEEDBACK_LIST:// 打开用户反馈页工单列表
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new FeedBackOrderFragment());
                    break;
                case AppConstants.PAGE_TO_PAYALBUM_COMMENT:// 打开主播付费专辑列表
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(activity);
                    } else {
                        mainActivity.startFragment(AlbumListFragment.newInstanceByPayComment(UserInfoMannage.getUid(), ConstantsOpenSdk.PLAY_FROM_OTHER));
                    }
                    break;
                // 跳转群组列表页面
                case AppConstants.PAGE_TO_GROUP_LIST: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }


                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }

                case AppConstants.PAGE_TO_REDEEM_CODE://跳转到兑换码兑换页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!TextUtils.isEmpty(pm.redeemCode)) {
                        mainActivity.startFragment(NativeHybridFragment.newInstance(MainUrlConstants.getInstanse().getRedeemCodeWebUrl(pm.redeemCode), true));
                    }
                    break;
                case AppConstants.PAGE_TO_BOUTIQUE://跳转付费精品页
                case AppConstants.PAGE_TO_HOTWORD_PAID:
                case AppConstants.PAGE_BOUTIQUE_VIRTUAL://精品页虚拟分类
                    // 精品页被删除，根据产品（余子诚）的要求，对应3个跳转均进入一个H5页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(BoutiqueFraSelectManager.getBoutiqueFragmentH5Version(true));
                    break;

                case AppConstants.PAGE_TO_GROUP_TOPIC: {//群话题
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }


                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }

                case AppConstants.PAGE_TO_GROUP_NOTICE: {//群通知
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }


                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }
                case AppConstants.PAGE_TO_TTS_MAIN: {
                    break;
                }

                case AppConstants.PAGE_TO_GROUP_DETAIL: { // iting 打开群资料页
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }


                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }

                case AppConstants.PAGE_TO_GROUP_DETAIL_PAID: { // 私有 iting， 仅用于付费群支付业务
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    try {
                        BaseFragment fra =
                                Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newGroupDetailFragmentForPaid(pm.groupId);

                        if (fra != null) {
                            mainActivity.startFragment(fra);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }

                case AppConstants.PAGE_TO_RADIO_PLAY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm.from == FROM_DESKTOP_SHORTCUT) {
                        new UserTracking()
                                .setRadioId(pm.radioId)
                                .setId("6511")
                                .statIting(XDCSCollectUtil.SERVICE_OPEN_RADIO_SHORTCUT);
                    }

                    final Radio radio = new Radio();
                    radio.setDataId(pm.radioId);
                    if (XmPlayerManager.getInstance(mainActivity).isConnected()) {
                        PlayTools.PlayLiveRadio(mainActivity, radio, true, null);
                    } else {
                        final MainActivity finalMainActivity4 = mainActivity;
                        HandlerManager.postOnUIThreadDelay(new Runnable() {
                            @Override
                            public void run() {
                                PlayTools.PlayLiveRadio(finalMainActivity4, radio, true, null);
                            }
                        }, 3000);
                    }
                    break;
                case AppConstants.PAGE_USE_ITING:
                    Logger.logToSd("itingHandler: " + AppConstants.PAGE_USE_ITING
                            + ", redirect and url is " + pm.url + ", schema: " + pm.schema + ", pushUrl: " + pm.pushUrl);
                    if (!TextUtils.isEmpty(pm.url)) {
                        handleITing(mainActivity, Uri.parse(pm.url), pm);
                    }
                    break;
                case AppConstants.PAGE_TO_LOCAL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.cityCode != null && pm.cityCode.trim().length() > 0
                            && pm.cityName != null && pm.cityName.trim().length() > 0) {
                        CityModel cityModel = new CityModel(pm.cityCode, pm.cityName);
                        mainActivity.startFragment(CategoryContentFragment.newInstanceForCityCode(cityModel));
                        break;
                    }

                    String local = SharedPreferencesUtil
                            .getInstance(activity).getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_NAME);
                    if (TextUtils.isEmpty(local)) {
                        local = "";
                    } else {
                        local = "听" + local;
                    }
                    mainActivity.startFragment(CategoryContentFragment.newInstanceForCity(local));
                    break;
                case AppConstants.PAGE_TO_DAILY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    mainActivity.startFragment(new NewDailyRecommendFragment());
//                    mainActivity.startFragment(DailyNewsFragment2.newInstance(ConstantsOpenSdk.CHANNEL_TYPE_DAILY_RECOMMEND));
                    break;
                case AppConstants.PAGE_TO_XIMATOUTIAO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    mainActivity.startFragment(new XiMaNoticeFragment());
                    break;
                case AppConstants.PAGE_TO_LISTEN_HEAD_LINE: // 听头条
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    String newsitingUrl = "iting://open?msg_type=74";
                    handleITing(activity, Uri.parse(newsitingUrl));
                    break;
                case AppConstants.PAGE_TO_FEEDBACK_DETAIL: // 工单反馈详情页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(FeedBackOrderDetailFragment.newInstance(pm.sessionId, pm.opGroup, pm.opName, pm.created, pm.processTime, parseInt(pm.status)));
                    break;
                case AppConstants.PAGE_TO_ONE_KEY_LISTEN: // 一键听
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    new XMTraceApi.Trace()
                            .setMetaId(8816)
                            .setServiceId("openIting")
                            .put("itingUrl", pm.schema)
                            .put("from", pm.fromDesktop ? "desktop" : "other")
                            .createTrace();
                    if (TextUtils.isEmpty(pm.schema)) {
                        XDCSCollectUtil.statErrorToXDCS("iting", "has model: " + (pm != null) + " " + Log.getStackTraceString(new Throwable()));
                    }

                    long oneKeyChannelId = pm.onekeyListenChannelId;
                    if (oneKeyChannelId == 0) {
                        oneKeyChannelId = pm.toNewsId;
                    }
                    String dailyNewsSource = pm.isPush ? "push" : pm.source;
                    if (pm.isPush) {
                        pm.sourceType = 2;
                    }
                    if (pm.sourceType == 0) {
                        pm.sourceType = 7;
                    }
                    OneKeyListenTabModel cacheModel = DailyNewsLogicManager.INSTANCE.getEntryFromLocalMap(pm.channelGroupId);
                    if (cacheModel != null) {
                        gotoDailyNewsPage(mainActivity, pm, oneKeyChannelId, dailyNewsSource, cacheModel);
                        break;
                    }
                    gotoDailyNewsPage(mainActivity, pm, oneKeyChannelId, dailyNewsSource, null);
                    break;
                case AppConstants.PAGE_TO_ONE_KEY_LISTEN_DETAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    String newsitingUrl1 = "iting://open?msg_type=74";
                    handleITing(activity, Uri.parse(newsitingUrl1));
                    break;
                case AppConstants.PAGE_CATEGORY_TAB:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    final CategoryContentFragment ccFragment =
                            CategoryContentFragment.newInstance(pm.categoryId, "", "album", null);
                    if (!TextUtils.isEmpty(pm.metadataStr)) {
                        if (pm.fold < 0) {
                            pm.fold = 1;
                        }
                        // 0 折叠 1 展开 默认展开
                        pm.metadataStr = pm.metadataStr + ":" + pm.fold;
                        if (ccFragment.getArguments() != null) {
                            ccFragment.getArguments().putString(CategoryMetadataFragment.BUNDLE_KEY_SELECTED_METADATA
                                    , pm.metadataStr);
                        }
                    }
                    mainActivity.startFragment(ccFragment);
                    //为了与iOS兼容统一，将needToAll进行转换
                    if (pm.needToAll == 1) {
                        pm.title = "全部";
                    }
                    if (!TextUtils.isEmpty(pm.title)) {// 如果传分类标签信息，则跳转到指定的tag标签下
                        ccFragment.setAction(new CategoryContentFragment.IOpenTagFragment() {

                            @Override
                            public void open() {
                                ccFragment.changeTagFragment(pm.title);
                            }
                        });
                    }
                    break;
                case AppConstants.PAGE_CATEGORY_DETAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    bundle = new Bundle();
                    bundle.putBoolean(BundleKeyConstants.KEY_SHOW_HEADERS, true);
                    bundle.putString(BundleKeyConstants.KEY_CATEGORY_ID,
                            String.valueOf(pm.categoryId));
                    bundle.putString(BundleKeyConstants.KEY_TITLE, pm.title);
                    bundle.putInt(BundleKeyConstants.KEY_MODULE_TYPE,
                            MainAlbumMList.MODULE_CALCDIMENSION);
                    bundle.putString(BundleKeyConstants.KEY_METADATAS, pm.metadatas);
                    bundle.putString(BundleKeyConstants.KEY_CALC_DIMENSION, pm.calcDimension);
                    BaseFragment fragment1 = new CategoryDetailFragment();
                    fragment1.setArguments(bundle);
                    mainActivity.startFragment(fragment1);
                    break;
                case AppConstants.PAGE_TO_FEED_RECOMMEND:
                    if (mainActivity == null) {

                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                        gotoMainBundleAsync(pm, mainActivity);
                    } else {
                        gotoFeedBundleAsync(pm, mainActivity);
                    }

                    break;
                //feed 相关
                case AppConstants.PAGE_TO_DYNAMIC_DETAIL:
                case AppConstants.PAGE_TO_TOPIC_DETAIL:
                case AppConstants.PAGE_TO_NEW_TOPIC_DETAIL:
                case AppConstants.PAGE_TO_DYNAMIC_COMMENT_REPLY_DETAIL:
                case AppConstants.PAGE_TO_CREATE_DYNAMIC_FRAGMENT:
                case AppConstants.PAGE_TO_DYNAMIC_SHORT_VIDEO:
//                case AppConstants.PAGE_TO_FEED_RECOMMEND:
                case AppConstants.PAGE_TO_FEED_RECOMMEND_DYNAMIC_POP:
                case AppConstants.PAGE_TO_FEED_CHOOSE_VIDEO:
                case AppConstants.PAGE_TO_FEED_ANCHOR_VIDEO_FRAGMENT:
                case AppConstants.PAGE_COMMUNITY_POST:
                case AppConstants.PAGE_RN_DYNAMIC_DETAIL:
                case AppConstants.PAGE_TO_BOOK_TOPIC_LIST:

                    if (mainActivity == null) {

                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoFeedBundleAsync(pm, mainActivity);

                    break;
                case AppConstants.OPEN_MY_LIKE_V2:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    MainActivity finalMainActivity10 = mainActivity;
                    MyListenRouterUtil.getMyListenBundle(new Router.SimpleBundleInstallCallback() {
                        @Override
                        public void onInstallSuccess(BundleModel bundleModel) {
                            IMyListenFragmentAction fragmentAction = MyListenRouterUtil.getFragAction();
                            if (fragmentAction != null) {
                                finalMainActivity10.startFragment(fragmentAction.newMyLikeV2Fragment());
                            }
                        }
                    });
                    break;
                case AppConstants.PAGE_TO_DAILYSIGN:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    // 废弃
//                    mainActivity.startFragment(DailySignFragment.newInstance(pm.dailyId));
                    break;
                // 拍摄工具相关
                case AppConstants.PAGE_TO_SHOOT_CAPTURE:
                case AppConstants.PAGE_TO_SHOOT_UPLOAD_RECORD:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoShootBundleAsync(pm, mainActivity);
                    break;

                //圈子相关iTing
                case AppConstants.PAGE_COMMUNITY_HOMEPAGE:
                case AppConstants.PAGE_MY_COMMUNITIES:
                case AppConstants.PAGE_TO_QUESTION_DETAIL:
                case AppConstants.PAGE_TO_CREATE_POST:
                case AppConstants.PAGE_CREATE_COMMUNITY:
                case AppConstants.PAGE_COMMUNITY_SQUARE:
                case AppConstants.PAGE_COMMUNITY_RELATED_ALBUM:
                case AppConstants.PAGE_COMMUNITY_RELATED_TRACK:
                case AppConstants.PAGE_TO_My_POST:
                case AppConstants.PAGE_TO_QUESTION_POST:

                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoZoneBundleAsync(pm, mainActivity);

                    break;

                case AppConstants.PAGE_VIDEO_PLAY:
                case AppConstants.PAGE_CARTOON_PLAY_VIDEO:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    long trackId = pm.trackId;
                    VideoPlayParamsBuildUtil.Builder bundleBuilder = new VideoPlayParamsBuildUtil.Builder();
                    if (pm.albumId > 0) {
                        Track lastPlayTrack = XmPlayerManager
                                .getInstance(BaseApplication.getMyApplicationContext())
                                .getLastPlayTrackInAlbum(pm.albumId);
                        if (lastPlayTrack != null && lastPlayTrack.getDataId() != 0) {
                            trackId = lastPlayTrack.getDataId();
                        }
                        bundleBuilder.setAlbumId(pm.albumId);
                        bundleBuilder.setFromVideoChannel("videoPlaylet".equalsIgnoreCase(pm.channel));
                    }
                    Track track = new TrackM();
                    track.setDataId(trackId);
                    Bundle bundle1 =
                            bundleBuilder.setTrack(track).
                                    setTrackId(pm.trackId).build();
                    VideoPlayParamsBuildUtil.startChosenFragment(mainActivity, bundle1, null);
                    break;

                case AppConstants.PAGE_DUBBING_FLOW:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new DubbingRecommendFragment());
                    break;

                case AppConstants.PAGE_DUBBING_PLAY:
                    if (mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    Bundle args = new Bundle();
                    args.putLong(BundleKeyConstantsInMain.KEY_TRACK_ID, pm.trackId);
                    //iting://open?msg_type=88&track_id=xx&sourceType=xx&pageId=xx&trackList=xx
                    // &limit=xx&topicId=xx 旧版本是这样
                    //iting://open?msg_type=88&track_id=xx&sourceType=100&pageId=xx&reqType=5
                    // &moduleId=xx&limit=10 新版本不传trackList，带moduleId
                    args.putInt(BundleKeyConstantsInMain.KEY_DUBBING_SOURCE_TYPE, pm.sourceType);
                    args.putInt(BundleKeyConstantsInMain.KEY_PAGE_ID, pm.pageId);
                    if (pm.trackList != null && (pm.sourceType != AppConstants.TYPE_AGGREGATE_PAGE)) {
                        String[] split = pm.trackList.split("\\|");
                        long[] trackIds = new long[split.length];
                        for (int i = 0; i < split.length; i++) {
                            try {
                                trackIds[i] = Long.parseLong(split[i]);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        args.putLongArray(BundleKeyConstantsInMain.KEY_TRACK_ID_ARRAY, trackIds);
                    }
                    args.putInt(BundleKeyConstantsInMain.KEY_PAGE_NUM, pm.limit);
                    args.putLong(BundleKeyConstants.KEY_TOPIC, pm.topicId);
                    args.putInt(BundleKeyConstantsInMain.KEY_DUBBING_REQUEST_TYPE, pm.reqType);
                    args.putString(BundleKeyConstantsInMain.KEY_DUBBING_MODULE_ID, pm.moduleId);
                    args.putLong(BundleKeyConstantsInMain.KEY_ACTIVITY_ID, pm.activityId);
                    PlayTools.checkToDubShowPPTPlayFragment(mainActivity, args, true, null);
                    break;
                case AppConstants.PAGE_ANCHOR_LIST:
                    boolean hasParams =
                            !TextUtils.isEmpty(pm.title) && AnchorListFragment.isAnchorListType(pm.type);
                    if (!hasParams || mainActivity == null) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    AnchorListFragment anchorListFragment =
                            AnchorListFragment.newInstance(pm.title, pm.category, pm.type,
                                    pm.category,
                                    ConstantsOpenSdk.PLAY_FROM_FIND_ANCHOR);
                    mainActivity.startFragment(anchorListFragment);
                    break;

                case AppConstants.PAGE_WEIKE_COURSE_DETAIL://打开知识直播 微课的详情页面
                case AppConstants.PAGE_WEIKE_HOMEPAGE://打开知识直播首页
                case AppConstants.PAGE_TO_WEIKE_CLASSIFY:
                case AppConstants.PAGE_TO_WEIKE_NEWSCENTER: //打开微课 课程动态页面
                case AppConstants.PAGE_TO_WEIKE_COUPON_COURSELIST: //打开微课可用优惠券课程列表
                case AppConstants.PAGE_TO_WEIKE_HOST_ALLCOURSES: //打开微课业务的个人课程页面
                case AppConstants.PAGE_TO_OPEN_WEIKE_LIVEROOM: //打开微课 直播间页面
                case AppConstants.PAGE_WEIKE_PAID_LIST: {//打开知识微课已购页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openWeikeH5Page(pm, activity);

                    break;
                }


                case AppConstants.PAGE_TO_SELECT_ADDRESS_RESULT:
                    Intent intent = new Intent("intent_filter_action_select_address");
                    intent.putExtra("intent_key_address_user_contact_id", pm.userContactId);
                    LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext()).sendBroadcast(intent);
                    break;
                case AppConstants.PAGE_TO_DYNAMIC_LOTTERY_RESULT:
                    if (UserInfoMannage.hasLogined()) {
                        try {
                            if (!pm.notOpenIfFragmentExist) {
                                BaseFragment2 fragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                        .getFragmentAction()
                                        .newDynamicLotteryResultFragmentFromITing(pm.lotteryId);
                                mainActivity.startFragment(fragment);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        UserInfoMannage.gotoLogin(mainActivity);
                    }
                    break;
                case AppConstants.PAGE_BINDING_ACCOUNT://进入账号绑定
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (UserInfoMannage.hasLogined()) {
                        MainActivity finalMainActivity6 = mainActivity;
                        getLoginFragmentActionCallBack(new IHandleOk() {
                            @Override
                            public void onReady() {
                                BaseFragment2 bindFragment = null;
                                try {
                                    bindFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getBindFragment();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                                if (bindFragment != null) {
                                    finalMainActivity6.startFragment(bindFragment);
                                }
                            }
                        });
                    }
                    break;
                case AppConstants.PAGE_TO_RN:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    RnMobDebugUtil.saveMobData(pm.schema);

                    return toRNComponent(activity, pm.schema, pm.resPosition, pm);
                case AppConstants.PAGE_TO_HOME_VIP:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoVipFragment(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_CITY_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    CityListFragment fragment = new CityListFragment();
                    mainActivity.startFragment(fragment);
                    break;
                case AppConstants.PAGE_TO_PERSONAL_MUSIC_RADIO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    PlayCompleteRecommendManager.getInstance().reportOnStartRecommendForCategory(String.valueOf(pm.categoryId), true);
                    break;
                case AppConstants.PAGE_TO_OPEN_FIND_DUBSHOW_TAB:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    final MainActivity finalMainActivity2 = mainActivity;
                    HandlerManager.postOnUIThread(new Runnable() {
                        @Override
                        public void run() {
                            finalMainActivity2.switchHomeFindingTargetTab(TabFragmentManager.FINDING_TAB_DUBSHOW);

                        }
                    });
                    break;
                case AppConstants.PAGE_TO_MY_DUB_USER_INFO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.uid > 0) {
                        mainActivity.startFragment(DubbingUserInfoFragment.newInstance(pm.uid));
                    }
                    break;
                case AppConstants.PAGE_TO_MY_LISTERN:   // 跳转到iting
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    final MainActivity finalMainActivity1 = mainActivity;
                    boolean isSubscribe = "subscribe".equals(pm.listenTab);
                    boolean isUpdated = "updated".equals(pm.listenTab);
                    if (isSubscribe && pm.sortType > 0 && !MyListenAbUtil.INSTANCE.getSubscribePageVisible()) {
                        MyListenAbUtil.INSTANCE.setSubscribeSortTypeOnce(pm.sortType);
                    }
                    if (MyListenAbUtil.INSTANCE.isKeepSubscribeInFeed() && (isSubscribe || isUpdated)) {
                        if (MyListenAbUtil.INSTANCE.isInABExperimentGroup() && isUpdated) {
                            MyListenUtil.INSTANCE.goPodcastUpdatePage();
                        } else {
                            Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
                                @Override
                                public void onInstallSuccess(BundleModel bundleModel) {
                                    if (!TextUtils.equals(Configure.feedBundleModel.bundleName, bundleModel.bundleName)) {
                                        return;
                                    }
                                    try {
                                        MyListenUtil.INSTANCE.setJump2ChasingUpdateByRecommend(pm.updatedRec);
                                        finalMainActivity1.clearAllFragmentFromManageFragment();
                                        finalMainActivity1.hidePlayFragment(null);
                                        finalMainActivity1.showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);

                                        FragmentManager fragmentManager = finalMainActivity1.getSupportFragmentManager();
                                        BaseFragment2 findingFragment = (BaseFragment2) fragmentManager.findFragmentByTag(String.valueOf(TAB_FINDING));
                                        int findingTabId = isUpdated ? 12 : 11;
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().showFeedTabByItingTabId(findingFragment, findingTabId, 0);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }

                                @Override
                                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                                }

                                @Override
                                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                                }
                            });
                        }
                    } else {
                        HandlerManager.postOnUIThread(new Runnable() {
                            @Override
                            public void run() {
                                if (MyListenAbUtil.INSTANCE.isInABExperimentGroup() && isUpdated) {
                                    MyListenUtil.INSTANCE.goPodcastUpdatePage();
                                } else {
                                    MyListenUtil.INSTANCE.setJump2ChasingUpdateByRecommend(pm.updatedRec);
                                    finalMainActivity1.gotoListen(MyListenUtil.INSTANCE.getTabPosition(pm.listenTab), pm.listenSecTab);
                                }
                            }
                        });
                    }
                    break;
                case AppConstants.PAGE_TO_EVERYDAY_UPDATE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    final MainActivity finalMainActivity5 = mainActivity;
                    if (MyListenAbUtil.INSTANCE.isKeepSubscribeInFeed()) {
                        Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                if (!TextUtils.equals(Configure.feedBundleModel.bundleName, bundleModel.bundleName)) {
                                    return;
                                }
                                try {
                                    finalMainActivity5.clearAllFragmentFromManageFragment();
                                    finalMainActivity5.hidePlayFragment(null);
                                    finalMainActivity5.showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);

                                    FragmentManager fragmentManager = finalMainActivity5.getSupportFragmentManager();
                                    BaseFragment2 findingFragment = (BaseFragment2) fragmentManager.findFragmentByTag(String.valueOf(TAB_FINDING));
                                    Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().showFeedTabByItingTabId(findingFragment, MyListenAbUtil.INSTANCE.isInABExperimentGroup() ? 11 : 12, 0);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                            }
                        });
                    } else {
                        HandlerManager.postOnUIThread(() -> {
                            Bundle bundle11 = new Bundle();
                            bundle11.putInt(BundleKeyConstants.KEY_DESTINATION, MyListenTabEnum.TAB_EVERYDAY_UPDATE.position);
                            bundle11.putBoolean(BundleKeyConstants.KEY_AUTO_PLAY, pm.autoPlay);
                            finalMainActivity5.gotoListen(bundle11);
                        });
                    }
                    break;
                case AppConstants.PAGE_TO_MY_LISTEN_EBOOK_TAB: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(
                            Router.<MyListenActionRouter>getActionRouter(Configure.BUNDLE_MY_LISTEN)
                                    .getFragmentAction()
                                    .newEBookTabFragment()
                    );
                }
                break;
                case AppConstants.PAGE_TO_MY_SUBSCRIBE: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (MyListenAbUtil.INSTANCE.isInABExperimentGroup()) {
                        mainActivity.startFragment(
                                Router.<MyListenActionRouter>getActionRouter(Configure.BUNDLE_MY_LISTEN)
                                        .getFragmentAction().newMySubscribeFragment(true, pm.myListenFrom)
                        );
                    } else {
                        mainActivity.startFragment(
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN)
                                        .getFragmentAction().newMySubscribeFragment(true, pm.myListenFrom)
                        );
                    }
                }
                break;
                case AppConstants.PAGE_TO_GROUP_CHAT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(
                            Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction()
                                    .newGroupChatViewFragment(pm.groupId, "", 0)
                    );
                    break;
                case AppConstants.PAGE_TO_CHILD_PROTECT_FORBID_PLAYPAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    //跳转到儿童保护禁止播放页面
                    ChildProtectManager.handChildProtectIting(pm.from);
                    break;
                case AppConstants.PAGE_TO_NEW_USER_LISTEN:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    mainActivity.startFragment(NewUserListenFragment.newInstance(pm.squareOperationId));
                    break;
                case AppConstants.PAGE_TO_ANCHOR_SKILL_SETTING:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    mainActivity.startFragment(new AnchorSkillSettingFragment());
                    break;
                case AppConstants.PAGE_TO_VOICE_FRIEND:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
//                    Router.getActionByCallback(Configure.BUNDLE_SEA, new Router.IBundleInstallCallback() {
//                        @Override
//                        public void onInstallSuccess(BundleModel bundleModel) {
//                            try {
//                                Router.<SeaActionRouter>getActionRouter(Configure.BUNDLE_SEA).getFunctionAction().queryAuthAndStartFragment(pm.voice_tab_id);
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                        }
//
//                        @Override
//                        public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
//
//                        }
//
//                        @Override
//                        public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
//
//                        }
//
//                    }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
                    break;
                case AppConstants.PAGE_TO_APPLICATION_SETTING:
                    if (mainActivity != null) {
                        IntentUtil.gotoManageApplicationSetting(mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_XIAO_YA:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
//                    final MainActivity finalMainActivityForXiaoYa = mainActivity;
//                    final Runnable startXiaoYaPageTask = new Runnable() {
//                        @Override
//                        public void run() {
////                            try {
////                                Fragment fragment =
////                                        Router.getSoundNetworkActionRouter().getFragmentAction()
////                                                .newFragmentByFid(Configure.SoundNetworkFragmentId.XY_CHANNEL_PAGE_FRAGMENT);
////                                finalMainActivityForXiaoYa.startFragment(fragment);
////                            } catch (Exception e) {
////                                e.printStackTrace();
////                            }
//                        }
//                    };
//                    if (Configure.soundaNetworkBundleModel.needAsync()) {
////                        Router.getSoundNetworkActionRouter(new Router.IBundleInstallCallback() {
////                            @Override
////                            public void onInstallSuccess(BundleModel bundleModel) {
////                                startXiaoYaPageTask.run();
////                            }
////
////                            @Override
////                            public void onInstallError(Throwable t, BundleModel bundleModel) {
////
////                            }
////                        });
//                    } else {
//                        startXiaoYaPageTask.run();
//                    }
                case AppConstants.PAGE_TO_BOUTIQUE_RANK_FRA:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    AggregateRankUtil.gotoNewRankPage2Home();
                    break;
                case AppConstants.PAGE_TO_ALBUM_REFUND:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.albumId > 0) {
                        AlbumRefundInfoFragment albumRefundInfoFragment;
                        if (!TextUtils.isEmpty(pm.type) && "3".equals(pm.type)) {
                            //是训练营专辑
                            albumRefundInfoFragment =
                                    AlbumRefundInfoFragment.getTrainingCampInstance(pm.albumId, 0);
                            mainActivity.startFragment(albumRefundInfoFragment);
                        } else {
                            //是普通专辑
                            albumRefundInfoFragment =
                                    AlbumRefundInfoFragment.getInstance(pm.albumId, 0);
                            mainActivity.startFragment(albumRefundInfoFragment);
                        }
                    }
                    break;
                case AppConstants.PAGE_TO_ALBUM_REFUND_INFO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.albumId > 0 && pm.refundId > 0) {
                        RefundFragment refundFragment = RefundFragment.newInstance(pm.refundId,
                                pm.albumId);
                        mainActivity.startFragment(refundFragment);
                    }
                    break;
                case AppConstants.PAGE_TO_DISABLED_VERIFY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    handDisabledVerifyFragment(mainActivity, pm.uid);
                    break;
                case AppConstants.PAGE_CATEGORY_TAB_ALBUM:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    NewCategoryRecommendFragment newCategoryRecommendFragment =
                            NewCategoryRecommendFragment.newInstance(pm.categoryId, "", NewCategoryRecommendFragment.CONTENT_TYPE_ALBUM);
                    mainActivity.startFragment(newCategoryRecommendFragment);
                    break;
                case AppConstants.PAGE_CATEGORY_TAB_TRACK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    NewCategoryRecommendFragment newCategoryRecommendFragment2 =
                            NewCategoryRecommendFragment.newInstance(pm.categoryId, "", NewCategoryRecommendFragment.CONTENT_TYPE_TRACK);
                    mainActivity.startFragment(newCategoryRecommendFragment2);
                    break;
                case AppConstants.PAGE_COMMENT_SETTING: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new CommentSettingFragment());
                    break;
                }
                case AppConstants.PAGE_TO_WEEKLY_DRAMA:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    WeeklyDramaFragment weeklyDramaFragment =
                            WeeklyDramaFragment.newInstance(String.valueOf(pm.categoryId), pm.title);
                    mainActivity.startFragment(weeklyDramaFragment);
                    break;
                case AppConstants.PAGE_TO_KACHA_NOTE_TIMELINE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    openKachaNoteTimelinePage(pm.albumId, mainActivity);
                    break;
//                case AppConstants.PAGE_TO_PAID_ALBUM_RATE_DETAIL:
//                    if (null == mainActivity) {
//                        sendToMainActivityToHandle(activity);
//                        return false;
//                    }
//                    fragment1 = AlbumCommentDetailFragment.newInstance(pm.albumId, pm.commentid);
//                    mainActivity.startFragment(fragment1);
//                    break;
                case AppConstants.PAGE_TO_OPEN_NOTIFY_SETTING:
                    FireworkManager.checkAndOpenNotify();
                    break;
                case AppConstants.PAGE_TO_CHAT_COMMENT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (UserInfoMannage.hasLogined()) {
                        AppConstants.MsgCount.sCount_Comments = 0;

                        gotoChatBundleAsync(pm, mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_OPEN_CREATE_TINGLIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (UserInfoMannage.hasLogined()) {
                        gotoEditTinglistFragment(pm, mainActivity);
                    } else {
                        UserInfoMannage.gotoLogin(mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_OPEN_TING_COMMENT_DETAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (UserInfoMannage.hasLogined()) {
                        gotoTingCommentFragment(pm, mainActivity);
                    } else {
                        UserInfoMannage.gotoLogin(mainActivity);
                    }
                    break;
                case AppConstants.PAGE_TO_PAY_ALBUM_RANK_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    PayAlbumRankFragment payAlbumRankFragment = new PayAlbumRankFragment();
                    args = new Bundle();
                    args.putInt(PayAlbumRankFragment.ARGS_SELECT, pm.selectSortRuleId);
                    args.putString(PayAlbumRankFragment.ARGS_SORTS, pm.sortRuleGroup);
                    payAlbumRankFragment.setArguments(args);
                    mainActivity.startFragment(payAlbumRankFragment);
                    break;
                case AppConstants.PAGE_TO_OPEN_HOME_PAGE_WITH_TAB:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    String tab = pm.tab;
                    if (TextUtils.isEmpty(tab)) {
                        tab = "推荐";
                    }
                    gotoHomePageWithTab(tab, mainActivity);
                    FreeListenTimeDialogManager.checkFreeListenOutsideStationITing(pm.data);
                    break;
                case AppConstants.SHARE_TO_FREE_LISTEN:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoHomePageWithTab("推荐", mainActivity);
                    ItingFreeListenRewardManager.getInstance().rewardTimeFromShare(pm);
                    break;
                case AppConstants.PAGE_TO_HOME_PAGE_WITH_TAB_ID: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    String tabId = pm.id;
                    if(!TextUtils.isEmpty(pm.moduleTitle) && !TextUtils.isEmpty(pm.tabName)){
                        Bundle switchArg = new Bundle();
                        switchArg.putString("tabName",pm.tabName);//目标页面的参数
                        HomePageSwitchManager.getInstance().notifySwitch(pm.moduleTitle,"",pm.id,switchArg);
                    }
                    gotoHomePageWithTabId(tabId, mainActivity);
                    break;
                }
                case AppConstants.PAGE_TO_ALARM_SETTING:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(AddOrEditAlarmFragment.newInstance(pm.alarmFrom > 0 ? pm.alarmFrom : IMainFragmentAction.FROM_NORMAL));
                    break;
                case AppConstants.PAGE_TO_OPEN_FIND_FRIEND:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    BaseFragment2 frag = new FindFriendFragmentNew();
                    Bundle arg = new Bundle();
                    arg.putBoolean("FROM_CHAT", true);
                    frag.setArguments(arg);
                    mainActivity.startFragment(frag);
                    break;
                case AppConstants.PAGE_TO_CHILD_PLATFORM_BIND:
                    long now = System.currentTimeMillis() / 1000;
                    if (now - pm.timestamp > 60) {
                        CustomToast.showToast("二维码已失效，请重新扫描");
                        return false;
                    }

                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    ChildPlatformAcceptFragment acceptFragment = ChildPlatformAcceptFragment
                            .newInstance(pm.deviceId, pm.uid, pm.name);
                    mainActivity.startFragment(acceptFragment, ChildPlatformAcceptFragment.TAG, 0, 0);
                    break;

                case AppConstants.PAGE_TO_BOOK_READER: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    final MainActivity tempMainActivity = mainActivity;
                    BookUtil.dispatchRedirectReaderJump(pm, new IDataCallBack<Uri>() {
                        @Override
                        public void onSuccess(@Nullable Uri data) {
                            new ITingHandler().handleITing(tempMainActivity, data);
                        }

                        @Override
                        public void onError(int code, String message) {
                            if (pm.code <= 7 || pm.code >= 10) {
                                BookUtil.loadReadBundleAndHandIting(tempMainActivity, pm);
                            } else {
                                long albumId = pm.albumId;
                                long trackId = pm.trackId;
                                int startTime = pm.startTime;
                                int trackDuration = pm.trackDuration;
                                if (pm.code == 8) {
                                    tempMainActivity.startFragment(CardAggregationFragment.Companion.newInstance(albumId, trackId, startTime, trackDuration));
                                } else if (pm.code == 9) {
                                    tempMainActivity.startFragment(CardPublishFragment.Companion.newInstance(albumId, trackId, startTime, trackDuration));
                                }
                            }
                        }
                    });
                    break;
                }

                case AppConstants.PAGE_TO_HANDLE_GROUP_INVITE: { // iting 打开处理群组邀请的页面
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }


                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }

                case AppConstants.PAGE_TO_RECORD_UPLOAD_FRAGMENT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    try {
                        BaseFragment recordChooseUploadFragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordChooseUploadFragment(pm.audioType, pm.prj_id);
                        mainActivity.startFragment(recordChooseUploadFragment);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;

                case AppConstants.PAGE_TO_UNCOMMENTED_ALBUM:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new UncommentedAlbumFragment());
                    break;
                case AppConstants.PAGE_TO_HANDLE_ITING_AB:
                    ITingAbHandle.handleITingAb(activity, pm);
                    break;
                case AppConstants.PAGE_TO_RECOMMEND_ALBUM:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    String newCodes = MmkvCommonUtil.getInstance(activity).getStringCompat(
                            PreferenceConstantsInHost.KEY_CHOOSE_LIKE_SELECTED_ALL_CODES);
                    mainActivity.startFragment(RecommendAlbumCardManager.INSTANCE.
                            getRecommendAlbumCardFraFromIting(newCodes, pm.radioCategoryId, pm.source, pm.albumId, pm.trackId, pm.firstTrackPlayPoint, pm.deliveryChannelid, pm.playCardPageStyle, pm.type, pm.uiType, pm.autoPlay));
                    break;
                case AppConstants.PAGE_TO_NEW_ALBUM_TIME_LIMIT_FREE_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    NewAlbumTimeLimitFreeListFragment newAlbumTimeLimitFreeListFragment
                            = NewAlbumTimeLimitFreeListFragment.getInstance(String.valueOf(pm.activityId));
                    mainActivity.startFragment(newAlbumTimeLimitFreeListFragment);
                    break;
                //消息中心通知页
                case AppConstants.PAGE_TO_NEWSCENTER_NOTIFY_PAGE:
                case AppConstants.PAGE_TO_NEWSCENTER_COMMENT_LIKE_PAGE: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoChatBundleAsync(pm, mainActivity);

                    break;
                }
                case AppConstants.PAGE_TO_DRIVE_MODE_V2_PAGE: {
                    //驾驶模式 iting://open?msg_type=221
                    //我听-驾驶模式 iting://open?msg_type=221&from=listen
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    //DriveModeActivityV2.startDriveModeActivityV2(pm.driveModeFrom);
                    DriveModeActivityV3.startDriveModeActivityV3(pm.driveModeFrom);

                    break;

                }
                case AppConstants.PAGE_TO_CUSTOMIZE_RANK_FRAGMENT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!TextUtils.isEmpty(pm.schema)) {
                        int clusterType = getClusterFromSchema(pm.schema, 0);
                        mainActivity.startFragment(CustomizeRankFragment.getInstance(clusterType, pm.rankingListId));
                    }
                    break;
                case AppConstants.PAGE_TO_GROUP_RANK_SINGLE_FRAGMENT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(CategoryAggregateRankFragment.Companion.newInstance("", pm.clusterId, pm.rankingListId));
                    break;

                case AppConstants.PAGE_TO_KIDS_HOME:
                case AppConstants.PAGE_TO_KIDS_KEY_WORD:
                case AppConstants.PAGE_TO_KIDS_IP_SERIAL:
                case AppConstants.PAGE_TO_KIDS_SERIAL_DETAIL:
                case AppConstants.PAGE_TO_KIDS_TRACK_PLAYING:
                case AppConstants.PAGE_TO_KIDS_PB_PLAYING:
                case AppConstants.PAGE_TO_KIDS_SINGLE_KEY_WORD:
                case AppConstants.PAGE_TO_KIDS_SINGLE_RANK:
                case AppConstants.PAGE_TO_KIDS_SUBSCRIBE:
                case AppConstants.PAGE_TO_KIDS_HISTORY:
                case AppConstants.PAGE_TO_KIDS_READING_LIST:
                case AppConstants.PAGE_TO_KIDS_READING: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoKidsBundleAsync(pm, mainActivity);
                    break;
                }

                case AppConstants.PAGE_TO_H5_DIALOG_OPERATION:
                    if (!TextUtils.isEmpty(pm.operation)) {
                        try {
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().operateH5Dialog(pm.operation, pm.targetDialog);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                case AppConstants.PAGE_TO_PREFFERED_CONTENT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new PreferredContentFragment());
                    break;
                case AppConstants.PAGE_TO_ELDERLY_MODE: {
                    //老年模式 iting://open?msg_type=288
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    MainActivity finalElderlyMainActivity = mainActivity;
                    if (!TextUtils.isEmpty(pm.target) && !ElderlyModeManager.getInstance().isElderlyMode()) {
                        ElderlyModeManager.getInstance().setTargetArgs(pm.target);
                    }
                    mainActivity.clearAllFragmentFromManageFragment();
                    if (!TextUtils.isEmpty(pm.tab)) {
                        ElderlyModeManager.getInstance().setSelectTabFromIting(pm.tab);
                    }
                    if ("album".equals(pm.elderlyAction)) {
                        if (ElderlyModeManager.getInstance().isElderlyMode() && pm.albumId > 0) {
                            AlbumEventManage.startMatchAlbumFragment(pm.albumId,
                                    AlbumEventManage.FROM_OUTER_LINK, ConstantsOpenSdk.PLAY_FROM_NONE,
                                    null, null, -1, mainActivity);
                        } else {
                            ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                        }
                    } else if ("playpage".equals(pm.elderlyAction)) {
                        if (ElderlyModeManager.getInstance().isElderlyMode() && pm.trackId > 0) {
                            if (pm.isVideoPage == 1) {
                                String videoScheme = "iting://open?msg_type=94&bundle=rn_elder_mode"
                                        + "&jump_type=" + pm.jumpType
                                        + "&album_id=" + pm.albumId
                                        + "&track_id=" + pm.trackId
                                        + "&isVideoPage=" + pm.isVideoPage;
                                if (!TextUtils.isEmpty(pm.coverUrl)) {
                                    videoScheme = videoScheme + "&coverUrl=" + pm.coverUrl;
                                }
                                handleITing(activity, Uri.parse(videoScheme));
                            } else {
                                PlayTools.playTrackByCommonList(activity, pm.trackId, ConstantsOpenSdk.PLAY_FROM_PUSH, null, true);
                            }
                        } else {
                            ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                        }
                    } else if ("listenlist".equals(pm.elderlyAction)) {
                        if (ElderlyModeManager.getInstance().isElderlyMode() && pm.albumId > 0) {
                            MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                                IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                                if (fragAction != null && finalElderlyMainActivity != null) {
                                    BaseFragment2 listenLIstFrag = fragAction.newTingListDetailFragment(pm.albumId, pm.oneKeyNormalExtra, pm.trackId, pm.toAlbumId);
                                    finalElderlyMainActivity.startFragment(listenLIstFrag);
                                }
                            });
                        } else {
                            ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                        }
                    } else {
                        if (pm.fromToggle) {
                            Logger.logToFile("ElderlyModeManager fromToggle = " + ElderlyModeManager.getInstance().isElderlyMode());
                            ElderlyModeManager.getInstance().toggleElderlyMode(true);
                        } else {
                            ElderlyModeManager.getInstance().openElderlyMode(true);
                        }
                    }
                    break;
                }
                case AppConstants.PAGE_TO_HISTORY_PREFFERED:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new HistoryPreferredFragment());
                    break;
                case AppConstants.PAGE_TO_NEW_PUBLISH:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(NewPublishFragment.Companion.newInstance(pm.rankingListId));
                    break;
                case AppConstants.PAGE_TO_EPUB_READER:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    BookUtil.loadReadBundleAndHandIting(mainActivity, pm);
                    break;

                case AppConstants.PAGE_TO_XIAO_YA_SCAN_BIND: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mainActivity);
                        return false;
                    }

                    Router.getActionByCallback(Configure.BUNDLE_SMARTDEVICE, new Router.IBundleInstallCallback() {
                        @Override
                        public void onInstallSuccess(BundleModel bundleModel) {
                            if (TextUtils.equals(bundleModel.bundleName, Configure.smartDeviceBundleModel.bundleName)) {

                                try {
                                    if (!TextUtils.isEmpty(pm.url)) {
                                        Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction().getScanUrl(pm.url);
                                    }
                                } catch (Exception e) {
                                    CustomToast.showToast(MainApplication.getMyApplicationContext()
                                            .getString(com.ximalaya.ting.android.host.R.string.host_warning_no_dlna_plugin));
                                    e.printStackTrace();
                                }
                            }
                        }

                        @Override
                        public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                            CustomToast.showToast(MainApplication.getMyApplicationContext()
                                    .getString(com.ximalaya.ting.android.host.R.string.host_warning_no_dlna_plugin));
                        }

                        @Override
                        public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                        }

                    });
                    break;
                }

                case AppConstants.PAGE_TO_SMART_DEVICE: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mainActivity);
                        return false;
                    }

                    MainActivity finalMainActivity7 = mainActivity;
                    switch (pm.target) {
                        case "xiaoyalibrary": {
                            gotoQRCodeScanFragment(pm, finalMainActivity7);
                            break;
                        }

                        case "nano": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startBleWifiConnectActivity(mainActivity, pm.nanoType);
                            break;
                        }

                        case "suicheting": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startSmartDeviceMainActivity(mainActivity, "suicheting");
                            break;
                        }

                        case "rubikcube": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startRubikCubeActivity(mainActivity, "rubikcube");
                            break;
                        }

                        case "luna": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startSmartDeviceMainActivity(mainActivity, "luna");
                            break;
                        }

                        case "twscare": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startTwsBindActivity(mainActivity, "care");
                            break;
                        }

                        case "twslite": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startTwsBindActivity(mainActivity, "lite");
                            break;
                        }
                        case "aiglasses": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startTwsBindActivity(mainActivity, "aiglasses");
                            break;
                        }
                        case "magicbox": {
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startTwsBindActivity(mainActivity, "magicbox");
                            break;
                        }
                        case "twslitedetail": {
                            // 添加设备跳转tws基础设置页面
                            // pm.target.twsId
                            Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE)
                                    .getFragmentAction().startTwsBaseSetActivityNew(mainActivity, pm.twsId);
                            break;
                        }

                        case "twsautolisten":
                            // 由入耳即听跳转基础设置页面
                            try {
                                Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction().judgeGoToTwsBaseSetPage(activity);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case "twslistenmode":
                            // 跳转听书模式
                            try {
                                Router.<SmartDeviceActionRouter>getActionRouter(Configure.BUNDLE_SMARTDEVICE).getFunctionAction().judgeGoToTwsListenMode(activity);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        default: {
                            Logger.d(TAG, MainApplication.getMyApplicationContext()
                                    .getString(com.ximalaya.ting.android.host.R.string.host_smart_device_down_failed));
                            break;
                        }
                    }

                    break;
                }

                case AppConstants.PAGE_TO_CATEGORY_LIST: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    int tabIndex = 0;
                    try {
                        tabIndex = Integer.parseInt(pm.tab);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (tabIndex == 0) {
                        mainActivity.startFragment(new HomePageTabAndChannelListNewFragmentDialog(),
                                R.anim.main_slide_in_top, R.anim.main_slide_out_top);
                    } else {
                        mainActivity.startFragment(new CategoryListFragment());
                    }
                    break;
                }
                case AppConstants.PAGE_TO_TAG_CHANNEL: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    long channelId = pm.channelId;
                    int tabIndex = -1;
                    try {
                        if (!TextUtils.isEmpty(pm.tab)) {
                            tabIndex = Integer.parseInt(pm.tab);
                        }
                    } catch (Exception e) {
                        Logger.e(e);
                    }
                    int tabId = -1;
                    try {
                        if (!TextUtils.isEmpty(pm.selTabId)) {
                            tabId = Integer.parseInt(pm.selTabId);
                        }
                    } catch (Exception e) {
                        Logger.e(e);
                    }
                    // iting://open?msg_type=280&metadataValueId=931408&pageType=new&userType=anchor&startPlayTrackId=xxx
                    if ("new".equals(pm.pageType)) {
                        boolean tob = "anchor".equals(pm.userType);
                        if (tob) {
                            mainActivity.startFragment(TagContentFragment.Companion.newInstance(pm.metadataValueId, tabIndex, channelId, tabId, tob, pm.startPlayTrackId));
                        } else {
                            mainActivity.startFragment(TopicDetailFragment.Companion.newInstance(pm.metadataValueId, pm.startPlayTrackId));
                        }
                    } else {
                        mainActivity.startFragment(TagChannelContentFragment.Companion.newInstance(pm.metadataValueId, tabIndex, channelId, tabId, pm.templateCategoryId, pm.metadatas));
                    }
                    break;
                }
                case AppConstants.PAGE_TO_ELDERLY_RANK_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    //iting://open?msg_type=297&clusterId=123
                    if (pm.clusterId > 0) {
                        ElderlyModeManager.getInstance().jump2ElderlyRankFragment(String.valueOf(pm.clusterId));
                    }
                    break;
                case AppConstants.PAGE_TO_SINGLE_RANK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm.rankingListId > 0) {
                        mainActivity.startFragment(SingleRankFragment.Companion.newInstance(pm.rankingListId));
                    }

                    break;
                case AppConstants.PAGE_TO_NEW_PRODUCT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(NewProductFragment.Companion.newInstance(pm.categoryId));

                    break;

                case AppConstants.PAGE_TO_MINE_MODULE_ENTRANCE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(MineModuleEntranceListFragment.Companion.newInstance(Integer.parseInt(pm.moduleId)));
                    break;

                case AppConstants.PAGE_TO_NEW_USER_RANK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new NewUserRankFragment());
                    break;

                case AppConstants.PAGE_TO_ELDERLY_ALBUM:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (ElderlyModeManager.getInstance().isElderlyMode() && pm.albumId > 0) {
                        AlbumEventManage.startMatchAlbumFragment(pm.albumId,
                                AlbumEventManage.FROM_OUTER_LINK, ConstantsOpenSdk.PLAY_FROM_NONE,
                                null, null, -1, mainActivity);
                    } else {
                        if (!TextUtils.isEmpty(pm.tab)) {
                            ElderlyModeManager.getInstance().setSelectTabFromIting(pm.tab);
                        }
                        ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                    }

                    break;

                case AppConstants.PAGE_TO_ELDERLY_PLAY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (ElderlyModeManager.getInstance().isElderlyMode() && pm.trackId > 0) {
                        if (pm.isVideoPage == 1) {
                            String videoScheme = "iting://open?msg_type=94&bundle=rn_elder_mode"
                                    + "&jump_type=" + pm.jumpType
                                    + "&album_id=" + pm.albumId
                                    + "&track_id=" + pm.trackId
                                    + "&isVideoPage=" + pm.isVideoPage;
                            if (!TextUtils.isEmpty(pm.coverUrl)) {
                                videoScheme = videoScheme + "&coverUrl=" + pm.coverUrl;
                            }
                            handleITing(activity, Uri.parse(videoScheme));
                        } else {
                            PlayTools.playTrackByCommonList(activity, pm.trackId, ConstantsOpenSdk.PLAY_FROM_PUSH, null, true);
                        }
                    } else {
                        if (!TextUtils.isEmpty(pm.tab)) {
                            ElderlyModeManager.getInstance().setSelectTabFromIting(pm.tab);
                        }
                        ElderlyModeManager.getInstance().openElderlyMode(true, pm.schema);
                    }

                    break;
                case AppConstants.PAGE_TO_EDIT_CHILD_INFO:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(EditChildInfoFragment.Companion.newInstance(
                            Integer.valueOf(pm.type), pm.text, pm.url));
                    break;
                case AppConstants.PAGE_TO_FAMILY_RECOMMEND_DETAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mainActivity);
                        return false;
                    }
                    mainActivity.startFragment(FamilyRecommendAlbumFragment.newInstance(pm.tabName));
                    break;

                case AppConstants.OPEN_XIAOMAN_GAME:
                    gotoThirdGameWeb(pm, null, null, false);
                    break;
                case AppConstants.OPEN_BAO_QU_GAME:
                    gotoThirdGameWeb(pm, pm.gameToken, pm.gameUid, true);
                    break;
                case AppConstants.DEAL_INSTRUCTION_TO_VOICE_WAKE:
                    Activity topActivity = BaseApplication.getTopActivity();

                    IVoiceWakeInstructionCallback callback = null;
                    if (topActivity instanceof DriveModeActivityV3) {
                        callback = (IVoiceWakeInstructionCallback) topActivity;
                    } else if (topActivity != null) {
                        callback = new VoiceWakeInstructionGlobalHandler(topActivity);
                    }
                    VoiceWakeInstructionHandler.INSTANCE.handleIting(callback, pm.subType, pm.value, pm.localPeriph, pm.localDialogId, pm.schema, pm.url);
                    break;
                case AppConstants.PAGE_TO_VIP_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (pm.vipTabType != 1 && VipHostUtil.enableUniversalCombineVipDialog()) {
                        ITingExtraUriParamsManager.bindITingUriScheme(pm.schema, new Function1<String, HashMap<String, String>>() {
                            @Override
                            public HashMap<String, String> invoke(String s) {
                                HashMap<String, String> result = new HashMap<>();
                                try {
                                    Uri data = Uri.parse(pm.schema);
                                    String sourceValue = data.getQueryParameter("orderSourceValue");
                                    if (!TextUtils.isEmpty(sourceValue)) {
                                        result.put("orderSourceValue", sourceValue);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return result;
                            }
                        });
                        //大会员，走新的弹窗
                        VipHostUtil.showUniversalCombineVipDialog(pm.schema, pm.albumId, pm.locatedTab);
                        break;
                    }
                    break;
                case AppConstants.PAGE_TO_SHOW_PLATINUM_VIP_VOUCHER_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.albumId > 0) {
                        if (UserInfoMannage.hasLogined()) {
                            PlatinumVipCouponConsumeDialog.showDialog(pm.albumId, mainActivity.getSupportFragmentManager());
                        } else {
                            UserInfoMannage.gotoLogin(activity);
                        }
                    }
                    break;
                case AppConstants.PAGE_TO_SHOW_PLATINUM_VIP_AHEADLISTEN_SCHEDULE_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    try {
                        MainActivity finalMainActivity3 = mainActivity;
                        Router.getActionByCallback(Configure.BUNDLE_VIP, new Router.IBundleInstallCallback() {

                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                if (pm.albumId > 0) {
                                    try {
                                        Router.<VipActionRouter>getActionRouter(Configure.BUNDLE_VIP)
                                                .getFunctionAction().showPlatinumAheadListenUpdateScheduleDialog(pm.albumId, pm.fromSource, finalMainActivity3.getSupportFragmentManager());
                                    } catch (Throwable throwable) {
                                        throwable.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                            }
                        });

                    } catch (Throwable throwable) {
                        if (ConstantsOpenSdk.isDebug) {
                            throw throwable;
                        }
                    }
                    break;
                case AppConstants.PAGE_TO_ALBUM_RESERVATION_LIST_NEW:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.reservationId >= 0) {
                        try {
                            MainActivity finalMainActivity3 = mainActivity;
                            Router.getActionByCallback(Configure.BUNDLE_VIP, new Router.IBundleInstallCallback() {

                                @Override
                                public void onInstallSuccess(BundleModel bundleModel) {
                                    try {
                                        Router.<VipActionRouter>getActionRouter(Configure.BUNDLE_VIP)
                                                .getFragmentAction().startAlbumReservationListFragmentNew(finalMainActivity3, pm.reservationId, pm.tabId);
                                    } catch (Throwable throwable) {
                                        throwable.printStackTrace();
                                    }
                                }

                                @Override
                                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                                }

                                @Override
                                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                                }
                            });

                        } catch (Throwable throwable) {
                            if (ConstantsOpenSdk.isDebug) {
                                throw throwable;
                            }
                        }
                    }
                    break;
                case AppConstants.PAGE_TO_SEARCH_RESULT_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoSearchResultPage(mainActivity, pm.keyword, pm.autoPlay, pm.focusTab);
                    break;
                case AppConstants.PAGE_TO_SEARCH_RESULT_PAGE_1:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.source != null && "widget".equals(pm.source)) {
                        mainActivity.getManageFragment().clearAllFragmentFromStacks();
                    }
                    gotoSearchResultPage(mainActivity, pm.text, pm.autoPlay, pm.channel, pm.keywordId, pm.focusTab);
                    break;
                case AppConstants.PAGE_TO_ANCHOR_TRACK_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    Fragment trackListFragment = TrackListFragment.newInstanceByAnchor(pm.uid, "全部声音");
                    mainActivity.startFragment(trackListFragment);
                    break;
                case AppConstants.PAGE_TO_AUDIO_DOC:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.trackId > 0) {
                        PlayableModel playableModel = XmPlayerManager.getInstance(activity).getCurrSound();
                        if (!(playableModel instanceof Track) || playableModel.getDataId() != pm.trackId) {
                            PlayTools.playTrackByCommonList(activity, pm.trackId, ConstantsOpenSdk.PLAY_FROM_OTHER, null, false);
                        }
                    }
                    mainActivity.startFragment(new PlayManuscriptNotInTabFragment());
                    break;

                case AppConstants.OPEN_AD_DOWNLOAD_PAGE:
                    if (mainActivity != null) {
                        Bundle responseIdBundle = new Bundle();
                        responseIdBundle.putLong(BundleKeyConstants.KEY_AD_DOWNLOAD_RESPONSE_ID, pm.responseId);
                        mainActivity.startFragment(AdDownloadListFragment.class, responseIdBundle);
                    }
                    break;
                case AppConstants.OPEN_CCB_SDK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    goToCcbSdk(mainActivity, pm);
                    break;

                case AppConstants.PAGE_TO_PLC_SHARE_DIALOG:
                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mainActivity);
                        return false;
                    }
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (pm.trackId > 0) {
                        ShareUtilsInMain.openPLCShareDialogFromITing(activity, pm.trackId);
                    }
                    break;

                case AppConstants.PAGE_TO_LOAN_SDK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoTLLoanSdk(mainActivity, pm.mgm_callback_url);
                    break;
                case AppConstants.PAGE_TO_RECOMMEND_NEW_USER_RANK:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(RecommendNewUserRankFragment.Companion.newInstance(
                            pm.rankingListId, pm.newUserOperationAb));
                    break;
                case AppConstants.PAGE_TO_MINE_ALL_SERVICES:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (ConfigureCenter.getInstance().getBool("toc", "all_service_use_rn", false)) {
                        String iTing = "iting://open?msg_type=94&bundle=mineallservice";
                        handleITing(activity, Uri.parse(iTing));
                    } else {
                        mainActivity.startFragment(AllServiceFragment.Companion.newInstance());
                    }
                    break;

                case AppConstants.PAGE_TO_MYCLUB_CHAT_ROOM:
                case AppConstants.PAGE_TO_MYCLUB_NEW_CHAT_ROOM:
                    try {
                        Uri mcUri = Uri.parse(pm.schema);
                        trackMyclubSource(mcUri);

                        String preUrl = mcUri.getQueryParameter("pre_url");
                        if (!TextUtils.isEmpty(preUrl)) {
                            new ITingHandler().handleITing(mainActivity, Uri.parse(preUrl));
                        }
                    } catch (Throwable ignored) {
                        if (ConstantsOpenSdk.isDebug) {
                            ignored.printStackTrace();
                        }
                    }
                    gotoMyclubChatRoom(mainActivity, pm.liveRoomId, pm.purchased);
                    break;
                case AppConstants.START_MYCLUB_OTHER_TASKS:
                    try {
                        trackMyclubSource(Uri.parse(pm.schema));
                    } catch (Throwable ignored) {
                        if (ConstantsOpenSdk.isDebug) {
                            ignored.printStackTrace();
                        }
                    }
                    gotoMyclubScheduleEdit(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_CHOOSE_LIKE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(ChooseLikeUtil.newInstance(true, false, false, CustomizeFragment.FROM_OTHER));
                    break;
                case AppConstants.ITING_KEY_EXIT_APP:
                    HandlerManager.postOnUIThreadDelay(new Runnable() {
                        @Override
                        public void run() {
                            Activity mainActivity = MainApplication.getMainActivity();
                            if (mainActivity instanceof MainActivity) {
                                ((MainActivity) mainActivity).finishMy();
                            }
                        }
                    }, 1000);
                    break;

                case AppConstants.PAGE_TO_MORE_CALABASH:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    RecommendCalabashFragment calabashFragment = RecommendCalabashFragment.getInstance(pm.calabashId, pm.moreIndexId);
                    mainActivity.startFragment(calabashFragment);
                    break;

                case AppConstants.PAGE_TO_TING_LIST_GROUP_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    TingListGroupFragment tingListFragment;
                    if (pm.tingListGroupId > 0) {
                        tingListFragment = TingListGroupFragment.getInstance(pm.tingListGroupId, pm.opType);
                        mainActivity.startFragment(tingListFragment);
                    } else if (pm.scenceId >= 0) {
                        tingListFragment = TingListGroupFragment.getInstance(true, pm.scenceId, pm.opType);
                        mainActivity.startFragment(tingListFragment);
                    }
                    break;

                case AppConstants.PAGE_TO_RADIO_SQUARE_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    gotoRadioContentFragmentNew(mainActivity, pm.liveRadio);
                    break;

                case AppConstants.OPEN_WECHAT_RESERVE_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    WeChatReserveDialog dialog = WeChatReserveDialog.Companion.getInstance(pm.title, pm.content, pm.receive, pm.thirdPartyId, BusinessTypeEnum.TYPE_MY_CLUB.type, pm.toThirdpartyId);
                    dialog.show(mainActivity.getSupportFragmentManager(), "WhChatReserveDialog");
                    break;

                case AppConstants.START_SOUND_EFFECT_CHOOSE_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    ChooseTrackSoundEffectDialog.openByIting();
                    break;
                case AppConstants.PAGE_TO_NEW_AGGREGATE_RANK_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!TextUtils.isEmpty(pm.schema)) {
                        AggregateRankUtil.startRankDetailPageNew(pm.tabId, pm.categoryId, pm.rankingListId,
                                pm.source, pm.singleRank, pm.scene_id, pm.prefixContentIds, pm.recDefaultFlag, mainActivity);
                    }
                    break;

                case AppConstants.MONTHLY_VOTE_LIST_PAGE:
                    if (mainActivity != null) {
                        mainActivity.startFragment(
                                new MonthlyVoteListFragment("guide".equals(pm.source))
                        );
                    }
                    break;

                case AppConstants.PAGE_TO_TALK_PANEL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openTalkPanel(mainActivity, pm);
                    break;

                case AppConstants.PAGE_TO_VOTE_PANEL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openVotePanel(mainActivity, pm);
                    break;

                case AppConstants.SIGN_TO_VOTE_PANEL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openMonthlyVotePanel(mainActivity, null);
                    break;

                case AppConstants.PAGE_LOCK_SCREEN_SETTING:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    mainActivity.startFragment(LockScreenSettingFragment.newInstance(true));
                    break;
                case AppConstants.PAGE_TO_CHANNEL_FRAGMENT_PREVIEW:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    Bundle channelBundle = new Bundle();
                    channelBundle.putBoolean(BundleKeyConstants.KEY_CATEGORY_PREVIEW, pm.preview);
                    channelBundle.putBoolean(BundleKeyConstants.KEY_CATEGORY_SHOW_TITLE, true);
                    channelBundle.putString(BundleKeyConstants.KEY_CATEGORY_CATEGORY_TITLE, pm.categoryTitle);
                    channelBundle.putString(BundleKeyConstants.KEY_CATEGORY_ID, String.valueOf(pm.categoryId));
                    channelBundle.putString(BundleKeyConstants.KEY_CONTENT_TYPE, "album");
                    channelBundle.putInt(BundleKeyConstants.KEY_FLAG, CategoryContentFragment.FLAG_CATEGORY);
                    channelBundle.putBoolean(BundleKeyConstants.KEY_IS_FROM_HOMEPAGE, true);
                    mainActivity.startFragment(ChannelFragment.Companion.getInstance(channelBundle));
                    break;
                case AppConstants.PAGE_TO_CHANNEL_FRAGMENT_CHILD_CATEGORY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(ChannelChildCategoryFragment.Companion.newInstance(pm.categoryId));
                    break;
                case AppConstants.PAGE_TO_MAILBOX_MAIL_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(MailBoxMailListFragment.Companion.newInstance(pm.recordId));
                    break;
                case AppConstants.PAGE_TO_MAILBOX_MAIL_DETAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(MailDetailFragment.Companion.newInstance(new Mail(
                            pm.commentid, pm.recordId, "", 0, true, "", "", 0, false, 0, 0, 1, null)));
                    break;
                case AppConstants.PAGE_TO_MAILBOX_WRITE_MAIL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(MailBoxWriteMailFragment.Companion.newInstance(pm.recordId, parseLong(pm.type)));
                    break;
                case AppConstants.PAGE_TO_MY_MAIL_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!UserInfoMannage.hasLogined()) {
                        UserInfoMannage.gotoLogin(mainActivity);
                        return false;
                    }
                    mainActivity.startFragment(new MyMailListFragment());
                    break;
                case AppConstants.PAGE_TO_INTERACTIVE_CARD_PANNEL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openInteractiveCardPanel(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_TEXT_TO_SOUND_AND_PLAY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    if (TextUtils.isEmpty(pm.requestId)) {
                        return false;
                    }

                    ReadBundleHelper.requestArticleAndOpenRead(pm.requestId);
                    break;
                case AppConstants.PAGE_TO_MONTH_TICKET_CONTRIBUTE_RANK_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    long albumId = null == pm ? 0 : pm.albumId;
                    if (ChildProtectManager.isChildProtectOpen(mainActivity)) {
                        ChildProtectManager.showFeatureCannotUseToast();
                    } else {
                        mainActivity.startFragment(MonthTicketContributeRankFragment.getInstance(albumId, pm.anchorId, 0L));
                    }
                    break;
                case AppConstants.PAGE_TO_KID_STORY:
                case AppConstants.PAGE_TO_KID_ACCOMPANY:
                case AppConstants.PAGE_TO_KID_VIP:
                case AppConstants.PAGE_TO_KID_ALL_COLUMN:
                case AppConstants.PAGE_TO_KID_LIMIT_FREE:
                case AppConstants.PAGE_TO_KID_CONTENT_RECOMMEND:
                case AppConstants.PAGE_TO_KID_TEEN_RESET_PWD_PAGE:
                case AppConstants.PAGE_TO_KID_PUT_TO_BED_SETTING:
                case AppConstants.PAGE_TO_KID_AI_SOUNDE:
                case AppConstants.PAGE_TO_KID_KNOWLEDGE:
                case AppConstants.PAGE_TO_KID_CONTENT_QUERY:
                case AppConstants.PAGE_TO_KID_APP:
                case AppConstants.PAGE_TO_KID_NEW_USERS_ONBOARDING:
                case AppConstants.PAGE_TO_KID_CHANNEL_PAGE:
                case AppConstants.PAGE_TO_KID_COMMON_CHANNEL:
                case AppConstants.PAGE_TO_KID_RECOMMEND_PAGE:
                case AppConstants.PAGE_TO_KID_IP_COLLECTION:
                case AppConstants.PAGE_TO_KID_DUBBING_RECORD:
                case AppConstants.PAGE_TO_KID_SEGMENT_DETAIL:
                case AppConstants.PAGE_TO_KID_DUBBING_WORK:
                case AppConstants.PAGE_TO_KID_MY_DUBBING:
                case AppConstants.PAGE_TO_KID_CHILD_MODE:
                case AppConstants.GOTO_KID_TING_LIMIT_FREE_PAGE:
                    gotoKidBundleAsync(pm, mainActivity);
                    break;
                case AppConstants.PAGE_TO_POD_CAST_IMMERSIVE:
                    goToPodCastImmersivePage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_MASTER_VIP_PURCHASE_DIALOG:
                    //大师课会员购买弹窗
                    try {

                        MainActivity finalMainActivity3 = mainActivity;
                        Router.getActionByCallback(Configure.BUNDLE_VIP, new Router.IBundleInstallCallback() {

                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                try {
                                    Router.<VipActionRouter>getActionRouter(Configure.BUNDLE_VIP)
                                            .getFragmentAction().showMasterPurchaseDialog(finalMainActivity3,
                                                    pm.sceneType, pm.utmsource, pm.orderUpType, pm.orderUpValue,
                                                    pm.orderSubType, pm.orderSubValue, pm.pcreditNumInterest, pm.type);
                                } catch (Throwable throwable) {
                                    throwable.printStackTrace();
                                }
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                            }
                        });

                    } catch (Throwable throwable) {
                        if (ConstantsOpenSdk.isDebug) {
                            throw throwable;
                        }
                    }
                    break;
                case AppConstants.TO_PREVIEW_CHILD_PROTECT_DIALOG:
                    toPreviewChildProtectDialog(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_CHARACTER_DETAIL:
                    goToRoleDetailPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_HALF_SCREEN_LOGIN_AND_GET_PRIZE:
                    loginByHalfScreenAndGetPrize(activity);
                    break;
                case AppConstants.PAGE_TO_TRACK_COMMENT_PANEL:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    int sourceType = 0;
                    if (pm != null) {
                        sourceType = pm.sourceType;
                    }
                    PlayingSoundInfo soundInfo = PlayPageDataManager.getInstance().getSoundInfo();
                    if (soundInfo != null && YUtils.INSTANCE.isNoTab(soundInfo) && sourceType != FROM_PAGE_SHOW_NOTES_PAGE) {
                        openIndependentCommentFragment(mainActivity);
                    } else {
                        openFloatingTrackCommentPanel(mainActivity, pm, true);
                    }
                    break;
//                case AppConstants.PAGE_TO_QQ_GAME_CENTER:
//                    gotoGameCenter();
//                    break;
                case AppConstants.PAGE_TO_REPORT:
                    gotoReportPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_SHUHUANG_TOPIC:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openBookTopicPage(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_OPEN_ACCESSIBILITY_MODE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!AccessibilityModeManager.INSTANCE.canShowEntry()) {
                        CustomToast.showToast("本版本不支持无障碍模式");
                        return true;
                    }
                    AccessibilityModeManager.INSTANCE.openAccessibilityMode(true);
                    new XMTraceApi.Trace()
                            .setMetaId(58792)
                            .setServiceId("others")
                            .put("from", pm.fromSource)
                            .createTrace();
                    break;
                case AppConstants.PAGE_TO_SHARE_MEDAL_POSTER:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(MedalPosterFragment.Companion.newInstance(
                            pm.medalId, pm.medalFrom, pm.medalTheme
                    ));
                    break;

                case AppConstants.PAGE_TO_ANCHOR_CHAT_GROUP:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    go2AnchorChatGroupPage(mainActivity, pm, true);
                    break;

                case AppConstants.PAGE_TO_ANCHOR_CHAT_GROUP_SHAPE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    go2AnchorChatGroupPage(mainActivity, pm, false);
                    break;

                case AppConstants.PAGE_TO_LISTEN_TIME_FRAGMENT:
                    ListenTimeDialogManager.showListenTimeFragment(pm.source);
                    break;
                case AppConstants.GOTO_LOCAL_READ_PAGE:
                    ReadBundleHelper.INSTANCE.startLocalReadFragment(pm.bookId, pm.autoRead);
                    break;
                case AppConstants.GOTO_DUAL_TRANSFER_PAGE:
                    if (pm.opType == 1 || pm.opType == 3) {
                        Activity topAct = BaseApplication.getTopActivity();
                        if (topAct instanceof MainActivity) {
                            Map<String, Object> params = new HashMap<>();
//                            params.put("schema", pm.schema);
                            try {
                                Uri uri = Uri.parse(pm.schema);
                                Set<String> keySet = uri.getQueryParameterNames();
                                if (keySet != null && keySet.size() > 0) {
                                    Iterator<String> iterator = keySet.iterator();
                                    while (iterator.hasNext()) {
                                        String key = iterator.next();
                                        params.put(key, uri.getQueryParameter(key));
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            FragmentManager fragmentManager = ((MainActivity) topAct).getSupportFragmentManager();
                            ReactNativeDialogFragment.show(fragmentManager, pm.bundle, pm.opName, pm.opType == 1, pm.from_page, params);
                        } else {
                            Logger.logToFile(TAG, "activity not MainAct");
                        }
                    } else if (pm.opType == 2) {
                        toRNComponent(activity, pm.schema, pm.resPosition, pm);
                    }
                    break;
                case AppConstants.PAGE_TO_SCAN_QR:
                    gotoQRCodeScanFragment(pm, mainActivity);
                    break;
//                case AppConstants.PAGE_TO_SKETCH_CATEGORY:
//                    go2SketchCategoryPage(mainActivity, pm.poolId);
//                    break;

                case AppConstants.PAGE_TO_MINE_ANCHOR_AND_RECOMMEND_GROUP_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    go2MineAnchorAndGroupGroupPage(mainActivity);
                    break;


                case AppConstants.PLAY_TO_TING_LIST_OR_CONTENT_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    doPlayTingListOrContentList(mainActivity, pm);
                    break;
                case AppConstants.PAGE_TO_SEARCH_WITH_HINT:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    BaseFragment searchHintFragment = null;
                    try {
                        searchHintFragment = Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH)
                                .getFragmentAction()
                                .newSearchFragmentByHintWord(pm.hint);
                        mainActivity.startFragment(searchHintFragment);
                        return true;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case AppConstants.PAGE_TO_SEARCH_SPEECH_RECOGNITION:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    try {
                        if (ChatXmlyPopupManager.INSTANCE.useChatXmlyVoiceAssistant()) {
                            ChatXmlyPopupManager.INSTANCE.showChatXmlyPopup(mainActivity, "iting415", null);
                        } else {
                            final long openTime = SystemClock.elapsedRealtime();
                            mOpenSpeechRecognitionTime = openTime;
                            SpeechRecognitionRouterUtil.getBundle(bundleModel -> {
                                ISpeechRecognitionFragmentAction fraAction = SpeechRecognitionRouterUtil.getFragAction();
                                if (fraAction != null && openTime == mOpenSpeechRecognitionTime) {
                                    String iTing = "iting://open?msg_type=94&bundle=rn_assistant&reuse=true&type=0";
                                    new ITingHandler().handleITing(activity, Uri.parse(iTing));
                                }
                            });
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case AppConstants.PAGE_TO_TAB_FREE_LISTEN:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.goFreeListenTab(pm.source);
                    break;
                case AppConstants.PAGE_TO_TO_LISTEN: {
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    // 0. 开播待播并进入播放页，不打开待播列表；
                    // 1. 开播待播并进入播放页，打开待播列表；
                    // 2. 直接进入播放页，打开待播列表（播放器有声音的情况下才能生效）；
                    // 3. 直接开播待播，不进入播放页；
                    if (pm.open_type == 0) {
                        ToListenManager.INSTANCE.playToListenTracks(true, null, null);
                    } else if (pm.open_type == 1) {
                        try {
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().setAutoOpenToListenStatus(true);
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().setAutoOpenStatus(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        ToListenManager.INSTANCE.playToListenTracks(true, null, null);
                    } else if (pm.open_type == 2) {
                        try {
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().setAutoOpenToListenStatus(true);
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().setAutoOpenStatus(true);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        mainActivity.showPlayFragment(null, null, PlayerManager.PLAY_CURRENT);
                    } else if (pm.open_type == 3) {
                        ToListenManager.INSTANCE.playToListenTracks(false, null, null);
                    }
                    break;
                }
                case AppConstants.PAGE_TO_TO_PLAYER_PAGE_SHOP_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }

                    //iting://open?msg_type=439&track_id=xxx&title=xxx&url=xxx（产品：覃君洋）

                    try {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction()
                                .showPlayerPageShopDialog(pm.trackId, pm.title, pm.url);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    break;
                case AppConstants.PAGE_TO_TO_NEW_SHOW_NOTES:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    NewShowNotesManager.INSTANCE.startShowNotesDetailFragment(pm.source, pm.clickPlayIting, pm.albumId, pm.trackId,
                            pm.timeline, pm.autoPlay, pm.openComment, null, null, -1, null, pm.previewId, pm.showStartTime);
                    break;
                case AppConstants.PAGE_TO_DOC_READ_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(DocReadFragment.newInstance());
                    break;
                case AppConstants.PAGE_TO_CHAT_XMLY_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    ChatXmlyPopupManager.INSTANCE.showChatXmlyPopup(mainActivity, "iting", null);
                    break;
                case AppConstants.PAGE_TO_CHAT_XMLY_SETTING:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(ChatXmlySettingFragment.Companion.newInstance(false));
                    break;
                case AppConstants.PAGE_TO_CHAT_XMLY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
//                    mainActivity.startFragment(new ChatXmlyFragment(), ChatXmlyFragment.Companion.getENTER_ANIM(), ChatXmlyFragment.Companion.getEXIT_ANIM());
                    break;
                case AppConstants.PAGE_TO_AD_SETTING_SHAKE_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new SplashShakeSettingFragment());
                    break;
                case AppConstants.PAGE_TO_ALL_CATEGORY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(AllCategoryFragment.Companion.newInstance(pm.selTabId));
                    break;
                case AppConstants.PAGE_TO_PLAY_CONTENT_POOL_TRACK_LIST:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!TextUtils.isEmpty(pm.playListMode)) {
                        NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode));
                    }
                    Track original = new Track();
                    original.setKind(PlayableModel.KIND_TRACK);
                    original.setDataId(pm.trackId);
                    if (pm.albumId != 0) {
                        SubordinatedAlbum album = new SubordinatedAlbum();
                        album.setAlbumId(pm.albumId);
                        original.setAlbum(album);
                    }
                    CommonRequestM.getContentPoolRecommendTrackList(original, "planRecommend", new IDataCallBack<List<Track>>() {
                        @Override
                        public void onSuccess(@Nullable List<Track> data) {
                            if (data != null) {
                                PlayTools.playList(BaseApplication.getMyApplicationContext(), data, 0, !pm.notShowPlayPage, null);
                            } else {
                                PlayTools.playTrackWithAlbum(BaseApplication.getMyApplicationContext(), original, !pm.notShowPlayPage);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            PlayTools.playTrackWithAlbum(BaseApplication.getMyApplicationContext(), original, !pm.notShowPlayPage);
                        }
                    });
                    break;
                case AppConstants.PAGE_TO_ALL_CATEGORY_FILTER_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(TagChannelContentFragmentV2.Companion.newInstance(
                            pm.categoryId, pm.metaValueId, pm.metaTitle, pm.albumIds, pm.sortType, pm.source, pm.fromSource));
                    break;
                case AppConstants.PAGE_TO_CATEGROY_V2_PREVIEW:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(CategoryListV2Fragment.newInstance(false, pm.previewId, pm.scrollToCategory, pm.fromSource));
                    break;
                case AppConstants.PAGE_TO_CATEGROY_V2:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(CategoryListV2Fragment.newInstance(false, pm.previewId, pm.scrollToCategory, pm.fromSource));
                    break;
                case AppConstants.PAGE_TO_BATCH_DOWNLOAD:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(BatchDownloadFragmentNew.Companion.newInstance(pm.albumId));
                    break;
                case AppConstants.PAGE_TO_INTEREST_PAGE_NEW:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    InterestUtil.startInterestPageFromSetting(pm.from_page);
                    break;
                case AppConstants.THIRD_APP_AUTHORIZE:
                    if ("GaoDe".equalsIgnoreCase(pm.source)) {
                        XmRemoteControlUtil.saveAuthorizeBindDataForHome(XmRemoteControlUtil.INSTANCE.getAPP_KEY(), XmRemoteControlUtil.obtainThirdUid());
                    }
                    break;

                case AppConstants.PAGE_TO_MS_BIZ_LIST:
                    openMSBizList(mainActivity);
                    break;
                case AppConstants.PAGE_TO_PlanTerminal:
                    PlanTerminalNewDialog.Companion.show(mainActivity.getSupportFragmentManager(), pm.source);
                    break;

                case AppConstants.PAGE_TO_LISTEN_PERMISSION:
                    mainActivity.startFragment(new ListenPermissionFragment(true));
                    break;
                case AppConstants.PAGE_TO_MINI_DRAMA_HOME_PAGE:
                case AppConstants.PAGE_TO_MINI_DRAMA_DETAIL_PAGE:
                case AppConstants.PAGE_TO_MINI_DRAMA_HISTORY_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoMiniDramaBundleAsync(pm, mainActivity);
                    break;
                case AppConstants.DIRECT_TO_FREE_LISTEN_REWARD:
                    ItingFreeListenRewardManager.getInstance().showRewardAd(pm.rewardTime, pm.source);
                    break;
                case AppConstants.DIRECT_TO_SETTING_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(new SettingFragment());
                    break;
                case AppConstants.DIRECT_TO_CHAT_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    try {
                        BaseFragment chatfragment = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).
                                getFragmentAction().getNewCenterFragment(true);
                        mainActivity.startFragment(chatfragment);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    break;
                case AppConstants.DIRECT_TO_TIMER_OPEN_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (ABTest.getInt("setting_alarm", 1) == 2 ||
                            ConstantsOpenSdk.isDebug && BaseUtil.getIntSystemProperties("debug.setting_alarm") == 2) {
                        Bundle alarmBundle = new Bundle();
                        alarmBundle.putString("fragmentName", "rn");
                        alarmBundle.putString("bundle", "alarm_play_setting");
                        try {
                            RNActionRouter router = Router.getActionRouter(Configure.BUNDLE_RN);
                            if (router != null && router.getFragmentAction() != null) {
                                BaseFragment rnFragment = router.getFragmentAction().newRNFragment("rn", alarmBundle);
                                mainActivity.startFragment(rnFragment);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        AlarmManagerFragment alarmManagerFragment = AlarmManagerFragment.newInstance();
                        mainActivity.startFragment(alarmManagerFragment);
                    }
                    break;
                case AppConstants.DIRECT_TO_MODE_SWITCH_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    ModeSwitchUtil.clickModeSwitch(false);
                    break;
                case AppConstants.DIRECT_SMART_DEVICE_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    if (!UserInfoMannage.hasLogined()) {
                        Bundle smartBundle = new Bundle();
                        smartBundle.putBoolean(BundleKeyConstants.KEY_TO_SMART_DEVICE_PAGE, true);
                        UserInfoMannage.gotoLogin(mainActivity, LoginByConstants.LOGIN_BY_DEFUALT, smartBundle);
                    } else {
                        String smartUrl = AppConstants.SMART_DEVISES_LIST_MANAGE_URL + "&ts=/ts-" + System.currentTimeMillis();
                        ToolUtil.clickUrlAction(mainActivity, smartUrl, null);
                    }
                    break;
                case AppConstants.MINE_LINE_RECORD_ENTRY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    MineTopUtil.INSTANCE.clickRecordLive((MainActivity) activity);
                    break;
                case AppConstants.FIND_FRIENDS_SEARCH_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    try {
                        BaseFragment searchFriendFragment = Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction()
                                .newSearchFragmentWithChooseType(IMainFunctionAction.FragmentConstants.CHOOSE_TYPE_ANCHOR);
                        Bundle searchFriendBundle = searchFriendFragment.getArguments();
                        if (searchFriendBundle != null) {
                            searchFriendBundle.putBoolean(BundleKeyConstants.KEY_FIND_ALL_USER, true);
                        }
                        searchFriendFragment.setArguments(searchFriendBundle);
                        mainActivity.startFragment(searchFriendFragment);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                    break;
                case AppConstants.FIND_FRIENDS_TXL_PAGE:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    mainActivity.startFragment(FindFriendFragmentV2.newInstance(FindFriendFragmentV2.TYPE_PHONE));
                    break;
                case AppConstants.GET_VIP_RETENTION_GIFT:
                    if(null == mainActivity){
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    PaidRetentionUtil.requestPaidRetentionDialogForITing();
                    break;
                case AppConstants.HOT_SEARCH_CATEGORY:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    gotoSearchHotCategoryPage(mainActivity, pm.categoryId, (int) pm.contentId);
                    break;
                case AppConstants.HALF_WEB_DIALOG:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    openWebDialog(mainActivity, pm);
                    break;
                case AppConstants.RESERVE_ALBUM:
                    if (null == mainActivity) {
                        sendToMainActivityToHandle(activity);
                        return false;
                    }
                    reserveAlbum(mainActivity, pm);
                    break;
                default:
                    if (!TextUtils.isEmpty(pm.url)) {//打开服务端给的链接
                        Intent intentToWeb = new Intent(activity, WebActivity.class);
                        intentToWeb.putExtra(BundleKeyConstants.KEY_EXTRA_URL, pm.url);
                        activity.startActivity(intentToWeb);
                    } else {
                        // 不能识别的iting类型，默认提示升级
                        CustomToast.showToast(ITING_NOT_SUPPORT_TIP);
                    }
                    break;
            }
            TouTiaoAdManager.sHasHandleIting = true;
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            if (mainActivity == null) {
                sendToMainActivityToHandle(activity);
            }
            return false;
        }
    }

    // 播放内容池或者听单
    private void doPlayTingListOrContentList(MainActivity activity, PushModel pushModel) {
        if (activity == null || pushModel == null) {
            return;
        }
        NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(!TextUtils.isEmpty(pushModel.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pushModel.playListMode));

        if (pushModel.fromPool == 3) {
            requestRankList(activity, pushModel);
            return;
        }

        final int type = pushModel.fromPool == 1 ? 1 : 2;
        Map<String, String> params = new HashMap<>();
        params.put("contentId", String.valueOf(pushModel.tingListId));
        params.put("trackId", String.valueOf(pushModel.trackId));
        params.put("type", String.valueOf(type));
        params.put("pageSize", type == 1 ? "10" : "20"); // 为了兼容播放器中内容池和听单的加载页数量不同，播放器中内容池加载10条，听单加载20条

        CommonRequestM.getTingListOrContentListTracks(params, new IDataCallBack<TingListTracksInfoModel>() {
            @Override
            public void onSuccess(@Nullable TingListTracksInfoModel model) {
                if (pushModel == null
                        || model == null || model.trackList == null || model.trackList.size() <= 0) {
                    Logger.d("iting411", "track list is empty");
                    return;
                }

                model.tingListId = pushModel.tingListId;
                if (type == 1) {
                    doRealPlayContentList(activity, model, pushModel.trackId, pushModel.notShowPlayPage);
                } else {
                    doRealPlayTingList(activity, model, pushModel.trackId, pushModel.notShowPlayPage);
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.d("iting411", code + "---" + message);
            }
        });
    }

    private void requestRankList(MainActivity activity, PushModel pushModel) {
        if (activity == null || pushModel == null) {
            return;
        }

        Map<String, String> params = new HashMap<>();
        params.put("trackId", String.valueOf(pushModel.trackId));
        params.put("pageSize", "20");
        params.put("rankingListId", pushModel.id);

        CommonRequestM.getRankContentTrackList(params, new IDataCallBack<ListModeBase<TrackM>>() {
            @Override
            public void onSuccess(@Nullable ListModeBase<TrackM> listModeBase) {
                if (pushModel == null
                        || listModeBase == null
                        || listModeBase.getList() == null
                        || listModeBase.getList().size() == 0) {
                    return;
                }

                doRealPlayRankTrackList(activity, listModeBase, pushModel.trackId, pushModel.id, pushModel.notShowPlayPage);
            }

            @Override
            public void onError(int code, String message) {
                Logger.d("iting411", code + "---" + message);
            }
        });
    }

    private void doRealPlayTingList(Context context, TingListTracksInfoModel model, long playTrackId, boolean notShowPlayPage) {
        if (model == null) {
            return;
        }

        List<TrackM> tracks = model.trackList;
        if (tracks == null || tracks.size() <= 0) {
            return;
        }

        int index = 0;
        for (int i = 0; i < tracks.size(); i++) {
            TrackM t = tracks.get(i);
            if (t == null) {
                continue;
            }
            if (playTrackId == t.getDataId()) {
                index = i;
            }
            t.setKind(PlayableModel.KIND_TRACK);
            t.setTrackRecordAlbumTitle(model.tingListTitle);
            t.setTrackRecordAlbumId(model.tingListId);
            t.setTingListOpType(TingListConstants.TYPE_TRACK);
            t.setPlaySource(ConstantsOpenSdk.PLAY_FROM_LISTEN_LIST);
        }

        CommonTrackList commonTrackList = new CommonTrackList();
        commonTrackList.setTotalPage(model.maxPageId);
        commonTrackList.setTotalCount(model.totalCount);
        commonTrackList.setTracks(tracks);
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.PAGE, String.valueOf(model.pageId));
        params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(model.maxPageId));
        params.put(DTransferConstants.PAGE_SIZE, "20");
        params.put(DTransferConstants.ALBUMID, String.valueOf(model.tingListId));
        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getTingListDetailContentUrl());
        params.put(DTransferConstants.PARAM_PLAY_LIST_FROM, DTransferConstants.PARAM_PLAY_LIST_FROM);
        commonTrackList.setParams(params);

        PlayTools.playCommonList(context, commonTrackList, index, !notShowPlayPage, null);
    }

    private void doRealPlayContentList(Context context, TingListTracksInfoModel model, long playTrackId, boolean notShowPlayPage) {
        if (model == null) {
            return;
        }

        List<TrackM> tracks = model.trackList;
        if (tracks == null || tracks.size() <= 0) {
            return;
        }

        int index = 0;
        for (int i = 0; i < tracks.size(); i++) {
            TrackM t = tracks.get(i);
            if (t == null) {
                continue;
            }
            if (t.getDataId() == playTrackId) {
                index = i;
            }
            t.setKind(PlayableModel.KIND_TRACK);
            t.setListenId(model.tingListId);
            t.setTrackRecordAlbumTitle(model.tingListTitle);
            t.setTrackRecordAlbumId(model.tingListId);
            t.setTingListOpType(TingListConstants.TYPE_TRACK);
        }
        CommonTrackList commonTrackList = new CommonTrackList();
        commonTrackList.setTotalPage(model.maxPageId);
        commonTrackList.setTotalCount(model.totalCount);
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.PAGE_SIZE, "10");
        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getRnListenListTrackListUrl());
        params.put(DTransferConstants.PARAM_PLAY_LIST_FROM, DTransferConstants.PARAM_PLAY_LIST_FROM);
        params.put(DTransferConstants.ALBUMID, model.tingListId + "");
        params.put(DTransferConstants.PARAM_LISTEN_LIST_SCHEME, model.iting); // 内容池听单的跳转scheme
        params.put(DTransferConstants.PAGE, String.valueOf(model.pageId));
        params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(model.maxPageId));
        commonTrackList.setParams(params);
        // 中间还需要处理trackList信息
        commonTrackList.setTracks(tracks);

        PlayTools.playCommonList(context, commonTrackList, index, !notShowPlayPage, null);
    }

    private void doRealPlayRankTrackList(Context context, ListModeBase<TrackM> listModeBase, long playTrackId, String rankingListId, boolean notShowPlayPage) {
        if (listModeBase == null) {
            return;
        }

        List<TrackM> tracks = listModeBase.getList();
        if (tracks == null || tracks.size() <= 0) {
            return;
        }

        int index = 0;
        for (int i = 0; i < tracks.size(); i++) {
            TrackM t = tracks.get(i);
            if (t == null) {
                continue;
            }
            if (t.getDataId() == playTrackId) {
                index = i;
            }
            t.setKind(PlayableModel.KIND_TRACK);
            t.setTrackRecordAlbumTitle(listModeBase.getTitle());
        }
        CommonTrackList commonTrackList = new CommonTrackList();
        commonTrackList.setTotalPage(listModeBase.getMaxPageId());
        commonTrackList.setTotalCount(listModeBase.getTotalCount());
        Map<String, String> params = new HashMap<>();
        params.put(DTransferConstants.PAGE_SIZE, "20");
        params.put(DTransferConstants.TRACK_BASE_URL, UrlConstants.getInstanse().getRankTrackListUrl());
        params.put(DTransferConstants.PARAM_CONTENT_POOL_ID, "3");
        params.put(DTransferConstants.ALBUMID, rankingListId);
        params.put(DTransferConstants.PAGE, String.valueOf(listModeBase.getPageId()));
        params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(listModeBase.getMaxPageId()));
        commonTrackList.setParams(params);
        // 中间还需要处理trackList信息
        commonTrackList.setTracks(tracks);
        for (int i = 0; i < tracks.size(); i++) {
            tracks.get(i).setIsRankPool(true);
            tracks.get(i).setPageFromSource(TrackPageFromSourceUtil.Companion.getValue(TrackPageFromSourceEnum.SOURCE_RANK));
        }

        PlayTools.playCommonList(context, commonTrackList, index, !notShowPlayPage, null);
    }

    // 主播消息聚合页
    private void go2AnchorChatGroupPage(MainActivity mainActivity, PushModel pushModel, boolean showAllGroup) {
        if (mainActivity == null || pushModel == null) {
            return;
        }

        long id = showAllGroup ? pushModel.uid : pushModel.groupId;
        String anchorName = showAllGroup ? pushModel.userName : "";
        try {
            BaseFragment fragment =
                    Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().getAnchorChatGroupAggregationFragment(id, anchorName, showAllGroup, pushModel.source);
            mainActivity.startFragment(fragment);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void go2MineAnchorAndGroupGroupPage(MainActivity activity) {
        if (activity == null) {
            return;
        }

        try {
            BaseFragment fragment =
                    Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().getMineAnchorAndRecommendGroupListFragment();
            activity.startFragment(fragment);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void toPreviewChildProtectDialog(MainActivity activity, PushModel pm) {
        ChildProtectDialogManager.INSTANCE.previewChildProtectDialog(activity.getSupportFragmentManager(), pm.id);
    }

    private void loginByHalfScreenAndGetPrize(Activity activity) {
        LoginDeferredActionHelper.getInstance().gotoLoginWithDeferredAction(activity, LoginByConstants.LOGIN_BY_SOUND_EFFECT_PRIZE, () -> {
            MainCommonRequest.getActivityPrize("loginYellowZone", new IDataCallBack<String>() {
                @Override
                public void onSuccess(@Nullable String data) {
                    if (!TextUtils.isEmpty(data)) {
                        CustomToast.showToast(data);
                        BaseFragment fragment = PlayerManager.getInstanse().getCurrentFragment();
                        if (fragment instanceof PlayFragmentNew && fragment.isRealVisable()) {
                            Track track = PlayTools.getCurTrack(activity);
                            if (track != null && track.getAlbum() != null && PlayableModel.KIND_TRACK.equals(track.getKind())) {
                                long trackId = track.getDataId();
                                long albumId = track.getAlbum().getAlbumId();
                                ChooseTrackSoundEffectAiDialogXNew.newInstance(albumId, trackId).show(fragment.getChildFragmentManager(), ChooseTrackSoundEffectAiDialogXNew.class.getSimpleName());
                                //ChooseTrackSoundEffectDialogX.newInstance((BaseFragment2) fragment, albumId, trackId, null, null).show();
                            }
                        }
                    }
                }

                @Override
                public void onError(int code, String message) {
                    Logger.i(TAG, "领奖失败 " + code + " " + message);
                }
            });
        });
    }

    private void openInteractiveCardPanel(MainActivity activity, PushModel pm) {
        String cardType = pm.cardType;
        long cardId = pm.cardId;
        if (cardType == null || cardType.length() <= 0 || cardId <= 0) {
            return;
        }
        switch (cardType) {
            case "TALK":
                openTalkPanel(activity, pm);
                break;
            case "VOTE":
                openVotePanel(activity, pm);
                break;
            case "SEQUENTIALVOTE":
                InteractiveCardUtil.startSequentialVotePanelFragment(pm.trackId, cardId, CommentConstants.FROM_PAGE_AUDIO, null);
                break;
            case "TOPIC":
                openTopicPanel(activity, pm);
                break;
            case "FINDPLAYER":
                openFindPartner(activity, pm);
                break;
            default:
                break;
        }
    }

    private void gotoRadioContentFragmentNew(MainActivity mainActivity, String liveRadio) {
        if (TextUtils.isEmpty(liveRadio) || TextUtils.isEmpty(liveRadio.trim())) {
            return;
        }

        Router.getActionByCallback(Configure.BUNDLE_RADIO, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.radioBundleModel.bundleName, bundleModel.bundleName)) {
                    try {
                        String url = URLDecoder.decode(liveRadio.trim(), "utf-8");
                        if (TextUtils.isEmpty(url)) {
                            return;
                        }
                        BaseFragment fra =
                                Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO)
                                        .getFragmentAction().newRadioSquareFragment(url);
                        if (fra != null) {
                            mainActivity.startFragment(fra);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });
    }

    private void doBocPayResult(int payResult, double payAmount) {
        // 直接回调前端支付成功页
        IBocPayManager bocService = RouterServiceManager.getInstance().getService(IBocPayManager.class);
        if (bocService == null) {
            return;
        }
        IBocPayManager.IBocCallback callback = bocService.getCallback();
        if (callback == null) {
            return;
        }

        PayResult result = new PayResult();
        result.payType = PayConstants.TYPE_CCB_SDK_PAY + "";
        result.retCode = -1;

        Router.getActionByCallback(Configure.BUNDLE_RN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (payResult == 1) {
                    callback.onSuccess("" + payAmount);
                    result.retCode = PayResult.RESULT_SUCCESS;
                    result.errorMsg = "支付成功";
                } else {
                    callback.onError("error", "支付失败");
                    result.errorMsg = "支付失败";
                }

                // 资源回收，防止内存泄漏
                if (bocService != null) {
                    bocService.unRegisterBocCallBack(callback);
                }
                PayActionLog log = PayActionManager.getInstance().getCurPayActionLog();
                if (result != null && log != null && log.getType() == PayConstants.TYPE_BOC_PAY) {
                    log.endPay(result);
                    PayActionManager.getInstance().setCurPayActionLog(null);
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }

    private void gotoTLLoanSdk(MainActivity mainActivity, String mgmCallbackUrl) {
        if (UserInfoMannage.hasLogined()) {
            ToolUtil.clickUrlAction(mainActivity, LoanFunctionUtil.getGoToTLLoanUrl(), null);
        } else {
            UserInfoMannage.gotoLogin(mainActivity);
        }
    }

    private void trackMyclubSource(Uri uri) {
        String utmSource = uri.getQueryParameter("utmsource");
        if (TextUtils.isEmpty(utmSource)) {
            utmSource = uri.getQueryParameter("utm_source");
        }
        if (TextUtils.isEmpty(utmSource)) {
            utmSource = "";
        }
        String xbsource = uri.getQueryParameter("xbsource");
        if (xbsource == null) xbsource = "";

        new XMTraceApi.Trace()
                .setMetaId(46777)
                .setServiceId("startUp") // 立即上报
                .put("itingUrl", uri.getScheme()) // 可以为空
                .put("utmsource", utmSource) // 可以为空
                .put("xbsource", xbsource) // 可以为空
                .createTrace();
    }

    private void gotoMyclubChatRoom(MainActivity mainActivity, long roomId, boolean purchased) {
        PlayTools.checkToMyclubRoomFragment(mainActivity, roomId, purchased);
    }

    private void gotoMyclubScheduleEdit(MainActivity mainActivity, PushModel pm) {
        final HashSet<String> ignoreLoginSet = new HashSet<String>() {{
            add("openReplay");
            add("shareSchedulePoster");
            add("openProfile");
        }};
        if (UserInfoMannage.hasLogined() || ignoreLoginSet.contains(pm.type)) {
            Router.getActionByCallback(Configure.BUNDLE_MYCLUB, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    try {
                        switch (pm.type) {
                            case "createRoom":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startCreateRoomFragment(mainActivity, pm.source, pm.ximi == 1);
                                break;
                            case "editSchedule":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startScheduleEditFragment(mainActivity, pm.scheduleId);
                                break;
                            case "showBottomScheduleInvite":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startScheduleInviteFragment(mainActivity, pm.scheduleId, pm.from_page, pm.title);
                                break;
                            case "openReplay":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startReplayRoomFragment(mainActivity, pm.liveRoomId, false);
                                break;
                            case "editProfile":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startEditUserInfoFragment(mainActivity, pm.url, pm.name, pm.roleType, pm.scheduleId);
                                break;
                            case "editFullProfile":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startUserEditFragment(mainActivity);
                                break;
                            case "playReport":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startPlayReportFragment(mainActivity, pm.liveRoomId);
                                break;
                            case "showInviteCard":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().showInviteCard(mainActivity, pm.uid, pm.name, pm.url);
                                break;
                            case "sendQuota":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startInviteFragment(mainActivity);
                                break;
                            case "shareSchedulePoster":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().startSchedulePosterShareFragment(mainActivity, pm.scheduleId, pm.bizType, pm.shareUrl);
                                break;
                            case "openProfile":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().showUserProfileDialog(mainActivity, pm.scheduleId, pm.uid, pm.name, pm.darkMode);
                                break;
                            case "chooseScheduleInviteType":
                                Router.<MyclubActionRouter>getActionRouter(Configure.BUNDLE_MYCLUB)
                                        .getFragmentAction().showChooseScheduleInviteTypeDialogFragment(mainActivity, pm.scheduleId);
                                break;
                            default:
                                break;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);
        } else {
            UserInfoMannage.gotoLogin(mainActivity);
        }
    }

    private void goToCcbSdk(MainActivity mainActivity, PushModel pushModel) {
        final long uid;
        if (UserInfoMannage.hasLogined() && (0 != (uid = UserInfoMannage.getUid()))) {
            /*if (null != pushModel && "testInCcb".equals(pushModel.type)) {
                CcbSdkManager.getInstance().entryCcbSdkInTheirTestEnvironment(uid);
                return;
            }*/
            CcbSdkManager.getInstance().entryCcbSdk(uid);
            /*Router.getActionByCallback(Configure.BUNDLE_RNUNIONPAY, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    try {
                        Router.<RNUnionPayActionRouter>getActionRouter(Configure.BUNDLE_RNUNIONPAY).getFunctionAction().enterCcbSdkPlatform(uid);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    // do Nothing
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                    // do Nothing
                }
            }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);*/
        } else {
            UserInfoMannage.gotoLogin(mainActivity);
        }
    }

    private void gotoDailyNewsPage(MainActivity mainActivity, PushModel pm, long channelIdFinal, String dailyNewsSource, OneKeyListenTabModel tabModel) {
        mainActivity.startFragment(
                DailyNewsFragment4.newInstance(
                        pm.channelGroupId,
                        channelIdFinal,
                        pm.onekeyPlayTrackId,
                        pm.onekeyPlayTrackIds,
                        pm.channelType,
                        pm.toDetailInfo,
                        dailyNewsSource,
                        pm.fromWeather,
                        pm.oneKeyNormalExtra,
                        tabModel,
                        pm.dailyNewsContentId,
                        pm.sourceType));
    }

    private void gotoSearchResultPage(MainActivity mainActivity, String keyWord, boolean autoPlay, String focusTab) {
        gotoSearchResultPage(mainActivity, keyWord, autoPlay, "", -1, focusTab);
    }

    private void gotoSearchResultPage(MainActivity mainActivity, String keyWord, boolean autoPlay, String channel, int keyWordId, String focusTab) {
        if (TextUtils.isEmpty(keyWord)) {
            BaseFragment fragment = null;
            try {
                fragment =
                        Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                                Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchFragment() :
                                null;
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fragment != null) {
                mainActivity.startFragment(fragment);
            }
            return;
        }
        BaseFragment fragment = null;
        try {
            fragment =
                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchFragmentByWordAndSearchNow(keyWord, autoPlay, channel, keyWordId, focusTab) :
                            null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (fragment != null) {
            mainActivity.startFragment(fragment);
        }
    }

    private void gotoSearchHotCategoryPage(MainActivity mainActivity, int categoryId, int contentId) {
        BaseFragment fragment = null;
        try {
            fragment =
                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchHotWordBillboardFragment(categoryId, contentId) :
                            null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (fragment != null) {
            mainActivity.startFragment(fragment);
        }
    }

    private void reserveAlbum(MainActivity mainActivity, PushModel pm) {
        NewAlbumReserveManager.INSTANCE.reserveAlbum(pm.productId, pm.toReserve, pm.coverUrl, pm.currPage, null);
    }

    private void openWebDialog(MainActivity mainActivity, PushModel pm) {
//        BaseFragment fragment = null;
//        try {
//            fragment =
//                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
//                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchHotWordBillboardFragment(categoryId, contentId) :
//                            null;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        if (fragment != null) {
//            mainActivity.startFragment(fragment);
//        }
        String title = pm.title != null? pm.title : "";
        String url = pm.url;
        HalfWebVIewDialog rankDescTipDialog = HalfWebVIewDialog.Companion.newInstance(title,
                url, 0.6f, null);
        rankDescTipDialog.show(mainActivity.getSupportFragmentManager(), "itingWebDialog");
    }

    private void gotoThirdGameWeb(PushModel pm, String gameToken, String gameUid, boolean isNeedFullScreen) {
        Activity topActivity = MainApplication.getOptActivity();
        if (TextUtils.isEmpty(pm.url) || topActivity == null) {
            return;
        }
        Intent intent = new Intent(topActivity, ThirdGameWebViewActivity.class);
        intent.putExtra(ThirdGameAdConstants.BASE_URL, pm.url);
        if (!TextUtils.isEmpty(pm.gameId)) {
            intent.putExtra(ThirdGameAdConstants.GAME_ID, pm.gameId);
        }
        if (!TextUtils.isEmpty(pm.gameResource)) {
            intent.putExtra(ThirdGameAdConstants.GAME_RESOURCE, pm.gameResource);
        }
        if (!TextUtils.isEmpty(pm.positionName)) {
            intent.putExtra(ThirdGameAdConstants.POSITION_NAME, pm.positionName);
        }
        if (!TextUtils.isEmpty(pm.adType)) {
            intent.putExtra(ThirdGameAdConstants.AD_TYPE, pm.adType);
        }
        if (!TextUtils.isEmpty(pm.gameTip)) {
            intent.putExtra(ThirdGameAdConstants.GAME_TIP, pm.gameTip);
        }
        if (!TextUtils.isEmpty(pm.gameSign)) {
            intent.putExtra(ThirdGameAdConstants.GAME_SIGN, pm.gameSign);
        }
        if (isNeedFullScreen) {
            intent.putExtra(ThirdGameAdConstants.NEED_FULL_SCREEN_SHOW, true);
        }
        if (!TextUtils.isEmpty(gameToken)) {
            intent.putExtra(ThirdGameAdConstants.BAO_QU_GAME_TOKEN, gameToken);
        }
        if (!TextUtils.isEmpty(gameUid)) {
            intent.putExtra(ThirdGameAdConstants.BAO_QU_GAME_UID, gameUid);
        }
        topActivity.startActivityForResult(intent, ThirdGameAdConstants.THIRD_GAME_REQUEST_CODE);
    }

    private void gotoVipFragment(MainActivity mainActivity, PushModel pm) {
        if (mainActivity == null || pm == null) {
            return;
        }
        Bundle bundle = new Bundle();
//        bundle.putBoolean(BundleKeyConstants.KEY_SHOW_TITLE, true);
        bundle.putInt(BundleKeyConstants.KEY_DISPLAY_TYPE, 2);
        bundle.putString("locatedTab", pm.locatedTab);
        Router.getActionByCallback(Configure.BUNDLE_VIP, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                BaseFragment2 vipFragment = VipHostUtil.chooseVipFragment(null);
                if (vipFragment != null) {
                    vipFragment.setArguments(bundle);
                    mainActivity.startFragment(vipFragment);
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });


    }

    private void gotoHomePageWithTab(String tab, MainActivity mainActivity) {
        if (TextUtils.isEmpty(tab) || mainActivity == null) {
            return;
        }
        mainActivity.clearAllFragmentFromManageFragment();
        mainActivity.hidePlayFragment(null);

        if (mainActivity.getTabFragmentManager() != null
                && mainActivity.getTabFragmentManager().getCurrentTab() == TAB_HOME_PAGE) {
            // 当前已选中 HomePageFragment
            try {
                mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE,
                        null);
                IMainFragmentAction fragmentAction =
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction();
                Fragment currentFra =
                        mainActivity.getTabFragmentManager().getCurrFragment();

                if (fragmentAction != null) {
                    fragmentAction.switchTabWithName(currentFra,
                            tab);
                } else if (currentFra != null) {
                    Bundle bundle = currentFra.getArguments();
                    if (bundle == null) {
                        bundle = new Bundle();
                    }
                    bundle.putString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME, tab);
                    currentFra.setArguments(bundle);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {

            Bundle bundle1 = new Bundle();
            if (!TextUtils.isEmpty(tab)) {
                bundle1.putString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_NAME, tab);
            }
            mainActivity.checkRadio(TAB_HOME_PAGE, bundle1);
        }
    }

    private void gotoHomePageWithTabId(String tabId, MainActivity mainActivity) {
        if (TextUtils.isEmpty(tabId) || mainActivity == null) {
            return;
        }
        mainActivity.clearAllFragmentFromManageFragment();
        mainActivity.hidePlayFragment(null);

        if (mainActivity.getTabFragmentManager() != null
                && mainActivity.getTabFragmentManager().getCurrentTab() == TAB_HOME_PAGE) {
            // 当前已选中 HomePageFragment
            try {
                mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE,
                        null);
                IMainFragmentAction fragmentAction =
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction();
                Fragment currentFra =
                        mainActivity.getTabFragmentManager().getCurrFragment();

                if (fragmentAction != null) {
                    fragmentAction.switchTabWithId(currentFra,
                            tabId);
                } else if (currentFra != null) {
                    Bundle bundle = currentFra.getArguments();
                    if (bundle == null) {
                        bundle = new Bundle();
                    }
                    bundle.putString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID, tabId);
                    currentFra.setArguments(bundle);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {

            Bundle bundle1 = new Bundle();
            if (!TextUtils.isEmpty(tabId)) {
                bundle1.putString(BundleKeyConstants.KEY_SELECT_HOME_PAGE_TAB_ID, tabId);
            }
            mainActivity.checkRadio(TAB_HOME_PAGE, bundle1);
        }
    }

    /**
     * 跳转到听单评论详情页
     */
    private void gotoTingCommentFragment(PushModel pm, MainActivity mainActivity) {
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            if (mainActivity != null && mainActivity.canUpdateUi()) {
                IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                if (fragAction != null) {
                    BaseFragment2 frag = fragAction.newTingListDetailFragment(pm.tingListId);
                    mainActivity.startFragment(frag);
                }
            }
        });
    }

    /**
     * 跳转到创建听单页面
     */
    private void gotoEditTinglistFragment(PushModel pm, MainActivity mainActivity) {
        MyListenRouterUtil.getMyListenBundle(bundleModel -> {
            IMyListenFunctionAction funAction = MyListenRouterUtil.getFunAction();
            if (funAction != null && mainActivity != null && mainActivity.canUpdateUi()) {
                int tingListType = 0;
                if (!TextUtils.isEmpty(pm.type)) {
                    if (pm.type.equals("album")) {
                        tingListType = TingListConstants.TYPE_ALBUM;
                    } else if (pm.type.equals("sound")) {
                        tingListType = TingListConstants.TYPE_TRACK;
                    }
                }
                XmBaseDialog dialog = funAction.newTingListCreatorDialogAndSetSubmitCallback(mainActivity, tingListType);
                dialog.show();
            }
        });
    }

    private void openKachaNoteTimelinePage(long albumId, final MainActivity mainActivity) {
        if (mainActivity == null) {
            return;
        }
        KachaHintHelper.gotoKachaPage();
    }

    private void handDisabledVerifyFragment(final MainActivity mainActivity, final long uid) {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mainActivity);
            return;
        }
        if (!ConfigureCenter.getInstance().getBool("toc", "switch_disabled_people", false)) {
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("", "");
        MainCommonRequest.postDisabledVerifyEntry(params, new IDataCallBack<DisabledVerifyBean>() {
            @Override
            public void onSuccess(@Nullable DisabledVerifyBean object) {
                if (object != null) {
                    BaseFragment2 fragment = null;
                    if (object.isState()) {
                        fragment = DisabledVerifyResultFragment.newInstance(uid == 0 ? UserInfoMannage.getUid() : uid, object);
                    } else {
                        fragment = DisabledVerifyPostFragment.newInstance(DisabledVerifyPostFragment.TYPE_MANUAL, uid == 0 ? UserInfoMannage.getUid() : uid);
                    }
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (code == 50) {
                    UserInfoMannage.gotoLogin(mainActivity);
                } else {
//                    CustomToast.showToast(message);
                }
            }
        });
    }

    private void sendToMainActivityToHandle(Activity activity) {
        if (mData != null) {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(mData);
            ToolUtil.checkIntentAndStartActivity(activity, intent);
        }
    }

    public PushModel getPushModelFromUri(Uri data) {
        return ItingManager.getPushModelFromUri(data, "");
    }

    private boolean toWebComponent(Activity activity, String url) {
        if (activity != null && !(activity instanceof MainActivity)) {
            boolean result = false;
            if (!TextUtils.isEmpty(url)) {
                Uri uri = Uri.parse(url);
                String host = uri == null ? "" : uri.getHost();
                if (uri != null && !TextUtils.isEmpty(host) && host.contains("component.xm")) {
                    String queryString = uri.getQuery();
                    ComponentSecuritySignUtils.getInstance().randCount();
                    final int currentCount = ComponentSecuritySignUtils.getInstance().getCount();
                    if (!TextUtils.isEmpty(queryString)) {
                        if (url.endsWith("&")) {
                            url += "__signcheck=" + currentCount;
                        } else {
                            url += "&__signcheck=" + currentCount;
                        }
                    } else {
                        if (url.endsWith("?")) {
                            url += "__signcheck=" + currentCount;
                        } else {
                            url += "?__signcheck=" + currentCount;
                        }
                    }
                    mData = Uri.parse(url);
                    result = true;
                }
            }
            if (result) {
                sendToMainActivityToHandle(activity);
                return true;
            }

            return false;
        }
        if (!TextUtils.isEmpty(url)) {
            Uri uri = Uri.parse(url);
            if (activity != null && uri != null && !uri.isOpaque()) {
                String host = uri.getHost();
                if ("component.xm".equals(host)) {
                    MainActivity mainActivity;
                    if (activity instanceof MainActivity) {
                        mainActivity = (MainActivity) activity;
                        try {
                            Intent intent = new Intent();
                            intent.setData(uri);
                            mainActivity.startFragment(Router.<HybridViewActionRouter>getActionRouter(Configure.BUNDLE_HYBRID).getFragmentAction()
                                    .newHybridViewFragment(intent));
                            return true;
                        } catch (Exception e) {
                            e.printStackTrace();
                            return false;
                        }
                    }
                } else if ("component.xm.debug".equals(host)) {
                    try {
                        String compId = uri.getQueryParameter("compId");
                        String downloadUrl = uri.getQueryParameter("downloadUrl");
                        Router.<HybridViewActionRouter>getActionRouter(Configure.BUNDLE_HYBRID).getFunctionAction().toDebugComponent(compId, downloadUrl);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return true;
                }
            }
        }
        return false;
    }

    private void gotoRadioContentFragment(MainActivity mainActivity) {
        // 产品要求和ios保持一致 直接打开非频道的广播首页
        Router.getActionByCallback(Configure.BUNDLE_RADIO, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.radioBundleModel.bundleName, bundleModel.bundleName)) {
                    try {
                        BaseFragment radioContentFragment = Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO)
                                .getFragmentAction()
                                .newRadioContentFragment(true);
                        mainActivity.startFragment(radioContentFragment);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });
    }

    private boolean toRNComponent(final Activity activity, String url, String resPosition, PushModel pushModel) {
        if (activity == null || TextUtils.isEmpty(url)) {
            return false;
        }
        Uri uri = Uri.parse(url);
        if (uri == null) {
            return false;
        }
        String bundleName = uri.getQueryParameter("bundle");
        if (TextUtils.isEmpty(bundleName)) {
            Logger.e(new Exception("bundle name cannot be null"));
            PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("RN bundle name cannot be null");
            return false;
        }

        RnFragmentOnlyOneUtil.removeSameBundleFragment(bundleName);

        if ("rn_asmr".equals(bundleName) && XimaTenDataUtil.isXimaTenPlayList()) {
            LastAudioPlayListCache.INSTANCE.saveAudioPlayListCache(activity);
        }

        if (!(activity instanceof MainActivity)) {
            mData = Uri.parse(url);
            sendToMainActivityToHandle(activity);
            return false;
        }

        Set<String> keySet = uri.getQueryParameterNames();
        final Bundle bundle = new Bundle();
        if (keySet != null && keySet.size() > 0) {
            Iterator<String> iterator = keySet.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                bundle.putString(key, uri.getQueryParameter(key));
            }
        }
        boolean isDebug = ConstantsOpenSdk.isDebug && ("1".equals(bundle.getString("__debug")));
        boolean keepPlayFra = "true".equals(bundle.getString("keepPlayFra", ""));
        final String fragmentName = isDebug ? "rntest" : "rn";
        bundle.putString("fragmentName", fragmentName);
        if (!TextUtils.isEmpty(resPosition)) {
            bundle.putString("rn_apm_ext", String.format("{\"%s\":\"%s\"}", ResPositionConstant.RES_POSITION, resPosition));
            Logger.d("fcl_bug", "rn:" + String.format("{\"%s\":%s}", ResPositionConstant.RES_POSITION, resPosition));
        }
        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before install rn bundle");
        Router.getActionByCallback(Configure.BUNDLE_RN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                try {
                    PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "after install rn bundle");
                    IRNFunctionRouter functionAction = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFunctionAction();
                    if (functionAction != null && !functionAction.checkBundleSupport(bundleName)) {
                        CustomToast.showToast(ITING_NOT_SUPPORT_TIP);
                    }

                    BaseFragment fragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).
                            getFragmentAction().newRNFragment(fragmentName, bundle);
                    setPlayListForElderly(pushModel);

                    CommercialHostUtil.SettlementDialogMaterial material = new CommercialHostUtil.SettlementDialogMaterial(activity, bundle);

                    // oh 我的老天鹅啊，下面代码可真糟糕，但是有什么办法呢，万恶的产品要求这么搞
                    // 判断是否为从播放页视频广告的开通vip跳转过来
                    if (TempDataManager.getInstance().getInt(RewardVideoTaskUtil.TEMP_DATA_KEY_REWARD_VIDEO_TASK_TYPE) ==
                            RewardVideoTaskUtil.REWARD_VIDEO_TASK_OPEN_VIP) {
                        TempDataManager.getInstance().saveInt(RewardVideoTaskUtil.TEMP_DATA_KEY_REWARD_VIDEO_TASK_TYPE,
                                RewardVideoTaskUtil.REWARD_VIDEO_TASK_ITING_OPEN_VIP);
                        fragment.setUnderThisHasPlayFragment(true);
                        IFragmentFinish finishCallback = new IFragmentFinish() {
                            @Override
                            public void onFinishCallback(Class<?> cls, int fid, Object... params) {
                                // 购买会员成功后，会跳h5页面，在h5页面setUnderThisHasPlayFragment保证返回播放页
                                Activity mainActivity = (MainActivity) BaseApplication.getTopActivity();
                                if (mainActivity instanceof MainActivity) {
                                    if (!ToolUtil.isEmptyCollects(((MainActivity) mainActivity).getManageFragment().mStacks)) {
                                        ManageFragment.MySoftReference softReference = ((MainActivity) mainActivity).getManageFragment().mStacks.
                                                get(((MainActivity) mainActivity).getManageFragment().mStacks.size() - 1);
                                        if (softReference != null) {
                                            Fragment targetFragment = softReference.get();
                                            if (targetFragment instanceof BaseFragment) {
                                                ((BaseFragment) targetFragment).setUnderThisHasPlayFragment(true);
                                            }
                                        }
                                    }
                                }
                                TempDataManager.getInstance().removeInt(RewardVideoTaskUtil.TEMP_DATA_KEY_REWARD_VIDEO_TASK_TYPE);
                                Intent intent = new Intent(RewardVideoTaskUtil.INTENT_FILTER_ACTION_VIDEO_AD_TASK);
                                intent.putExtra(RewardVideoTaskUtil.INTENT_KEY_REWARD_VIDEO_TASK_TYPE, RewardVideoTaskUtil.REWARD_VIDEO_TASK_ITING_OPEN_VIP);
                                LocalBroadcastManager.getInstance(BaseApplication.getMyApplicationContext()).sendBroadcast(intent);
                            }
                        };
                        ((BaseFragment2) fragment).setCallbackFinish(finishCallback);
                        material.fragmentFinishCallBack = finishCallback;
                    }
                    if (CommercialHostUtil.openSettlementFloatingDialog(activity, url)) {
                        // 被半浮层形式的Rn页面消费，不再使用全屏展示
                        return;
                    }

                    if (bundle.getString("reuse", "").equals("true")) {
                        Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).
                                getFragmentAction().startRNFragment(activity, ConstantsOpenSdk.isDebug ? fragmentName : "rn", bundle);
                        return;

                    }
                    if (keepPlayFra && XPlayPageRef.get() != null) {
                        XPlayPageRef.get().showFragmentOnPlayPage(fragment, true);
                    } else if (bundle.getString("animate", "").equals("0")) {
                        ((MainActivity) activity).startFragment(fragment, -1, -1);
                    } else {
                        ((MainActivity) activity).startFragment(fragment);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("RN bundle LocalInstallError");
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }

        });
        return true;
    }

    // 大字模式下，打开RN视频播放页时，需要记录，下次大字模式点击肚脐眼的时候，需要还是打开视频
    private void setPlayListForElderly(PushModel model) {
        if (model == null) {
            return;
        }

        if (ElderlyModeManager.getInstance().isElderlyMode() && model.jumpType == 100) {
            List<Track> tracks = new ArrayList<>();
            Track track = new TrackM();
            track.setDataId(model.trackId);
            track.setCoverUrlLarge(model.coverUrl);
            SubordinatedAlbum album = new SubordinatedAlbum();
            album.setAlbumId(model.albumId);
            track.setAlbum(album);
            track.setIsElderlyVideoMode(model.isVideoPage);
            tracks.add(track);
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext())
                    .setPlayList(tracks, 0);
        }
    }


    private void gotoChatBundleAsync(final PushModel pm, MainActivity activity) {

        if (pm == null || activity == null) {
            return;
        }

        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_CHAT, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.chatBundleModel.bundleName,
                        bundleModel.bundleName)) {


                    switch (pm.messageType) {
//                        case AppConstants.PAGE_IN_COMMENT:// 打开收到的评论页
//                            try {
//                                BaseFragment commonNoticeFragment =
//                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newFragmentByFid(Configure.ChatFragmentFid.COMMON_NOTICE_FRAGMENT);
//                                if (commonNoticeFragment != null) {
//                                    mainActivity.startFragment(commonNoticeFragment);
//                                }
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                            break;
                        case AppConstants.PAGE_LETTER:// 打开私信页（即消息中心）
                            long uid = pm.uid;
                            if (uid > 0) {
                                try {
                                    BaseFragment talkViewFragment =
                                            Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newTalkViewFragment(uid, "", "");
                                    if (talkViewFragment != null) {
                                        mainActivity.startFragment(talkViewFragment);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            } else {
                                try {
                                    BaseFragment newsCenterFragment =
                                            Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newFragmentByFid(Configure.ChatFragmentFid.NEWS_CENTER_FRAGMENT);
                                    if (newsCenterFragment != null) {
                                        mainActivity.startFragment(newsCenterFragment);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                            break;

                        case AppConstants.PAGE_TO_GROUP_LIST:// 群组列表页面
                            long albumId = -1;
                            boolean isMine = false;

                            if (pm.open_type == 1) {
                                isMine = true;
                            }

                            if (pm.open_type == 3) {
                                albumId = pm.albumId;
                            }
                            try {
                                BaseFragment groupListFragment = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT)
                                        .getFragmentAction()
                                        .newGroupListFragment(isMine, albumId, pm.groupMasterUid,
                                                true);

                                if (groupListFragment != null) {
                                    mainActivity.startFragment(groupListFragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_TO_GROUP_TOPIC: //群话题
                            try {
                                BaseFragment groupTopicFragment =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newFragmentByFid(Configure.ChatFragmentFid.GROUP_TOPIC_LIST_FRAGMENT);
                                if (groupTopicFragment != null) {
                                    mainActivity.startFragment(groupTopicFragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case AppConstants.PAGE_TO_GROUP_NOTICE: //群通知
                            try {
                                BaseFragment groupNoticeFragment =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newFragmentByFid(Configure.ChatFragmentFid.GROUP_NOTICE_LIST_FRAGMENT);
                                if (groupNoticeFragment != null) {
                                    mainActivity.startFragment(groupNoticeFragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case AppConstants.PAGE_TO_GROUP_DETAIL:// 群资料页
                            try {
                                BaseFragment fra =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().getAnchorChatGroupAggregationFragment(pm.groupId, "", false);

                                if (fra != null) {
                                    mainActivity.startFragment(fra);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case AppConstants.PAGE_TO_CHAT_COMMENT:
                            try {
                                BaseFragment fra =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().newCommentAndLikeFragment(IChatNewsCenterBuzType.NEWS_CENTER_COMMENT, pm.commentId);
                                if (fra != null) {
                                    mainActivity.startFragment(fra);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_HANDLE_GROUP_INVITE:
                            try {
                                BaseFragment fra = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction()
                                        .newHandleGroupInviteFragment(pm.groupId, pm.groupInviteId);
                                if (fra != null) {
                                    mainActivity.startFragment(fra);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_NEWSCENTER_COMMENT_LIKE_PAGE:
                            try {

                                BaseFragment fragment =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction()
                                                .newFragmentByFid(Configure.ChatFragmentFid.COMMENT_AND_LIKE_FRAGMENT);

                                if (fragment != null) {
                                    Bundle bundle = new Bundle();
                                    bundle.putInt("chat_newscenter_notify_type", Integer.parseInt(pm.type.trim()));
                                    fragment.setArguments(bundle);
                                    mainActivity.startFragment(fragment);
                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_NEWSCENTER_NOTIFY_PAGE: {
                            try {
                                BaseFragment notifyzfragment =
                                        Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction()
                                                .newFragmentByFid(Configure.ChatFragmentFid.NEWSCENTER_NOTIFY_FRAGMENT);

                                if (notifyzfragment != null) {
                                    mainActivity.startFragment(notifyzfragment);
                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        }

                        default:
                            break;

                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
    }

    private void gotoFeedBundleAsync(final PushModel pm, MainActivity activity) {

        if (pm == null || activity == null) {

            return;
        }

        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {

                if (TextUtils.equals(Configure.feedBundleModel.bundleName,
                        bundleModel.bundleName)) {


                    switch (pm.messageType) {

                        case AppConstants.PAGE_TO_DYNAMIC_DETAIL:

                            try {

                                Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction()
                                        .startDynamicDetailFragmentNewOnlyForIting(mainActivity, pm.articleId, pm.channel, false);

                            } catch (Exception e) {

                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_COMMUNITY_POST:

                            try {

                                BaseFragment fragment =
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction()
                                                .newDynamicDetailFragmentNew(pm.articleId, false);

                                if (fragment != null) {

                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {

                                e.printStackTrace();
                            }


                            break;

                        case AppConstants.PAGE_TO_TOPIC_DETAIL:
                            try {
                                TopicDetailParam topicDetailParam = new TopicDetailParam();
                                topicDetailParam.hideRelated = pm.hideRelated;
                                topicDetailParam.topicId = pm.feedTopicId;
                                BaseFragment topDetailFragment =
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().newTopicDetailFragment(topicDetailParam);

                                if (topDetailFragment != null) {

                                    mainActivity.startFragment(topDetailFragment);
                                }

                            } catch (Exception e) {

                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_NEW_TOPIC_DETAIL:
                            try {

                                BaseFragment topDetailFragment =
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().newDynamicTopicDetailFragment(pm.pkTopicId);

                                if (topDetailFragment != null) {

                                    mainActivity.startFragment(topDetailFragment);
                                }

                            } catch (Exception e) {

                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_DYNAMIC_COMMENT_REPLY_DETAIL:
                            try {
                                BaseFragment2 fragment =
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().newDynamicCommentReplyListFragment(pm.feedId, 0, 0, pm.rootCommentId, true);

                                mainActivity.startFragment(fragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_CREATE_DYNAMIC_FRAGMENT: {
                            try {
                                if (!UserInfoMannage.hasLogined()) {
                                    UserInfoMannage.gotoLogin(mainActivity);
                                    return;
                                }
                                BaseFragment2 createDynamicFragment;
                                if (!TextUtils.isEmpty(pm.localPicPath)) {
                                    createDynamicFragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                            .getFragmentAction()
                                            .newCreateDynamicFragmentForITing(pm.text, pm.localPicPath);
                                } else {
                                    createDynamicFragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                            .getFragmentAction()
                                            .newCreateDynamicFragmentForITing(pm.text);
                                }
                                mainActivity.startFragment(createDynamicFragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        }
                        case AppConstants.PAGE_TO_DYNAMIC_SHORT_VIDEO:
                            Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
                                @Override
                                public void onInstallSuccess(BundleModel bundleModel) {
                                    if (TextUtils.equals(Configure.videoBundleModel.bundleName,
                                            bundleModel.bundleName)) {
                                        try {
                                            if (!pm.notOpenIfFragmentExist) {
                                                long[] videoIdArrTemp = null;
                                                BaseFragment2 createDynamicFragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                                        .getFragmentAction()
                                                        .newDynamicShortVideoDetailFragmentFromITing(
                                                                pm.feedId,
                                                                pm.startTime,
                                                                pm.paramsFilePath,
                                                                pm.source,
                                                                pm.topicId,
                                                                pm.topicName,
                                                                pm.ubtTraceId,
                                                                pm.mRecSrc,
                                                                pm.mRecTrack
                                                        );
                                                if (!TextUtils.isEmpty(pm.ids)) {
                                                    try {
                                                        String[] split = pm.ids.split(",");
                                                        if (split != null && split.length > 0) {
                                                            long[] videoIdArr = new long[split.length];
                                                            for (int i = 0; i < split.length; i++) {
                                                                try {
                                                                    videoIdArr[i] = Long.parseLong(split[i]);
                                                                } catch (Exception e) {
                                                                    e.printStackTrace();
                                                                }
                                                            }
                                                            videoIdArrTemp = videoIdArr;
                                                            Bundle bundle = new Bundle();
                                                            bundle.putLongArray(BundleKeyConstants.KEY_VIDEO_ID_ARRAY, videoIdArr);
                                                            createDynamicFragment.setArguments(bundle);
                                                        }
                                                    } catch (Exception e) {
                                                        e.printStackTrace();
                                                    }
                                                }

                                                ShortVideoItingUtil.updateItingData(pm.source, pm.feedId, videoIdArrTemp);

                                                mainActivity.startFragment(createDynamicFragment);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }

                                @Override
                                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                                }

                                @Override
                                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                                }
                            });
                            break;
                        case AppConstants.PAGE_TO_FEED_RECOMMEND:
                            try {
                                if (MyListenAbUtil.INSTANCE.isKeepSubscribeInMine()) {
                                    gotoMainBundleAsync(pm, mainActivity);
                                } else {
                                    mainActivity.clearAllFragmentFromManageFragment();
                                    mainActivity.hidePlayFragment(null);
                                    mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);

                                    FragmentManager fragmentManager = mainActivity.getSupportFragmentManager();
                                    BaseFragment2 findingFragment = (BaseFragment2) fragmentManager.findFragmentByTag(String.valueOf(TAB_FINDING));
                                    if (pm.keepPage) {
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().showFeedRecommendTab(findingFragment, Integer.parseInt(pm.tabId));
                                    } else {
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().showFeedTabByItingTabId(findingFragment, Integer.parseInt(pm.tabId), 0);
                                    }
                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_FEED_RECOMMEND_DYNAMIC_POP:
                            try {

                                mainActivity.clearAllFragmentFromManageFragment();
                                mainActivity.hidePlayFragment(null);
                                mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);

                                FragmentManager fragmentManager = mainActivity.getSupportFragmentManager();
                                BaseFragment2 findingFragment = (BaseFragment2) fragmentManager.findFragmentByTag(String.valueOf(TAB_FINDING));
                                Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().showFeedCreateDynamicPop(findingFragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_FEED_CHOOSE_VIDEO:
                            try {

                                mainActivity.clearAllFragmentFromManageFragment();
                                mainActivity.hidePlayFragment(null);
                                mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_FINDING, null);

                                FragmentManager fragmentManager = mainActivity.getSupportFragmentManager();
                                BaseFragment2 findingFragment = (BaseFragment2) fragmentManager.findFragmentByTag(String.valueOf(TAB_FINDING));
                                Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFunctionAction().openVideoChoosePage(findingFragment);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_FEED_ANCHOR_VIDEO_FRAGMENT:
                            try {
                                if (!UserInfoMannage.hasLogined()) {
                                    UserInfoMannage.gotoLogin(mainActivity);
                                    return;
                                }

                                BaseFragment2 feedAnchorVideoFragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                        .getFragmentAction()
                                        .newFeedAnchorVideoFragment(pm.feedId);
                                if (feedAnchorVideoFragment != null) {
                                    mainActivity.startFragment(feedAnchorVideoFragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_TO_BOOK_TOPIC_LIST:
                            try {
                                BaseFragment2 bookTopicListFragment = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED)
                                        .getFragmentAction()
                                        .newBookTopicListFragment();
                                if (bookTopicListFragment != null) {
                                    mainActivity.startFragment(bookTopicListFragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }

        });
    }

    private void gotoShootBundleAsync(final PushModel pm, MainActivity activity) {

        if (pm == null || activity == null) {

            return;
        }

        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_SHOOT, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {

                if (TextUtils.equals(Configure.shootBundleModel.bundleName,
                        bundleModel.bundleName)) {


                    switch (pm.messageType) {

                        case AppConstants.PAGE_TO_SHOOT_CAPTURE:


                            try {
                                if (!UserInfoMannage.hasLogined()) {
                                    UserInfoMannage.gotoLogin(mainActivity);
                                    return;
                                }
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().checkPermission(mainActivity,
                                        mainActivity,
                                        new HashMap<String, Integer>() {
                                            {
                                                put(Manifest.permission.RECORD_AUDIO, com.ximalaya.ting.android.host.R.string.host_deny_perm_record);
                                                put(Manifest.permission.CAMERA, com.ximalaya.ting.android.host.R.string.host_deny_perm_camera);
                                                put(Manifest.permission.WRITE_EXTERNAL_STORAGE, com.ximalaya.ting.android.host.R.string.host_deny_perm_read_sdcard);
                                            }
                                        },
                                        new IMainFunctionAction.IPermissionListener() {
                                            @Override
                                            public void havedPermissionOrUseAgree() {
                                                Router.getActionByCallback(Configure.BUNDLE_SHOOT, new Router.IBundleInstallCallback() {
                                                    @Override
                                                    public void onInstallSuccess(BundleModel bundleModel) {
                                                        try {
                                                            final MyProgressDialog dialog = ToolUtil.createProgressDialog(mainActivity, "正在初始化拍摄工具");
                                                            dialog.show();
                                                            final ShootActionRouter shootActionRouter = getActionRouter(Configure.BUNDLE_SHOOT);
                                                            shootActionRouter.getFunctionAction().downloadShootLicense(new ShootCallback.IDownloadLicenseCallback() {

                                                                @Override
                                                                public void onSuccess() {
                                                                    BaseFragment2 captureFragment = shootActionRouter.getFragmentAction().newCaptureFragment(pm.url, pm.paramsFilePath);
                                                                    if (captureFragment != null) {
                                                                        mainActivity.startFragment(captureFragment);
                                                                    }
                                                                    dialog.dismiss();
                                                                }

                                                                @Override
                                                                public void onFailure() {
                                                                    dialog.dismiss();
                                                                    CustomToast.showFailToast("拍摄工具初始化失败");
                                                                }

                                                                @Override
                                                                public void onProgress(int progress) {

                                                                }
                                                            });
                                                        } catch (Exception e) {
                                                            e.printStackTrace();
                                                        }
                                                    }

                                                    @Override
                                                    public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                                                    }

                                                    @Override
                                                    public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                                                    }
                                                }, true, BundleModel.DOWNLOAD_ASK_USER);
                                            }

                                            @Override
                                            public void userReject(Map<String, Integer> noRejectPermiss) {

                                            }
                                        });
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;
                        case AppConstants.PAGE_TO_SHOOT_UPLOAD_RECORD:
                            try {
                                Router.<ShootActionRouter>getActionRouter(Configure.BUNDLE_SHOOT).getFunctionAction().uploadShootVideoRecord(pm.uploadId, pm.shootVideoId);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });
    }


    private void gotoZoneBundleAsync(final PushModel pm, MainActivity activity) {

        if (pm == null || activity == null) {

            return;
        }

        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_ZONE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {

                if (TextUtils.equals(Configure.zoneBundleModel.bundleName,
                        bundleModel.bundleName)) {


                    switch (pm.messageType) {

                        case AppConstants.PAGE_COMMUNITY_HOMEPAGE:

                            try {
                                CommunityHomeParam param = new CommunityHomeParam();

                                param.setCommunityId(pm.communityId);
                                param.setTabId(pm.tabId);
                                param.setAutoJoin(pm.auto_join == 1);
                                param.setPopId(pm.popId);
                                param.setSource(pm.source);
                                param.setHotTopIds(pm.hotTopIds);

                                BaseFragment fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction()
                                        .newCommunityHomepageFragment(param);

                                if (fragment != null) {
                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {

                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_MY_COMMUNITIES:
                            try {

                                BaseFragment fragment =
                                        Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                                .getFragmentAction()
                                                .newMyCommunitiesFragment();

                                if (fragment != null) {

                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {

                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_QUESTION_DETAIL:

                            try {

                                BaseFragment fragment =
                                        Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                                .getFragmentAction()
                                                .newQuestionDetailFragment(pm.communityId,
                                                        pm.questionId);

                                if (fragment != null) {

                                    mainActivity.startFragment(fragment);
                                }

                            } catch (Exception e) {

                                e.printStackTrace();
                            }


                            break;
                        case AppConstants.PAGE_TO_CREATE_POST:
                            long topicId = pm.feedTopicId;
                            String type = pm.type;

                            try {

                                BaseFragment pkTopicPage =
                                        Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                                .getFragmentAction().newPkCreatePostFragment(type, topicId);

                                if (pkTopicPage != null) {

                                    mainActivity.startFragment(pkTopicPage);
                                }

                            } catch (Exception e) {

                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_CREATE_COMMUNITY:
                            if (!UserInfoMannage.hasLogined()) {
                                UserInfoMannage.gotoLogin(mainActivity);
                                return;
                            }
                            try {
                                BaseFragment2 fra = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newCreateStep1Fragment();

                                if (fra != null) {

                                    mainActivity.startFragment(fra);

                                }

                            } catch (Exception e) {

                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_COMMUNITY_SQUARE:
                            String tab_id = pm.tab_id;

                            try {
                                BaseFragment2 communitySquareFragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newCommunitySquareFragment(tab_id);

                                if (communitySquareFragment != null) {
                                    mainActivity.startFragment(communitySquareFragment);
                                }
                            } catch (Exception e) {

                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_COMMUNITY_RELATED_ALBUM:
                            try {
                                BaseFragment2 fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newStarAlbumsListFragment(pm.idolId);

                                if (fragment != null) {
                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_COMMUNITY_RELATED_TRACK:
                            try {
                                BaseFragment2 fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newStarTracksListFragment(pm.idolId);

                                if (fragment != null) {
                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;

                        case AppConstants.PAGE_TO_My_POST:
                            try {
                                BaseFragment2 fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newMyPostFragment(Integer.parseInt(pm.tab_id));

                                if (fragment != null) {
                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        case AppConstants.PAGE_TO_QUESTION_POST:
                            try {
                                BaseFragment2 fragment = Router.<ZoneActionRouter>getActionRouter(Configure.BUNDLE_ZONE)
                                        .getFragmentAction().newQuestionCreateFragment();

                                if (fragment != null) {
                                    mainActivity.startFragment(fragment);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            break;

                        default:
                            break;
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);
    }

    /**
     * 微课模块插件下线，使用前端页面替代基础功能
     * 打开微课模块的H5页面
     *
     * @param pm       数据输入
     * @param activity 上下文
     */
    private void openWeikeH5Page(final PushModel pm, Activity activity) {
        if (activity == null || !(activity instanceof MainActivity)) {
            return;
        }

        String targetUrl = null;
        if (pm.messageType == AppConstants.PAGE_WEIKE_COURSE_DETAIL) {

            if (pm.weikeCourseId > 0) {
                if (pm.weikeCourseType == 2) {
                    targetUrl = "https://mobile.ximalaya.com/gatekeeper/weike/detail/" + pm.weikeCourseId;
                } else if (pm.weikeCourseType == 1) {
                    targetUrl = "https://mobile.ximalaya.com/gatekeeper/weike/series/" + pm.weikeCourseId;
                }
            }

        } else if (pm.messageType == AppConstants.PAGE_WEIKE_PAID_LIST) {
            targetUrl = "https://mobile.ximalaya.com/gatekeeper/weike/purchased";
        } else if (pm.messageType == AppConstants.PAGE_TO_OPEN_WEIKE_LIVEROOM) {
            if (pm.weikeCourseId > 0) {
                targetUrl = "https://mobile.ximalaya.com/gatekeeper/weike/live/" + pm.weikeCourseId;
            }
        } else {
            targetUrl = null;
        }

        if (targetUrl != null) {
            BaseFragment frag = new NativeHybridFragment.Builder().url(targetUrl).showTitle(true).builder();
            ((MainActivity) activity).startFragment(frag);
        }

    }


    private void gotoRecordBundleAsync(final PushModel pm, MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }
        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_RECORD, new Router.BundleInstallCallbackWrapper() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.recordBundleModel.bundleName,
                        bundleModel.bundleName)) {

                    if (handledRecordItingModel(mainActivity)) {
                        return;
                    }
                    switch (pm.messageType) {
                        case AppConstants.PAGE_UPLOAD:
                            handleRecordUpload();
                            break;
                        case AppConstants.PAGE_SHARE_MY_TRACK:
                            handleShareTrack();
                            break;
                        case AppConstants.PAGE_TO_RECORD_FRA:
                            handleToRecordPage();
                            break;
                        case AppConstants.PAGE_IMPROVE_CLICK_RATE:
                            handleImproveClickRate();
                            break;
                        case AppConstants.PAGE_RECORD_DUB_SHOW:
                            handleDubShow(pm);
                            break;
                        case AppConstants.PAGE_EDIT_ALBUM:
                            handleAlbumEdit();
                            break;
                        case AppConstants.PAGE_VIDEO_DUB:
                        case AppConstants.PAGE_TO_AUDITION:
                            handleVideoDub();
                            break;
                        case AppConstants.PAGE_TO_DUB_MATERIAL:
                            handleDubMaterial();
                            break;
                        case AppConstants.PAGE_TO_UGC_MY_MATERIAL:
                            handleUgcMyMaterialPage();
                            break;
                        case AppConstants.PAGE_TO_AUDIO_COMIC_MATERIAL:
                            handleAudioComicMaterial();
                            break;
                        case AppConstants.PAGE_TO_COPYRIGHT_BOOK_RECORD:
                            handleToRecordBook();
                            break;
                        case AppConstants.PAGE_TO_OPEN_RECORD_HOME_PAGE:
                            handleToRecordHomePage();
                            break;
                        case AppConstants.PAGE_TO_RECORD_JOIN_CHAT_ROOM:
                            handleToRecordJoinChatRoom();
                            break;
                        case AppConstants.PAGE_TO_ANCHOR_CHILD_PROTECT_PAGE:
                            handleToAnchorChildProtectPage();
                            break;
                        case AppConstants.PAGE_TO_RECORD_TOPIC_PAGE:
                            handleToRecordTopicPage();
                            break;
                        case AppConstants.PAGE_TO_RECORD_AUDIO_COMIC:
                            handleToRecordAudioComic();
                            break;
                        case AppConstants.PAGE_TO_CREATE_ALBUM:
                            handleCreateAlbum();
                            break;
                        case AppConstants.PAGE_TO_UPLOAD_AUDIO_BY_UPLOADID:
                            handleUploadAudioByUploadId();
                            break;
                        case AppConstants.PAGE_TO_RECORD_CREATE_CHAT_ROOM:
                            handleToRecordCreateChatRoom();
                            break;
                        case AppConstants.PAGE_TO_RECORD_AI_ALBUM_LIST:
                            handleToRecordAiAlbumList();
                            break;
                        case AppConstants.PAGE_TO_RECORD_AI_ALBUM:
                            handleToRecordAiAlbum();
                            break;
                        case AppConstants.PAGE_TO_MY_WORKS:
                            handleToMyWorks();
                            break;
                        case AppConstants.PAGE_TO_RECORD_INTERACTIVE_RECORD:
                            handleToInteractiveRecord();
                            break;
                        case AppConstants.PAGE_TO_RECORD_EDIT:
                            handleToRecordEditPage();
                            break;
                        case AppConstants.PAGE_TO_TTS_PARSE_VOICE:
                            handleToRecordVoiceTTSPage();
                            break;
                        case AppConstants.PAGE_TO_AI_BROKER_TOOLS:
                            handleToAiBrokerTools();
                            break;
                        case AppConstants.PAGE_TO_RECORD_UGC_HOME_PAGE:
                            handleToRecordUgcPage();
                            break;
                        case AppConstants.PAGE_TO_RECORD_UGC_READ_PAGE:
                            handleToRecordUgcReadPage();
                            break;
                        case AppConstants.PAGE_TO_RECORD_FROM_CLOUD_EDIT:
                            handleRecordCloudEdit();
                            break;
                        default:
                            break;
                    }
                }
            }

            private void handleToMyWorks() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                LoginInfoModelNew loginInfoModelNew = UserInfoMannage.getInstance().getUser();
                if (loginInfoModelNew == null) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newMyProgramsFragmentNew(pm.recordTab, pm.recordStatusFilter);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleToInteractiveRecord() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                LoginInfoModelNew loginInfoModelNew = UserInfoMannage.getInstance().getUser();
                if (loginInfoModelNew == null) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    long tagId = Long.parseLong(pm.tag_id);
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newInteractiveRecordFragment(tagId);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            /**
             * 跳转录音剪辑页面
             * iting://open?msg_type=407&trackId=226236&url=http%3A%2F%2Faudiotest.cos.tx.xmcdn.com%2Fstorages%2F8205-audiotest%2FF8%2F8A%2FGKwaD2cI2ZFSADjOJAAA5UBw.m4a%3Fuid%3D80326%26deviceId%3D%26timestamp%3D1699414593820%26duration%3D459%26xm_sign%3D83e02e835e34b600da25f5b498d827b8&marks=4.0,7.0
             */
            private void handleToRecordEditPage() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                LoginInfoModelNew loginInfoModelNew = UserInfoMannage.getInstance().getUser();
                if (loginInfoModelNew == null) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().
                            newRecordEditFragment(pm.trackId, pm.marks, (int) pm.duration, pm.url, pm.specialPosition);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleUploadAudioByUploadId() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                // iting://open?msg_type=294&upload_id=xxx&title=xxxxx&intro=xxxx
                long uploadId = 0;
                if (!TextUtils.isEmpty(pm.uploadId)) {
                    uploadId = Long.parseLong(pm.uploadId);
                }
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordUploadFragment(uploadId, pm.title, pm.intro, (int) pm.duration);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleCreateAlbum() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    Bundle bundle = new Bundle();
                    if (!TextUtils.isEmpty(pm.tags)) {
                        bundle.putString("tags", pm.tags);
                    }
                    bundle.putInt("categoryId", pm.categoryId);
                    BaseFragment fra = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newCreateAlbumFragment(bundle);
                    mainActivity.startFragment(fra);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleAlbumEdit() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                AlbumM album = new AlbumM();
                album.setId(pm.albumId);
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction()
                            .newCreateAlbumFragment(album);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleToRecordHomePage() {
                try {
                    /*
                     * iting://open?msg_type=189&tab_id=xxx&src=xxx
                     * 6.6.48版本增加tab_id,src参数，分别指代进入录音页默认打开的业务tab_id，进入录音页来源，
                     * tab_id默认枚举值创作活动：743 普通录音：765 读短文：745 读长文：746 趣味配音：747 有声漫：748
                     * PM司志伟
                     * */
                    long tabId = TextUtils.isEmpty(pm.tab_id) ? 0 : Long.parseLong(pm.tab_id);
                    Bundle bundle = new Bundle();
                    bundle.putLong("tabId", tabId);
                    if (!TextUtils.isEmpty(pm.tags)) {
                        bundle.putString("tags", pm.tags);
                    }
                    if (!TextUtils.isEmpty(pm.src)) {
                        bundle.putString("src", pm.src);
                    }

                    if (!TextUtils.isEmpty(pm.topic)) {
                        bundle.putString("topic", pm.topic);
                    } else if (!TextUtils.isEmpty(pm.activity)) {
                        bundle.putString("activity", pm.activity);
                    }

                    if (pm.ai_content_id > 0) {
                        bundle.putLong("ai_content_id", pm.ai_content_id);

                        if (!UserInfoMannage.hasLogined()) {
                            UserInfoMannage.gotoLogin(mainActivity);
                            return;
                        }

                        if (pm.userId > 0 && UserInfoMannage.getUid() != pm.userId) {
                            DialogBuilder accountDialog = new DialogBuilder(mainActivity);
                            try {
                                ImageView ivClose = accountDialog.getView().findViewById(R.id.dialog_close_icon);
                                int dp = BaseUtil.dp2px(ToolUtil.getCtx(), 2);
                                ivClose.setPadding(dp, dp, dp, dp);
                                ImageViewCompat.setImageTintList(
                                        ivClose,
                                        ColorStateList.valueOf(mainActivity.getColor(com.ximalaya.ting.android.host.R.color.host_color_acacaf))
                                );
                                if (accountDialog.getOkBtn() != null) {
                                    accountDialog.getOkBtn().getPaint().setFakeBoldText(false);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            accountDialog.setDialogWidth(DeviceUtil.getScreenWidth(mainActivity) - BaseUtil.dp2px(mainActivity, 96));
                            accountDialog.setCloseVisible(true);
                            if (accountDialog.getView() != null) {
                                ImageView ivClose = accountDialog.getView().findViewById(R.id.dialog_close_icon);
                                if (ivClose != null) {
                                    ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) ivClose.getLayoutParams();
                                    lp.width = BaseUtil.dp2px(mainActivity, 20);
                                    lp.height = BaseUtil.dp2px(mainActivity, 20);
                                    ivClose.setLayoutParams(lp);
                                    ivClose.setImageTintList(ColorStateList.valueOf(mainActivity.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_acacaf, null)));
                                }
                            }
                            accountDialog.setTitle("请切换登录账号");
                            accountDialog.setMessage("当前手机登录的账号和网页端登录的账号不一致");
                            accountDialog.setOkBtn("切换账号", new DialogBuilder.DialogCallback() {
                                @Override
                                public void onExecute() {
                                    UserInfoMannage.gotoLogin(mainActivity, LoginByConstants.LOGIN_BY_CHANGE_USER, new Bundle());
                                }
                            });
                            accountDialog.showWarning();
                            return;
                        }
                    }
                    if (!TextUtils.isEmpty(pm.ai_content_title)) {
                        bundle.putString("ai_content_title", pm.ai_content_title);
                    }

                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordHomePageFragment(bundle);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("打开录音首页失败");
                    e.printStackTrace();
                }
            }

            private void handleToRecordVoiceTTSPage() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFunctionAction().getTTSInitRelation();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            private void handleToAiBrokerTools() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newAIBrokerAIToolsFragment(pm);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleToRecordUgcPage() {
                LoginEventBridge.goLogin(() -> {
                    try {
                        BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordUgcHomeFragment(pm);
                        mainActivity.startFragment(fragment);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }

            private void handleToRecordUgcReadPage() {
                LoginEventBridge.goLogin(() -> {
                    try {
                        String id = pm.id;
                        int type = -1;
                        if (pm.type != null) {
                            try {
                                type = Integer.parseInt(pm.type);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                        if (pm.bookId > 0) {
                            id = String.valueOf(pm.bookId);
                        }
                        if (!TextUtils.isEmpty(id)) {
                            String returnUrl = pm.extraUrl;
                            BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordUgcReadListFragment(id, type, returnUrl);
                            mainActivity.startFragment(fragment);
                            return;
                        }

                        BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordUgcReadListFragment(type, pm.secondaryType, pm.emotionType, pm.source);
                        mainActivity.startFragment(fragment);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }

            private void handleToAnchorChildProtectPage() {
                ChildProtectInfo childProtectInfo = new ChildProtectInfo();
                childProtectInfo.form = FROM_TOB;
                ChildProtectManager.openForbidPlayPage(childProtectInfo);
            }

            private void handleToRecordJoinChatRoom() {
                // iting://open?msg_type=204&room_id=xxx
                long roomId = pm.liveRoomId;
                BaseFragment fragment = null;
                try {
                    fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newChatRoomFragment(false, roomId, null);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("加入语聊房失败！");
                    e.printStackTrace();
                }
            }

            private void handleToRecordCreateChatRoom() {
                // iting://open?msg_type=316
                BaseFragment fragment = null;
                try {
                    fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newChatRoomFragment(true, 0, pm.prj_id);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("加入语聊房失败！");
                    e.printStackTrace();
                }
            }

            private void handleToRecordAiAlbum() {
                // iting://open?msg_type=356
                BaseFragment fragment = null;
                try {
                    fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newAiAlbumFragment(pm.albumId, pm.albumName);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("跳转ai专辑失败！");
                    e.printStackTrace();
                }
            }

            private void handleToRecordAiAlbumList() {
                // iting://open?msg_type=355
                BaseFragment fragment = null;
                try {
                    fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newAiAlbumListFragment(pm.albumId, pm.albumName);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("跳转ai专辑失败！");
                    e.printStackTrace();
                }
            }

            private void handleToRecordBook() {
                try {
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordTrackFragment(pm.chapterId);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("打开录音页失败");
                    e.printStackTrace();
                }
            }

            private void handleToRecordPage() {
                try {
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordTrackFragment(pm.url, 2,
                                    pm.actionId, pm.classId, pm.from_page, pm.activity_code);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("打开录音页失败");
                    e.printStackTrace();
                }
            }

            private void handleRecordUpload() {
                if (isOuterLink) {
                    XDCSCollectUtil.getInstanse().statIting(XDCSCollectUtil.APP_NAME_ITING,
                            XDCSCollectUtil.SERVICE_ITING, pm.xdcsParams);
                }

                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }

                try {
                    TrackPushInfo push = new TrackPushInfo();
                    push.setActivityId(pm.activityId);
                    push.setActivityName(pm.activityName);
                    push.setPrj_id(pm.prj_id);
                    push.setActivity_code(pm.activity_code);
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordTrackFragment(push);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("录音上传失败");
                    e.printStackTrace();
                }
            }

            private void handleRecordCloudEdit() {
                if (isOuterLink) {
                    XDCSCollectUtil.getInstanse().statIting(XDCSCollectUtil.APP_NAME_ITING,
                            XDCSCollectUtil.SERVICE_ITING, pm.xdcsParams);
                }

                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }

                if (UserInfoMannage.getUid() != pm.userId) {
                    DialogBuilder accountDialog = new DialogBuilder(mainActivity);
                    try {
                        ImageView ivClose = accountDialog.getView().findViewById(R.id.dialog_close_icon);
                        int dp = BaseUtil.dp2px(ToolUtil.getCtx(), 2);
                        ivClose.setPadding(dp, dp, dp, dp);
                        ImageViewCompat.setImageTintList(
                                ivClose,
                                ColorStateList.valueOf(mainActivity.getColor(com.ximalaya.ting.android.host.R.color.host_color_acacaf))
                        );
                        if (accountDialog.getOkBtn() != null) {
                            accountDialog.getOkBtn().getPaint().setFakeBoldText(false);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    accountDialog.setDialogWidth(DeviceUtil.getScreenWidth(mainActivity) - BaseUtil.dp2px(mainActivity, 96));
                    accountDialog.setCloseVisible(true);
                    if (accountDialog.getView() != null) {
                        ImageView ivClose = accountDialog.getView().findViewById(R.id.dialog_close_icon);
                        if (ivClose != null) {
                            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) ivClose.getLayoutParams();
                            lp.width = BaseUtil.dp2px(mainActivity, 20);
                            lp.height = BaseUtil.dp2px(mainActivity, 20);
                            ivClose.setLayoutParams(lp);
                            ivClose.setImageTintList(ColorStateList.valueOf(mainActivity.getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_acacaf, null)));
                        }
                    }
                    accountDialog.setTitle("请切换登录账号");
                    accountDialog.setMessage("检测到手机和云剪辑使用账号不一致，无法同步录音");
                    accountDialog.setOkBtn("切换账号", new DialogBuilder.DialogCallback() {
                        @Override
                        public void onExecute() {
                            UserInfoMannage.gotoLogin(mainActivity, LoginByConstants.LOGIN_BY_CHANGE_USER, new Bundle());
                        }
                    });
                    accountDialog.showWarning();
                    return;
                }

                try {
                    TrackPushInfo push = new TrackPushInfo();
                    push.setActivityId(pm.activityId);
                    push.setActivityName(pm.activityName);
                    push.setPrj_id(pm.prj_id);
                    push.setActivity_code(pm.activity_code);
                    push.setFromPage(pm.fromPage);
                    push.setUserId(pm.userId);
                    push.setTaskId(pm.taskId);
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordTrackFragment(push);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("录音上传失败");
                    e.printStackTrace();
                }
            }

            private boolean handledRecordItingModel(MainActivity mainActivity) {
                try {
                    return Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().handleItingModel(pm, mainActivity);
                } catch (Exception e) {
                    return false;
                }
            }

            private void handleToRecordAudioComic() {
                try {
                    if (ChildProtectManager.checkChildrenModeOpenFromToB(mainActivity)) {
                        return;
                    }
                    /*
                     * template_id：素材id， chapterId：章节id， chapter_name：章节名称
                     * iting://open?msg_type=222&template_id=980629&chapterId=2317&chapter_name=章节名称
                     * */
                    BaseFragment fragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newAudioComicMaterialDownloadFragment(pm.chapterName, pm.templateId, pm.chapterId);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleToRecordTopicPage() {
                try {
                    Bundle bundle = new Bundle();
                    bundle.putLong("topicId", pm.dubTopicId);
                    bundle.putString("topicContent", pm.dubTopicName);
                    bundle.putBoolean("canShowSelectCommunity", false);
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newRecordTrackFragment(bundle);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleShareTrack() {
                if (pm.trackId > 0) {
                    try {
                        DialogFragment fragment =
                                Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newShareTrackDialogFragment(pm.trackId);
                        fragment.show(mainActivity.getSupportFragmentManager(),
                                "ShareTrackDialogFragment");
                    } catch (Exception e) {
                        CustomToast.showFailToast("声音分享失败");
                        e.printStackTrace();
                    }
                }
            }

            private void handleImproveClickRate() {
                try {
                    BaseFragment fragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newAlbumClickRateImproveFragment(pm.albumId, null);
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    CustomToast.showFailToast("专辑提升点击率页面跳转失败");
                    e.printStackTrace();
                }
            }

            private void handleDubShow(PushModel pm) {
                String channel = TextUtils.isEmpty(pm.src) ? "iting" : pm.src;
                try {
                    if (ChildProtectManager.checkChildrenModeOpenFromToB(mainActivity)) {
                        return;
                    }

                    BaseFragment dubbingFragment =
                            Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction().newDubMaterialSquareFragment(channel);
                    if (dubbingFragment != null) {
                        mainActivity.startFragment(dubbingFragment, R.anim.main_slide_in_bottom,
                                R.anim.main_slide_out_bottom);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("页面打开失败");
                }
            }

            private void handleVideoDub() {
                try {
                    if (pm.videoId > 0) {
                        Router.getActionByCallback(Configure.BUNDLE_RECORD, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                if (bundleModel.bundleName.equals(Configure.recordBundleModel.bundleName)) {

                                    if (ChildProtectManager.checkChildrenModeOpenFromToB(mainActivity)) {
                                        return;
                                    }
                                    BaseFragment baseFragment = null;
                                    DubTransferModel dubTransferModel =
                                            new DubTransferModel.DubTransferItemBuilder()
                                                    .setMaterialId(pm.videoId)
                                                    .setTopicId(0)
                                                    .setTopicName(null)
                                                    .setTopicUploadType(0)
                                                    .setSource(pm.source)
                                                    .setFromType(0)
                                                    .setUp();
                                    try {
                                        baseFragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD)
                                                .getFragmentAction()
                                                .newVideoDubMakeFragment(dubTransferModel);
                                        mainActivity.startFragment(baseFragment);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                            }
                        });
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("页面打开失败");
                }
            }

            private void handleUgcMyMaterialPage() {
                try {
                    BaseFragment ugcMyMaterialFragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction()
                            .newUgcMyMaterialFragment();
                    if (ugcMyMaterialFragment != null) {
                        mainActivity.startFragment(ugcMyMaterialFragment, com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("页面打开失败");
                }
            }

            private void handleAudioComicMaterial() {
                try {
                    if (ChildProtectManager.checkChildrenModeOpenFromToB(mainActivity)) {
                        return;
                    }
                    BaseFragment audioComicFragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction()
                            .newAudioComicFragment();
                    if (audioComicFragment != null) {
                        mainActivity.startFragment(audioComicFragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("页面打开失败");
                }
            }

            private void handleDubMaterial() {
                try {
                    BaseFragment dubMaterialFragment = null;
                    if (!TextUtils.isEmpty(pm.tag_id)) {
                        dubMaterialFragment = Router.<RecordActionRouter>getActionRouter(Configure.BUNDLE_RECORD).getFragmentAction()
                                .newDubMaterialCommonListFragment(pm.title, pm.tag_id,
                                        pm.order_by, pm.type_id);
                    }
                    if (dubMaterialFragment != null) {
                        mainActivity.startFragment(dubMaterialFragment,
                                R.anim.main_slide_in_bottom, R.anim.main_slide_out_bottom);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("页面打开失败");
                }
            }
        }, true, BundleModel.DOWNLOAD_ASK_USER);
    }

    private void gotoMainBundleAsync(final PushModel pm, final MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }
        final MainActivity mainActivity = activity;

        Router.getActionByCallback(Configure.BUNDLE_MAIN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.mainBundleModel.bundleName,
                        bundleModel.bundleName)) {

                    switch (pm.messageType) {
                        case AppConstants.PAGE_FANS:
                            handleFanPage(pm);
                            break;
                        case AppConstants.PAGE_FRIEND:
                            handleFriendPage();
                            break;
                        case AppConstants.PAGE_HOMEPAGE:
                            handleHomePage();
                            break;
                        case AppConstants.PAGE_SOUND:
                            handleSoundPage();
                            VipGiftShareManager.INSTANCE.receiveVipShareGift(pm);
                            break;
                        case AppConstants.PAGE_TO_PLAY_ALBUM_LAST_TRACK:
                            handlePlayAlbumLastTrack();
                            break;
                        case AppConstants.PAGE_BIND_PHONE:
                            handleBindPhonePage();
                            break;
                        case AppConstants.PAGE_ALBUM_PLAY:
                            handleAlbumPlayPage(mainActivity);
                            break;
                        case AppConstants.PAGE_MINE_CENTER:
                            handleManageCenterPage();
                            break;
                        case AppConstants.PAGE_SEARCH_RESULT:
                            handleSearchResultPage();
                            break;
                        case AppConstants.PAGE_TING_LIST:
                            handleTingListPage();
                            break;
                        case AppConstants.PAGE_TO_BOUGHT:
                            handleBoughtPage(pm);
                            break;
                        case AppConstants.PAGE_TO_EDIT:
                        case AppConstants.PAGE_TO_MY_DETAIL:
                            handleEditPage();
                            break;
                        case AppConstants.PAGE_TO_WALLET:
                            handleWalletPage();
                            break;
                        case AppConstants.PAGE_TO_RECHARGE_ITEM:
                            handleRechargePage();
                            break;
                        case AppConstants.PAGE_TO_DAKA:
                            handleDakaPage();
                            break;
                        case AppConstants.PAGE_TO_LIVE_HOME_PAGE:
                        case AppConstants.PAGE_TO_LIVE_HOME_PAGE_SELECTED_CATEGORY_TAB:
                            handleMainLivePage(pm);
                            break;
                        case AppConstants.PAGE_TO_FEED_RECOMMEND:
                            handleMainConcernPage(pm);
                            break;
                        case AppConstants.PAGE_TO_TINGYOU:
                            handleTingYouPage();
                            break;
                        case AppConstants.PAGE_TO_FEEDBACK:
                            handleFeedbackPage();
                            break;
                        case AppConstants.PAGE_TO_RADIO_LIST:
                            handleRadioListPage();
                            break;
                        case AppConstants.PAGE_TO_RADIO_PROVINCE_LIST:
                            handleRadioProvinceListPage();
                            break;
                        case AppConstants.PAGE_TO_COMMENT_LIST:
                            handleCommentListPage();
                            break;
                        case AppConstants.PAGE_TO_OPEN_SEARCH_TRACK:
                            handleSearchTrackPage();
                            break;
                        case AppConstants.PAGE_TO_DOWNLOADED:
                            handleDownloadedPage(pm);
                            break;
                        case AppConstants.PAGE_TO_HISTORY:
                            handleHistoryPage(pm);
                            break;
                        case AppConstants.PAGE_TO_LIKE_PAGE:
                            handleLikePage(pm);
                            break;
                        case AppConstants.PAGE_TO_COMMENT_DETAIL:
                            handleCommentDetail(pm);
                            break;
                        case AppConstants.PAGE_TO_TINGLIST:
                            handleTingList(pm);
                            break;
                        case AppConstants.PAGE_TO_MY_TINGLIST:
                            handleMyTingList(pm);
                            break;
                        case AppConstants.PAGE_TO_FREE_ALBUM_RATE_DETAIL:
                        case AppConstants.PAGE_TO_PAID_ALBUM_RATE_DETAIL:
                            handleFreeAlbumRateDetail(pm);
                            break;
                        case AppConstants.PAGE_TO_ALBUM_RATE:
                            handleAlbumRate(pm.albumId);
                            break;
                        case AppConstants.PAGE_TO_XIMALAYA_HOT_COMMENT:
                            handleHotComment(pm.commentid);
                            break;
                        case AppConstants.PAGE_TO_ANCHOR_MY_ALL_ALBUMS:
                            handleAnchorAllAlbum();
                            break;
                        case AppConstants.PAGE_TO_MODIFY_PWD:
                            handleModifyPwdFragment();
                            break;
                        case AppConstants.PAGE_TO_CHILD_PROTECT_FORGET_PED:
                            handleChildProtectForgetPwdFragment();
                            break;
                        case AppConstants.PAGE_TO_DOWNLOAD_CACHE:
                            handleDownloadCacheFragment();
                            break;
                        case AppConstants.PAGE_TO_UNLOCK_PAID_ALBUM:
                            handleUnLockPaidAlbumFragment();
                            break;
                        case AppConstants.PAGE_TO_KACHA_POST:
                            handleKachaPostFragment();
                            break;
                        case AppConstants.PAGE_TO_TRACK_PROGRESS:
                            handleSeekSoundProgress();
                            break;
                        case AppConstants.PAGE_TO_PODCAST:
                            handle2PodcastFragment();
                            break;
                        case AppConstants.PAGE_TO_PODCAST_RANK:
                            handle2PodcastRankFragment(pm.topId);
                            break;
                        case AppConstants.PAGE_TO_PODCAST_ALL_CATEGORY:
                            handle2PodcastAllCategoryFragment();
                            break;
                        case AppConstants.PAGE_TO_COMMENT_THEME_PAGE:
                            handle2CommentThemePage(pm.trackId, pm.albumId, pm.commentActivityType, pm.commentActivityId);
                            break;
                        case AppConstants.PAGE_TO_RECOMMEND_BOTTOM_DIALOG:
                            handle2RecommendBottomDialog();
                            break;
                        case AppConstants.PAGE_TO_REWARD_DIAN_DIALOG:
                            handle2RewardDianDialog();
                            break;
                        case AppConstants.PAGE_TO_SWITCH_ACCOUNT:
                            handleSwitchAccount();
                            break;
                        default:
                            break;
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }

            private void handle2CommentThemePage(long trackId, long albumId, int commentActivityType, int commentActivityId) {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                if (commentActivityType == CommentThemeUtil.COMMENT_THEME_TYPE_ANCHOR_BUILDING_ACTIVITY ||
                        commentActivityType == CommentThemeUtil.COMMENT_THEME_TYPE_ANCHOR_LOTTERY_ACTIVITY) {
                    CommentThemePageFragment commentThemePageFragment = new CommentThemePageFragment(
                            trackId, albumId, PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_1, commentActivityId);
                    mainActivity.startFragment(commentThemePageFragment);
                } else if (commentActivityType == CommentThemeUtil.COMMENT_THEME_TYPE_AWARDS_ANNOUNCEMENT) {
                    mainActivity.startFragment(CommentLotteryThemeResultFragment.Companion.
                            newInstance(trackId, albumId, commentActivityId));
                }

            }

            private void handle2RecommendBottomDialog() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                RecommendBottomDialogCaller.handleIting(mainActivity, pm.type, pm.albumId, pm.trackId);
            }

            private void handleSwitchAccount() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }

                try {
                    if (ChildProtectManager.isChildBind(mainActivity) && ChildProtectManager.isChildProtectOpen(mainActivity)) {
                        CustomToast.showToast("此设备已开启未成年人模式，无法切换账号");
                    } else {
                        if (!UserInfoMannage.hasLogined()) {
                            UserInfoMannage.gotoLogin(mainActivity);
                            return;
                        }

                        Router.<LoginActionRouter>getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                BaseFragment2 changeAccountFragment = null;
                                try {
                                    changeAccountFragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction().getChangeAccountFragment();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                mainActivity.startFragment(changeAccountFragment);
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                            }
                        });

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handle2RewardDianDialog() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }

                final RewardDianGiftDialog dialog = new RewardDianGiftDialog.Builder(mainActivity)
                        .setReceiveUid(pm.uid)
                        .setSrcType(pm.srcType)
                        .setTargetId(pm.targetId)
                        .setItingString(pm.data)
                        .builder();
                if (dialog != null) {
                    dialog.show();
                }
            }

            private void handle2PodcastFragment() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                mainActivity.startFragment(PodCastFragmentV2.newInstance(true));
            }

            private void handle2PodcastAllCategoryFragment() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                PodCastCategoryListFragment.startFromActivity(mainActivity, pm.categoryId);
            }

            private void handle2PodcastRankFragment(int topId) {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                int initIndex;
                if (topId > 0 && topId < 4) {
                    initIndex = topId - 1;
                } else {
                    initIndex = 0;
                }
                // 默认声音榜
                mainActivity.startFragment(PodCastRankFragment.newInstance(initIndex));
            }

            //跳转到咔嚓投稿页面
            private void handleKachaPostFragment() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }
                mainActivity.startFragment(KachaPostFragment.newInstance(pm.activityId));
            }

            // 跳转到可以解锁的付费声音专辑
            private void handleUnLockPaidAlbumFragment() {
                if (null == mainActivity) {
                    sendToMainActivityToHandle(activity);
                    return;
                }

                AlbumEventManage.AlbumFragmentOption option =
                        new AlbumEventManage.AlbumFragmentOption();
                option.activityParams = pm.albumActivityParams;
                option.fromLiveParams = pm.fromLiveParams;
                if (pm.trackId > 0) {
                    option.trackId = pm.trackId;
                }
                option.isFromUnLock = true;
                option.unLockPageSource = pm.source;
                int from = AlbumEventManage.FROM_UNDEFINED;
                AdUnLockPaidManager.updatePaidPageSource(pm.albumId, pm.source);
                AlbumEventManage.startMatchAlbumFragment(pm.albumId, from, ConstantsOpenSdk.PLAY_FROM_NONE,
                        null, null
                        , -1, activity, option);
            }

            private void handleAnchorAllAlbum() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                MyAllAlbumCommentsFragment fragment = new MyAllAlbumCommentsFragment();
                mainActivity.startFragment(fragment);
            }

            private void handleHotComment(long commentId) {
                SelectedHotCommentFragment fragment = SelectedHotCommentFragment.newInstance(commentId);
                mainActivity.startFragment(fragment);
            }

            private void handleAlbumRate(final long albumId) {
                MainCommonRequest.isAlbumRateAvailable(albumId, new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean object) {
                        if (object != null) {
                            if (object) {
                                BaseFragment2 fragment = CreateAlbumRateFragment.newInstanceForCreate(albumId, 0, SharedConstant.ALBUM_RATE_CHANNEL_OTHER, pm.isPaid, 0);
                                mainActivity.startFragment(fragment);
                            } else {
                                CustomToast.showFailToast("您还不能评价该专辑！");
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {

                    }
                });
            }

            private void handleFreeAlbumRateDetail(PushModel pm) {
                AlbumRateDetailFragment fragment = AlbumRateDetailFragment.newInstance(pm.albumId, pm.commentid, true, true);
                mainActivity.startFragment(fragment);
            }

            private void handleMyTingList(PushModel pm) {
                MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                    if (mainActivity != null && mainActivity.canUpdateUi()) {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null) {
                            BaseFragment2 frag = fragAction.newTingListFragment();
                            mainActivity.startFragment(frag);
                        }
                    }
                });
            }

            private void handleCommentDetail(PushModel pm) {
                try {
                    BaseFragment fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newCommentDetailFragment(pm.trackId, pm.commentid);
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleLikePage(PushModel pm) {
                MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                    IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                    if (fragAction != null && mainActivity != null && mainActivity.canUpdateUi()) {
                        BaseFragment2 fra = fragAction.newMyLikeFragment();
                        Bundle arguments = fra.getArguments();
                        if (arguments == null) {
                            arguments = new Bundle();
                        }
                        TingListInfoModel myLike = new TingListInfoModel();
                        myLike.setSource(TingListConstants.SOURCE_MY_LIKE);
                        myLike.setTitle("我喜欢的声音");
                        myLike.setOpType(TingListConstants.TYPE_TRACK);
                        arguments.putParcelable("model", myLike);
                        arguments.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST, pm.playFirst);
                        fra.setArguments(arguments);
                        mainActivity.startFragment(fra);
                    }
                });
            }

            private void handleDownloadedPage(PushModel pm) {
                try {
                    MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null && mainActivity.canUpdateUi()) {
                            BaseFragment2 frag = fragAction.newDownloadFragment();
                            Bundle bundle = frag.getArguments();
                            if (bundle == null) {
                                bundle = new Bundle();
                            }
//                            bundle.putString(BundleKeyConstants.KEY_SOURCE, AppConstants.DOWNLOAD_FROM_HOME_PAGE);
//                            bundle.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST, pm.playFirst);
                            bundle.putString(BundleKeyConstants.KEY_MYLISTEN_IS_FROM, pm.myListenFrom);
                            if (frag != null) {
                                frag.setArguments(bundle);
                                mainActivity.startFragment(frag);
                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleHistoryPage(PushModel pm) {
                MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                    IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                    if (fragAction != null && mainActivity != null && mainActivity.canUpdateUi()) {
                        if (pm.openMonthlyTicketDialog) {
                            BaseFragment frag = fragAction.newHistoryPlayFragmentForMonthlyVote();
                            if (frag != null) {
                                mainActivity.startFragment(frag);
                            }
                        } else {
                            BaseFragment frag = fragAction.newHistoryFragment(true, false, true, pm.myListenFrom);
                            if (frag != null) {
                                Bundle arguments = frag.getArguments();
                                if (arguments == null) {
                                    arguments = new Bundle();
                                }
                                arguments.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST, pm.playFirst);
                                if (pm.sharePoint > 0) {
                                    arguments.putInt(BundleKeyConstants.KEY_HISTORY_SHARE_POINT, pm.sharePoint);
                                    arguments.putString("sourceType", "任务");
                                }
                                frag.setArguments(arguments);
                                mainActivity.startFragment(frag);
                            }
                        }
                    }
                });
            }

            private void handleSearchTrackPage() {
                try {
                    BaseFragment fragment =
                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchAlbumTrackFragment(pm.albumId) : null;
                    if (fragment != null) {
                        mainActivity.startFragment(fragment);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleCommentListPage() {
//                try {
//                    BaseFragment fra = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newCommentListFragment(pm.trackId, PlayingSoundInfo.OtherInfo.ALLOW_COMMENT_TYPE_LEVEL_1);
//                    if (fra != null) {
//                        mainActivity.startFragment(fra);
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }

                final Track sound = new Track();
                sound.setDataId(pm.trackId);
                sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);

                pm.focusPlayTabType = PlayPageTab.TYPE_COMMENT;

                if (XmPlayerManager.getInstance(activity).isConnected()) {
                    gotPlayPageAfterConnected(sound);
                } else {
                    mLastHandleTrackTime = System.currentTimeMillis();
                    XmPlayerManager.getInstance(activity).setIfInAppInitialization(false);
                    XmPlayerManager.getInstance(activity).init(true);
                    XmPlayerManager.getInstance(activity).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                        @Override
                        public void onConnected() {
                            XmPlayerManager.getInstance(activity).removeOnConnectedListerner(this);
                            //超过30s放弃以前的操作
                            if (System.currentTimeMillis() - mLastHandleTrackTime > 30_000) {
                                return;
                            }

                            if (activity == null || sound == null || pm == null) {
                                return;
                            }
                            gotPlayPageAfterConnected(sound);
                        }
                    });
                }
            }

            private void handleTingList(PushModel pm) {
                MyListenRouterUtil.getMyListenBundle(bundleModel -> {
                    if (mainActivity != null && mainActivity.canUpdateUi()) {
                        IMyListenFragmentAction fragAction = MyListenRouterUtil.getFragAction();
                        if (fragAction != null) {
                            BaseFragment2 frag = fragAction.newTingListDetailFragment(pm.albumId, pm.oneKeyNormalExtra, pm.trackId, pm.toAlbumId);
                            mainActivity.startFragment(frag);
                        }
                    }
                });
            }

            private void handleRadioProvinceListPage() {
                try {
                    BaseFragment fra =
                            Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO)
                                    .getFragmentAction().newLiveProvinceRadioFragment();
                    if (fra != null) {
                        mainActivity.startFragment(fra);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleRadioListPage() {
                try {
                    BaseFragment fra =
                            Router.<RadioActionRouter>getActionRouter(Configure.BUNDLE_RADIO)
                                    .getFragmentAction().newRadioListFragment(pm.api, pm.title, pm.radioCategoryId, pm.type);
                    if (fra != null) {
                        mainActivity.startFragment(fra);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleFeedbackPage() {
                try {
                    BaseFragment feedBackMainFragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newFeedBackMainFragment(new String[]{pm.screenShotPath});
                    mainActivity.startFragment(feedBackMainFragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleTingYouPage() {
                try {
                    Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
                        @Override
                        public void onInstallSuccess(BundleModel bundleModel) {
                            if (bundleModel == null || bundleModel != Configure.feedBundleModel) {
                                return;
                            }

                            BaseFragment dynamicMessageFragment = null;
                            try {
                                dynamicMessageFragment =
                                        Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction().newListenerGroupMessageFragment();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (dynamicMessageFragment != null) {
                                mainActivity.startFragment(dynamicMessageFragment);
                            }
                        }

                        @Override
                        public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                        }

                        @Override
                        public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleMainConcernPage(PushModel pm) {
                mainActivity.clearAllFragmentFromManageFragment();
                mainActivity.hidePlayFragment(null);
                if (mainActivity.getTabFragmentManager() != null
                        && mainActivity.getTabFragmentManager().getCurrentTab() == TAB_HOME_PAGE) {
                    // 当前已选中 HomePageFragment
                    try {
                        mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE,
                                null);
                        IMainFragmentAction fragmentAction =
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction();
                        Fragment currentFra =
                                mainActivity.getTabFragmentManager().getCurrFragment();
                        if (pm != null && currentFra != null) {
                            Bundle bundle = currentFra.getArguments();
                            if (bundle == null) {
                                bundle = new Bundle();
                            }
//                            bundle.putInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID, pm.segmentId);
                            bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, pm.playSource);
                            currentFra.setArguments(bundle);
                        }
                        if (fragmentAction != null) {
                            fragmentAction.switchChildTabInFindingFragment(currentFra,
                                    TabFragmentManager.INTENT_CHILD_TAB_CONCERN);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    // 当前未选中 HomePageFragment
                    Bundle bundle = new Bundle();
                    bundle.putString(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY,
                            TabFragmentManager.INTENT_CHILD_TAB_CONCERN);
                    if (pm != null) {
                        bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, pm.playSource);
//                        bundle.putInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID
//                                , pm.segmentId);
                    }
                    mainActivity.checkRadio(TAB_HOME_PAGE, bundle);
                }
            }

            private void handleMainLivePage(PushModel pm) {
                mainActivity.clearAllFragmentFromManageFragment();
                mainActivity.hidePlayFragment(null);
                if (mainActivity.getTabFragmentManager() != null
                        && mainActivity.getTabFragmentManager().getCurrentTab() == TAB_HOME_PAGE) {
                    // 当前已选中 HomePageFragment
                    try {
                        mainActivity.showFragmentInMainFragment(TabFragmentManager.TAB_HOME_PAGE,
                                null);
                        IMainFragmentAction fragmentAction =
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction();
                        Fragment currentFra =
                                mainActivity.getTabFragmentManager().getCurrFragment();
                        if (pm != null && currentFra != null) {
                            Bundle bundle = currentFra.getArguments();
                            if (bundle == null) {
                                bundle = new Bundle();
                            }
                            bundle.putInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID, pm.segmentId);
                            bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, pm.playSource);
                            currentFra.setArguments(bundle);
                        }
                        if (fragmentAction != null) {
                            fragmentAction.switchChildTabInFindingFragment(currentFra,
                                    TabFragmentManager.INTENT_CHILD_TAB_ANCHOR_LIVE);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    // 当前未选中 HomePageFragment
                    Bundle bundle = new Bundle();
                    bundle.putString(TabFragmentManager.INTENT_CHILD_TAB_IN_FIND_KEY,
                            TabFragmentManager.INTENT_CHILD_TAB_ANCHOR_LIVE);
                    if (pm != null) {
                        bundle.putInt(BundleKeyConstants.KEY_PLAY_SOURCE, pm.playSource);
                        bundle.putInt(BundleKeyConstants.KEY_LIVE_HOME_PAGE_SELECTED_CATEGORY_ID
                                , pm.segmentId);
                    }
                    mainActivity.checkRadio(TAB_HOME_PAGE, bundle);
                }
            }

            private void handleDakaPage() {
                BaseFragment fragment = null;
                try {
                    fragment =
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newFragmentByFid(Configure.MainFragmentFid.LISTEN_CALENDAR_FRAGMENT);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mainActivity.startFragment(fragment);
            }

            private void handleRechargePage() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                try {
                    if (pm.productType == 1) {
                        //喜钻充值
                        mainActivity.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRechargeDiamondFragment(Configure.RechargeFragmentFid.DIAMOND_RECHARGE, pm.amount));
                    } else {
                        //喜点充值
                        mainActivity.startFragment(Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newRechargeFragment(PayManager.TO_RECHARGE_CHOOSE, pm.amount));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleWalletPage() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }

                Fragment fragment = null;
                try {
                    fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newMyWalletFragment();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (fragment != null) {
                    mainActivity.startFragment(fragment);
                }
            }

            private void handleEditPage() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                BaseFragment fragment1 = null;
                try {
                    fragment1 = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newMyDetailFragment();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (fragment1 != null) {
                    mainActivity.startFragment(fragment1);
                }
            }

            private void handleBoughtPage(PushModel pm) {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                Fragment boughtBothFragment = null;
                try {
                    BaseFragment2 frag = MyListenRouterUtil.getFragAction().newBoughtFragment();
                    frag.fid = Configure.MainFragmentFid.BOUGHT_BOTH_FRAGMENT;
                    boughtBothFragment = frag;
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (boughtBothFragment != null) {
                    Bundle args = boughtBothFragment.getArguments();
                    if (args == null) {
                        args = new Bundle();
                    }

                    if (pm != null) {
                        args.putBoolean(BundleKeyConstants.KEY_PLAY_FIRST, pm.playFirst);
                    }
                    boughtBothFragment.setArguments(args);
                    mainActivity.startFragment(boughtBothFragment);
                }
            }

            private void handleTingListPage() {
                BaseFragment fragment = null;
                try {
                    fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                            .newFragmentByFid(Configure.MainFragmentFid.SUBJECT_LIST_FRAGMENT);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                mainActivity.startFragment(fragment);
            }

            private void handleSearchResultPage() {
                BaseFragment fragment = null;
                try {
                    fragment =
                            Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction() != null ?
                                    Router.<SearchActionRouter>getActionRouter(Configure.BUNDLE_SEARCH).getFragmentAction().newSearchFragmentByWordAndSearchNow(pm.term, pm.keyword, pm.directExit) :
                                    null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fragment != null) {
                    mainActivity.startFragment(fragment);
                }
            }

            private void handleManageCenterPage() {
                mainActivity.checkRadio(TabFragmentManager.TAB_MY_AND_LISTEN, null);

                Fragment manageCenterFragment = null;
                try {
                    manageCenterFragment =
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newManageCenterFragment();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (manageCenterFragment != null) {
                    mainActivity.startFragment(manageCenterFragment);
                }
            }

            private void handleAlbumPlayPage(MainActivity mainActivity) {
                try {
                    String jumpUrl = pm.schema;
                    if (TextUtils.isEmpty(jumpUrl)) {
                        jumpUrl = pm.data;
                    }
                    if (TextUtils.isEmpty(jumpUrl)) {
                        jumpUrl = pm.pushUrl;
                    }
                    if (!TextUtils.isEmpty(jumpUrl) && jumpUrl.contains("iting://open?msg_type=29")) {
                        jumpUrl = jumpUrl.replace("msg_type=29", "msg_type=13");
                        jumpUrl += "&isAutoPlayForce=true";
                        handleITing(mainActivity, Uri.parse(jumpUrl));
                        return;
                    }
                    AlbumEventManage.AlbumFragmentOption option =
                            new AlbumEventManage.AlbumFragmentOption();
                    option.isAutoPlay = true;
                    int from = (pm.from > 0) ? pm.from : AlbumEventManage.FROM_OUTER_LINK;
                    int playSource = (pm.playSource > 0) ? pm.playSource :
                            ConstantsOpenSdk.PLAY_FROM_OTHER;
                    BaseFragment newFragment =
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newAlbumFragment(
                                    "",
                                    pm.albumId,
                                    null,
                                    from,
                                    playSource,
                                    null,
                                    null,
                                    -1,
                                    option);
                    String tag = newFragment.getClass().getSimpleName() + pm.albumId;
                    // 如果已存在该tag的fragment，移除掉上面的其他的fragment
                    Fragment fragment =
                            mainActivity.getManageFragment().removeTopAndReturnTagFragment(tag);
                    if (fragment != null) {
                        CustomTipsView.onStartFragment(); //防止切换页面后tips突然显示出来
                        mainActivity.hidePlayFragment(fragment);
                    } else {
                        mainActivity.startFragment(newFragment, tag, 0, 0);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleBindPhonePage() {
                getLoginFragmentActionCallBack(new IHandleOk() {
                    @Override
                    public void onReady() {
                        try {
                            Fragment fragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction()
                                    .newGetAndVerifySmsCodeFragment(false, AppConstants.FROM_BIND_PAGE);
                            mainActivity.startFragment(fragment);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

            }

            private void handleModifyPwdFragment() {
                getLoginFragmentActionCallBack(new IHandleOk() {
                    @Override
                    public void onReady() {
                        try {
                            Fragment fragment = Router.<LoginActionRouter>getActionRouter(Configure.BUNDLE_LOGIN).getFragmentAction()
                                    .newModifyPwdFragment();
                            mainActivity.startFragment(fragment);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }

            private void handleChildProtectForgetPwdFragment() {
                try {
                    Fragment fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                            .newChildProtectForgetPwdFragment();
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleDownloadCacheFragment() {
                try {
                    Fragment fragment = Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction()
                            .newDownloadCacheFragment();
                    mainActivity.startFragment(fragment);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            private void handleSeekSoundProgress() {
                if (!TextUtils.isEmpty(pm.seekTimeStr)) {
                    int seekTime = PlayManuscriptTimeStampUtil.convertToCorrectTimeMs(pm.seekTimeStr);
                    if (seekTime >= 0) {
                        if (pm.trackId == PlayTools.getCurTrackId(activity)) {
                            XmPlayerManager.getInstance(activity).play();
                            XmPlayerManager.getInstance(activity).seekTo(seekTime);
                        } else {
                            // 判断是否在播放列表中
                            int index = XmPlayerManager.getInstance(activity).getTrackIndex(pm.trackId);
                            if (index >= 0) {
                                XmPlayerManager.getInstance(activity).play(index);
                                HandlerManager.postOnUIThreadDelay(() -> XmPlayerManager.getInstance(activity).seekTo(seekTime), 300);
                            }
                        }
                    }
                }
            }

            private void handlePlayAlbumLastTrack() {
                long albumId = pm.albumId;
                long trackId = pm.trackId;
                IHistoryManagerForMain historyManagerForMain = RouteServiceUtil.getHistoryManagerForMain();
                PlayableModel playableModel = XmPlayerManager.getInstance(mainActivity.getApplicationContext()).getCurrSound();
                if (playableModel instanceof Track) {
                    Track track = (Track) playableModel;
                    if (track.getDataId() == trackId && track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_DAILYNEWS4) {
                        try {
                            // 直接跳转卡片页的场景
                            String itingUrl = "iting://open?msg_type=74&channelGroupId=" + track.getChannelGroupId() + "&toChannelId=" + track.getChannelId();
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().handleIting(mainActivity, Uri.parse(itingUrl));
                            return;
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if (!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode) &&
                            !XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlayingPodCastPlayList() &&
                            !XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).isPlayingToListenTracks()) {
                        // 当前播放器播放列表模式不是播客类型  iting指定播客类型  就重新设置类型
                        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).resetPodCastListMode(true);
                    }
                    // 短剧类型跳转至短剧播放页
                    if (track.getDataId() == trackId && track.isSketchVideoTrack()) {
                        PlayTools.playMiniDrama(mainActivity, trackId);
                        return;
                    }
                    if (track.getDataId() == trackId && XmPlayerManager.getInstance(mainActivity.getApplicationContext()).hasNextSound()) {
                        XmPlayerManager.getInstance(mainActivity).play();
                        mainActivity.showPlayFragment(null, PlayerManager.PLAY_CURRENT);
                        return;
                    }
                }
                if (historyManagerForMain != null) {
                    Track track = historyManagerForMain.getTrackByHistory(albumId);
                    if (track != null) {
                        if (!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode)) {
                            NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode));
                        }
                        // 短剧类型跳转至短剧播放页
                        if (track.getDataId() == trackId && track.isSketchVideoTrack()) {
                            PlayTools.playMiniDrama(mainActivity, trackId);
                            return;
                        }

                        PlayTools.playTrackHistoy(mainActivity, true, track, null);
                    } else {
                        handleSoundPage();
                    }
                }
            }

            private void handleSoundPage() {
                if (!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode)) {
                    NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode));
                }
                TempDataManager.getInstance().saveString(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SCHEME, pm.schema);
                int channel = SharedConstant.CHANNEL_ACTIVATION_DEEP_LINK;
                if (!TextUtils.isEmpty(pm.source)) {
                    try {
                        Integer iSource = Integer.valueOf(pm.source);
                        if (iSource != null && iSource > 0) {
                            channel = iSource;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if (pm != null && !TextUtils.isEmpty(pm.deliveryChannelid)) {
                    RecommendChannelITing.INSTANCE.getChannelItems(pm, pm.schema, pm.deliveryChannelid);
                }

                final Track sound = new Track();
                sound.setDataId(pm.trackId);
                sound.setPlaySource(ConstantsOpenSdk.PLAY_FROM_PUSH);

                UserInteractivePlayStatistics.clickPlay(pm.trackId, "iting");

                if (isOuterLink) {
                    XDCSCollectUtil.getInstanse().statIting(XDCSCollectUtil.APP_NAME_ITING,
                            XDCSCollectUtil.SERVICE_ITING, pm.xdcsParams);
                }

                if (pm.targetUid > 0 && pm.trackId > 0 && XmPlayerManager.getInstance(mainActivity).getHistoryPos(pm.trackId)
                        == SharedModelConstants.PLAY_NO_HISTORY) {
                    PlayPageDocAndCoverHelper.setNotShowDocTrackId(pm.trackId);
                }

                if (pm.isPush) {
                    String pushUrl;
                    int type = 0;
                    if (!TextUtils.isEmpty(pm.pushUrl)) {
                        pushUrl = pm.pushUrl;
                        type = 1;
                    } else if (!TextUtils.isEmpty(pm.schema)) {
                        pushUrl = pm.schema;
                        type = 2;
                    } else if (!TextUtils.isEmpty(pm.url)) {
                        pushUrl = pm.url;
                        type = 3;
                    } else {
                        pushUrl = "messageType=" + pm.messageType + ",trackId=" + pm.trackId + ",source=" + pm.source + ",albumId=" + pm.albumId;
                        type = 4;
                    }
                    Logger.logToSd("miPush: " + "from notification click, " + pm.isPush + ", " + pm.schema + ", pushUrl=" + pushUrl);
                    new XMTraceApi.Trace().setMetaId(9303).setServiceId("openPushTrack")
                            .put("pushUrl", pushUrl)
                            .put("type", String.valueOf(type))
                            .createTrace();
                }
                /**
                 * 处理广告透传参数
                 */
                if (pm.responseId > 0 || pm.materialId > 0) {
                    if (mainActivity != null) {
                        MmkvCommonUtil.getInstance(mainActivity)
                                .saveLong(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_INFO_RESPONSE_ID, pm.responseId);
                        MmkvCommonUtil.getInstance(mainActivity).
                                saveLong(PreferenceConstantsInOpenSdk.ITEM_PLAY_PAGE_AD_INFO_MATERIAL_ID, pm.materialId);
                    }
                }
                if (pm.tingListId > 0) {
                    try {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().getSpecialTingList(activity, pm.tingListId, new IDataCallBack<ListModeBase<Track>>() {
                            @Override
                            public void onSuccess(@Nullable ListModeBase<Track> object) {
                                if (object != null && object.getList() != null) {
                                    int index = object.getList().indexOf(sound);
                                    if (index >= 0) {
                                        for (Track track : object.getList()) {
                                            track.setKind(PlayableModel.KIND_TRACK);
                                        }
                                        PlayTools.playList(activity, object.getList(), index, null);
                                    }
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                CustomToast.showFailToast(message);
                            }
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else if (pm.adAlbumId > 0 && pm.trackId > 0) {  // 广告打开形式
                    AlbumEventManage.AlbumFragmentOption albumFragmentOption = new AlbumEventManage
                            .AlbumFragmentOption();
                    albumFragmentOption.isFromAd = true;
                    albumFragmentOption.trackId = pm.trackId;
                    AlbumEventManage.startMatchAlbumFragment(pm.adAlbumId,
                            AlbumEventManage.FROM_UNDEFINED, ConstantsOpenSdk.PLAY_FROM_PUSH,
                            null, null
                            , -1, activity, albumFragmentOption);
                } else if (pm.businessType > 0 && !TextUtils.isEmpty(pm.trackIds)) {
                    String[] trackArray = null;
                    try {
                        trackArray = pm.trackIds.split(",");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    if (trackArray == null || trackArray.length <= 0) {
                        return;
                    }

                    if (UserInteractivePlayStatistics.Optimizer_ItingDirectPlay.enable()) {
                        playTrackAndGetTrackListDetail(pm);
                        return;
                    }

                    Map<String, String> params = new HashMap();
                    params.put("trackIds", pm.trackIds);

                    String stepName = "getTrackInfoListDetailV2_";
                    UserInteractivePlayStatistics.stepBegin(stepName);
                    CommonRequestM.getTrackInfoListDetailV2(params, new IDataCallBack<List<Track>>() {
                        @Override
                        public void onSuccess(@Nullable List<Track> data) {
                            if (data == null || data.size() <= 0) {
                                return;
                            }

                            int pageFromSource = pm.pageFromSource;
                            CommonTrackList commonTrackList = new CommonTrackList();
                            Map<String, String> params = new HashMap<>();
                            params.put(DTransferConstants.PAGE, String.valueOf(1));
                            params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(1));
                            params.put(DTransferConstants.PAGE_SIZE, String.valueOf(data.size()));
                            commonTrackList.setParams(params);
                            commonTrackList.setTotalPage(1);
                            commonTrackList.setTotalCount(data.size());
                            int index = 0;
                            // 待播放声音是否是短剧类型，若是短剧类型，跳转至短剧播放页
                            boolean isSketchVideoTrack = false;
                            for (int i = 0; i < data.size(); i++) {
                                Track t = data.get(i);
                                if (t == null) {
                                    continue;
                                }

                                if (t.getDataId() == pm.trackId) {
                                    index = i;
                                    isSketchVideoTrack = t.isSketchVideoTrack();
                                }
                                if (pageFromSource > 0) {
                                    t.setPageFromSource(pageFromSource);
                                }
                                t.setSceneId(pm.sceneId);
                                t.setKeyWord(pm.keyword);
                                t.setOpenPlayList(pm.openPlayList ? 1 : 0);
                                t.setBundle(pm.bundle);
                                t.setSourceGuidance(pm.sourceGuidance);
                            }

                            // 待播放声音短剧类型，跳转至短剧播放页
                            if (isSketchVideoTrack) {
                                PlayTools.playMiniDrama(mainActivity, pm.trackId);
                                return;
                            }

                            commonTrackList.setTracks(data);
                            boolean notShowPlayPage = pm.notShowPlayPage; // 默认都是false，进播放页播放
                            if (!notShowPlayPage && pm.openPlayList) {
                                PlayListAndHistoryDialogManager.Companion.setAutoOpenStatus(true);
                            }
                            UserInteractivePlayStatistics.stepEnd(pm.trackId, stepName);
                            PlayTools.playCommonList(BaseApplication.getMyApplicationContext(),
                                    commonTrackList, index, !notShowPlayPage, null);
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                } else {
                    if ("ad".equals(pm.source)) {
                        AudioPlayPageSubscribeFirstManager.INSTANCE.putFromAdTrack(pm.trackId);
                        // 跳转广告时默认跳转播放tab页
                        TempDataManager.getInstance().saveInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, PlayPageTab.TYPE_SOUND);
                    }
                    if (XmPlayerManager.getInstance(activity).isConnected() || notWaitingPlayerToEnterPlayPage(pm)) {
                        gotPlayPageAfterConnected(sound);
                    } else {
                        Logger.i("ArriveTraceManagerTest", "ITingHandler wait player start");
                        mLastHandleTrackTime = System.currentTimeMillis();
                        if (pm.isPush) {
                            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before connect");
                        }
                        XmPlayerManager.getInstance(activity).setIfInAppInitialization(false);
                        XmPlayerManager.getInstance(activity).init(true);
                        XmPlayerManager.getInstance(activity).addOnConnectedListerner(new XmPlayerManager.IConnectListener() {
                            @Override
                            public void onConnected() {
                                XmPlayerManager.getInstance(activity).removeOnConnectedListerner(this);
                                //超过30s放弃以前的操作
                                if (System.currentTimeMillis() - mLastHandleTrackTime > 30_000) {
                                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("Time out: Connect Service");
                                    return;
                                }

                                if (activity == null || sound == null || pm == null) {
                                    return;
                                }
                                gotPlayPageAfterConnected(sound);
                            }
                        });
                    }
                }
                HandlerManager.postOnUIThreadDelay(() -> checkShowShareAssistResult(pm), 1000);
                HandlerManager.postOnUIThreadDelay(() -> openInteractiveCardPanel(mainActivity, pm), 3000);
                ListenTimeDialogManager.checkOutsideStationITing(pm.data);
            }

            private void checkShowShareAssistResult(PushModel pm) {
                if (TextUtils.isEmpty(pm.share_unlock_activityId) || TextUtils.isEmpty(pm.share_unlock_key)) {
                    return;
                }
                if (!UserInfoMannage.hasLogined()) {
                    ShareAssistDialogFragment fragment = ShareAssistDialogFragment.Companion
                            .newInstance(pm.share_unlock_avatar, pm.share_unlock_nickname);
                    fragment.setCallback(() -> getShareAssistResultAndShow(pm));
                    if (activity instanceof MainActivity) {
                        fragment.show(activity.getSupportFragmentManager(), ShareAssistDialogFragment.class.getSimpleName());
                    }
                } else {
                    getShareAssistResultAndShow(pm);
                }
            }

            private void getShareAssistResultAndShow(PushModel pm) {
                CommonRequestM.getDoShareAssist(
                        pm.share_unlock_activityId,
                        pm.share_unlock_key,
                        pm.trackId,
                        new IDataCallBack<BaseResponseData<ShareAssistData>>() {
                            @Override
                            public void onSuccess(@Nullable BaseResponseData<ShareAssistData> data) {
                                if (data != null && data.isSuccess()) {
                                    if (data.getData() != null && data.getData().getCode() == 0) {
                                        CustomToast.showToast("助力成功！好友可免费解锁", ToastManager.LENGTH_LONG);
                                    } else if (data.getData() != null && data.getData().getCode() != 0) {
                                        CustomToast.showToast(data.getData().getMsg(), ToastManager.LENGTH_LONG);
                                    } else {
                                        CustomToast.showToast(data.getMsg(), ToastManager.LENGTH_LONG);
                                    }
                                } else {
                                    CustomToast.showToast("服务异常，助力失败", ToastManager.LENGTH_LONG);
                                }
                            }

                            @Override
                            public void onError(int code, String message) {
                                CustomToast.showToast("服务异常，助力失败", ToastManager.LENGTH_LONG);
                            }
                        }
                );
            }

            private void gotPlayPageAfterConnected(Track sound) {
                if (pm == null || sound == null) {
                    return;
                }
                if (sound.getDataId() == 0 && pm.albumId > 0) {
                    PlayTools.playByAlbumByIdIfHasHistoryUseHistoryStartPlayPage(activity, pm.albumId, null);
                } else if (pm.albumId > 0) {
                    PlayTools.playTrackHistoy(activity, sound.getDataId(), pm.albumId,
                            null, ConstantsOpenSdk.PLAY_FROM_PUSH, false);
                } else {
                    if (pm.onlyShowPlay && XmPlayerManager.getInstance(mainActivity).getCurrSound() != null) {
                        if ("widget".equals(pm.source) && XmPlayerManager.getInstance(mainActivity).getCurrSound() instanceof Track) {
                            // 小组件自动播放
                            XmPlayerManager.getInstance(mainActivity).play();
                        }
                        mainActivity.showPlayFragment(null, PlayerManager.PLAY_CURRENT);
                    } else {
                        if (pm.startTime > 0) {
                            PlayableModel track = XmPlayerManager.getInstance(activity).getCurrSound();
                            if (track instanceof Track && track.getDataId() == pm.trackId) {
                                XmPlayerManager.getInstance(activity).pause(PauseReason.Business.ITingGotoPlayPagePause);
                                XmPlayerManager.getInstance(activity).seekTo(pm.startTime);
                            }
                            XmPlayerManager.getInstance(activity).saveSoundHistoryPos(pm.trackId, pm.startTime);
                        }
                        // 判断跳转新版播放页相应tab
                        if (pm.focusPlayTabType > 0) {
                            TempDataManager.getInstance().saveInt(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_SECTION, pm.focusPlayTabType);
                            if (pm.focusPlayTabType == PlayPageTab.TYPE_COMMENT) {
                                TempDataManager.getInstance().saveLong(BundleKeyConstants.KEY_XPLAY_FRAGMENT_OPEN_COMMENT_TRACK_ID, pm.trackId);
                            }
                        }
                        // 是否自动打开评论
                        if (pm.isOpenComment) {
                            TempDataManager.getInstance().saveBoolean(BundleKeyConstants.KEY_PLAY_COMMENT_FRAGMENT_TAB_NEED_AUTO_SHOW_KEYBOARD, pm.isOpenComment);
                        }
                        //积分任务id
                        if (pm.taskId > 0) {
                            TempDataManager.getInstance().saveLong(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_TASK_ID, pm.taskId);
                        }
                        // 跳转到播放页且targetUid不为0则赋值
                        if (pm.focusPlayTabType == 0 && pm.targetUid > 0) {
                            TempDataManager.getInstance().saveString(BundleKeyConstants.KEY_ITING_PLAY_FRAGMENT_PLAY_FRIEND_LISTENED_RECORD,
                                    PlayFriendListenedManager.getPlayFriendListenedItingParamsStr(pm.targetUid, pm.targetAlbums));
                        }
                        if (!TextUtils.isEmpty(pm.pShareC)) {
                            TempDataManager.getInstance().saveString(TempDataManager.DATA_PLAY_PAGE_P_SHARE_C, pm.pShareC);
                        }
//                                mainActivity.showPlayFragment(null, PlayerManager.PLAY_TAG);


                        if (pm.isPush) {
                            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "before open playFragment");
                        }

                        if (!XmPlayerManager.getInstance(activity).isConnected() && notWaitingPlayerToEnterPlayPage(pm)) {
                            TempDataManager.getInstance().saveLong(TempDataManager.DATA_PLAY_PAGE_OUTER_ITING_TRACK_ID, pm.trackId);
                            Logger.i("ArriveTraceManagerTest", "ITingHandler player not connected set track id " + pm.trackId);
                        }
                        boolean notShowPlayPage = pm.notShowPlayPage; // 默认都是false，进播放页播放
                        if (pm.isOuterIting || "11".equals(ArriveTraceManager.INSTANCE.getMsgType())) {
                            PlayTools.playTrackWithAlbum(activity, pm.trackId, !notShowPlayPage, true, ConstantsOpenSdk.PLAY_FROM_PUSH);
                        } else {
                            PlayTools.playTrackByCommonList(activity, pm.trackId,
                                    ConstantsOpenSdk.PLAY_FROM_PUSH, null, !notShowPlayPage, null, SharedConstant.CHANNEL_UNKNOWN, pm.autoPlay);
                        }
//                        PlayTools.playTrackWithAlbum(activity, pm.trackId, true, true, ConstantsOpenSdk.PLAY_FROM_PUSH);

                        if (!StringUtil.isEmpty(pm.vipExtraUrl)) {
                            PlayTools.showVipBenefitDialog(activity, pm.vipExtraUrl);
                        }
                    }
                }
            }

            private void handleHomePage() {
                if (isOuterLink) {
                    XDCSCollectUtil.getInstanse().statIting(XDCSCollectUtil.APP_NAME_ITING,
                            XDCSCollectUtil.SERVICE_ITING, pm.xdcsParams);
                }
                long uid = pm.uid;
                if (uid == 0) {
                    uid = UserInfoMannage.getUid();
                }
                if (uid == 0) {
                    CustomToast.showToast("未找到用户，请重试");
                    return;
                }
                Fragment anchorSpaceFragment = null;
                try {
                    MainActionRouter router = getActionRouter(Configure.BUNDLE_MAIN);
                    if (router != null) {
                        anchorSpaceFragment = router.getFragmentAction().newAnchorSpaceFragment(uid, pm.videoPrior);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (anchorSpaceFragment != null) {
                    mainActivity.startFragment(anchorSpaceFragment);
                }
            }

            private void handleFriendPage() {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                if (ConfigureCenter.getInstance().getBool("toc", "find_friend_use_rn", true)) {
                    String iTing = "iting://open?msg_type=94&bundle=rn_find_friend";
                    handleITing(activity, Uri.parse(iTing));
                } else {
                    AppConstants.MsgCount.sCount_Friends = 0;
                    BaseFragment fragment;
                    try {
                        fragment =
                                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newFragmentByFid(Configure.MainFragmentFid.FIND_FRIEND_FRAGMENT);
                        Bundle bundle1 = new Bundle();
                        bundle1.putBoolean("isSwitchTab", true);
                        fragment.setArguments(bundle1);
                        mainActivity.startFragment(fragment);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            private void handleFanPage(PushModel pm) {
                if (!UserInfoMannage.hasLogined()) {
                    UserInfoMannage.gotoLogin(mainActivity);
                    return;
                }
                AppConstants.MsgCount.sCount_Fans = 0;

                if (!pm.keepPage) {
                    mainActivity.checkRadio(TabFragmentManager.TAB_MY_AND_LISTEN, null);
                }

                Bundle bundle = new Bundle();
                try {
                    int tabIndex = Integer.parseInt(pm.tab);
                    bundle.putInt("type", tabIndex == 2 ? 2 : 1);
                } catch (Exception e) {
                    e.printStackTrace();
                    bundle.putInt("type", 1);
                }
                bundle.putLong("uid", UserInfoMannage.getUid());
                Fragment myAttentionFragment = null;
                try {
                    myAttentionFragment =
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFragmentAction().newMyAttentionFragment();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (myAttentionFragment != null) {
                    myAttentionFragment.setArguments(bundle);
                    activity.startFragment(myAttentionFragment);
                }
            }
        });
    }

    private void playTrackAndGetTrackListDetail(final PushModel pm) {
        if (pm == null) {
            return;
        }
        long trackId = pm.trackId;
        String trackIds = pm.trackIds;

        //开启优化，先起播这首，并发请求接口信息
        PlayTools.playTrackAndGetTrackListDetail(BaseApplication.getMyApplicationContext(), trackId, trackIds);

        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                requestTrackListDetail(trackId, trackIds, pm);
            }
        };

        //延迟请求，避免请求到数据后 setPlayList 时 reset currentModel 导致一些问题
        HandlerManager.postOnUIThreadDelay(runnable, 1000);
    }

    private void requestTrackListDetail(long trackId, String trackIds, PushModel pm) {

        Map<String, String> params = new HashMap();
        params.put("trackIds", trackIds);
        CommonRequestM.getTrackInfoListDetailV2(params, new IDataCallBack<List<Track>>() {
            @Override
            public void onSuccess(@Nullable List<Track> data) {
                if (data == null || data.size() <= 0) {
                    return;
                }

                int pageFromSource = pm.pageFromSource;
                CommonTrackList commonTrackList = new CommonTrackList();
                Map<String, String> params = new HashMap<>();
                params.put(DTransferConstants.PAGE, String.valueOf(1));
                params.put(DTransferConstants.TOTAL_PAGE, String.valueOf(1));
                params.put(DTransferConstants.PAGE_SIZE, String.valueOf(data.size()));
                commonTrackList.setParams(params);
                commonTrackList.setTotalPage(1);
                commonTrackList.setTotalCount(data.size());
                int index = 0;
                for (int i = 0; i < data.size(); i++) {
                    Track t = data.get(i);
                    if (t == null) {
                        continue;
                    }

                    if (t.getDataId() == trackId) {
                        index = i;
                        t.setAuthorized(true);
                    }
                    if (pageFromSource > 0) {
                        t.setPageFromSource(pageFromSource);
                    }
                    t.setSceneId(pm.sceneId);
                    t.setKeyWord(pm.keyword);
                    t.setOpenPlayList(pm.openPlayList ? 1 : 0);
                    t.setBundle(pm.bundle);
                    t.setSourceGuidance(pm.sourceGuidance);
                }
                if (!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode)) {
                    NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode));
                }

                commonTrackList.setTracks(data);
                boolean notShowPlayPage = pm.notShowPlayPage; // 默认都是false，进播放页播放
                if (!notShowPlayPage && pm.openPlayList) {
                    PlayListAndHistoryDialogManager.Companion.setAutoOpenStatus(true);
                }
                if (!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode)) {
                    NewShowNotesManager.INSTANCE.setTempPodCastPlayListMode(!TextUtils.isEmpty(pm.playListMode) && NewShowNotesManager.ITING_PARAMS_PLAYLIST_MODE.equals(pm.playListMode));
                }
                PlayTools.playCommonList(BaseApplication.getMyApplicationContext(),
                        commonTrackList, index, !notShowPlayPage, null);
            }

            @Override
            public void onError(int code, String message) {

            }
        });
    }

    private boolean notWaitingPlayerToEnterPlayPage(PushModel pushModel) {
        // 是站外iting
        return (pushModel.isOuterIting || "11".equals(ArriveTraceManager.INSTANCE.getMsgType())) && pushModel.albumId <= 0;
    }

    /**
     * 将iting收敛至直播LiveBundle中
     *
     * @param pm       推送消息
     * @param activity 上下文环境
     */
    private void gotoLiveBundleAsync(final PushModel pm, final MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }
        PlayableModel currSound = XmPlayerManager.getInstance(activity.getApplicationContext()).getCurrSound();
        if (!PlayTools.isLiveMode(currSound)) {
            LastAudioPlayListCache.INSTANCE.saveAudioPlayListCache(activity.getApplicationContext());
        }

        if (pm.isPush) {
            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before install live bundle");
        }
        Router.getActionByCallback(Configure.BUNDLE_LIVE, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.liveBundleModel.bundleName, bundleModel.bundleName)) {
                    if (pm.isPush) {
                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "after install live bundle");
                    }
                    try {
                        Router.<LiveActionRouter>getActionRouter(Configure.BUNDLE_LIVE)
                                .getFunctionAction().handleITing(pm, activity);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                CustomToast.showFailToast("直播模块安装失败，请稍后重试");
                if (pm.isPush) {
                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("Live bundle LocalInstallError");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
    }

    /**
     * 将 iting 收敛至短剧 MiniDramaBundle 中
     *
     * @param pm       推送消息
     * @param activity 上下文环境
     */
    private void gotoMiniDramaBundleAsync(final PushModel pm, final MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }

        // todo(zoeywoohoo): 2024/12/17 这里的保存逻辑对吗？
        PlayableModel currSound = XmPlayerManager.getInstance(activity.getApplicationContext()).getCurrSound();
        if (!PlayTools.isLiveMode(currSound)) {
            LastAudioPlayListCache.INSTANCE.saveAudioPlayListCache(activity.getApplicationContext());
        }

        if (pm.isPush) {
            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before install minidrama bundle");
        }
        Router.getActionByCallback(Configure.BUNDLE_MINI_DRAMA, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.miniDramaBundleModel.bundleName, bundleModel.bundleName)) {
                    if (pm.isPush) {
                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "after install minidrama bundle");
                    }
                    try {
                        Router.<MiniDramaActionRouter>getActionRouter(Configure.BUNDLE_MINI_DRAMA)
                                .getFunctionAction().handleITing(pm, activity);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                CustomToast.showFailToast("短剧模块安装失败，请稍后重试");
                if (pm.isPush) {
                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("Minidrama bundle LocalInstallError");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        }, true, BundleModel.DOWNLOAD_IN_BACKGROUND);
    }

    //获取 schema='iting://open?msg_type=107&cluster_type=4&category_id=6'
    // 获取
    private static int getClusterFromSchema(String schema, int defaultValue) {
        if (TextUtils.isEmpty(schema))
            return defaultValue;

        try {
            String mClusterType = Uri.parse(schema).getQueryParameter("cluster_type");
            if (!TextUtils.isEmpty(mClusterType) && mClusterType.matches("^[-+]?(([0-9]+)([.]" +
                    "([0-9]+))?|([.]([0-9]+))?)$")) {

                return Integer.parseInt(mClusterType);

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultValue;
    }

    private void getLoginFragmentActionCallBack(IHandleOk handleOk) {

        Router.getActionByCallback(Configure.BUNDLE_LOGIN, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.loginBundleModel.bundleName,
                        bundleModel.bundleName)) {


                    if (handleOk != null) {
                        handleOk.onReady();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }

        });
    }

    /**
     * bugfix:在handleITing中closeWebFragment操作会造成在app内部h5页面，执行iting操作如果是选中底部tab，出现页面透明并且重叠问题。
     * <p>
     * NOTE:需要处理的iting是需要切换选中底部导航栏，首页，我听，发现，我的等页面
     * 返回true 如果当前页面是h5页面，当前web页将不会进行关闭，如果要关闭，可以在对应iting处理的地方使用clearAllFragmentFromStacks
     */
    private boolean isNeedForbidCloseWebFragment(int messageType) {
        switch (messageType) {
            case AppConstants.PAGE_FIND:
            case AppConstants.PAGE_MINE_HOME:
//            case AppConstants.PAGE_TO_HOME_VIP:
            case AppConstants.PAGE_TO_MY_LISTERN:
            case AppConstants.PAGE_TO_EVERYDAY_UPDATE:
            case AppConstants.PAGE_TO_SMART_DEVICE:
            case AppConstants.PAGE_TO_COMMENT_DETAIL:  // 某个活动页产品在后台配置生成的，没有加_ka=1，有几百个跳转的iting，产品手动再改太麻烦，所以在这里处理下，跳评论详情这个iting的都不关闭h5页面
                return true;
            default:
                break;
        }
        return false;
    }

    private void gotoKidsBundleAsync(final PushModel pm, MainActivity activity) {
        CategoryRecommendKidEntryManager.go2KidsProtectionSettings(pm.schema);
    }

    /**
     * 儿童相关iTing跳转
     *
     * @param pm       推送消息
     * @param activity 上下文环境
     */
    private void gotoKidBundleAsync(final PushModel pm, final MainActivity activity) {
        if (pm == null || activity == null) {
            return;
        }

        if (pm.isPush) {
            PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("2", "before install kid bundle");
        }
        Router.getActionByCallback(Configure.BUNDLE_KID, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (TextUtils.equals(Configure.kidBundleModel.bundleName, bundleModel.bundleName)) {
                    if (pm.isPush) {
                        PushArrivedTraceManager.INSTANCE.getInstance().statViaTraceBeforeLanding("3", "after install kid bundle");
                    }
                    try {
                        Router.<KidActionRouter>getActionRouter(Configure.BUNDLE_KID)
                                .getFunctionAction().handleITing(pm, activity);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                if (pm.isPush) {
                    PushArrivedTraceManager.INSTANCE.getInstance().statErrorTrace("kid bundle LocalInstallError");
                }
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        }, true, BundleModel.DOWNLOAD_SHOW_PROGRESS);
    }

    /**
     * 跳转到二维码扫码页面
     */
    private void gotoQRCodeScanFragment(PushModel pm, MainActivity mainActivity) {
        BaseFragment2 fragment2 = new QRCodeScanFragment();
        mainActivity.startFragment(fragment2);
    }

//    private void gotoGameCenter() {
//        Logger.logToFile("ITingHandler gotoGameCenter");
//        GameRouterUtil.getGameBundle(true, new Router.SimpleBundleInstallCallback() {
//            @Override
//            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
//                Router.SimpleBundleInstallCallback.super.onLocalInstallError(t, bundleModel);
//                CustomToast.showFailToast("打开游戏中心失败，请检查网络");
//            }
//
//            @Override
//            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
//                Router.SimpleBundleInstallCallback.super.onRemoteInstallError(t, bundleModel);
//                CustomToast.showFailToast("打开游戏中心失败，请检查网络");
//            }
//
//            @Override
//            public void onInstallSuccess(BundleModel bundleModel) {
//                IGameFunctionAction funAct = GameRouterUtil.getFunAction();
//                if (funAct != null) {
//                    funAct.showGameCenter("8004");
//                }
//            }
//        });
//    }

    private void gotoReportPage(MainActivity mainActivity, PushModel pushModel) {
        if ("track_comment".equals(pushModel.type)) {
            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(mainActivity);
            } else {
                Map<String, String> params = new HashMap<>();
                params.put("trackId", "" + pushModel.trackId);
                params.put("commentId", "" + pushModel.commentid);
                params.put("pageId", "1");
                params.put("pageSize", "1");
                params.put("order", "0");
                int imageLimitSize = PicCropUtil.getImageViewLimitWidth(BaseApplication.getMyApplicationContext());
                if (imageLimitSize > 0) {
                    params.put("imageViewSize", String.valueOf(imageLimitSize));
                }
                MainCommonRequest.getTrackCommentDetailInfo(params, new IDataCallBack<ListModeBase<CommentModel>>() {
                    @Override
                    public void onSuccess(@Nullable ListModeBase<CommentModel> object) {
                        if (object != null && object.getExtraData() instanceof CommentModel) {
                            CommentModel info = (CommentModel) object.getExtraData();
                            mainActivity.startFragment(
                                    ReportFragment.newInstance(
                                            SharedConstant.REPORT_FRAGMENT_TYPE_REPORT_COMMENT,
                                            0,
                                            info.trackId,
                                            info.id,
                                            info.content,
                                            info.uid,
                                            info.createdAt,
                                            CommentOperationUtil.convertCommentModelToImageUrlList(info)
                                    )
                            );
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(message);
                    }
                });
            }
        }
    }

    private void openIndependentCommentFragment(MainActivity mainActivity) {
        if (mainActivity.isPlayFragmentVisible()) {
            YPlayCommentIndependentFragment fragment = YPlayCommentIndependentFragment.newInstanceForTrack(
                    CommentConstants.FROM_PAGE_AUDIO,
                    PlayTools.getCurTrackId(ToolUtil.getCtx()),
                    false
            );
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
                service.startFragmentOnPlayPage(fragment);
            }
        } else {
            String iting = "iting://open?msg_type=11&track_id=" + PlayTools.getCurTrackId(ToolUtil.getCtx()) + "&focus_xtab=2";
            ToolUtil.clickUrlAction(mainActivity, iting, null);
        }
    }

    private void openFloatingTrackCommentPanel(MainActivity mainActivity, PushModel pushModel, boolean onlyInPlayPage) {
//        if (mainActivity.isPlayFragmentVisible()) {
//            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
//            if (service != null) {
//                boolean isXPlayPage = PlayPageTabUtil.isPlayX(PlayPageDataManager.getInstance().getSoundInfo());
//                FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTrack(
//                        CommentConstants.FROM_PAGE_AUDIO, PlayTools.getCurTrack(ToolUtil.getCtx()), false,
//                        pushModel.commentid, null, 0L, isXPlayPage, isXPlayPage);
//                service.startFragmentOnPlayPage(fragment);
//            }
//        } else
        if (!onlyInPlayPage) {
            Bundle bundle = new Bundle();
            bundle.putBoolean(BundleKeyConstants.KEY_OPEN_FLOATING_COMMENT_PAGE, true);
            bundle.putLong(BundleKeyConstants.KEY_COMMENT_ID, pushModel.commentid);
            mainActivity.showPlayFragment(null, bundle, PlayerManager.PLAY_CURRENT);
        } else if (pushModel.openGlobal) {
            FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTrack(pushModel.sourceType,
                    pushModel.trackId, false, 0L);
            boolean isPlayButtonShow = mainActivity.playButtonIsShow();
            fragment.setCallbackFinish((cls, fid, params) -> {
                if (mainActivity == null) {
                    return;
                }
                if (isPlayButtonShow) {
                    mainActivity.showPlayButton();
                } else {
                    mainActivity.hidePlayButton();
                }
            });
            mainActivity.startFragment(
                    fragment,
                    com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom,
                    com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom
            );
        }
    }

    /**
     * 打开讨论浮层
     */
    private void openTalkPanel(MainActivity mainActivity, PushModel pushModel) {
        if (pushModel.talkId <= 0 && String.valueOf(CommentConstants.FROM_PAGE_LOCK_SCREEN).equals(pushModel.from_page)) {
            openFloatingTrackCommentPanel(mainActivity, pushModel, false);
            return;
        }
        FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTalk(pushModel.trackId,
                pushModel.talkId > 0 ? pushModel.talkId : pushModel.cardId,
                TextUtils.isEmpty(pushModel.from_page) ? -1 : Integer.parseInt(pushModel.from_page),
                pushModel.showKeyboard, pushModel.commentid);
        if (mainActivity.isPlayFragmentVisible()) {
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
                service.startFragmentOnPlayPage(fragment);
            }
        } else {
            mainActivity.startFragment(fragment, com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
        }
    }

    /**
     * 打开投票浮层
     */
    private void openVotePanel(MainActivity mainActivity, PushModel pushModel) {
        if (mainActivity.isPlayFragmentVisible()) {
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
//                FloatingTrackCommentFragment fragment = service.getChildFragment(FloatingTrackCommentFragment.class);
//                if (fragment == null || fragment.getListType() != CommentListView.TYPE_VOTE_COMMENT
//                        || fragment.getVoteId() != pushModel.voteId) {
//                    fragment = createFloatingFragment(pushModel);
//                    fragment.setOnDismissListener(new IFloatingFragmentDismissListener() {
//                        @Override
//                        public void onDismiss(@NonNull BaseFragment2 floatingFragment) {
//                            service.hideFragment(floatingFragment);
//                        }
//                    });
//                }
//                service.showFragmentOnPlayPage(fragment);
                service.startFragmentOnPlayPage(createFloatingFragment(pushModel));
            }
        } else {
            mainActivity.startFragment(createFloatingFragment(pushModel), com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
        }
    }

    private FloatingTrackCommentFragment createFloatingFragment(PushModel pushModel) {
        return FloatingTrackCommentFragment.newInstanceForVote(pushModel.trackId,
                pushModel.voteId > 0 ? pushModel.voteId : pushModel.cardId,
                TextUtils.isEmpty(pushModel.from_page) ? -1 : Integer.parseInt(pushModel.from_page),
                pushModel.showKeyboard, pushModel.commentid);
    }

    /**
     * 打开话题浮层
     */
    private void openTopicPanel(MainActivity mainActivity, PushModel pushModel) {
        FloatingTrackCommentFragment fragment = FloatingTrackCommentFragment.newInstanceForTopic(
                pushModel.trackId, pushModel.cardId, pushModel.talkId,
                TextUtils.isEmpty(pushModel.from_page) ? CommentConstants.FROM_PAGE_AUDIO : Integer.parseInt(pushModel.from_page),
                pushModel.showKeyboard);
        if (mainActivity.isPlayFragmentVisible()) {
            IPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IPlayFragmentService.class);
            if (service != null) {
                service.startFragmentOnPlayPage(fragment);
            }
        } else {
            mainActivity.startFragment(fragment, com.ximalaya.ting.android.host.R.anim.host_slide_in_bottom, com.ximalaya.ting.android.host.R.anim.host_slide_out_bottom);
        }
    }

    /**
     * 打开找搭子浮层
     */
    private void openFindPartner(MainActivity mainActivity, PushModel pushModel) {
        new FloatingCommentManager().openFindPartner(mainActivity, pushModel);
    }

    /**
     * 打开月票浮层
     */
    private void openMonthlyVotePanel(MainActivity mainActivity, @Nullable PushModel pushModel) {
        String sourcePage;
        long trackId;
        long albumId;
        long anchorId;
        Integer initialTab;
        if (pushModel == null) {
            sourcePage = VoteDialogFragment.getLastSourcePage();
            if (sourcePage == null) {
                sourcePage = "";
            }
            trackId = VoteDialogFragment.getLastTrackId();
            albumId = VoteDialogFragment.getLastAlbumId();
            anchorId = VoteDialogFragment.getLastAnchorId();
            initialTab = null;
        } else {
            sourcePage = "push";
            trackId = pushModel.trackId;
            albumId = pushModel.albumId;
            anchorId = pushModel.anchorId;
            initialTab = pushModel.initialMonthlyTicketDialogTab;
        }
        XUtils.exitLandScapeIfLandScape(mainActivity);
        VoteDialogFragment.show(mainActivity.getSupportFragmentManager(), sourcePage, trackId, albumId, anchorId, initialTab);
    }

    private void goToRoleDetailPage(MainActivity mainActivity, PushModel pushModel) {
        if (pushModel.inviteUid > 0) {
            MmkvCommonUtil.getInstance(mainActivity).saveString(
                    PreferenceConstantsInHost.KEY_LAST_OPEN_SHARED_ROLE_PAGE_INFO,
                    pushModel.inviteUid + "_" + pushModel.albumId + "_" + System.currentTimeMillis()
            );
        }
        mainActivity.startFragment(RoleDetailFragment.newInstance(pushModel.albumId,
                pushModel.characterId, pushModel.source, pushModel.pageType));
    }

    private void goToPodCastImmersivePage(MainActivity mainActivity, PushModel pushModel) {
        mainActivity.startFragment(PodCastImmersiveAudioListFragment.newInstance(pushModel.categoryId, pushModel.trackId, pushModel.poolId, null));
    }

    public static String getPageNameByMsgType(int msgType) {
        switch (msgType) {
            case AppConstants.PAGE_TO_ONE_KEY_LISTEN:
                return DailyNewsFragment4.class.getCanonicalName();
            case AppConstants.PAGE_ALBUM:
                return AlbumFragmentNew2.class.getCanonicalName();
            case AppConstants.PAGE_SOUND:
                return PlayFragmentNew.class.getCanonicalName();
            default:
                return null;
        }
    }

    private void handleAlbumBookShare(Activity activity, AlbumEventManage.AlbumFragmentOption option, String albumId) {
        if (!(activity instanceof MainActivity)) {
            return;
        }
        if (option.giftKey == null || option.giftKey.isEmpty()) {
            return;
        }
        Map<String, String> simpleParams = new HashMap<>();
        simpleParams.put(HttpParamsConstants.PARAM_ALBUM_ID, albumId);
        CommonRequestM.getAlbumSimpleInfo(simpleParams, new IDataCallBack<AlbumM>() {
            @Override
            public void onSuccess(AlbumM object) {
                String currPage = "album";
                if (object != null && object.isPaid()) {
                    if (object.isAuthorized()) {
                        currPage = "afterSaleDetailsPage";
                    } else {
                        currPage = "preSaleDetailsPage";
                    }
                }
                checkShowAlbumBookShareDialog(activity, option, currPage, albumId);
            }

            @Override
            public void onError(int code, String message) {
                checkShowAlbumBookShareDialog(activity, option, "album", albumId);
            }
        });
    }

    private void checkShowAlbumBookShareDialog(Activity activity,
                                               AlbumEventManage.AlbumFragmentOption option,
                                               String currPage,
                                               String albumId) {
        if (!(activity instanceof MainActivity)) {
            return;
        }
        AlbumBookShareDialogFragment fragment = AlbumBookShareDialogFragment.Companion.newInstance(
                currPage, albumId, option.giftKey, option.bookPrice, option.bookAlbumCover
        );
        fragment.show(((MainActivity) activity).getSupportFragmentManager(),
                AlbumBookShareDialogFragment.class.getSimpleName());
    }

    private void openBookTopicPage(MainActivity activity, PushModel pushModel) {
        if (!(activity instanceof MainActivity)) {
            return;
        }

        Router.getActionByCallback(Configure.BUNDLE_FEED, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (!TextUtils.equals(Configure.feedBundleModel.bundleName, bundleModel.bundleName)) {
                    return;
                }
                try {
                    BaseFragment2 fra = Router.<FeedActionRouter>getActionRouter(Configure.BUNDLE_FEED).getFragmentAction()
                            .newBookTopicDetail(pushModel.feedTopicId);
                    if (fra != null) {
                        activity.startFragment(fra);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });

    }

    private void openMSBizList(MainActivity activity) {
        if (!(activity instanceof MainActivity)) {
            return;
        }

        Router.getActionByCallback(Configure.BUNDLE_CHAT, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                if (!TextUtils.equals(Configure.chatBundleModel.bundleName, bundleModel.bundleName)) {
                    return;
                }
                try {
                    BaseFragment2 fra = Router.<ChatActionRouter>getActionRouter(Configure.BUNDLE_CHAT).getFragmentAction().getMsBizFragment();
                    if (fra != null) {
                        activity.startFragment(fra);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
            }
        });

    }
}
