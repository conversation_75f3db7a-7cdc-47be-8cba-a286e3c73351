package com.ximalaya.ting.android.main.fragment.find.child.rn.manager

import android.text.TextUtils
import android.view.View
import com.google.gson.Gson
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.toast.ToastManager
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.dialog.interest.manager.HomeChooseInterestManager
import com.ximalaya.ting.android.host.listener.IHomeRnNetAction
import com.ximalaya.ting.android.host.listener.IHomeRnNetCallBack
import com.ximalaya.ting.android.host.manager.HomeRnTraceTimeManager
import com.ximalaya.ting.android.host.manager.RecommendPreLoadOptManager
import com.ximalaya.ting.android.host.manager.account.NewUserLoginManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.adfree.AdResourceInfo
import com.ximalaya.ting.android.host.manager.adfree.CommonAdFreeManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.iting.ITingOutsideSourceManager
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.base.BaseModel
import com.ximalaya.ting.android.host.play.util.PlayDataUtils
import com.ximalaya.ting.android.host.satisfaction.SatisfactionManager
import com.ximalaya.ting.android.host.service.xmremotecontrol.XmRemoteControlUtil
import com.ximalaya.ting.android.host.util.GrowthItingUtil
import com.ximalaya.ting.android.host.util.VersionUtil
import com.ximalaya.ting.android.host.util.common.DeviceUtil
import com.ximalaya.ting.android.host.util.common.SceneInfoUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.AppConstants
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.template.XmTemplateDetail
import com.ximalaya.ting.android.host.view.BannerView
import com.ximalaya.ting.android.host.view.IScrollView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.RecommendCenterBigPicAdManager
import com.ximalaya.ting.android.main.fragment.find.child.rn.RecommendRnFragment
import com.ximalaya.ting.android.main.fragment.find.child.rn.util.FeedItemClickUtil
import com.ximalaya.ting.android.main.fragment.find.child.rn.util.IHomeRnRequestCallBack
import com.ximalaya.ting.android.main.fragment.find.child.rn.util.SceneStyleUtil
import com.ximalaya.ting.android.main.fragment.find.child.rn.view.RecommendRnViewAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentRealTimeFeedManager
import com.ximalaya.ting.android.main.fragment.myspace.util.HomePreviewUtil
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager.mode
import com.ximalaya.ting.android.main.model.rec.RankExtraData
import com.ximalaya.ting.android.main.model.rec.RecommendBusinessExtraInfo
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendModelNew
import com.ximalaya.ting.android.main.model.rec.Ubt
import com.ximalaya.ting.android.main.model.rec.UserGiftPendantModel
import com.ximalaya.ting.android.main.rankModule.AggregateRankUtil
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.satisfy.SatisfactionHomeModuleManager
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil
import com.ximalaya.ting.android.template.TemplateManager
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.NetworkType
import org.json.JSONArray
import org.json.JSONObject
import java.io.UnsupportedEncodingException
import java.lang.ref.WeakReference
import java.net.URLEncoder
import java.util.concurrent.ConcurrentHashMap


/**
 * Rn首页网络请求类
 */
object RecommendRnNetManager {

    private var sMotFreshListMap: ConcurrentHashMap<String, JSONObject>? = ConcurrentHashMap()

    // 直播曝光列表
    val liveExposureIds = mutableSetOf<Long>()

    @Volatile
    var mIsRecommendDataLoading = false
    var mIsFirstLoad = true
    var mNewestPageClicked = false // 最新加载的一页是否有点击过，只算专辑条、声音条和直播条的点击。
    private var sLastPassFirstInstallParamTime = 0L
    private var mLastStartTs = 0L
    private var mLastRequestUidForStartTs = -1L
    private var pageNumber = 1
    private var mRefreshTraceHelper: TraceHelper? = null

    private var mScreenId: String? = null
    private var mRecommendRnFragmentWK: WeakReference<RecommendRnFragment>? = null

    var mRecommendData: RecommendModelNew? = null
        set(value) {
            field = value
            checkMotFreshList(value)
            value?.let {
                SceneStyleUtil.checkNewSceneCardStyle(it)
                HomeChooseInterestManager.loadHomeDataFinish()
            }
        }

    private var homeNetDataCallback: IHomeRnNetCallBack? = null

    val homeRnNetAction = object : IHomeRnNetAction {

        override fun getHomeNetData(callback: IHomeRnNetCallBack?) {
            homeNetDataCallback = callback
            if (mRecommendData != null) {
                sendHomeData(mRecommendData)
            } else {
                if (!mIsRecommendDataLoading) {
                    sendHomeData(null)
                }
            }
        }

        override fun getHomeNetUrlParams(
            isLoadMore: Boolean
        ): MutableMap<String, String?> {
            return getUrlParams(isLoadMore)
        }

        override fun getHomeNetBodyParams(
            isLoadMore: Boolean,
            isUserRefresh: Boolean
        ): MutableMap<String, String?> {
            return getParams(isLoadMore, isUserRefresh)
        }

        override fun mixRequestData(json: JSONObject?, data: String?) {
            try {
                val model = RecommendModelNew(json, data, true, false)
                if (mRecommendData != null) {
                    if (mRecommendData!!.header == null) {
                        mRecommendData!!.header = mutableListOf()
                    }
                    if (mRecommendData!!.body == null) {
                        mRecommendData!!.body = mutableListOf()
                    }

                    mRecommendData!!.header.addAll(model.header)
                    mRecommendData!!.body.addAll(model.body)
                } else {
                    mRecommendData = model
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        override fun mixRequestDidComplete(isLoadMore: Boolean, data: String?) {
            mRecommendRnFragmentWK?.get()?.setRefreshing(false)
            if (!isLoadMore) {
                mRecommendRnFragmentWK?.get()?.cancelHomeTabRefreshDrawable()
            }
        }

        override fun mixRequestWillSend(isLoadMore: Boolean, isByUser: Boolean) {
            mRecommendRnFragmentWK?.get()?.setRefreshing(!isLoadMore)
            if (!isLoadMore) {
                mRecommendRnFragmentWK?.get()?.onPageRefresh()
            }
        }

        override fun traceOnItemShow(itemType: String, index: Int) {
            RecommendRnViewAdapter.traceOnItemShow(itemType, index)
        }

        override fun feedItemClick(type: String, data: String, isClickPlayBtn: Boolean, view: View): Boolean {
            if (type == "track") {
                return FeedItemClickUtil.clickTrack(data, isClickPlayBtn ,view)
            } else if (type == "album") {
                return FeedItemClickUtil.clickAlbum(data ,view)
            }
            return false
        }

        override fun onScrollChange(scrollY: Int) {
            mRecommendRnFragmentWK?.get()?.onScrollChange(scrollY)
        }

        override fun changeRefreshBtn(isShowRefreshing: Boolean) {
            mRecommendRnFragmentWK?.get()?.changeRefreshBtn(isShowRefreshing)
        }

        override fun onScrollViewChange(realView: View, view: IScrollView) {
            mRecommendRnFragmentWK?.get()?.onScrollViewChange(realView, view)
        }

        override fun clickHomeModuleSatisfaction(params: String) {
            SatisfactionHomeModuleManager.rnClickSatisfactionView(params)
        }

        override fun clickModuleDislike(params: String) {
            SatisfactionHomeModuleManager.rnClickModuleDislike(params)
        }

        override fun getSatisfactionShowTime(): Long {
            return SatisfactionManager.getRnSatisfyTime()
        }

        override fun saveSatisfactionShowTime(time: Long) {
            SatisfactionManager.saveRnSatisfyTime(time)
        }

        override fun saveRankData(params: JSONArray) {
            AggregateRankUtil.clearRankMapData()
            val gson = Gson()
            for (i in 0 until params.length()) {
                try {
                    val item = params.optJSONObject(i)
                    val refId = item.optLong("refId")
                    val subRefInfo = item.optJSONObject("subRefInfo")
                    val ubt = item.optJSONObject("ubt")
                    val ubtModel = if (ubt != null) {
                        gson.fromJson(ubt.toString(), Ubt::class.java)
                    } else {
                        null
                    }
                    val businessExtraInfo = if (subRefInfo != null) {
                        gson.fromJson(
                            subRefInfo.toString(), RecommendBusinessExtraInfo::class.java
                        )
                    } else {
                        null
                    }
                    if (refId != 0L && (businessExtraInfo != null || ubtModel != null)) {
                        val extraData = RankExtraData(ubtModel, businessExtraInfo)
                        AggregateRankUtil.addRankMapData(refId, extraData)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        override fun getSceneStyle(): Int {
            return SceneStyleUtil.getSceneStyle()
        }
    }

    private fun sendHomeData(data: RecommendModelNew? = null) {
//        val json = JSONObject()
//        json.put("mixPreloadDada", data?.jsonStr)
//        homeNetDataCallback?.onResult(json.toString())
        homeNetDataCallback?.onResult(data?.jsonStr)
        homeNetDataCallback = null
    }

    fun loadDataFromNet(
        isLoadMore: Boolean = false,
        isFirstRequestAd: Boolean = false,
        callBack: IDataCallBack<RecommendModelNew?>? = null,
        rnCallBack: IHomeRnRequestCallBack? = null
    ) {
        loadDataFromNet(isLoadMore, isFirstRequestAd, false, callBack, rnCallBack)
    }

    fun getParams(
        isLoadMore: Boolean,
        isUserRefresh: Boolean,
        rnCallBack: IHomeRnRequestCallBack? = null
    ): MutableMap<String, String?> {
        val params = mutableMapOf<String, String?>()

        val mContext = BaseApplication.getMyApplicationContext()

        val string = SharedPreferencesUtil.getInstance(mContext)
            .getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE)
        if (!TextUtils.isEmpty(string)) {
            params[HttpParamsConstants.PARAM_CODE] = string
        }
        // 服务端返回的数据塞回去  本地数据也需要塞进去
        val offset = mRecommendData?.offset ?: 0
        params["streamOffSet"] = offset.toString()
        // 服务端返回的数据塞回去 本地数据也需要塞进去
        val showModules = mRecommendData?.showModules ?: ""
        if (!(TextUtils.isEmpty(showModules) || TextUtils.equals("null", showModules))) {
            params["showModules"] = showModules
        }
        params["onlyBody"] = isLoadMore.toString()
        params["displayMode"] = RecommendFragmentAbManager.getDisplayMode()
        params["click"] = mNewestPageClicked.toString()

        // 请求的时候添加原始渠道
        if (!TextUtils.isEmpty(DeviceUtil.getOriginalChannel(mContext))) {
            params["originalChannel"] = DeviceUtil.getOriginalChannel(mContext)
        }
        if (BaseUtil.isFoldScreen(ToolUtil.getCtx())) {
            params["isFoldDevice"] = "1"
        }
        params["size"] = "18"

        val strInterestCard = MMKVUtil.getInstance().getString(
            PreferenceConstantsInHost.KEY_CUSTOMIZED_INTEREST_CARD_MODEL_NEW
        )
        if (!TextUtils.isEmpty(strInterestCard)) {
            try {
                val jsonObject = JSONObject(strInterestCard)
                if (jsonObject.has("gender")) {
                    params["gender"] = jsonObject.optString("gender")
                }
                if (jsonObject.has("ageRange")) {
                    params["ageRange"] = jsonObject.optString("ageRange")
                }
                if (jsonObject.has("interestedCategories")) {
                    params["interestedCategories"] = jsonObject.optString("interestedCategories")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        // 传这个字段主要是为了在刚设置完后服务端可能拿不到的情况做备用，所以只在新安装时传，后面不传
        if (ToolUtil.isFirstInstallApp(mContext)) {
            val newCodes = MmkvCommonUtil.getInstance(mContext).getStringCompat(
                PreferenceConstantsInHost.KEY_CHOOSE_LIKE_SELECTED_ALL_CODES
            )
            if (!TextUtils.isEmpty(newCodes) && newCodes.length < 512) {
                params["newCodes"] = newCodes
            }
        }

        if (RecommendFragmentAbManager.MODE_MIX == mode) {
//            var dynamicParams: String? = ""
//            val templateInfo = TemplateManager.getInstance()
//                .getTemplateInfo(RecommendPageVirtualViewManager.BUSINESS_NAME)
//            dynamicParams = (templateInfo?.templateDetail as? XmTemplateDetail)?.dynamicParams
//            if (!dynamicParams.isNullOrEmpty()) {
//                params["dynamicParams"] = dynamicParams
//            }
        }
        val playDataParams = PlayDataUtils.getAndRefreshSceneRequestParams(false)
        params.putAll(playDataParams)
        if (!isLoadMore) {
            pageNumber = 1
            ScenePlayDataUtil.refreshRequestParams(playDataParams)
        }
        params["pageNum"] = pageNumber.toString()
        params["chasingGroup"] = ABTest.getString(CConstants.Group_toc.ITEM_CHASING_PRODUCT, "0")

        val screenId = rnCallBack?.getFeedAdScreenId() ?: mScreenId
        params["screenId"] = screenId

        // 增加搜索相关的字段，确保和搜索逻辑一致。 只要是为了焦点标签模块中出搜索标签
        // com.ximalaya.ting.android.search.out.SearchModuleUtils
        val history = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInHost.KEY_RECENT_SEARCH_WORDS_RECORD)
        if (!TextUtils.isEmpty(history)) {
            try {
                params["history"] = URLEncoder.encode(history, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        val albumExposureRecord = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInOpenSdk.KEY_RECENT_ALBUM_EXPOSURE_RECORD)
        if (!TextUtils.isEmpty(albumExposureRecord)) {
            try {
                params["albumExposure"] = URLEncoder.encode(albumExposureRecord, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }
        val tracPlayRecord = MMKVUtil.getInstance()
            .getString(PreferenceConstantsInOpenSdk.KEY_RECENT_TRACK_PLAY_RECORD)
        if (!TextUtils.isEmpty(tracPlayRecord)) {
            try {
                params["trackPlay"] = URLEncoder.encode(tracPlayRecord, "utf-8")
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }

        // 带上实验分组标志
        params["searchIntentionJumpParam"] = AdMakeVipLocalManager.getInstance().abExpScript ?: ""

        // 带上畅听人群画像标志 9.1.96版本
        params["sceneInfo"] = SceneInfoUtil.getSceneInfo()

        if (mLastRequestUidForStartTs != UserInfoMannage.getUid()) {
            mLastRequestUidForStartTs = UserInfoMannage.getUid()
            if (UserInfoMannage.hasLogined()) {
                mLastStartTs = MMKVUtil.getInstance().getLong(
                    "recommendLastStartTs_" + UserInfoMannage.getUid(),
                    System.currentTimeMillis()
                )
                MMKVUtil.getInstance().saveLong(
                    "recommendLastStartTs_" + UserInfoMannage.getUid(),
                    System.currentTimeMillis()
                )
            } else {
                mLastStartTs = System.currentTimeMillis()
            }
            sMotFreshListMap?.clear()
        } else if (mLastRequestUidForStartTs > 0L) {
            mLastStartTs = MMKVUtil.getInstance().getLong(
                "recommendLastStartTs_" + UserInfoMannage.getUid(),
                System.currentTimeMillis()
            )
            MMKVUtil.getInstance().saveLong(
                "recommendLastStartTs_" + UserInfoMannage.getUid(),
                System.currentTimeMillis()
            )
            if (!sMotFreshListMap.isNullOrEmpty()) {
                val motFreshListArray = JSONArray()
                sMotFreshListMap?.keys?.forEach {
                    motFreshListArray.put(sMotFreshListMap?.get(it))
                }
                params["motFreshList"] = motFreshListArray.toString()
            }
        }
        if (mLastStartTs > 0L) {
            // 上次冷启动时间
            params["lastStartTs"] = mLastStartTs.toString()
        }
        val growthContentId = GrowthItingUtil.getGrowthContentId()
        if (!growthContentId.isNullOrEmpty()) {
            params["hasIting"] = "true"
            params["itingContentId"] = growthContentId
        }
        // 上报本地缓存ab值
        params["clientLocalCache"] = RecommendFragmentAbManager.getClientLocalCache()
//        if (isLoadMore) {
//            mRecommendData?.cardStreamParam?.let {
//                if (it.isNotEmpty()) {
//                    params["cardStreamParam"] = it
//                }
//            }
//        }

        if (!isLoadMore) {
            resetCenterBigPicAdOptimization(rnCallBack)
            val adFreeType = CommonAdFreeManager.addFreeAdTypeRequestParam(
                AdResourceInfo(
                    AppConstants.AD_POSITION_NAME_STAGGERED_HOME_AD, null
                )
            )
            var filterModuleType = "focus"
            filterModuleType = if (adFreeType == null) {
                filterModuleType
            } else {
                "$filterModuleType,$adFreeType"
            }
            if (filterModuleType.isNotEmpty()) {
                params["filterModuleType"] = filterModuleType
            }
        }

        SceneInfoUtil.addNewFreeCrowdToParams(params)
        params["isAppleReview"] = "false"
        params["refreshStatus"] = if (isUserRefresh) {
            "1"
        } else {
            "0"
        }
        params["liveExposure"] = liveExposureIds.joinToString(",")
        liveExposureIds.clear()

        // 上报高德的授权信息
        val map = XmRemoteControlUtil.getAuthorizeBindDataForHome()
        var outAct = ""
        if (!map.isNullOrEmpty()) {
            try {
                outAct = Gson().toJson(map)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        params["outAct"] = outAct

        ITingOutsideSourceManager.addOutsideSourceParams(mContext, params)

        // 添加预览参数
        HomePreviewUtil.homeRequestMap?.forEach {
            params[it.key] = it.value
        }

        return params
    }

    fun getUrlParams(
        isLoadMore: Boolean,
        rnCallBack: IHomeRnRequestCallBack? = null
    ): MutableMap<String, String?> {
        val paramsQuery = mutableMapOf<String, String?>()
        val mContext = BaseApplication.getMyApplicationContext()

        paramsQuery["appid"] = "0"
        paramsQuery["deviceId"] = DeviceUtil.getDeviceToken(mContext)
        paramsQuery["operator"] = NetworkType.getOperator(mContext).toString() + ""
        val string = SharedPreferencesUtil.getInstance(mContext)
            .getString(PreferenceConstantsInHost.TINGMAIN_KEY_LOCAL_CITY_CODE)
        if (!TextUtils.isEmpty(string)) {
            paramsQuery[HttpParamsConstants.PARAM_CODE] = string
        }
        paramsQuery["version"] = DeviceUtil.getVersion(mContext)
        if (UserInfoMannage.hasLogined()) {
            paramsQuery["uid"] = UserInfoMannage.getUid().toString() + ""
        }
        paramsQuery["scale"] = "1"
        paramsQuery["categoryId"] = "" + BannerView.RECOMMEND_CATEGORY_ID
        paramsQuery["device"] = "android"
        paramsQuery["network"] = CommonRequestM.getInstanse().netWorkType
        paramsQuery["xt"] = System.currentTimeMillis().toString()
        paramsQuery["channel"] = CommonRequestM.getInstanse().umengChannel
        if (ConstantsOpenSdk.isDebug) {
            // 方便mock数据
            paramsQuery["loadMore"] = isLoadMore.toString()
        }

        // 新安装首次启动时传一下firstInstall参数，方便服务端做处理，由于可能存在一些强制刷新的操作，所以30秒内请求都带一下这个参数
        if (ToolUtil.isFirstInstallApp(mContext)) {
            val time = System.currentTimeMillis()
            if (sLastPassFirstInstallParamTime == 0L || time - sLastPassFirstInstallParamTime < 30 * 1000) {
                sLastPassFirstInstallParamTime = time
                paramsQuery["firstInstall"] = "2"
            }
        }

        val playDataParams = PlayDataUtils.getAndRefreshSceneRequestParams(false)
        if (!isLoadMore) {
            pageNumber = 1
            ScenePlayDataUtil.refreshRequestParams(playDataParams)
        }

        val screenId = rnCallBack?.getFeedAdScreenId() ?: mScreenId
        paramsQuery["screenId"] = screenId

        return paramsQuery
    }

    fun loadDataFromNet(
        isLoadMore: Boolean = false,
        isFirstRequestAd: Boolean = false,
        isUserRefresh: Boolean = false, // 用户主动刷新
        callBack: IDataCallBack<RecommendModelNew?>? = null,
        rnCallBack: IHomeRnRequestCallBack? = null
    ) {
        if (mIsRecommendDataLoading) {
            return
        }
        mRefreshTraceHelper = if (!isLoadMore && !isFirstRequestAd) {
            TraceHelper("首页刷新")
        } else {
            null
        }
        mRefreshTraceHelper?.postPageStartNode()
        TraceHelperManager.postNode(
            TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED, "准备请求数据"
        )
        mIsRecommendDataLoading = true
        val mContext = BaseApplication.getMyApplicationContext()

        val params = getParams(isLoadMore, isUserRefresh, rnCallBack)
        val paramsQuery = getUrlParams(isLoadMore, rnCallBack)

        val playDataParams = PlayDataUtils.getAndRefreshSceneRequestParams(false)
        if (!isLoadMore) {
            pageNumber = 1
            ScenePlayDataUtil.refreshRequestParams(playDataParams)
        }

        var showModuleIds = ""
        if (isLoadMore) { // 原样传递给服务端
            showModuleIds = mRecommendData?.showModules ?: ""
        }

        HomeRnTraceTimeManager.onRecommendRequestDataStart()

        MainCommonRequest.getRecommendFeedStreamV4(
            paramsQuery, params, null, showModuleIds, null,
            object : IDataCallBack<RecommendModelNew> {
                override fun onSuccess(recommendModel: RecommendModelNew?) {
                    HomeRnTraceTimeManager.onRecommendRequestDataSuccess()
                    RecommendFragmentPageErrorManager.checkAndUploadEmptyError(
                        recommendModel, isLoadMore
                    )
                    if (!isLoadMore) {
                        val act = BaseApplication.getMainActivity() as? MainActivity?
                        act?.cancelHomeTabRefreshDrawable()
                    }
                    mIsRecommendDataLoading = false
                    mRecommendData = recommendModel
                    callBack?.onSuccess(recommendModel)
                    sendHomeData(recommendModel)

                    TraceHelperManager.postNode(
                        TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
                        "数据加载完成"
                    )
                    var isNoContent = recommendModel?.ret != BaseModel.SUCCESS
                            || (recommendModel.header.isNullOrEmpty() && recommendModel.body.isNullOrEmpty())
                    val headerSize = recommendModel?.header?.size ?: 0
                    val bodySize = recommendModel?.body?.size ?: 0
                    val totalSize = headerSize + bodySize
                    if (!isLoadMore && totalSize < RecommendFragmentPageErrorManager.getFirstScreenMinCardNum()
                        && RecommendFragmentAbManager.is2024NewRecommendFragment()
                    ) {
                        isNoContent = true
                        Logger.e("cf_test", "服务端返回数据太少，并且是下拉刷新，丢弃该数据")
                    }

                    if (isNoContent) {
                        notifyTraceFinish(rnCallBack)
                        return
                    }
                    // 有数据返回  移除高德授权信息
                    XmRemoteControlUtil.removeAuthorizeBindDataForHome()

                    if (!isLoadMore) {
                        RecommendFragmentRealTimeFeedManager.instance.clearRequestTimeMap()
                    }

                    mNewestPageClicked = false
                    val giftTag1 = recommendModel?.giftTag ?: 0
                    if (giftTag1 >= 0 && UserInfoMannage.hasLogined()) {
                        val prefKey = getGiftTagPrefKey(UserInfoMannage.getUid())
                        SharedPreferencesUtil.getInstance(mContext).saveInt(prefKey, giftTag1)
                    }

                    // 更新用户状态是否是新用户
                    VersionUtil.setNewUser(recommendModel!!.isNewUser)
                    NewUserLoginManager.setIsNewUser(recommendModel.isNewUser)
                    if (MMKVUtil.getInstance().getBoolean(
                            PreferenceConstantsInMain.KEY_IS_LOGIN_FROM_STRONG_LOGIN_GUIDE_DIALOG,
                            false
                        )
                    ) {
                        MMKVUtil.getInstance().saveBoolean(
                            PreferenceConstantsInMain
                                .KEY_IS_LOGIN_FROM_STRONG_LOGIN_GUIDE_DIALOG, false
                        )
                        if (!recommendModel.isNewUser) {
                            CustomToast.showToast(R.string.main_text_can_not_receive_new_user_gift)
                        }
                    }

                    rnCallBack?.onSetDataToView()

                    if (!isLoadMore) {
                        loadHomeAd(isFirstRequestAd, rnCallBack)
                    }

                    uploadPageError(true)

                    TraceHelperManager.postNode(
                        TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
                        "数据绑定完成"
                    )
                }

                override fun onError(code: Int, message: String?) {
                    callBack?.onError(code, message)
                    val act = BaseApplication.getMainActivity() as? MainActivity?
                    if (!isLoadMore) {
                        act?.cancelHomeTabRefreshDrawable()
                    }
                    mIsRecommendDataLoading = false
                    sendHomeData()

                    if (rnCallBack?.canUpdateUi() == true) {
                        RecommendPreLoadOptManager.onRecommendRequestDataError(act)
                        HomeRnTraceTimeManager.onRecommendRequestDataError()
                        if (TextUtils.isEmpty(message)) {
                            CustomToast.showToast(
                                "当前无网络，请检查网络后重试",
                                ToastManager.LENGTH_LONG.toLong()
                            )
                        } else {
                            CustomToast.showFailToast(message)
                        }
                    }

                    uploadPageError(false, code, message)
                }
            })

        if (!isLoadMore) {
            loadUserGiftPendant(rnCallBack)
        }
    }

    private var mHasUploadPageError = false

    private fun uploadPageError(isSuccess: Boolean, code: Int? = 0, message: String? = "") {
        if (mHasUploadPageError) {
            return
        }
        mHasUploadPageError = true
        val pageName = if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
            RecommendFragmentPageErrorManager.PAGE_NAME_RN_2025
        } else {
            RecommendFragmentPageErrorManager.PAGE_NAME_RN_2025
        }

        if (isSuccess) {
            RecommendFragmentPageErrorManager.uploadError(pageName, true)
        } else {
            RecommendFragmentPageErrorManager.uploadError(pageName, false, code, message)
        }
    }

    private fun checkMotFreshList(recommendModel: RecommendModelNew?) {
        if (recommendModel == null || recommendModel.header.isNullOrEmpty()) {
            return
        }

        sMotFreshListMap?.clear()
        for (headerIndex in recommendModel.header.indices) {
            val headerItem = recommendModel.header[headerIndex]
            if (headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_CHASING_FOR_UPDATE ||
                headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_MOT_FOLLOW_UPDATE_LIST ||
                headerItem.itemType == RecommendItemNew.RECOMMEND_ITEM_MOT_AUTHOR_UPDATE_LIST
            ) {
                val motName = (headerItem.item as? RecommendCommonItem)?.ext?.extraInfo?.motName
                if (!motName.isNullOrEmpty()) {
                    val jsonObject = JSONObject()
                    jsonObject.put("motName", motName)
                    jsonObject.put("motPosition", (headerIndex + 1))
                    sMotFreshListMap?.put(motName, jsonObject)
                }
            }
        }
    }

    private fun loadHomeAd(firstRequestAd: Boolean, rnCallBack: IHomeRnRequestCallBack?) {
        rnCallBack?.loadHomeAd(firstRequestAd)
    }

    public fun setFirstLoad(isFirstLoad: Boolean) {
        mIsFirstLoad = isFirstLoad
    }

    /**
     * 获取新人礼包挂件数据
     */
    private fun loadUserGiftPendant(rnCallBack: IHomeRnRequestCallBack?) {
        if (rnCallBack == null) {
            return
        }
        MainCommonRequest.getUserGiftPendant(object : IDataCallBack<UserGiftPendantModel?> {
            override fun onSuccess(result: UserGiftPendantModel?) {
                if (!rnCallBack.canUpdateUi()) {
                    return
                }
                if (!result?.pendantPic.isNullOrEmpty()) {
                    rnCallBack.showNewUserGiftFloatingView(true, result)
                } else {
                    rnCallBack.showNewUserGiftFloatingView(false)
                }
            }

            override fun onError(code: Int, message: String) {
                if (!rnCallBack.canUpdateUi()) {
                    return
                }
                rnCallBack.showNewUserGiftFloatingView(false)
            }
        })
    }

    private fun notifyTraceFinish(rnCallBack: IHomeRnRequestCallBack?) {
        rnCallBack?.notifyTraceFinish()
    }

    private fun getGiftTagPrefKey(uid: Long): String {
        return PreferenceConstantsInMain.KEY_RECOMMEND_NEW_USER_GIFT_GIFT_TAG + "_" + uid
    }

    fun logRecommendLoad(msg: String) {
        Logger.logToFile("RecommendFragment:__  $msg")
    }

    var secondFloorLastFoldStatus = false

    fun secondFloorShow2LinesEnable(): Boolean {
        secondFloorLastFoldStatus =
            RecommendCenterBigPicAdManager.notifyCenterBigPicAdShowEnable() &&
                    RecommendCenterBigPicAdManager.isCenterBigAdShowing()
        return secondFloorLastFoldStatus
    }

    private fun resetCenterBigPicAdOptimization(rnCallBack: IHomeRnRequestCallBack?) {
        var foldEnable = MmkvCommonUtil.getInstance(ToolUtil.getCtx())
            .getBoolean(CConstants.Group_ad.ITEM_CENTER_BIG_AD_REFRESH_FOLD, true)
        if (ConstantsOpenSdk.isDebug) {
            foldEnable = "1".equals(ToolUtil.getSystemProperty("debug.center_ad.refresh_fold", "1"))
        }
        if (!foldEnable && rnCallBack?.isRefreshing() == true) {
            return
        }
        RecommendCenterBigPicAdManager.resetOptimizationData()
    }

    fun updateScreenId(screenId: String?) {
        mScreenId = screenId
    }

    fun setRecommendPage(recommendFragment: RecommendRnFragment) {
        mRecommendRnFragmentWK = WeakReference(recommendFragment)
    }
}