package com.ximalaya.ting.android.main.fragment.find.child.staggered

import android.animation.ObjectAnimator
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.*
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.google.gson.Gson
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView
import com.handmark.pulltorefresh.library.internal.IRefreshPullProportion
import com.handmark.pulltorefresh.library.internal.LoadingLayout
import com.ximalaya.ting.android.ad.splashad.ISplashAdStateChange
import com.ximalaya.ting.android.ad.splashad.SplashAdStateChangeManager
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter
import com.ximalaya.ting.android.firework.FireworkAgent
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.framework.startup.StartupManager
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelper
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager
import com.ximalaya.ting.android.framework.tracemonitor.TraceHelperManager.setTraceHelper
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenTimeDialogManager
import com.ximalaya.ting.android.host.dialog.freelistennew.FreeListenTimeDialogManagerCompat
import com.ximalaya.ting.android.host.dialog.interest.manager.HomeChooseInterestManager
import com.ximalaya.ting.android.host.fragment.BaseHomePageTabFragment
import com.ximalaya.ting.android.host.fragment.play.PlayBarAbManager
import com.ximalaya.ting.android.host.listener.IFragmentFinish
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.manager.*
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager.isNeedForcePreloadRecommendPage
import com.ximalaya.ting.android.host.manager.SkinManager.onRecommendFragmentVisibleChange
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.account.XmLocationManager
import com.ximalaya.ting.android.host.manager.activity.MainActivityVipTabLimitFreeManager
import com.ximalaya.ting.android.host.manager.ad.AdManager
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockSnackBarManager
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenCountDownManager
import com.ximalaya.ting.android.host.manager.growth.XmGrowthManagerHelper
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.perf.PerfMonitor
import com.ximalaya.ting.android.host.manager.play.AdMakeVipLocalManager
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager
import com.ximalaya.ting.android.host.manager.play.PlayerManager
import com.ximalaya.ting.android.host.model.ad.BannerModel
import com.ximalaya.ting.android.host.model.homepage.HomePageTabTheme
import com.ximalaya.ting.android.host.satisfaction.SatisfactionHomeManager
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.*
import com.ximalaya.ting.android.host.util.constant.ActionConstants
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants
import com.ximalaya.ting.android.host.util.extension.dp
//import com.ximalaya.ting.android.host.util.isTemplateOpenFull
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.video.VideoPlayManager
import com.ximalaya.ting.android.host.view.BaseBannerView
import com.ximalaya.ting.android.host.view.CustomTipsView
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.RecommendFocusAdapterProviderStaggered
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.RecommendStaggeredSizeManager
import com.ximalaya.ting.android.main.adapter.find.recommendStaggered.RecommendStaggeredTraceManager
import com.ximalaya.ting.android.main.adapter.find.util.FreeListenerUtils
import com.ximalaya.ting.android.main.adapter.find.util.SceneAnimalStatusUtils
import com.ximalaya.ting.android.main.adapter.find.util.SceneListenHorMoreUtil
import com.ximalaya.ting.android.main.adapter.find.util.ShowTagPlayUtil
import com.ximalaya.ting.android.main.adapter.find.util.TextWrapUtil
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel
import com.ximalaya.ting.android.main.constant.PreferenceConstantsInMain
import com.ximalaya.ting.android.main.fragment.find.FreeListenTimeFireworkDialogManagerNew
import com.ximalaya.ting.android.main.fragment.find.HomePageFragment
import com.ximalaya.ting.android.main.fragment.find.HomePageListViewHeightUtil
import com.ximalaya.ting.android.main.fragment.find.child.BigScreenAdManager
import com.ximalaya.ting.android.main.fragment.find.child.RecommendFragmentPageErrorManager
import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendChannelITing
import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendFragmentTraceViewManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageGaiaXManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager
//import com.ximalaya.ting.android.main.fragment.find.child.manager.RecommendPageVirtualViewManager.ACTION_ADD_TO_BE_PLAY
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.*
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item.VideoPreview
import com.ximalaya.ting.android.main.listener.IRecommendFeedItemActionListener
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager
import com.ximalaya.ting.android.main.manager.firework.FireWorkMainManager
import com.ximalaya.ting.android.main.manager.freelisten.manager.CheckFreeListenDialogManager
import com.ximalaya.ting.android.main.manager.listentask.ListenTaskManager
import com.ximalaya.ting.android.main.manager.newUser.NewUserManager
import com.ximalaya.ting.android.main.manager.playToBeAddTipHasShow
import com.ximalaya.ting.android.main.manager.todayHasShowPlayToBeAddTip
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.model.rec.RecommendModelNew
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem
import com.ximalaya.ting.android.main.model.rec.UserGiftPendantModel
import com.ximalaya.ting.android.main.satisfy.SatisfactionHomeModuleManager
import com.ximalaya.ting.android.main.util.FeedTrackPlayUtil
import com.ximalaya.ting.android.main.util.HomeSceneTitleClickGuideUtil
import com.ximalaya.ting.android.main.util.NewHomeGuideDataUtil
import com.ximalaya.ting.android.main.util.SceneListenAnimaUtil
import com.ximalaya.ting.android.main.util.setOnOneClickListener
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.main.view.PullToRefreshStaggeredRecyclerView
import com.ximalaya.ting.android.main.view.RecommendPullToRefreshStaggeredRecyclerView
import com.ximalaya.ting.android.main.view.StaggeredGridItemDecoration2
import com.ximalaya.ting.android.main.view.album.DateUtils
import com.ximalaya.ting.android.main.view.other.AdsorbView
import com.ximalaya.ting.android.main.view.other.UnfoldCollapseView
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.advertis.RecommendPromotionModel
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants
import com.ximalaya.ting.android.opensdk.model.track.SimpleTrackForToListen
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException
import com.ximalaya.ting.android.opensdk.util.EasyConfigure
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper
import com.ximalaya.ting.android.xmtrace.ManualExposureHelper
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.ref.SoftReference
import java.util.*


/**
 * Created by changle.fang on 2021/10/25.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
public class RecommendFragmentStaggered : BaseHomePageTabFragment(), IFragmentFinish,
    PullToRefreshStaggeredRecyclerView.IHeaderColorProvider, IScreenConfigChangedListener, HomeRecommendPageLoadingOptimizationManager.IAdShowAndDestroyListener {

    private var TAG = "RecommendFragmentStaggered"
    private var mShowTitleBar = false
    var mRecyclerView: RecommendPullToRefreshStaggeredRecyclerView? = null
    var mVBottomBg: View? = null
    private var mNetManager = RecommendFragmentNetManager.instance
    var mTraceHelper = TraceHelper("新首页", true)
    var mAdapter = RecommendFragmentStaggeredAdapter(this)
    private var mTipsView: CustomTipsView? = null
    var mAdapterAction: RecommendFragmentStaggeredAdapter.IDataAction? = null
    var mRealTimeFeedListener: IRecommendFeedItemActionListener? = null
    var mPromotionOperationColor = INVALID_COLOR
    var mHasPromotionOperationModule = false
    var mHomePageTabTheme: HomePageTabTheme? = null
    private var mHasSetHomePageTabAsRefreshBtn = false
    var mIsShowRefreshState = false
    var mIsRefreshing = false
    var mFirstBodyItem = -1

    private var mLoadingState = LoadCompleteType.OK
    private var mTvToast: TextView? = null
    private var mFlToast: View? = null
    private var mLlBottomToast: View? = null
    private var mTvBottomToast: TextView? = null
    private var mIvBottomToastFloatArrow: ImageView? = null
    var mRefreshAddedItemNum = -1
    var mBottomToastFloatingArrowAnimator: ObjectAnimator? = null
    var mUserGiftPendantModel: UserGiftPendantModel? = null // 右下角挂件数据
    private var mNewUserGiftFloatingView: AdsorbView? = null
    var mFocusAdapterProvider: RecommendFocusAdapterProviderStaggered? = null
//    var mCalabashAdapterProviderStaggered: CalabashAdapterProviderStaggered? = null
    var mVideoPlayManager: VideoPlayManager = VideoPlayManager(this)

    var mFocusImages: ArrayList<BannerModel> = ArrayList() // 轮播图片的信息
    val mMixFocusList: ArrayList<BannerModel> = ArrayList() // mix banner
    private var mHeaderHeight = 0
    private var mLayoutManager: OffsetStaggeredGridLayoutManager? = null
    private var mLastLeaveTime = 0L // 用户上次离开这个页面的时间
    private var mNeedReloadDataWhenResumed = false
    var mRecommendData: RecommendModelNew? = null
    private var mListDataUpdated = false
    var isLoadLocalData = false
    private var isRefreshData = false
    private var mHasAddListenerForMission = false
    private var mReadyToRemoveListener = false
    private var mRecommendAdManager: RecommendFragmentAdManager? = null
    var mFeedAdManager: IFeedAdManagerStaggered? = null
    var mCenterBigFeedAdManager: RecommendCenterBigFeedAdManager? = null
    var mFeedAnchorAdManager: IFeedAnchorAdManagerStaggered? = null
    var mCenterBigPicAdManager: RecommendCenterBigPicAdManager? = null
    private var mItemDecoration: StaggeredGridItemDecoration2? = null
    private var mRnClassName: String? = null
    private var mResetForChildMode = false // 切换青少年模式重置数据
    private var mLastScrollHeight = 0
    private var mLastScrollTraceHeight = 0
    private var mLastScrollTraceHeight2 = 0
    private var mListViewScrollDirection = 0 // 0-上滑 1-下滑
    private val mListViewHeightOnScreen = HomePageListViewHeightUtil.getListViewHeightOnScreen()
    private var mHasReportScrollUp = false
    private var mHasReportScrollDown = false
    private var mIsShowNewUserGiftView = false
    private var mNewUserGiftViewIting = ""
    private var mNewUserGiftExploreType = 1
    var mHasTraceNewUserGiftAfterData = false
    var mHasUploadPageError = false
    var mUseLocalData = false // 使用缓存数据
    var mIsRefreshByGuessYouLikeCycle = false // 是否是通过点击猜你喜欢的换一批更新的
    private var mCheckIsRealVisibleToAbandonTraceTask: Runnable? = null
    private var mHasCreate = false
    var useRealTimeFeed = false // 是否使用实时化信息流
    private var mLastIsNew2024RecPage: Boolean? = null
    private var mBottomPullDownTipView: View? = null
    private var mHidePullDownTip = false
    companion object {
        var mExploreType = 1 // 首屏曝光是1，其余是0
        var DEFAULT_RESET_INTERVAL = 6 * 3600 * 1000L // 默认重置时间间隔，单位毫秒
        var sFragmentReference: SoftReference<RecommendFragmentStaggered>? = null
        var mIsRenderComplete = false
        @JvmField
        var sUserTrackingAbTest = "" // 埋点中的abTest字段
    }

    override fun getPageLogicName(): String {
        return "recommendNewStaggered"
    }

    override fun getContainerLayoutId(): Int {
        return if (mShowTitleBar) R.layout.main_fra_recommend_new_two_column_with_title_bar else R.layout.main_fra_recommend_new_two_column
    }

    override fun inflateViewInner(
        inflater: LayoutInflater,
        layoutId: Int,
        container: ViewGroup?,
        attachToRoot: Boolean
    ): View? {
        return try {
            if (isNeedForcePreloadRecommendPage() && !mShowTitleBar) {
                ViewPool.getInstance()
                    .getViewForFragment(inflater, layoutId, container, attachToRoot, "RecommendFragmentStaggered")
            } else inflater.inflate(layoutId, container, attachToRoot)
        } catch (t: Throwable) {
            throw t
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        mHasCreate = true
        var pageName = "新首页"
        setTraceHelper(
            TraceHelperManager.TraceHelperName.HOME_RECOMMEND_PAGE_STAGGERED,
            mTraceHelper
        )
        mTraceHelper.mNeedCallPauseStopWhilePageEnd = true
        mNetManager.logRecommendLoad("onCreate")
        if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
            mTraceHelper.modifyTraceName(RecommendFragmentPageErrorManager.PAGE_NAME_2024)
            pageName = RecommendFragmentPageErrorManager.PAGE_NAME_2024
        }

        StartupManager.onHomeLoadBegin(pageName)
        HighValueFireworkManager.onHomeLoadBegin(pageName)

//        if (isTemplateOpenFull(this.mContext)) {
//            mTraceHelper.setIsDynamic()
//        }
        mTraceHelper.postPageStartNode()
        if (!userVisibleHint) {
            mTraceHelper.pauseStart()
        } else if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            mTraceHelper.mErrorInfoAtPageEnd = "1"
            mTraceHelper.pauseStart()
        }
        super.onCreate(savedInstanceState)
        ABTest.getString(CConstants.Group_toc.ITEM_RECOMMEND_PAGE_AA, "")
        fid = Configure.MainFragmentFid.RECOMMEND_FRAGMENT
        mShowTitleBar = arguments?.getBoolean(BundleKeyConstants.KEY_SHOW_TITLE, false) ?: false
        if (ViewUtil.isSplashAdShowing()) {
            SplashAdStateChangeManager.getInstance()
                .addSplashAdStateListener(object : ISplashAdStateChange {
                    override fun onSplashAdShow(splashAdShowData: ISplashAdStateChange.SplashAdShowData) {}
                    override fun onSplashAdClick() {}
                    override fun onSplashAdDestroy() {
                        FireworkAgent.onFragmentResume(this)
                    }
                })
        }
        FreeListenTimeFireworkDialogManagerNew.init(this@RecommendFragmentStaggered)
        val filter = IntentFilter()
        filter.addAction(AdUnLockSnackBarManager.REMOVE_TOUCH_AD_INTENT_ACTION)
        filter.addAction(ActionConstants.ACTION_BOUGHT_VIP_SUCCESS)
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalBroadcastReceiver, filter)
        sFragmentReference = SoftReference(this)

        PerfMonitor.init(context)
    }

    private fun checkIsRealVisibleForTrace() {
        if (!isRealVisable) {
            // 10秒后再次检查，如果还不是可见状态，则放弃上报
            mCheckIsRealVisibleToAbandonTraceTask = Runnable {
                if (!isRealVisable) {
                    mTraceHelper.abandon()
                }
            }
            postOnUiThreadDelayed(mCheckIsRealVisibleToAbandonTraceTask, 0)
        }
    }

    fun cancelCheckIsRealVisibleToAbandonTraceTask() {
        if (mCheckIsRealVisibleToAbandonTraceTask != null) {
            removeCallbacks(mCheckIsRealVisibleToAbandonTraceTask)
            mCheckIsRealVisibleToAbandonTraceTask = null
        }
    }

//    private val mItemViewCallback = object : RecommendPageGaiaXManager.IItemViewCallback {
//        override fun getItemWidth() = mItemDecoration?.itemWidth
//
//        override fun getItemHeight() = RpAdaptUtil.rp2Px(444)
//    }

    private val mAdapterActionCallback = object : IAdatperAction {
        override fun getItem(pos: Int): RecommendItemNew? {
            return mAdapter.getItem(pos) as? RecommendItemNew?
        }

        override fun remove(position: Int, needAddData: Boolean) {
            mAdapterAction?.remove(position, needAddData)
        }

        override fun onItemChanged(position: Int) {
            postOnUiThread {
                if (canUpdateUi()) {
                    mAdapter.notifyItemChanged(position)
                }
            }
        }

        override fun onItemAction(
            itemType: IRecommendFeedItemActionListener.FeedItemType,
            itemContentId: Long,
            actionType: IRecommendFeedItemActionListener.ActionType,
            categoryId: Long,
            itemData: RecommendItemNew
        ) {
            mRealTimeFeedListener?.onItemAction(itemType, itemContentId, actionType, categoryId, itemData)
        }

        override fun isPersonalRecOpen(): Boolean {
            return mAdapterAction?.isPersonalRecOpen() ?: false
        }
    }

    override fun initUi(savedInstanceState: Bundle?) {
        Logger.d(TAG, "init ui")
        PerformanceMonitor.traceBegin("Recommend_initUi")
        checkIsRealVisibleForTrace()
//        RecommendPageGaiaXManager.init(this, mItemViewCallback, mAdapterActionCallback)
//        RecommendPageVirtualViewManager.init(this, mAdapterActionCallback)
        mNetManager.mFragment = this
        RecommendFragmentTypeManager.mFragment = this
        RecommendFragmentRealTimeFeedManager.instance.mFragment = this
        if (mShowTitleBar) {
            arguments?.let {
                var title = it.getString(BundleKeyConstants.KEY_TITLE)
                if (title.isNullOrBlank()) {
                    title = getStringSafe(R.string.main_recommend)
                }
                setTitle(title)
            }
        }
        if (BaseFragmentActivity.sIsDarkMode) {
            view?.setBackgroundColor(0xff131313.toInt())
        }
        initToastView()
        initFreeListenCountDownView(false)
        initRecyclerView()
        var useRealTimeFeed = ABTest.getBoolean(CConstants.Group_ad.ITEM_REAL_TIME_FEED_AD, true)
        mFeedAdManager = if (useRealTimeFeed) FeedAdManagerStaggeredRealTime(mRecommendFeedAdProvider)
        else FeedAdManagerStaggeredOld(mRecommendFeedAdProvider)
        mCenterBigFeedAdManager = RecommendCenterBigFeedAdManager(mRecommendFeedAdProvider)

        mRecommendAdManager =
            RecommendFragmentAdManager(this, mRecyclerView) { mHasPromotionOperationModule }
        BigScreenAdManager.getInstance().setBigScreenAdLoadListener(mBigScreenAdLoadListener)

        var useRealTimeAnchorFeed = ABTest.getBoolean(CConstants.Group_ad.ITEM_REAL_TIME_ANCHOR_FEED_AD, false)
        mFeedAnchorAdManager = if (useRealTimeAnchorFeed) FeedAnchorAdManagerRealTime(mRecommendFeedAdProvider)
        else FeedAnchorAdManagerOld(mRecommendFeedAdProvider)

        mCenterBigPicAdManager = RecommendCenterBigPicAdManager(mRecommendFeedAdProvider, mCenterBigFeedAdManager)

        mRecommendAdManager?.init()
        mVBottomBg = findViewById(R.id.main_v_bottom_bg)
        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginStatusChangeListener)
        FoldableScreenCompatUtil.addListener(pageLogicName, this)
        RecommendChannelITing.setRecommendFragmentStaggered(this)
        mTraceHelper.postNode("初始化完成")
        PrivacyProtocolChangeManager.saveRemoteVersionCache()
        PerformanceMonitor.traceEnd("Recommend_initUi", 14)
        HomeChooseInterestManager.checkShowDialog()
    }

    private var visible = false

    private fun onPageView() {
        if (!isAdded) {
            return
        }
        if (!visible) {
            // 新首页  页面展示
            XMTraceApi.Trace()
                .pageView(336, "newHomePage", this)
                .put("currPage", "newHomePage")
                .createTrace()
        }
        visible = true
    }

    private fun onPageExit() {
        if (visible) {
            // 新首页  页面离开
            XMTraceApi.Trace()
                .pageExit2(2336, this)
                .put("currPage", "newHomePage")
                .createTrace()
        }
        visible = false
    }
    private var onMyResumed = false
    private var checkAdDestroyRunnable: Runnable = object : Runnable {
        override fun run() {
            if (HomeRecommendPageLoadingOptimizationManager.mIsHasAdShowed) {
                HandlerManager.obtainMainHandler().removeCallbacks(this)
                HandlerManager.obtainMainHandler().postDelayed(this, 200)
                return
            }
            setRecommendDataForView()
            mTraceHelper.pauseStop()
            HomeRecommendPageLoadingOptimizationManager.onRecommendFragmentResume(true)
        }
    }
    private var mHasCheckAdDestroyRunnable = false
    override fun onMyResume() {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent() && !mHasCheckAdDestroyRunnable) {
            mHasCheckAdDestroyRunnable = true
            checkAdDestroyRunnable.run()
        }
        if (HomeRecommendPageLoadingOptimizationManager.mIsHasAdShowed) {
            HomeRecommendPageLoadingOptimizationManager.addAdShowAndDestroyListeners(this)
        }
        HomeRecommendPageLoadingOptimizationManager.log("___RecommendFragmentStaggered__onMyResume_")
        mNetManager.logRecommendLoad("onMyResume")
        PerformanceMonitor.traceBegin("RecommendFragment_onMyResume")
        val fragment = parentFragment
        if (fragment is HomePageFragment) {
            fragment.updateAtmosphere()
        }
        if (onMyResumed && mNetManager.mRecommendData != null) {
            if (!ViewUtil.isSplashAdShowing()) {
                loadHomeAd(false)
            }
        }
        onMyResumed = true
        SkinManager.addSkinSettingChangeListener(mSkinSettingChangeListener)
        onRecommendFragmentVisibleChange(true)
        onFragmentVisibleChange(true)
        super.onMyResume()
        Logger.d("RecommendStaggered", "onmyresume")
        if (!mResetForChildMode) {
            traceOnTabShown()
            if (mHasTraceNewUserGiftAfterData) {
                traceNewUserGiftFloatingView()
            }
        } else {
            mResetForChildMode = false
        }
        if (SharedPreferencesUtil.getInstance(context).getBoolean(
                PreferenceConstantsInMain.KEY_SETTING_USE_PERSONAL_SERVICE_CHANGED,
                false
            )
        ) {
            SharedPreferencesUtil.getInstance(context).saveBoolean(
                PreferenceConstantsInMain.KEY_SETTING_USE_PERSONAL_SERVICE_CHANGED,
                false
            )
            reset()
        } else if (MMKVUtil.getInstance()
                .getBoolean(PreferenceConstantsInMain.KEY_NEW_USER_TASK_COMPLETE)
        ) {
            MMKVUtil.getInstance()
                .saveBoolean(PreferenceConstantsInMain.KEY_NEW_USER_TASK_COMPLETE, false)
            reset()
        } else {
            checkLeaveTimeToDoSomeAction()
        }
        RecommendFragmentRealTimeFeedManager.instance.removeRunnable()
        mFocusAdapterProvider?.onFragmentResume()

        if (mListDataUpdated) {
            mListDataUpdated = false
            doAfterAnimation(this::setRecommendDataForView)
        }

        val anInt = ConfigureCenter.getInstance().getInt(CConstants.Group_toc.GROUP_NAME, CConstants.Group_toc.ITEM_ZHUMIAN_ICON_SWITCH, 1)
        if (isRefreshData && anInt == 1) {
            mNetManager.loadDataFromNet(false, false, false)
            isRefreshData = false
        }

        if (mHasSetHomePageTabAsRefreshBtn && activity is MainActivity) {
            (activity as MainActivity?)?.setHomePageTabAsRefreshBtn(true)
        }

        // 新用户滚动任务
        if (NewUserManager.getInstance()
                .containsMission(NewUserManager.TYPE_MISSION_HOMEPAGE_SCROLL)
        ) {
            showNewUserScrollMissionTip()
        }

        // 开启推送功能的弹屏 首页展示事件
        FireWorkMainManager.getInstance()
            .setFireWork(this, FireWorkMainManager.TYPE_OPEN_PUSH_SERVICE, null)

        ListenTaskManager.getInstance().onPageResume(
            ListenTaskManager.LISTEN_TASK_POSITION_HOME,
            ListenTaskManager.LISTEN_TASK_PAGE_NAME_HOME, this
        )
        ChildProtectDialogManager.notifyRecommendFragmentVisibilityChanged(true)

        mRecommendAdManager?.onPageResume()
        mAdapter?.onResume()

        if (!ViewUtil.isSplashAdShowing()) {
            recordBannerAd()
        }
        onPageView()
        loadRecommendAd(false)
        XmPlayerManager.getInstance(mContext).addPlayerStatusListener(mPlayStatusListener)
        ToListenManager.addListChangeListener(mToListenListener)

        resumeRecyclerVideoViewPlay()
        ConfigureCenter.getInstance().registerConfigureCallback(configureCallback)
        FreeListenTimeDialogManager.notifyFragmentResume()
        FreeListenTimeDialogManagerCompat.notifyFragmentResume()

        FreeListenerUtils.notifyFragmentResume()

        PerformanceMonitor.traceEnd("RecommendFragment_onMyResume", 9)
    }

    private fun onFragmentVisibleChange(visible: Boolean) {
        if (visible) {
            initFreeListenCountDownView(true)
            val fragment = parentFragment
            if (fragment is HomePageFragment && isResumed) {
                var needRefresh = MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_LIMIT_NEW_USER_COMPLETE_TASK_NEED_REFRESH, false)
                Log.d("onFragmentVisibleChange","needRefresh:$needRefresh")
                if (needRefresh) {
                    MMKVUtil.getInstance().saveBoolean(PreferenceConstantsInHost.KEY_LIMIT_NEW_USER_COMPLETE_TASK_NEED_REFRESH, false)
                    onRefresh()
                }
            }
        } else {
            FreeListenCountDownManager.onRecommendFragmentHide()
        }
    }

    fun setRnClassName(rnClassName: String) {
        mRnClassName = rnClassName
    }

    private fun showNewUserScrollMissionTip() {
        if (mRecyclerView != null && !mHasAddListenerForMission) {
            val recyclerView = mRecyclerView?.refreshableView ?: return
            recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

                val scrollSize = NewUserManager.getInstance().scrollSize

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    if (mReadyToRemoveListener) {
                        return
                    }
                    val visibleViewAmount = mRecyclerView?.findLastVisiblePosition() ?: 0
                    if (scrollSize <= visibleViewAmount) {
                        var validAmount = 0
                        for (i in 0 until visibleViewAmount) {
                            val type = recyclerView.adapter?.getItemViewType(i) ?: 0
                            if (RecommendFragmentTypeManager.VIEW_TYPE_ALBUM == type
                                || RecommendFragmentTypeManager.VIEW_TYPE_TRACK == type
                            ) {
                                validAmount++
                                if (scrollSize <= validAmount) {
                                    NewUserManager.getInstance().tryToCommitMission(
                                        NewUserManager.TYPE_MISSION_HOMEPAGE_SCROLL,
                                        null
                                    )
                                    try {
                                        mReadyToRemoveListener = true
                                        recyclerView.post {
                                            recyclerView.removeOnScrollListener(this)
                                        }
                                    } catch (e: Exception) {
                                        Logger.e(e)
                                    }
                                    return
                                }
                            }
                        }
                    }
                }
            })
            mHasAddListenerForMission = true
        }
    }

    private fun checkCanShowPlayToBeAddTip() {
        Logger.d(TAG, "checkCanShowPlayToBeAddTip")
        if (!PlayBarAbManager.useNewPlayBar()) {
            Logger.d(TAG, "checkCanShowPlayToBeAddTip ab not open")
            return
        }
        if (todayHasShowPlayToBeAddTip()) {
            Logger.d(TAG, "checkCanShowPlayToBeAddTip todayHasShowed")
            return
        }
        postOnUiThreadDelayedAndRemovedOnPause(5_000, mShowTipRunnable)
    }

    private val mShowTipRunnable = Runnable {
        Logger.d(TAG, "mShowTipRunnable show tip")
        if (!canUpdateUi()) {
            Logger.d(TAG, "mShowTipRunnable canUpdateUi=false")
            return@Runnable
        }
        val anchorView = null
        if (anchorView == null) {
            Logger.d(TAG, "mShowTipRunnable anchorView=null")
            return@Runnable
        }
        Logger.d(TAG, "mShowTipRunnable show")
        val content = "下一条播放"
        val tip = CustomTipsView.Tips.Builder(content, anchorView, "play_to_be_add_recommend_feed")
                .setLevel(CustomTipsView.Tips.TIP_NORMAL)
                .setOffset(-20)
                .setDelay(5_000)
                .setAutoDismiss(true)
                .setAutoSaveKeyToSp(false)
                .setDismissCallback {
                    playToBeAddTipHasShow()
                }
                .setDirection(CustomTipsView.DOWN)
                .create()
        val mainAct = BaseApplication.getMainActivity()
        if (mTipsView == null && mainAct is MainActivity) {
            mTipsView = CustomTipsView(mainAct)
        }
        mTipsView?.let {
            it.addTip(tip)
            it.showAllTips()
        }
    }

//    private fun getFirstTrackAnchorViewFromVirtualView(): View? {
//        try {
//            mRecyclerView?.apply {
//                val firstVisiblePosition = findFirstVisiblePosition()
//                val lastVisiblePosition = findLastVisiblePosition()
//                val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
//                for (i in 0..visibleItemCount) {
//                    val view = refreshableView.getChildAt(i)
//                    var viewBase: ViewBase? = null
//                    var childView = view
//                    if (view is IContainer) {
//                        childView = view
//                    } else if (view is ViewGroup) {
//                        childView = view.getChildAt(0)
//                    }
//                    if (childView is IContainer) {
//                        viewBase = (childView as IContainer).virtualView
//                        if (VirtualViewUtils.viewIsRealShowing(childView)) {
//                            val anchorView = getAnchorView(childView)
//                            if (anchorView != null) {
//                                return anchorView
//                            }
//                        }
//                    }
//                }
//            }
//        } catch (e: Exception) {
//            if (ConstantsOpenSdk.isDebug) {
//                throw e
//            }
//            e.printStackTrace()
//        }
//        return null
//    }
//
//    private fun getAnchorView(rootView: View?): View? {
//        if (rootView == null || rootView !is IContainer) {
//            return null
//        }
//        if (rootView.virtualView?.action == ACTION_ADD_TO_BE_PLAY) {
//            return rootView
//        }
//        if (rootView is ViewGroup) {
//            val childCount = rootView.childCount
//            if (childCount > 0) {
//                for (i in 0 until childCount) {
//                    val view = getAnchorView(rootView.getChildAt(i))
//                    if (view != null) {
//                        return view
//                    }
//                }
//            }
//        }
//        return null
//    }

    private val mLoginStatusChangeListener: ILoginStatusChangeListener =
        object : ILoginStatusChangeListener {
            override fun onLogout(olderUser: LoginInfoModelNew) {
                handleLoginStatusChanged()
            }

            override fun onLogin(model: LoginInfoModelNew) {
                handleLoginStatusChanged()
            }

            override fun onUserChange(oldModel: LoginInfoModelNew, newModel: LoginInfoModelNew) {
                if (newModel != null && newModel != oldModel) {
                    handleLoginStatusChanged()
                }
            }
        }

    private fun handleLoginStatusChanged() {
        postOnUiThread { reset() }
        updateSlogan()
    }

    override fun onPause() {
        SkinManager.removeSkinSettingChangeListener(mSkinSettingChangeListener)
        onRecommendFragmentVisibleChange(false)
        onFragmentVisibleChange(false)
        super.onPause()

        PerformanceMonitor.traceBegin("RecommendFragment_onPause")
        mHidePullDownTip = true
        Logger.d("RecommendStaggered", "pause visble :$userVisibleHint")
        mFocusAdapterProvider?.stopAutoSwapFocusImage()
        if (mHasSetHomePageTabAsRefreshBtn && activity is MainActivity) {
            (activity as MainActivity?)?.setHomePageTabAsRefreshBtn(false)
        }
        if (userVisibleHint) {
            mLastLeaveTime = System.currentTimeMillis()
            onPageExit()
        }

        // MainActivity的公用CustomTipsView
        val tipsView =
            TempDataManager.getInstance().getObjectLayzed(MainActivity.TEMP_DATA_MAIN_TIPS)
        if (tipsView is CustomTipsView) {
            tipsView.dismissTips()
        }

        mTipsView?.dismissTips()

        mAdapter?.onPause()
        ListenTaskManager.getInstance().onPagePause(ListenTaskManager.LISTEN_TASK_POSITION_HOME)

        mFocusAdapterProvider?.onFragmentPause()
        mBottomToastFloatingArrowAnimator?.let {
            if (it.isRunning) {
                it.cancel()
            }
        }
        abandonTrace()
        ChildProtectDialogManager.notifyRecommendFragmentVisibilityChanged(false)
        mRecommendAdManager?.onPagePause()
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(mPlayStatusListener)
        ToListenManager.removeListChangeListener(mToListenListener)
        stopRecyclerVideoViewPlay()

        SatisfactionHomeManager.pagePause()
        SatisfactionHomeModuleManager.pagePause()
        FreeListenerUtils.notifyFragmentPause()

        PerformanceMonitor.traceEnd("RecommendFragment_onPause", 10)
    }

    private fun resumeRecyclerVideoViewPlay() {
        if (mRecyclerView == null || mRecyclerView?.refreshableView == null) {
            return
        }
        mVideoPlayManager.resumeAttachedVideoPlay()
    }

    private fun stopRecyclerVideoViewPlay() {
        if (mRecyclerView == null || mRecyclerView?.refreshableView == null) {
            return
        }
        mVideoPlayManager.pauseAttachedVideoPlay()

//        val firstVisiblePosition = mRecyclerView?.findFirstVisiblePosition() ?: 0
//        val currentPlaying: Int = mVideoPlayManager.currentPlayPosition
//        val headerCount = if (mFirstBodyItem > 0) mFirstBodyItem else 0
//        if (currentPlaying == VideoPlayManager.NO_PLAYING_VIDEO) {
//            return
//        }
//        val pos: Int = currentPlaying - firstVisiblePosition + headerCount
//        val pos: Int = currentPlaying - firstVisiblePosition
//        if (pos < 0 || pos >= mRecyclerView?.refreshableView!!.childCount) {
//            return
//        }
//
//        val child: View = mRecyclerView?.refreshableView?.getChildAt(pos) ?: return
//
//        val layout = child.findViewById<View>(R.id.host_video_item_view_layout)
//        if (layout != null) {
//            if (layout is VideoItemViewLayout) {
//                layout.stopPlay()
//            }
//        }
    }

    private fun reset() {
        mNeedReloadDataWhenResumed = false
        mNetManager.mNeedClearModuleHis = true
        XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext)
        if (mRecommendData != null) {
            mRecommendData?.setOffset(0)
            mNetManager.setFirstLoad(true)
            if (isRealVisable) {
                mNetManager.loadDataFromNet()
            } else {
                mNeedReloadDataWhenResumed = true
            }
            if (mRecyclerView != null && canUpdateUi()) {
                mRecyclerView?.refreshableView?.scrollToPosition(0)
            }
        }
    }

    override fun onDestroyView() {
        FoldableScreenCompatUtil.removeListener(pageLogicName)
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginStatusChangeListener)
        super.onDestroyView()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        RecommendStaggeredSizeManager.onConfigurationChanged()
        mItemDecoration?.onConfigurationChanged()
        mAdapter.notifyDataSetChanged()
    }

    private fun checkLeaveTimeToDoSomeAction() {
        val curTime = System.currentTimeMillis()
        var needResetInterval = DEFAULT_RESET_INTERVAL
        val n = ConfigureCenter.getInstance().getInt("toc", "homerestart", 0).toLong()
        if (n > 0) {
            needResetInterval = n * 3600 * 1000 // 配置的是小时，转成毫秒
        }
        // 距离上次离开，已经超过指定时间（默认为6小时），进行重置操作。让用户能重新看到第一页的数据。
        if (mLastLeaveTime > 0 && curTime - mLastLeaveTime > needResetInterval) {
            reset()
            RecommendCenterBigPicAdManager.setMainHotStart(true)
        } else {
            val customized = SharedPreferencesUtil.getInstance(mContext)
                .getBoolean(PreferenceConstantsInMain.KEY_CUSTOMIZED, false)
            val recommendCategory = MmkvCommonUtil.getInstance(mContext)
                .getBooleanCompat(PreferenceConstantsInMain.KEY_RECOMMEND_CATEGORY, false)
            if (customized) {
                SharedPreferencesUtil.getInstance(mContext)
                    .saveBoolean(PreferenceConstantsInMain.KEY_CUSTOMIZED, false)
                reset()
            } else if (recommendCategory) {
                MmkvCommonUtil.getInstance(mContext)
                    .saveBoolean(PreferenceConstantsInMain.KEY_RECOMMEND_CATEGORY, false)
                reset()
            }
        }
        checkToRefreshData()
    }

    private fun checkToRefreshData() {
        if (mNeedReloadDataWhenResumed) {
            mNeedReloadDataWhenResumed = false
            if (view != null) {
                postOnUiThreadDelayed({ mNetManager.loadDataFromNet() }, 400)
            }
        }
    }

    fun initRecommendPullDownTip() {
        postOnUiThreadDelayedAndRemovedOnPause(2000) {
            if (mHidePullDownTip) {
                return@postOnUiThreadDelayedAndRemovedOnPause
            }
            if (ViewUtil.haveDialogIsShowing(activity)) {
                ViewUtil.addDialogShowStateChange { hasShow ->
                    if (hasShow) {
                        false
                    } else {
                        showRecommendPullDownTipInner()
                        true
                    }
                }
                return@postOnUiThreadDelayedAndRemovedOnPause
            }
            showRecommendPullDownTipInner()
        }
    }

    private fun showRecommendPullDownTipInner() {
        if (!isRealVisable) {
            return
        }
        if (mHidePullDownTip) {
            return
        }
        SceneAnimalStatusUtils.isBottomPullDownTipViewAnimal(true)
        mBottomPullDownTipView = findViewById(R.id.main_recommend_bottom_pull_guide) ?: return
        mBottomPullDownTipView?.visibility = View.VISIBLE
        val freeListenView: View? = findViewById(R.id.main_ll_bottom_freelisten)
        freeListenView?.visibility = View.GONE
        mRecyclerView?.refreshableView?.smoothScrollBy(0, 100.dp)
        postOnUiThreadDelayedAndRemovedOnPause(500) {
            mRecyclerView?.refreshableView?.smoothScrollBy(0, -100.dp)
            HandlerManager.postOnUIThreadDelay({
                SceneAnimalStatusUtils.isBottomPullDownTipViewAnimal(false)
            }, 500)
        }
        MMKVUtil.getInstance().saveBoolean("recommend_key_first_pull_down_bottom_tip_should_show", false)
    }

    private fun initFreeListenCountDownView(reportStatistic: Boolean) {
        mContainerView?: return
        mBottomPullDownTipView?.visibility = View.GONE
        val freeListenView: View = findViewById(R.id.main_ll_bottom_freelisten) ?: return
        val leftTv : TextView? = freeListenView.findViewById(R.id.main_tv_bottom_freelisten_countdown)
        val rightTv : TextView? = freeListenView.findViewById(R.id.main_tv_bottom_freelisten_get_more)
        val rightImageView : ImageView? = freeListenView.findViewById(R.id.main_iv_bottom_freelisten_getmore)
        FreeListenCountDownManager.setViewStateByFreeListenState(freeListenView, leftTv, rightTv, rightImageView, reportStatistic)
    }

    private fun initToastView() {
        mTvToast = findViewById(R.id.main_tv_toast)
        mFlToast = findViewById(R.id.main_fl_toast)
        mTvBottomToast = findViewById(R.id.main_tv_bottom_toast)
        mLlBottomToast = findViewById(R.id.main_ll_bottom_toast)
        mIvBottomToastFloatArrow = findViewById(R.id.main_iv_float_arrow)
        mLlBottomToast?.setOnOneClickListener {
            mRecyclerView?.refreshableView?.let {
                mLayoutManager?.scrollToPositionWithOffset(mFirstBodyItem, 0)
                mLlBottomToast?.visibility = View.GONE
                postOnUiThread {
                    calculateAllHeaderHeight()
                    mRecyclerView?.scrollY = mHeaderHeight
                }
            }
            RecommendStaggeredTraceManager.traceOnRefreshToastClick(
                mTvBottomToast?.text?.toString() ?: "", "底部"
            )
        }
    }

    fun updateFocusAdapterProvider(focusAdapterProvider: RecommendFocusAdapterProviderStaggered) {
        mFocusAdapterProvider = focusAdapterProvider
        setFocusBackground()
    }

    fun setFocusBackground() {
        if (mFocusAdapterProvider == null) {
            mFocusAdapterProvider =
                mAdapter.getAdapterProvider(RecommendFragmentTypeManager.VIEW_TYPE_FOCUS)
                        as? RecommendFocusAdapterProviderStaggered
        }
        mFocusAdapterProvider?.let {
            if (mHasPromotionOperationModule) {
                var setColor = mPromotionOperationColor
                if (parentFragment is HomePageFragment) {
                    val color = (parentFragment as HomePageFragment?)?.skinColor ?: 0
                    if (color != 0 && color != ColorUtil.INVALID_COLOR) {
                        setColor = color
                    }
                }
                it.setBgColor(setColor)
            } else {
                it.setBgColor(INVALID_COLOR)
            }
        }
        if (BaseFragmentActivity.sIsDarkMode) {
            if (mHasPromotionOperationModule || SkinManager.isNeedShowAtmosphereImage()) {
                view?.background = null
            } else {
                view?.setBackgroundColor(0xff131313.toInt())
            }
        }
    }

//    fun getCalabashUiProvider(): CalabashAdapterProviderStaggered.ICalabashUiProvider? {
//        if (mCalabashAdapterProviderStaggered == null) {
//            mCalabashAdapterProviderStaggered =
//                    mAdapter.getAdapterProvider(RecommendFragmentTypeManager.VIEW_TYPE_CALABASH_TWO_LINE)
//                            as? CalabashAdapterProviderStaggered
//        }
//        if (mCalabashAdapterProviderStaggered == null) {
//            return null
//        }
//        return mCalabashAdapterProviderStaggered!!.calabashUiProvider
//    }

    fun getBgColorFromPromotionModel(promotionAd: Any?) {
        promotionAd ?: return
        if (promotionAd !is RecommendPromotionModel) {
            return
        }
        var firstPicUrl: String? = ""
        if (RecommendPromotionModel.MATERIAL_SOURCE_AD == promotionAd.materialSource) {
            firstPicUrl = promotionAd.morePics[0]
        } else if (RecommendPromotionModel.MATERIAL_SOURCE_OPERATION == promotionAd.materialSource && promotionAd.pictureLinks != null && promotionAd.pictureLinks[0] != null) {
            firstPicUrl = promotionAd.pictureLinks[0].image
        }
        ImageManager.from(mContext).downloadBitmap(firstPicUrl) { _, bitmap ->
            if (bitmap != null) {
                val bgColor = bitmap.getPixel(2, 2)
                if (bgColor != INVALID_COLOR) {
                    mPromotionOperationColor = bgColor
                    mHomePageTabTheme = HomePageTabTheme()
                    mHomePageTabTheme?.searchBoxColor = HomePageTabTheme.FOREGROUND_COLOR_WHITE
                    updateFocusAndHeadBgColor()
                }
            }
        }
    }

    private fun updateFocusAndHeadBgColor() {
        setFocusBackground()
        postOnUiThread(Runnable {
            val intent = Intent(BaseBannerView.COLOR_CHANGE_ACTION)
            intent.putExtra(BaseBannerView.COLOR_DATA, mPromotionOperationColor)
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent)
        })
    }

    private fun hidePromotionOperation() {
        mHasPromotionOperationModule = false
        changeBottomOvalViewHeightForPromotion(false)
        mPromotionOperationColor = INVALID_COLOR
        mFocusAdapterProvider?.setBgColor(BaseHomePageTabFragment.INVALID_COLOR)
        val intent = Intent(RefreshLoadMoreListView.SCROLL_CHANGE_LISTENER_ACTION)
        intent.putExtra(RefreshLoadMoreListView.SCROLL_CHANGE_DATA, 0)
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent)
    }

    fun setDataFromLocal(result: RecommendModelNew) {
        mRecommendData = result
        mNetManager.mRecommendData = result
    }

    fun setIsLoadLocalData(isLoadLocalData: Boolean) {
        this.isLoadLocalData = isLoadLocalData
    }

    private fun checkHomePageBg() {
        (parentFragment as HomePageFragment?)?.onRecommendDataLoadFinish()
    }

    fun setRecommendDataForView() {
        if (PerformanceMonitor.isTraceOpen()) {
            Log.d("PerformanceMonitor", "setRecommendDataForView: " + Log.getStackTraceString(Throwable()))
        }
        PerformanceMonitor.traceBegin("setRecommendDataForView")
        checkHomePageBg()
        checkToShowVipLimitFreeLabel()
        val hasFocusModuleLastTime = mFocusImages.isEmpty()
        val hasPromotionOperationModuleLastTime = mHasPromotionOperationModule
        val promotionOperationModuleColorLastTime = mPromotionOperationColor
        mFocusImages.clear()
        mMixFocusList.clear()
        mHasPromotionOperationModule = false
        changeBottomOvalViewHeightForPromotion(false)
        mPromotionOperationColor = INVALID_COLOR
        val isNew2024RecPage = RecommendFragmentAbManager.is2024NewRecommendFragment()
        if (mLastIsNew2024RecPage != null && mLastIsNew2024RecPage != isNew2024RecPage) {
            // 新老样式切换了，
            mAdapter = RecommendFragmentStaggeredAdapter(this)
            mRecyclerView?.refreshableView?.adapter = mAdapter
        }
        mLastIsNew2024RecPage = isNew2024RecPage
        val list = arrayListOf<Any>()
        var isCheckGuide = false
        if (mRecommendData?.header.isNullOrEmpty() && mRecommendData?.body.isNullOrEmpty()) {
            val itemModel = if (RecommendFragmentAbManager.is2024NewRecommendFragment()) {
                ItemModel(
                    com.ximalaya.ting.android.host.R.drawable.host_img_skeleton_header_loading_recommend_2024,
                    RecommendFragmentTypeManager.VIEW_TYPE_LOADING
                )
            } else {
                ItemModel(
                    com.ximalaya.ting.android.host.R.drawable.host_img_skeleton_header_loading_recommend,
                    RecommendFragmentTypeManager.VIEW_TYPE_LOADING
                )
            }
            list.add(itemModel)
        } else {
            isCheckGuide = true
            mNetManager.addData(list, mRecommendData?.header, true)
            if (mRecommendData?.body?.size != null && mRecommendData?.body?.size!! > 0) {
                addTittleWithCircle(list)
            }
            //添加首页视频专辑预览
            if (VideoPreview.enable()) {
                val previewItem = VideoPreview.getPreviewInfo()?.let {
                    RecommendItemNew.parseJson(it, Gson())
                }
                if (previewItem != null ) {
                    val preViewAlbums = mutableListOf(previewItem)
                    mNetManager.addData(list, preViewAlbums, false)
                }
            }

            mNetManager.addData(list, mRecommendData?.body, false)
        }
        setFocusBackground()
        mAdapter.setData(list, true)
        mNetManager.logRecommendLoad("mAdapter.setData___list.size = " + list.size)
        postOnUiThreadDelayed(object : Runnable {
            override fun run() {
                if (XmGrowthManagerHelper.getNeedReloadData()) {
                    XmGrowthManagerHelper.setNeedReloadData(false)
                    onRefresh()
                }
            }

        }, 2000)
        val hasFocusModuleNow = mFocusImages.isEmpty()
        // 焦点图有无或者大促模块有无发生了变化，需要触发一下首页背景的重新计算。
        if (hasFocusModuleNow != hasFocusModuleLastTime || mHasPromotionOperationModule != hasPromotionOperationModuleLastTime || mPromotionOperationColor != promotionOperationModuleColorLastTime) {
            postOnUiThread {
                val activity: Activity? = activity
                if (activity != null) {
                    val intent = Intent(RefreshLoadMoreListView.SCROLL_CHANGE_LISTENER_ACTION)
                    intent.putExtra(RefreshLoadMoreListView.SCROLL_CHANGE_DATA, mLastScrollHeight)
                    LocalBroadcastManager.getInstance(activity).sendBroadcast(intent)
                }
            }
        }

        if (mHasPromotionOperationModule && getParentFragment() is HomePageFragment) {
            mRecyclerView?.removeRefreshPullProportion(mPullProportion)
            mRecyclerView?.addRefreshPullProportion(mPullProportion)
        }
        PerformanceMonitor.traceEnd("setRecommendDataForView", 11)

        if (isCheckGuide) {
            HomeSceneTitleClickGuideUtil.init(mRecyclerView, mAdapter, this)
        }
    }

    fun addTittleWithCircle(list: ArrayList<Any>) {
        if (mRecommendData?.streamOptionInfo != null
            && mRecommendData?.streamOptionInfo?.isShowGuessCycle == true
            && !TextUtils.isEmpty(mRecommendData?.streamOptionInfo?.title)) {
            list.add(
                ItemModel(
                    mRecommendData?.streamOptionInfo,
                    RecommendFragmentTypeManager.VIEW_TYPE_TITLE_WITH_CYCLE
                )
            )
        } else {
            var recommendItemNew: RecommendItemNew? = null
            if (mRecommendData?.body != null && mRecommendData!!.body.size > 0) {
                recommendItemNew = mRecommendData!!.body[0] as RecommendItemNew
            }
            if (!mRecommendData?.streamTitle.isNullOrEmpty()) {
                val streamOptionInfo = RecommendModelNew.StreamOptionInfo()
                streamOptionInfo.title = mRecommendData?.streamTitle
                streamOptionInfo.streamSubTitle = mRecommendData?.streamSubTitle
                streamOptionInfo.streamTitleStyle = mRecommendData?.streamTitleStyle
                streamOptionInfo.xmRequestId = recommendItemNew?.xmRequestId
                list.add(
                    ItemModel(
                        streamOptionInfo,
                        RecommendFragmentTypeManager.VIEW_TYPE_TITLE_WITH_CYCLE
                    )
                )
                if (recommendItemNew != null) {
                    recommendItemNew.addStreamTitle = 1
                    recommendItemNew.jsonObject.put("addStreamTitle", 1)
                }
            } else {
                if (recommendItemNew != null) {
                    recommendItemNew.addStreamTitle = 0
                    recommendItemNew.jsonObject.put("addStreamTitle", 0)
                }
            }
        }
       setFirstBodyItem(list.size)
    }

    fun setFirstBodyItem(firstBodyItem: Int) {
        mFirstBodyItem = firstBodyItem
        mItemDecoration?.setFirstBodyPosition(mFirstBodyItem)
    }

    private var mPullProportion: IRefreshPullProportion? = object : IRefreshPullProportion {
        override fun onPullProportionChange(rate: kotlin.Float) {
            if (mHasPromotionOperationModule && getParentFragment() is HomePageFragment) {

                var deltaY = 0
                if (java.lang.Float.compare(rate, Int.Companion.MIN_VALUE.toFloat()) != 0) {
                    deltaY = (rate * BaseUtil.dp2px(mContext, 55f)).toInt()
                }
                mLastPullDeltaY = deltaY
                setListScrollHeight(deltaY)
            }
        }
    }

    private fun checkToShowVipLimitFreeLabel() {
        mRecommendData?.let {
            if (MainActivityVipTabLimitFreeManager.isShowVipTabLimitFreeTag(it.isShowLimitFree)) {
                val activity: Activity? = activity
                if (activity != null && activity is MainActivity) {
                    activity.updateVipTabLimitFreeTag(true)
                    MainActivityVipTabLimitFreeManager.saveVipTabLimitFreeShown()
                }
            }
        }
    }

    private var mLastPullDeltaY = 0

    private fun setListScrollHeight(deltaY: Int) {
        if (deltaY < BaseUtil.dp2px(mContext, 80f)) {
            (parentFragment as HomePageFragment?)?.listScrollHeight(-1 * deltaY)
        } else {
            (parentFragment as HomePageFragment?)?.listScrollHeight(
                -1 * deltaY + BaseUtil.dp2px(
                    mContext,
                    80f
                )
            )
        }
    }

    private fun resetListScrollHeightDelay() {
        postOnUiThreadDelayed({ setListScrollHeight(0) }, 125)
    }

    /**
     * 解析轮播广告数据
     */
    fun parseFocusImage(banners: List<BannerModel>) {
        mFocusImages.clear()
        if (ToolUtil.isEmptyCollects(banners)) {
            return
        }
        mFocusImages.addAll(banners)

        // 添加positionTrace埋点
        for (i in mFocusImages.indices) {
            mFocusImages[i].positionForTrace = i + 1
        }
        AdManager.setAdvertisePositionId(
            banners,
            AdManager.getFocusPositionId(IAdConstants.ICategoryId.RECOMMEND_CATEGORY_ID.toLong())
        )
    }

    /**
     * 解析mix 轮播广告数据
     */
    fun parseMixFocusImage(banners: List<BannerModel>) {
        mMixFocusList?.clear()
        if (ToolUtil.isEmptyCollects(banners)) {
            return
        }
        mMixFocusList?.addAll(banners)

        // 添加positionTrace埋点
        for (i in mMixFocusList.indices) {
            mMixFocusList[i]?.positionForTrace = i + 1
        }
        AdManager.setAdvertisePositionId(
            banners,
            AdManager.getFocusPositionId(IAdConstants.ICategoryId.RECOMMEND_CATEGORY_ID.toLong())
        )
    }

    fun calculateAllHeaderHeight() {
        mHeaderHeight = mLayoutManager?.calculateHeaderHeight(mFirstBodyItem) ?: 0
        Logger.d(TAG, "header height:$mHeaderHeight")
    }

    override fun onRefresh() {
        super.onRefresh()
        mIsRefreshByGuessYouLikeCycle = false
        if (mRecyclerView != null) {
            // 这里不会定位到body
            val refreshToHead = true // isFirstVisibleItemInBody()
            if (!refreshToHead) {
                mRecyclerView?.refreshableView?.let {
                    it.scrollToPosition(mFirstBodyItem)
                    postOnUiThread {
                        val scrollY = it.getChildAt(0).top
                        calculateAllHeaderHeight()
                        mRecyclerView?.scrollY = mHeaderHeight
                    }
                }
                mFeedAdManager?.updateScreenId()
                mCenterBigFeedAdManager?.updateScreenId()
                mCenterBigPicAdManager?.updateScreenId()
                mNetManager.loadDataFromNet(isUserRefresh = true)
                loadRecommendAd(true)
            } else {
                // 触发tab的刷新动画
                if (mActivity is MainActivity && mRecyclerView?.isRefreshing == false) {
                    mIsShowRefreshState = true
                    mIsRefreshing = true
                    (mActivity as MainActivity).loadHomeTabRefreshDrawable(true)
                }

                mRecyclerView?.refreshableView?.scrollToPosition(0)
                mRecyclerView?.scrollY = 0
                mRecyclerView?.isRefreshing = true
            }
        }
    }

    public fun isRefreshBecauseOfTabClick() = mIsShowRefreshState

    fun refreshFromGuessCycle() {
        if (!canUpdateUi()) {
            return
        }
        if (mRecyclerView != null) {
            showRefreshingToast(LoadCompleteType.LOADING)
            mIsRefreshByGuessYouLikeCycle = true
            mFeedAdManager?.updateScreenId()
            mCenterBigFeedAdManager?.updateScreenId()
            mNetManager.loadDataFromNet()
            XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext)
            loadRecommendAd(true)
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        PerformanceMonitor.traceBegin("Recommend_setUserVisibleHint")
        val lastUserVisibleHint = userVisibleHint
        if (!onMyResumed && isVisibleToUser && isResumed) {
            try {
                loadRecommendAd(false)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
        onRecommendFragmentVisibleChange(isVisibleToUser)
        onFragmentVisibleChange(isVisibleToUser)
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            onPageView()
            Logger.d("RecommendStaggered", "visble true")
        } else {
            onPageExit()
            Logger.d("RecommendStaggered", "visble false")
        }
        if (isVisibleToUser && isResumed) {
            recordBannerAd()
            traceOnTabShown()
            traceNewUserGiftFloatingView()
            checkLeaveTimeToDoSomeAction()
            ListenTaskManager.getInstance().onPageResume(
                ListenTaskManager.LISTEN_TASK_POSITION_HOME,
                ListenTaskManager.LISTEN_TASK_PAGE_NAME_HOME, this
            )
            if (mNetManager.mRecommendData != null) {
                loadRecommendAd(false)
                loadHomeAd(false)
            }
        } else if (!isVisibleToUser) {
            mFocusAdapterProvider?.stopAutoSwapFocusImage()
            if (mHasSetHomePageTabAsRefreshBtn && activity is MainActivity) {
                (activity as? MainActivity)?.setHomePageTabAsRefreshBtn(false)
            }
            if (lastUserVisibleHint) {
                mLastLeaveTime = System.currentTimeMillis()
            }
            stopRecyclerVideoViewPlay()
        }
        if (lastUserVisibleHint != isVisibleToUser && mAdapter != null) {
            if (isVisibleToUser) {
                mAdapter.onResume()
            } else {
                mAdapter.onPause()
            }
        }
        if (mHasCreate && lastUserVisibleHint != isVisibleToUser) {
            if (isVisibleToUser) {
                mTraceHelper.pauseStop()
            } else {
                mTraceHelper.abandon()
            }
        }
        mFocusAdapterProvider?.setUserVisibleHint(isVisibleToUser, isResumed)
        ChildProtectDialogManager.notifyRecommendFragmentVisibilityChanged(isVisibleToUser)
        mRecommendAdManager?.setUserVisibleHint(isVisibleToUser)

        FreeListenerUtils.setUserVisibleHint(isVisibleToUser)

        PerformanceMonitor.traceEnd("Recommend_setUserVisibleHint", 13)
    }

    private fun traceOnTabShown() {
        postOnUiThread {
            traceItemViewed()
            ManualExposureHelper.exposureViewsByResume(this, mRecyclerView)
        }
    }

    fun traceItemViewed() {
        mRecyclerView?.apply {
            val firstVisiblePosition = findFirstVisiblePosition()
            val lastVisiblePosition = findLastVisiblePosition()
            val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
            for (i in 0..visibleItemCount) {
                mAdapter.traceOnItemShow(i + firstVisiblePosition, refreshableView.getChildAt(i))
            }
        }
        if (isOpenDelayTraceView) {
            mRecyclerView?.apply {
                RecommendFragmentTraceViewManager.traceItemViewedDelay(mRecyclerView!!, mAdapter)
            }
        }
    }

    var isOpenDelayTraceView = false
    var configureCallback = object : IConfigureCenter.ConfigureCallback {
        override fun onResult(success: Boolean) {
            ConfigureCenter.getInstance().unRegisterConfigureCallback(this)
            isOpenDelayTraceView = ConfigureCenter.getInstance().getBool(
                CConstants.Group_android.GROUP_NAME,
                CConstants.Group_android.ITEM_KEY_IS_OPEN_DELAY_TRACE_VIEW, false)
            RecommendFragmentTraceViewManager.sTraceViewDelayTime = ConfigureCenter.getInstance().getInt(
                CConstants.Group_android.GROUP_NAME,
                CConstants.Group_android.ITEM_KEY_TRACE_VIEW_DELAY_TIME, 500).toLong()
            CheckFreeListenDialogManager.cacheConfig()
            NewHomeGuideDataUtil.cacheConfig()
            TextWrapUtil.cacheConfig()
            SceneListenHorMoreUtil.cacheConfig()
        }
    }

    fun firstViewShowingAndIsTop(view: View): Boolean {
        val refreshRecyclerView = mRecyclerView ?: return false
        val recyclerView = refreshRecyclerView.refreshableView ?: return false
        if (refreshRecyclerView.findFirstVisiblePosition() == 0) {
            val childView = recyclerView.getChildAt(0)
            if (childView == view) {
                return true
            }
        }
        return false
    }

    private fun isFirstVisibleItemInBody(): Boolean {
        return mFirstBodyItem != -1 &&
                (mRecyclerView?.findFirstVisiblePosition() ?: 0) >= mFirstBodyItem
    }

    private fun initRecyclerView() {
        mRecyclerView = findViewById(R.id.main_rv_content)
        mRecyclerView?.setHeaderColorProvider(this)
        mRecyclerView?.apply {
            setRefreshMinInterval(1100)
            setOpenRefreshMinInterval(true)
            setScrollYBeforeRefreshComplete(-30.dp)
            isFocusable = false
            isFocusableInTouchMode = false
            setIsShowLoadingLabel(true)
            setIsRandomLabel(true)
            refreshableView.isFocusable = false
            refreshableView.isFocusableInTouchMode = false
            (parent as? ViewGroup)?.apply {
                descendantFocusability = ViewGroup.FOCUS_BLOCK_DESCENDANTS
                isFocusable = false
                isFocusableInTouchMode = false
            }
            // 设置slogan
            updateSlogan()
            ViewUtil.onlySetViewPaddingOne(this, 0, ViewUtil.PADDING_BOTTOM)
            ViewUtil.onlySetViewPaddingOne(this.refreshableView, 0, ViewUtil.PADDING_BOTTOM)
            setOnRefreshLoadMoreListener(object :
                PullToRefreshRecyclerView.IRefreshLoadMoreListener {
                override fun onMore() {
                    mIsRefreshByGuessYouLikeCycle = false
                    mNetManager.loadDataFromNet(isLoadMore = true, onlyBody = true)
                }

                override fun onRefresh() {
                    // 新首页-下拉刷新  其他事件
                    XMTraceApi.Trace()
                        .setMetaId(62362)
                        .setServiceId("others") // 不区分新老首页都上报该埋点
                        .createTrace()
                    mIsRefreshByGuessYouLikeCycle = false
                    // 如果正在去广告二楼,则不处理刷新
                    if (HomePageFragment.isGoingSecondFloor || (mRecommendAdManager != null && !mRecommendAdManager!!.handleRefreshListener())) {
                        mRecommendAdManager?.setHandleRefreshListener(true)
                        return
                    }
                    mIsRefreshing = true
                    showRefreshingToast(LoadCompleteType.LOADING)
                    mFeedAdManager?.updateScreenId()
                    mCenterBigFeedAdManager?.updateScreenId()
                    mNetManager.loadDataFromNet(isUserRefresh = true)
                    XmLocationManager.getInstance().requestLocationInfoLimitByTime(mContext)
                    loadRecommendAd(true)
                }
            })
            setOnActionChange {
                val deltay = mLastPullDeltaY
                postOnUiThreadDelayed({
                    setListScrollHeight(deltay / 2)
                    resetListScrollHeightDelay()
                }, 125)
            }
        }
        val layoutManager = OffsetStaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
        layoutManager.mRecyclerView = mRecyclerView
        mLayoutManager = layoutManager
        mRecyclerView?.apply {
            setAdapter(mAdapter)
            setNeedSendHeightChangeBroadcast(true)
            setPreLoadMoreItemCount(4)
            addOnScrollHeightListener { scrollHeight ->
                if (scrollHeight > 0) {
                    // 当次滑动过,则不显示下拉提示
                    mHidePullDownTip = true
                }
                if (scrollHeight > 300.dp) {
                    mHidePullDownTip = true
                    ViewStatusUtil.setVisible(View.GONE, mBottomPullDownTipView)
                }
                if (scrollHeight > BaseUtil.getScreenHeight(mContext) && RecommendFragmentAbManager.is2024NewRecommendFragment()) {
                    // 新首页滑动超过一屏,则不显示下拉提示
                    mHidePullDownTip = true
                    MMKVUtil.getInstance().saveBoolean("recommend_key_first_pull_down_bottom_tip_should_show", false)
                    ViewStatusUtil.setVisible(View.GONE, mBottomPullDownTipView)
                }
                if (scrollHeight != mLastScrollHeight) {
                    mListViewScrollDirection = if (mLastScrollHeight < scrollHeight) 0 else 1
                    mLastScrollHeight = scrollHeight
                    ChildProtectDialogManager.notifyRecommendFragmentScrollHeightChanged(
                        scrollHeight
                    )
                }
            }
        }
        mRecyclerView?.refreshableView?.let {
            it.layoutManager = layoutManager
            mItemDecoration =
                if (RecommendFragmentAbManager.mode == RecommendFragmentAbManager.MODE_MIX) {
                    StaggeredGridItemDecoration2(16.dp, 0, 16.dp, 28.dp, 13.dp, true, true)
                } else {
                    StaggeredGridItemDecoration2(12.dp, 12.dp, 9.dp, 10.dp, true, true)
                }
            mItemDecoration?.let { decoration ->
                it.addItemDecoration(decoration)
            }
            it.recycledViewPool.setMaxRecycledViews(0, 6)
            it.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        traceItemViewed()
                        mRecyclerView?.let {
                            ManualExposureHelper.exposureViewsByScroll(
                                this@RecommendFragmentStaggered,
                                it
                            )
                        }
                        scrollCheckBannerRecord()
                        traceScrollDeep()
                        traceOnScroll(mListViewScrollDirection == 0)
                        CheckFreeListenDialogManager.checkHomePageShowFreeListenDialog(
                            BaseUtil.px2dip(mContext, mLastScrollHeight.toFloat())
                        )
                        PerfMonitor.stop()

                        val item = SatisfactionHomeModuleManager.getTraceRecommendItem(mAdapter)
                        SatisfactionHomeManager.scrollIdle(
                            this@RecommendFragmentStaggered, mLastScrollHeight,
                            item?.first, item?.second
                        )
                        SatisfactionHomeModuleManager.scrollIdle(
                            this@RecommendFragmentStaggered, mRecyclerView, mAdapter
                        )
                        HomeSceneTitleClickGuideUtil.scrollIdle(mRecyclerView, mAdapter, this@RecommendFragmentStaggered)
                    } else if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                        SatisfactionHomeManager.pageScroll()
                        SatisfactionHomeModuleManager.pageScroll()
                        PerfMonitor.start("recommendFragmentScroll")
                    }
//                    val headerViewCount: Int = mFirstBodyItem
                    val firstVisiblePosition = mRecyclerView?.findFirstVisiblePosition() ?: 0
                    val lastVisiblePosition = mRecyclerView?.findLastVisiblePosition() ?: 0
                    mVideoPlayManager.dispatchScrollStateChange(hashCode(), newState, firstVisiblePosition,
                            lastVisiblePosition)

                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        checkCanShowPlayToBeAddTip()
                    } else {
                        removeCallbacks(mShowTipRunnable)
                    }
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    val firstVisiblePosition = mRecyclerView?.findFirstVisiblePosition() ?: 0
                    if (!mIsShowRefreshState) {
                        if (!mHasSetHomePageTabAsRefreshBtn && mFirstBodyItem != -1 && firstVisiblePosition >= mFirstBodyItem) {
                            (activity as? MainActivity)?.let { activity ->
                                mHasSetHomePageTabAsRefreshBtn = true
                                activity.setHomePageTabAsRefreshBtn(true)
                            }
                        } else if (mHasSetHomePageTabAsRefreshBtn && firstVisiblePosition < mFirstBodyItem) {
                            (activity as? MainActivity)?.let { activity ->
                                mHasSetHomePageTabAsRefreshBtn = false
                                activity.setHomePageTabAsRefreshBtn(false)
                            }
                        }
                    } else {
                        mHasSetHomePageTabAsRefreshBtn = false
                    }
                    val lastVisiblePosition = mRecyclerView?.findLastVisiblePosition() ?: 0
                    if (lastVisiblePosition > 0 && mAdapter.itemCount > 0 && lastVisiblePosition > mAdapter.itemCount - 2 && !getBodyData().isNullOrEmpty()
                        && RecommendFragmentAbManager.mode != RecommendFragmentAbManager.MODE_MIX
                    ) {
                        ViewUtil.setViewVisibilitySafe(mVBottomBg, View.VISIBLE)
                    } else {
                        ViewUtil.setViewVisibilitySafe(mVBottomBg, View.GONE)
                    }
                    mFeedAdManager?.onListScroll(dy > 0)
                    mCenterBigFeedAdManager?.onListScroll(dy > 0)
                    mFeedAnchorAdManager?.onListScroll(dy > 0)
                    mVideoPlayManager.dispatchScrollChange(hashCode(), 0, 0)
                    if (dy == 0) {
                        SceneListenAnimaUtil.checkAnimalForInit(mRecyclerView)
                    }
                }
            })
        }
        initAdapterAction()
        initRealTimeFeedListener()
    }

    private fun updateSlogan() {
        var s = ConfigureCenter.getInstance().getString("toc", "slogan", null)
        if (UserInfoMannage.getInstance().isVip) {
            s = ConfigureCenter.getInstance().getString("toc", "vip_slogan", null)
        }
        if (!s.isNullOrBlank()) {
            var arr = s.split("|").toTypedArray()
            LoadingLayout.setRandomLabels(arr)
        }
    }

    override fun getHeaderColor(): Int {
        val headerColorId: Int
        headerColorId = if (UserInfoMannage.isVipUser()) {
            if (mHasPromotionOperationModule) {
                R.color.main_color_ffffff
            } else {
                R.color.main_color_f09068
            }
        } else {
            R.color.main_color_cc8d8d91
        }
        return mContext.resources.getColor(headerColorId)
    }

    private var mHasDelayed = false
    fun addItemsToGuessULike(insertData: List<RecommendItemNew>) {
        Logger.d(TAG, "RecommendFragmentStaggered : ${insertData?.size}")
        if (ToolUtil.isEmptyCollects(insertData)) {
            return
        }
        if (!mHasDelayed && (mRecommendData == null || mRecommendData?.body == null)) {
            mHasDelayed = true
            postOnUiThreadDelayed({ addItemsToGuessULike(insertData) }, 200)
            return
        }
        val body = mRecommendData?.body ?: return
        var foundIndex = -1
        for (i in body.indices) {
            val itemNew = body[i]
            if (itemNew is RecommendItemNew && RecommendItemNew.RECOMMEND_SOURCE_GUESS_YOU_LIKE == itemNew.sourceModuleType) {
                foundIndex = i
                break
            }
        }
        if (foundIndex == -1) {
            return
        }

        mRecommendData?.body?.addAll(foundIndex, insertData)

        if (isRealVisable && mAdapter != null && canUpdateUi()) {
            setRecommendDataForView()
        } else {
            mListDataUpdated = true
        }
    }

    private fun traceOnScroll(scrollUp: Boolean) {
        if (scrollUp) {
            traceDataOnScroll("上")
        } else {
            traceDataOnScroll("下")
        }
    }

    fun smoothScroll2Position(position: Int) {
        mRecyclerView?.refreshableView?.smoothScrollToPositionWithOffset(position, 0)
    }

    fun smoothScroll2Top() {
        mRecyclerView?.refreshableView?.smoothScrollToPositionWithOffset(0,0)
        mRecyclerView?.scrollY = 0
    }

    private fun traceDataOnScroll(direction: String) {
        val scrollHeightDp = BaseUtil.px2dip(mContext, mLastScrollHeight.toFloat())
        if (mLastScrollTraceHeight2 == scrollHeightDp) {
            return
        }
        XMTraceApi.Trace()
            .setMetaId(31187)
            .setServiceId("others")
            .put("currPage", "newHomepage")
            .put("direction", direction)
            .put("style", RecommendStaggeredTraceManager.getStyle())
            .put("topLeftPosition", String.format(Locale.getDefault(), "0,%d", scrollHeightDp))
            .put(
                "lowerRightPosition",
                String.format(
                    Locale.getDefault(),
                    "%d,%d",
                    BaseUtil.getScreenWidthForDp(mContext),
                    scrollHeightDp + mListViewHeightOnScreen
                )
            )
            .createTrace()
        mLastScrollTraceHeight2 = scrollHeightDp
    }

    private fun traceScrollDeep() {
//        val scrollHeightDp = BaseUtil.px2dip(mContext, mLastScrollHeight.toFloat())
//        if (mLastScrollTraceHeight == scrollHeightDp) {
//            return
//        }
//        // 新首页滑动深度  滑动深度
//        XMTraceApi.Trace()
//                .setMetaId(17263)
//                .setServiceId("scrollDepth")
//                .put("currPage", "newHomePage")
//                .put("moduleName", "")
//                .put("dimension", "1")
//                .put("direction", mListViewScrollDirection.toString())
//                .put("topLeftPosition", String.format(Locale.getDefault(), "0,%d", scrollHeightDp))
//                .put("lowerRightPosition", String.format(Locale.getDefault(), "%d,%d", BaseUtil.getScreenWidthForDp(mContext), scrollHeightDp + mListViewHeightOnScreen))
//                .createTrace()
//        mLastScrollTraceHeight = scrollHeightDp
    }

    private fun initAdapterAction() {
        mAdapterAction = object : RecommendFragmentStaggeredAdapter.IDataAction {
            override fun remove(position: Int, needAddData: Boolean) {
                val data = mAdapter.mListData.getOrNull(position)
                if (position + 1 <= mFirstBodyItem) {
                    setFirstBodyItem(mFirstBodyItem - 1)
                }
                // 双列卡片广告关闭之后需要补内容,单独处理
                if (data is ItemModel<*> && data.viewType == RecommendFragmentTypeManager.VIEW_TYPE_AD_MIX_CARD) {
                    removeCardAd(position, data)
                    return
                }
                if (data !is RecommendItemNew) {
                    mAdapter.removeData(position)
                    return
                }
                val bodyPosition = mRecommendData?.body?.indexOf(data) ?: -1
                mRecommendData?.body?.remove(data)
                mRecommendData?.header?.remove(data)
                if (needAddData) {
                    RecommendFragmentRealTimeFeedManager.instance.getReplenish(
                        data,
                        position,
                        bodyPosition
                    )
                } else {
                    mAdapter.removeData(position)
                }
            }

            override fun remove(position: Int, needAddData: Boolean, moduleType: String) {
                if (RecommendModuleItem.RECOMMEND_TYPE_PROMOTION_OPERATION == moduleType) {
                    hidePromotionOperation()
                    remove(position, needAddData)
                }
            }

            override fun refresh(pos: Int) {
                if (canUpdateUi()) {
                    mAdapter?.notifyItemChanged(pos)
                }
            }

            override fun headSize(): Int {
                return mFirstBodyItem
            }

            override fun isPersonalRecOpen(): Boolean {
                return mRecommendData?.isPersonalRecOpen ?: false
            }

            override fun updateAd(position: Int, needRemove: Boolean) {
                val prevItem = mAdapter.getItem(position - 1)
                val nextItem = mAdapter.getItem(position + 1)
                if (prevItem is RecommendItemNew) {
                    prevItem.isNextIsSingle = true
                }
                if (nextItem is RecommendItemNew && needRemove) {
                    nextItem.isPrevIsSingle = true
                }
                mAdapter.updateAd(position, needRemove)
            }

            override fun notifyDateSetChanged() {
                if (canUpdateUi() && mAdapter != null) {
                    mAdapter.notifyDataSetChanged()
                }
            }
        }
    }

    private fun removeCardAd(position: Int, data: ItemModel<*>) {
        var recommendItemNew = data.tag
        if (recommendItemNew !is RecommendItemNew) {
            return
        }
        val bodyPosition = mRecommendData?.body?.indexOf(recommendItemNew) ?: -1
        mRecommendData?.body?.remove(recommendItemNew)
        RecommendFragmentRealTimeFeedManager.instance.getReplenish(recommendItemNew, position, bodyPosition)
    }

    private fun initRealTimeFeedListener() {
        mRealTimeFeedListener =
            IRecommendFeedItemActionListener { itemType, itemContentId, actionType, categoryId, itemData ->
                RecommendFragmentRealTimeFeedManager.instance.getRealTimeFeed(
                    itemContentId,
                    itemType,
                    itemData
                )
            }
    }

    private fun traceNewUserGiftFloatingView() {
        if (!mIsShowNewUserGiftView) {
            return
        }
        // 新首页-挂件  控件曝光
        XMTraceApi.Trace()
            .setMetaId(40335)
            .setServiceId("slipPage")
            .put("itingUrl", mNewUserGiftViewIting)
            .put("currPage", "newHomePage")
            .put("exploreType", mNewUserGiftExploreType.toString())
            .createTrace()
        if (mNewUserGiftExploreType == 1) {
            mNewUserGiftExploreType = 0
        }
    }

    fun showNewUserGiftFloatingView(showNewUserGiftFloatingView: Boolean) {
        if (showNewUserGiftFloatingView) {
            val lastCloseTime = SharedPreferencesUtil.getInstance(mContext)
                .getLong(PreferenceConstantsInMain.KEY_TIME_CLOSE_USER_GIFT_PENDANT)
            if (DateUtils.isSameDay(
                    Date(lastCloseTime),
                    Date(System.currentTimeMillis())
                )
            ) { // 手动关闭后同一天不再展示
                return
            }
            if (mUserGiftPendantModel != null) {
                if (mNewUserGiftFloatingView == null) {
                    if (mContext == null) {
                        return
                    }
                    mNewUserGiftFloatingView = AdsorbView(mContext)
                    mNewUserGiftFloatingView?.setId(R.id.main_home_new_user_gift_floating)
                    mNewUserGiftFloatingView?.setCanAdsorbLeft(false)
                    val rootView = view
                    mNewUserGiftFloatingView?.setLayoutId(R.layout.main_view_recommend_new_user_gift_floating)
                    val layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    layoutParams.gravity = Gravity.BOTTOM or Gravity.END
                    layoutParams.bottomMargin = BaseUtil.dp2px(activity, 150f)
                    mNewUserGiftFloatingView?.setLayoutParams(layoutParams)
                    if (rootView is FrameLayout) {
                        (rootView as ViewGroup).addView(mNewUserGiftFloatingView)
                    }
                    mNewUserGiftFloatingView?.setOnClickListener(View.OnClickListener { view: View? ->
                        if (!TextUtils.isEmpty(mUserGiftPendantModel?.pendantIting)) {
                            ToolUtil.clickUrlAction(
                                this,
                                mUserGiftPendantModel?.pendantIting
                                    ?: "", mNewUserGiftFloatingView
                            )
                        }
                        // 新首页-挂件  点击事件
                        XMTraceApi.Trace()
                            .click(34767)
                            .put("itingUrl", mUserGiftPendantModel?.pendantIting ?: "")
                            .put("currPage", "newHomePage")
                            .createTrace()
                        UserTracking()
                            .setSrcPage("首页_推荐")
                            .setSrcModule("welfarePendant")
                            .setItem("page")
                            .setItemId(mUserGiftPendantModel?.pendantIting ?: "")
                            .setId("7400")
                            .statIting(XDCSCollectUtil.SERVICE_MAIN_PAGE_CLICK)
                    })
                    mNewUserGiftFloatingView?.let { view ->
                        mUserGiftPendantModel?.let { model ->
                            AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, model)
                        }
                    }
                    mIsShowNewUserGiftView = true
                    mNewUserGiftViewIting = mUserGiftPendantModel?.pendantIting ?: ""
                    if (!mHasTraceNewUserGiftAfterData) {
                        traceNewUserGiftFloatingView()
                        mHasTraceNewUserGiftAfterData = true
                    }
                    UserTracking()
                        .setModuleType("welfarePendant")
                        .setSrcPage("首页_推荐")
                        .setId("7399")
                        .statIting(XDCSCollectUtil.SERVICE_DYNAMIC_MODULE)
                }
                val unfoldCollapseView: UnfoldCollapseView? =
                    mNewUserGiftFloatingView?.findViewById(R.id.main_ucv_remain_days)
                unfoldCollapseView?.setOnCloseListener { removeUserGiftPendant() }
                unfoldCollapseView?.setData(mUserGiftPendantModel)
                unfoldCollapseView?.startAnim()
                // 展示新用户挂件则移除广告,以及收听时长金币挂件
                mRecommendAdManager?.removeAllBroadsideAd(IAdConstants.ICategoryId.RECOMMEND_CATEGORY_ID)
                ListenTaskManager.getInstance().hideListenTaskView()
            }
        } else {
            removeUserGiftPendant()
        }
    }

    private fun removeUserGiftPendant() {
        if (mNewUserGiftFloatingView != null) {
            val parent: ViewParent? = mNewUserGiftFloatingView?.getParent()
            if (parent is ViewGroup) {
                parent.removeView(mNewUserGiftFloatingView)
            }
            mNewUserGiftFloatingView = null
        }
        mIsShowNewUserGiftView = false
    }
    private fun setTextDrawable(tvToast: TextView?, drawable: Drawable?) {
        if (drawable == null) {
            tvToast?.setCompoundDrawables(null, null, null, null)
            return
        }
        drawable?.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
        tvToast?.setCompoundDrawables(drawable, null, null, null)
    }
    private fun showRefreshingToast(state: LoadCompleteType) {
        mLoadingState = state
        mFlToast?.visibility = View.INVISIBLE // 先隐藏，避免一直显示的问题
        mLlBottomToast?.visibility = View.INVISIBLE
        if (UserInfoMannage.isVipUser()) {
            mFlToast?.setBackgroundResource(R.color.main_color_fff2ed)
            mTvToast?.setTextColor(ContextCompat.getColor(mActivity, R.color.main_color_f09068))
        } else {
            mFlToast?.setBackgroundColor(ContextCompat.getColor(mActivity, R.color.main_color_ffece8))
            mTvToast?.setTextColor(ContextCompat.getColor(mActivity, R.color.main_color_f86442))
        }
        setTextDrawable(mTvToast, null)
        when (mLoadingState) {
            LoadCompleteType.LOADING -> if (isFirstVisibleItemInBody()) {
                mTvToast?.setText(R.string.main_refreshing_now)
                var drawable = mContext.resources.getDrawable(R.drawable.main_ic_refresh)
                if (UserInfoMannage.isVipUser()) {
                    drawable.setTint(ContextCompat.getColor(mActivity, R.color.main_color_f09068))
                } else {
                    drawable.setTint(ContextCompat.getColor(mActivity, R.color.main_color_f86442))
                }
                setTextDrawable(mTvToast, drawable)
                mFlToast?.visibility = View.VISIBLE
            }
            LoadCompleteType.NETWOEKERROR -> if (isFirstVisibleItemInBody()) {
                mTvToast?.setText(R.string.main_refresh_failed_retry_later_please)
                setTextDrawable(mTvToast, null)
                mFlToast?.visibility = View.VISIBLE
                postOnUiThreadDelayed({
                    // 因为延迟处理，状态可以已改变，这时再继续之前的操作，可以能会导致错乱
                    if (mLoadingState == LoadCompleteType.NETWOEKERROR) {
                        mFlToast?.visibility = View.INVISIBLE
                    }
                }, 1000)
            }
            LoadCompleteType.NOCONTENT -> if (isFirstVisibleItemInBody()) {
                mTvToast?.setText(R.string.main_no_more_content_now)
                setTextDrawable(mTvToast, null)
                mFlToast?.visibility = View.VISIBLE
                postOnUiThreadDelayed({
                    // 因为延迟处理，状态可以已改变，这时再继续之前的操作，可以能会导致错乱
                    if (mLoadingState == LoadCompleteType.NOCONTENT) {
                        mFlToast?.visibility = View.INVISIBLE
                    }
                }, 1000)
            }
            LoadCompleteType.OK -> if (mRefreshAddedItemNum > 0) {
                var content =
                    getString(R.string.main_refresh_added_content_format, mRefreshAddedItemNum)
                var drawable: Drawable? = null
                if (UserInfoMannage.isVipUser()) {
                    content =
                        getString(R.string.main_refresh_added_content_format_vip, mRefreshAddedItemNum)
                    drawable = mContext.resources.getDrawable(R.drawable.main_icon_vip_recommend)
                }
                setTextDrawable(mTvToast, drawable)
                mTvToast?.text = content
                mFlToast?.visibility = View.VISIBLE

                postOnUiThreadDelayed({
                    if (mLoadingState != LoadCompleteType.OK) {
                        return@postOnUiThreadDelayed
                    }

                    val animation = AlphaAnimation(1f, 0f)
                    animation.duration = 300
                    animation.setAnimationListener(object : Animation.AnimationListener {
                        override fun onAnimationStart(animation: Animation?) {
                        }

                        override fun onAnimationEnd(animation: Animation?) {
                            // 因为延迟处理，状态可以已改变，这时再继续之前的操作，可以能会导致错乱
                            if (mLoadingState == LoadCompleteType.OK) {
                                mFlToast?.visibility = View.INVISIBLE
                            }
                        }

                        override fun onAnimationRepeat(animation: Animation?) {
                        }
                    })
                    mFlToast?.startAnimation(animation)
                }, 1100)

                RecommendStaggeredTraceManager.traceOnRefreshToastShow(content, "顶部")

                mRefreshAddedItemNum = -1
            }
            else -> {
            }
        }
    }

    override fun loadData() {
        mNetManager.logRecommendLoad("loadData")
        HomeRecommendPageLoadingOptimizationManager.onHomeRecommendPageLoading(true)
        PerformanceMonitor.traceBegin("loadData")
        mFeedAdManager?.updateScreenId()
        mCenterBigFeedAdManager?.updateScreenId()
        var isNeedPreloadLocal = HomeRecommendPageLoadingOptimizationManager.isNeedPreLoadFromLocal()
        var localDataHeaderSize: Int = mNetManager.mPreLoadLocalRecommendData?.header?.size ?: 0
        var localDataBodySize: Int = mNetManager.mPreLoadLocalRecommendData?.body?.size ?: 0
        if (isNeedPreloadLocal && mNetManager.mPreLoadLocalRecommendData != null
            && (localDataHeaderSize + localDataBodySize >= 5)) {
            // 没有有效的网络预加载数据返回，直接拿本地数据展示
            setDataFromLocal(mNetManager.mPreLoadLocalRecommendData!!)
            setIsLoadLocalData(true)
            onPageLoadingCompleted(LoadCompleteType.OK)
            if (!HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                setRecommendDataForView()
            }
            if (view != null) {
                mTraceHelper.postPageEndNodeAfterRenderComplete(view) {
                    onPageRenderComplete(false)
                    RecommendPreLoadOptManager.onRecommendDataDraw(mActivity as? MainActivity, true)
                }
            }
//            traceItemViewed()
        } else {
            // 显示默认图
            if (!HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                setRecommendDataForView()
            }
        }
        HomeRecommendPageLoadingOptimizationManager.log("__loadData_loadDataFromNet")
        mNetManager.loadDataFromNet(isFirstRequestAd = true)
    }

    fun onPageRenderComplete(fromNet: Boolean) {
        if (!HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent() || !HomeRecommendPageLoadingOptimizationManager.mIsHasAdShowed) {
            tracePageRenderComplete(fromNet)
            Logger.i(TAG, "onPageRenderComplete 1 $fromNet")
        } else {
            HomeRecommendPageLoadingOptimizationManager.addRecommendFragmentVisibleListeners(
                object : HomeRecommendPageLoadingOptimizationManager.IRecommendFragmentVisibleListener {
                override fun onRecommendFragmentVisible() {
                    tracePageRenderComplete(fromNet)
                    Logger.i(TAG, "onPageRenderComplete 2 $fromNet")
                }
            })
        }
    }

    fun tracePageRenderComplete(fromNet: Boolean) {
        if (mIsRenderComplete) {
            return
        }
        mIsRenderComplete = true
        // 首页数据绘制完成  其他事件
        XMTraceApi.Trace()
            .setMetaId(63935)
            .setServiceId("others")
            .put("description", fromNet.toString()) // 记录是否网络数据
            .createTrace()
        Logger.i(TAG, "tracePageRenderComplete $fromNet")
    }

    fun notifyReset(resetForChildMode: Boolean) {
        mResetForChildMode = resetForChildMode
        reset()
    }

    override fun onFinishCallback(cls: Class<*>?, fid: Int, params: Array<Any?>?) {
        if (cls != null && cls.name == mRnClassName) {
            mNetManager.loadDataFromNet()
        }
        checkToRefreshData()
    }

    override fun onPageLoadingCompleted(loadCompleteType: LoadCompleteType) {
        // 对于NOCONTENT或者NETWOEKERROR，如果已有数据，不调用父类方法
        val callSuperMethod =
            !(loadCompleteType == LoadCompleteType.NOCONTENT || loadCompleteType == LoadCompleteType.NETWOEKERROR)
                    || (ToolUtil.isEmptyCollects(getHeaderData()) && ToolUtil.isEmptyCollects(
                getBodyData()
            ))
        if (callSuperMethod) {
            if (!(loadCompleteType == LoadCompleteType.NOCONTENT
                        || loadCompleteType == LoadCompleteType.NETWOEKERROR)) {
                super.onPageLoadingCompleted(loadCompleteType)
            }
        }
        showRefreshingToast(loadCompleteType)
    }

    public fun getHeaderData(): List<RecommendItemNew?>? {
        return mNetManager.mRecommendData?.header
    }

    // body部分数据，每个子tab不同，根据子tab来取
    private fun getBodyData(): List<RecommendItemNew?>? {
        return mNetManager.mRecommendData?.body
    }

    override fun getHomePageHeaderBgColor(): Int {
        if (BaseBannerView.sIsHasNewZoneStyleBanner) {
            return Color.WHITE
        }
        if (mHasPromotionOperationModule) {
            return mPromotionOperationColor
        }
        return INVALID_COLOR
    }

    override fun useColorInDarkMode(): Boolean {
        return mPromotionOperationColor != INVALID_COLOR
    }

    override fun getHomePageHeaderForegroundColorTheme(): String? {
        if (BaseBannerView.sIsHasNewZoneStyleBanner) {
            return HomePageTabTheme.FOREGROUND_COLOR_BLACK
        }
        return if (mHasPromotionOperationModule && !mHomePageTabTheme?.searchBoxColor.isNullOrEmpty()) mHomePageTabTheme?.searchBoxColor else null
    }

    override fun getHomePageHeaderBgType(): HeaderBgType? {
        if (BaseBannerView.sIsHasNewZoneStyleBanner) {
            return HeaderBgType.NOT_SHOW_BOTTOM_PART
        }
        return if (mHasPromotionOperationModule) HeaderBgType.SHOW_BOTTOM_PART else HeaderBgType.NOT_INSPECTED
    }

    override fun getHomePageShadowType(): HeaderShadowType? {
        if (BaseBannerView.sIsHasNewZoneStyleBanner) {
            return HeaderShadowType.NOT_SHOW_SHADOW
        }
        return if (mHasPromotionOperationModule) HeaderShadowType.NOT_SHOW_SHADOW else HeaderShadowType.NOT_INSPECTED
    }

    private fun abandonTrace() {
        mTraceHelper.abandon()
        HomeRecommendPageLoadingOptimizationManager.onHomeRecommendPageLoading(false)
        cancelCheckIsRealVisibleToAbandonTraceTask()
    }

    // 上报当前展示的bannerView正在展示的那一页广告
    fun scrollCheckBannerRecord() {
        val refreshableView = mRecyclerView?.getRefreshableView() ?: return
        if (refreshableView.layoutManager !is StaggeredGridLayoutManager) {
            return
        }
        var mBannerView: BaseBannerView? = null
        var childAt: View
        for (i in 0 until refreshableView.getChildCount()) {
            childAt = refreshableView.getChildAt(i)
            if (childAt is BaseBannerView) {
                mBannerView = childAt
            } else if (childAt is ViewGroup && childAt.childCount > 0) {
                if (childAt.getChildAt(0) is BaseBannerView) {
                    mBannerView = childAt.getChildAt(0) as BaseBannerView
                }
            }
            if (mBannerView != null) {
                val isShow = AdManager.checkViewIsVisOverHalfOnListView(
                    mBannerView, refreshableView,
                    refreshableView.layoutManager as StaggeredGridLayoutManager
                )
                mBannerView.setCurrVisState(isShow)
            }
        }
    }

    // 上报当前展示的bannerView正在展示的那一页广告
    fun recordBannerAd() {
        val refreshableView = mRecyclerView?.getRefreshableView() ?: return
        if (refreshableView.layoutManager !is StaggeredGridLayoutManager) {
            return
        }
        for (i in 0 until refreshableView.getChildCount()) {
            var childAt: View
            var mBannerView: BaseBannerView? = null
            childAt = refreshableView.getChildAt(i)
            if (childAt is BaseBannerView) {
                mBannerView = childAt
            } else if (childAt is ViewGroup && childAt.childCount > 0) {
                if (childAt.getChildAt(0) is BaseBannerView) {
                    mBannerView = childAt.getChildAt(0) as BaseBannerView
                }
            }
            if (mBannerView != null) {
                postOnUiThreadDelayed({
                    if (AdManager.checkViewIsVisOverHalfOnListView(
                            mBannerView, refreshableView,
                            refreshableView.layoutManager as StaggeredGridLayoutManager
                        )
                    ) {
                        mBannerView.setCurrentPageStatueOnlyValue(true)
                        mBannerView.adRecordCurrPage()
                        mBannerView.startAutoSwapFocusImage()
                        if (mFocusAdapterProvider != null) {
                            mFocusAdapterProvider!!.startAutoSwapFocusImage()
                        }
                    }
                }, 300)
            }
        }
    }

    fun loadHomeAd(firstRequestAd: Boolean) {
        mRecommendAdManager?.loadHomeAd(firstRequestAd, mFeedAdManager?.screenId)
    }

    fun createFocueAdapter(): RecommendFocusAdapterProviderStaggered? {
        return mRecommendAdManager?.createFocusAdapter(this, mMixScrollView, mAdapterAction)
    }

    fun hasBanner(): Boolean {
        return !ToolUtil.isEmptyCollects(mFocusImages)
    }

    fun hasPromotionOperationModule(): Boolean {
        return mHasPromotionOperationModule
    }

    fun changeBottomOvalViewHeightForPromotion(isPromotionShow: Boolean) {
        if (!ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_PROMOTION_CHANGE_HOME_BOTTOM, true)) {
            return
        }
        if (parentFragment is HomePageFragment) {
            (parentFragment as HomePageFragment?)?.changeBottomOvalViewHeightForPromotion(isPromotionShow)
        }
    }

    private val mMixScrollView = BaseBannerView.IScrollableView { touchListener ->
            if (mRecyclerView?.getRefreshableView() != null) {
                mRecyclerView?.getRefreshableView()?.setOnTouchListener(touchListener)
            } else {
                mRecyclerView?.setOnTouchListener(touchListener)
            }
        }

    private fun loadRecommendAd(isNeedRecordAdRefresh: Boolean) {
        mFeedAdManager?.loadRecommendAd(isNeedRecordAdRefresh, 28)
        mFeedAnchorAdManager?.loadRecommendAnchorAd(isNeedRecordAdRefresh)
        mCenterBigPicAdManager?.loadExpressFeedAd(isNeedRecordAdRefresh)
        mCenterBigFeedAdManager?.loadRecommendAd(isNeedRecordAdRefresh, 293)
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.d(TAG, "onDestroy")
        HomeRecommendPageLoadingOptimizationManager.onRecommendPageDestroy()
        mNetManager.mFragment = null
        RecommendFragmentTypeManager.mFragment = null
        RecommendFragmentRealTimeFeedManager.instance.mFragment = null
        mLayoutManager?.mRecyclerView = null
        XmPlayerManager.getInstance(mContext).removePlayerStatusListener(mPlayStatusListener)
        ToListenManager.removeListChangeListener(mToListenListener)
//        RecommendPageVirtualViewManager.release()
//        RecommendPageGaiaXManager.release()
        EventManager.getInstance().removeAction("ShowInterestTagGuide")
        FreeListenCountDownManager.release();
        LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mLocalBroadcastReceiver)
        SceneListenAnimaUtil.onDestroy()
        SatisfactionHomeManager.release()
        SatisfactionHomeModuleManager.release()
    }

    private val mRecommendFeedAdProvider: IRecommendFeedAdProviderNew =
        object : IRecommendFeedAdProviderNew {
            override fun getAdApter(slotId: Int): AbRecyclerViewAdapter<RecyclerView.ViewHolder> {
                return mAdapter
            }

            override fun setRecommendAdData(slotId: Int) {
                // 广告加载完成时回调，以便将广告数据插入adapter中
                if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                    return
                }
                mNetManager.setRecommendDataForView()
            }
        }

    private val mBigScreenAdLoadListener: BigScreenAdManager.IBigScreenAdLoadListener =
            BigScreenAdManager.IBigScreenAdLoadListener { // 巨幕广告加载完成时回调，以便在无焦点图模块时插入焦点图模块
                if (mFocusImages.isEmpty()) {
                    mNetManager.setRecommendDataForView()
                }
            }

    private val mToListenListener = object : ToListenManager.IToListenChangeListener {
        override fun isDeleted(deletedId: Long) {
            mAdapter?.onToListenDeleted(deletedId)
        }

        override fun isAdded(addedTrack: SimpleTrackForToListen, index: Int) {
            mAdapter?.onToListenAdded(addedTrack, index)
        }
    }

    private val mPlayStatusListener = object : IXmPlayerStatusListener {
        override fun onPlayStart() {
            if (!PlayerManager.getInstanse().isPlayFragmentVisable) {
                Logger.w("ignore_refresh_p", "notify_onPlayStart_staged")
            } else {
                Logger.d("ignore_refresh_p", "ignore_onPlayStart_staged")
                return
            }
            if (canUpdateUi()) {
                mAdapter?.notifyDataSetChanged()
                mAdapter?.onPlayStart()
            }
        }

        override fun onPlayPause() {
            if (!PlayerManager.getInstanse().isPlayFragmentVisable) {
                Logger.w("ignore_refresh_p", "notify_onPlayPause_staged")
            } else {
                Logger.d("ignore_refresh_p", "ignore_onPlayPause_staged")
                return
            }
            if (canUpdateUi()) {
                mAdapter?.notifyDataSetChanged()
                mAdapter?.onPlayPause()
            }
        }

        override fun onPlayStop() {
        }

        override fun onSoundPlayComplete() {
            mAdapter?.onSoundPlayComplete()
        }

        override fun onSoundPrepared() {
        }

        override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
            if (!PlayerManager.getInstanse().isPlayFragmentVisable) {
                Logger.w("ignore_refresh_p", "notify_onSoundSwitch_staged")
            } else {
                Logger.d("ignore_refresh_p", "ignore_onSoundSwitch_staged")
                return
            }
            mAdapter?.onSoundSwitch(lastModel, curModel)
            FeedTrackPlayUtil.checkPlayStatus(lastModel, curModel, mRecyclerView, mAdapter)
        }

        override fun onBufferingStart() {
        }

        override fun onBufferingStop() {
        }

        override fun onBufferProgress(percent: Int) {
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            if (!PlayerManager.getInstanse().isPlayFragmentVisable) {
                mAdapter?.onPlayProgress(currPos, duration)
                ShowTagPlayUtil.onPlayProgress(
                    currPos,
                    duration,
                    mRecyclerView,
                    mAdapter
                )
                Logger.w("ignore_refresh_p", "notify_onPlayProgress_staged")
            } else {
                Logger.d("ignore_refresh_p", "ignore_onPlayProgress_staged")
            }
        }

        override fun onError(exception: XmPlayerException?) = false
    }

    private val mSkinSettingChangeListener: SkinSettingChangeWrapListener =
        object : SkinSettingChangeWrapListener() {
            override fun onAtmosphereInfoChanged() {
                if (isRealVisable) {
                    onRecommendFragmentVisibleChange(true)
                }
            }
        }

    private val mLocalBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (AdUnLockSnackBarManager.isNeedConflictTouchAd() && intent != null && AdUnLockSnackBarManager.REMOVE_TOUCH_AD_INTENT_ACTION == intent.action) {
                mRecommendAdManager?.removeAllBroadsideAd(IAdConstants.ICategoryId.RECOMMEND_CATEGORY_ID)
            } else if (intent != null && ActionConstants.ACTION_BOUGHT_VIP_SUCCESS == intent.action) {
                if (canUpdateUi()) {
                    onRefresh()
                }
                val vipType = intent.getStringExtra("vipType")
                Logger.logToFile("RecommendFragment receive vip broadcast 1 vipType =" + vipType)
                HandlerManager.postOnUIThreadDelay(
                        {
                            Logger.logToFile("RecommendFragment receive vip broadcast 2 isFreeListenV2Ope =" + FreeListenConfigManager.isFreeListenV2Open() + " listenTime = " + FreeListenTimeManager.getListenTime(getContext()))
                            if (FreeListenConfigManager.isFreeListenV2Open() && FreeListenTimeManager.getListenTime(getContext()) != 0 && StringUtil.isEmpty(vipType)) {
                                // 开通大会员,需要清空剩余畅听时长，延迟是因为PlayFragmentNew的开会员广播监听中也会清除时长，避免重复清除
                                FreeListenTimeManager.clearAllListenTime(6, 0, null)
                            }

                            val enable = EasyConfigure.getBoolean("buy_vip_update_replace_config_home", false);
                            Logger.logToFile("RecommendFragment receive vip broadcast 3 enable update replaceConfig =" + enable)
                            if (enable && FreeListenConfigManager.isFreeListenV2Open()) {
                                AdMakeVipLocalManager.getInstance().requestIsTargetUser()
                            }
                        }, 1000)
            }
        }
    }

    override fun onScreenWidthChanged(config: Configuration) {
        mRecyclerView?.apply {
            val firstVisiblePosition = findFirstVisiblePosition()
            val lastVisiblePosition = findLastVisiblePosition()
            val visibleItemCount = lastVisiblePosition - firstVisiblePosition + 1
            for (i in 0..visibleItemCount) {
                mAdapter.onConfigurationChanged(i + firstVisiblePosition, refreshableView.getChildAt(i))
            }
            mAdapter.notifyDataSetChanged()
        }
    }

    override fun onAdClick() {
        HomeRecommendPageLoadingOptimizationManager.removeAdShowAndDestroyListeners(this)
    }

    override fun onAdDestroy() {
        HomeRecommendPageLoadingOptimizationManager.removeAdShowAndDestroyListeners(this)
    }
}