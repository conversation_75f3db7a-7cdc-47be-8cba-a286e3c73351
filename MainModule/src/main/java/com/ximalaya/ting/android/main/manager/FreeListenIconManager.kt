package com.ximalaya.ting.android.main.manager

import android.content.Context
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManagerCompat
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.view.bar.FreeRoundProgressBar
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.dialog.ListenTimeDialogManager
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.service.IFreeListenTimeListener
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import java.text.SimpleDateFormat
import java.util.Date

/**
 * Created by dekai.liu on 2021/9/7.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
class FreeListenIconManager(vgSearchBarRecommendPageAction: ViewGroup, var context: Context) :
    IFreeListenTimeListener, View.OnClickListener, FreeListenConfigManagerCompat.IFreeListenConfigCallback,
    XmPlayerManager.IConnectListener {
    var cslFreeListen: ConstraintLayout
    var rlSignVg: RelativeLayout
    var rpbLeftTime: FreeRoundProgressBar
    var ivFreeListenIcon: ImageView
    var iconColor: Int = -1
    var addConfigListener: Boolean = false
    val xmRequestId: String = XmRequestIdManager.getInstance(context).requestId
    var isPageVisible = false

    init {
        cslFreeListen = vgSearchBarRecommendPageAction.findViewById(R.id.main_csl_search_bar_free_listen)
        ivFreeListenIcon = vgSearchBarRecommendPageAction.findViewById(R.id.main_iv_search_bar_free_listen)
        rlSignVg = vgSearchBarRecommendPageAction.findViewById(R.id.main_rl_search_bar_sign)
        rpbLeftTime = vgSearchBarRecommendPageAction.findViewById(R.id.main_round_progressbar)

        updateSignShowState()

        if (FreeListenConfigManagerCompat.getInstance().isShowFreeListenTag) {
            cslFreeListen.visibility = View.VISIBLE
            cslFreeListen.setOnClickListener(this)

            if (!XmPlayerManager.getInstance(ToolUtil.getCtx()).isConnected) {
                XmPlayerManager.getInstance(ToolUtil.getCtx()).addOnConnectedListerner(this)
            }
            setProgress(getProgress())
        } else {
            cslFreeListen.visibility = View.GONE
        }

        FreeListenConfigManagerCompat.getInstance().addFreeListenConfigCallback(this)
    }

    private fun updateSignShowState() {
        if (FreeListenConfigManagerCompat.getInstance().isSignShowInOut) {
            rlSignVg.visibility = View.VISIBLE
        }  else {
            rlSignVg.visibility = View.GONE
        }
    }

    fun getProgress(): Float {
        var leftTime = FreeListenTimeManager.getListenTime(ToolUtil.getCtx())
        Logger.d("cf_test", "getListenTime____: $leftTime")
        return leftTime * 100.0f / (FreeListenConfigManagerCompat.getInstance().homePageFreeTimeM * 60)
    }

    fun isOutSignShow(): Boolean {
        return rlSignVg.visibility == View.VISIBLE
    }
    fun onPageResume() {
        isPageVisible = true
        if (FreeListenConfigManagerCompat.getInstance().isShowFreeListenTag) {
            cslFreeListen.visibility = View.VISIBLE
            cslFreeListen.setOnClickListener(this)
        } else {
            cslFreeListen.visibility = View.GONE
        }
        if (!FreeListenConfigManagerCompat.getInstance().isShowFreeListenTag) {
            return
        }
        // 新首页-顶部-免费 icon  控件曝光
        XMTraceApi.Trace()
            .setMetaId(63193)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newHomePage")
            .put("xmRequestId", xmRequestId)
            .put("contentId", "11")
            .put("contentType", "freeListen")
            .createTrace()
        setProgress(getProgress())
        FreeListenTimeManager.addListenTimeListener(ToolUtil.getCtx(), this)
        FreeListenConfigManagerCompat.getInstance().addFreeListenConfigCallback(this)
        addConfigListener = true
    }
    fun onPagePause() {
        isPageVisible = false
        if (!addConfigListener) {
            return
        }
        FreeListenTimeManager.removeListenTimeListener(ToolUtil.getCtx(), this)
        FreeListenConfigManagerCompat.getInstance().removeFreeListenConfigCallback(this)
        addConfigListener = false
    }

    fun onPageDestroy() {
        FreeListenTimeManager.removeListenTimeListener(ToolUtil.getCtx(), this)
        FreeListenConfigManagerCompat.getInstance().removeFreeListenConfigCallback(this)
    }

    fun hideFreeListen() {
        cslFreeListen.visibility = View.GONE
    }

    private fun setProgress(progress: Float) {
        rpbLeftTime.progress = progress.toInt()
        Logger.d("cf_test", "setProgress_____: $progress")
        if (cslFreeListen.visibility != View.VISIBLE) {
            return
        }
        if (progress == 0.0f) {
            ivFreeListenIcon.drawable.colorFilter = PorterDuffColorFilter(iconColor, PorterDuff.Mode.SRC_IN)
            rpbLeftTime.cricleColor = iconColor
        } else {
            ivFreeListenIcon.drawable.colorFilter = PorterDuffColorFilter(ToolUtil.getCtx().resources.getColor(R.color.host_color_1cc5a9), PorterDuff.Mode.SRC_IN)
            rpbLeftTime.cricleColor = ToolUtil.getCtx().resources.getColor(R.color.host_color_4d333333_4dcfcfcf)
        }
    }

    override fun onListenTimeChange(remainTime: Int, byAddTime: Boolean, addedTime: Int) {
        if (cslFreeListen.visibility != View.VISIBLE) {
            return
        }
        var progress = remainTime * 100.0f / (FreeListenConfigManagerCompat.getInstance().homePageFreeTimeM * 60)
        Logger.d("cf_test", "onListenTimeChange____: $progress")
        setProgress(progress)
    }

    fun setFreeListenIconColor(color: Int) {
        iconColor = color
        if (cslFreeListen.visibility != View.VISIBLE) {
            return
        }
        setProgress(getProgress())
    }

    override fun onListenTimeEmpty() {
    }

    override fun onEnterAllDayFreeListen() {
    }

    override fun onExitAllDayFreeListen() {
    }

    override fun onClick(v: View?) {
        // 新首页-顶部-免费 icon  点击事件
        XMTraceApi.Trace()
            .click(63192) // 用户点击时上报
            .put("currPage", "newHomePage")
            .put("xmRequestId", xmRequestId)
            .createTrace()
        doIconClick()
    }

    override fun onConfigChange() {
        try {
            if (FreeListenConfigManagerCompat.getInstance().isShowFreeListenTag) {
                cslFreeListen.visibility = View.VISIBLE
                cslFreeListen.setOnClickListener(this)
            } else {
                cslFreeListen.visibility = View.GONE
            }

            updateSignShowState()
        } catch (e: Exception) {
        }
    }

    override fun onConnected() {
        XmPlayerManager.getInstance(ToolUtil.getCtx()).removeOnConnectedListerner(this)
        HandlerManager.postOnUIThreadDelay(Runnable {
            setProgress(getProgress())
        }, 1000)
    }
    private fun doIconClick() {
        if (FreeListenConfigManager.getInstance().listenConfig != null && !TextUtils.isEmpty(FreeListenConfigManager.getInstance().listenConfig.offLineTip)) {
            CustomToast.showFailToast(FreeListenConfigManager.getInstance().listenConfig.offLineTip)
            return
        }
        if (FreeListenConfigManager.getInstance().isNeedDirectToVideo) {
            // 直接看激励视频
            AdUnLockTimeManagerV2.getInstance().unlockTrack(FreeListenConfigManager.getInstance().currentRewardTime, "homeFreeIcon", object : AdUnLockTimeManagerV2.IAdUnLockStatusCallBack {
                override fun onRewardSuccess(
                    currentAd: AdUnLockVipTrackAdvertis?,
                    rewardTime: Int
                ) {
                    FreeListenConfigManager.getInstance().increaseTimeIndex() // 进度条+1

                    var listenConfig: FreeListenConfigManager.ListenConfig? = FreeListenConfigManager.getInstance().listenConfig
                    if (isPageVisible && listenConfig != null && (FreeListenTimeManager.getListenTime(context) < listenConfig.playDurationLimit) && RewardAgainAdManager.isNeedRewardAgain()) {
                        // 支持再看一个
                        if (RewardAgainAdManager.getNextRewardType() == RewardAgainAdManager.NEXT_REWARD_INSPIRE && RewardAgainAdManager.getCurrentInspireAd() != null) {
                            // 展示唤端弹窗
                            RewardAgainAdManager.showInspireDialog(rewardTime * 60, RewardAgainAdManager.getNextRewardTime(), RewardAgainAdManager.getCurrentInspireAd(), "homeFreeIcon", true)
                        } else {
                            // 展示激励视频弹窗
                            RewardAgainAdManager.showWatchVideoDialog(rewardTime * 60, RewardAgainAdManager.getNextRewardTime(), "homeFreeIcon")
                        }
                    } else {
                        if (RewardAgainAdManager.isNeedRewardAgain()) {
                            RewardAgainAdManager.resetInspireAd()
                        }
                        CustomToast.showFailToast("恭喜你获得" + rewardTime + "分钟收听时长", Toast.LENGTH_LONG.toLong())
                        ListenTimeDialogManager.showListenTimeFragment("homeFreeIcon2")
                    }
                }

                override fun onRewardFail(code: Int) {
                    ListenTimeDialogManager.showListenTimeFragment("homeFreeIcon2")
                }
            })
        } else {
            // 打开二级页面
            ListenTimeDialogManager.showListenTimeFragment("homeFreeIcon2")
        }
    }
}