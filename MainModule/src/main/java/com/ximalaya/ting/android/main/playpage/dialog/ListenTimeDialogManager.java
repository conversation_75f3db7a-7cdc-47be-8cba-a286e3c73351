package com.ximalaya.ting.android.main.playpage.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.adsdk.external.feedad.IAdModel;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdFreeListenProtocolDialog;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.reactnative.IRNFunctionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.RNActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.play.AddListenTimeBuilder;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManagerCompat;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.model.freelisten.FreeListenAlbumData;
import com.ximalaya.ting.android.host.model.freelisten.PopupDurationInfo;
import com.ximalaya.ting.android.host.model.payment.UniversalPayment;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.fragment.FreeListenTimeFragment;
import com.ximalaya.ting.android.main.fragment.RewardTabFragment;
import com.ximalaya.ting.android.main.playpage.fragment.PlayFragmentNew;
import com.ximalaya.ting.android.main.playpage.playx.XUtils;
import com.ximalaya.ting.android.main.playpage.playy.YUtils;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.UnsupportedEncodingException;
import java.lang.ref.WeakReference;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class ListenTimeDialogManager {
    private static boolean isHandlingShowDialog;
    private static long lastStartFreezeTime;

    private static CountDownTimer jumpTimer; // 跳转计时器
    private static boolean isJumping; // 点击后发生跳转
    private static boolean canGetReward;

    private  static  int mFirstRewardTime;
    private  static  int mSecondRewardTime;
    private  static String mSourceName;
    private  static  WeakReference<BaseFragment2> mFrgReference;
    private  static  BaseRewardAgainDialog.IRewardClickCallBack mRewardClickCallBack;
    private  static  IAdModel mAdModel;
    private static PopupDurationInfo mPopupDurationInfo;
    private  static  boolean needPullUpDialog;

    public static void showRewardDialogNew(String sourceName, Track track, BaseFragment2 baseFragment2, int layerType) {
        showRewardDialogNew(sourceName, track, baseFragment2, layerType, null);
    }

    public static void showRewardDialogNew(String sourceName, Track track, BaseFragment2 baseFragment2, int layerType, String watchAdSource) {
        showRewardDialogNew(sourceName, track, baseFragment2, null,  true, 0, 0, watchAdSource, layerType);
    }

    /**
     * 判断是否是新畅听业务入口拉起领取畅听时长弹窗
     * @param layerType
     * @return
     */
    private static boolean isNewAdFreeListenLayerType(int layerType) {
        return layerType == 6 || layerType == 7 || layerType == 8 || layerType == 9 || layerType == 10;
    }

    /**
     *
     * @param sourceName
     * @param track
     * @param baseFragment2
     * @param callBack
     * @param isNeedDoAnimation 是否需要做出现动效
     * @param trackId
     * @param albumId
     * @param watchAdSource
     * @param layerType 1:点击播放页气泡 2:时长用完自动拉起 3:时长用完手动拉起，4.二次曝光 5.全天权益到期重新请求接口 6.自制页新畅听中插条入口 7.首页新畅听弹窗拉起 8.播放页新畅听弹窗拉起 9.播放页新畅听tip条拉起
     *                  10.听书任务返回播放页自动拉起弹窗 11.站外承接h5点击进入播放页自动拉起弹窗
     */
    public static void showRewardDialogNew(String sourceName, Track track, BaseFragment2 baseFragment2, FreeListenDialogNew.IRewardCallBack callBack,
                                           boolean isNeedDoAnimation, long trackId, long albumId, String watchAdSource, int layerType) {
        if (baseFragment2 == null || !baseFragment2.canUpdateUi() || UserInfoMannage.isVipUser()) {
            return;
        }
        if (baseFragment2 instanceof PlayFragmentNew) {
            //播放页
            if (XUtils.isLandScape(baseFragment2.getContext()) || YUtils.isLandScape(baseFragment2.getContext())) {
                return;
            }
        }
        if (FreeListenConfigManagerCompat.getInstance().isShowVIPBuyDialog(track) && !isNewAdFreeListenLayerType(layerType)) {
            // 新畅听人群 & 非新畅听业务拉起领取时长弹窗入口，出会员购买弹窗
            FreeListenConfigManagerCompat.getInstance().showVIPBuyDialog(baseFragment2, UniversalPayment.SOURCE_AFTER_SAMPLE, track, albumId, trackId);
            return;
        }

        if (FreeListenDialogNew.isCurrentDialogShowing() || FreeRewardDialog.isCurrentDialogShowing()) {
            return;
        }
        if (isHandlingShowDialog) {
            return;
        }
        isHandlingShowDialog = true;
        Map<String, String> params = new HashMap<>();
        params.put("sceneId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, ""));
        params.put("groupId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, ""));
        String freeListenExt = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXT, "");
        if (!TextUtils.isEmpty(freeListenExt)) {
            try {
                params.put("ext", URLEncoder.encode(freeListenExt, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        if (!TextUtils.isEmpty(watchAdSource)) {
            params.put("playPageExt", watchAdSource);
        }
        params.put("layerType", layerType + "");
        if (trackId != 0 && albumId != 0) {
            params.put("trackId", trackId + "");
            params.put("albumId", albumId + "");
        } else if (track != null){
            params.put("trackId", track.getDataId() + "");
            if (track.getAlbum() != null) {
                params.put("albumId", track.getAlbum().getAlbumId() + "");
            }
        }
        if (track != null) {
            params.put("isPaid", track.isPaid() + "");
        } else {
            params.put("isPaid", "true");
        }
        params.put("localDuration", FreeListenTimeManager.getListenTime(ToolUtil.getCtx()) + "");
        CommonRequestM.getFreeListenDialogInfo(params, new IDataCallBack<PopupDurationInfo>() {
            @Override
            public void onSuccess(@Nullable PopupDurationInfo data) {
                if (data != null) {
                    FreeListenConfigManager.getInstance().initTimeArray(data.getRewardInfos());
                    if (data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_AUTO_REWARD && data.getCountdown() == 0) {
                        // 增加兜底
                        data.setDurationLayerType(PopupDurationInfo.DIALOG_TYPE_NORMAL);
                    }
                    if (data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_NORMAL || data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_AUTO_REWARD) {
                        doShowFreeListenDialog(sourceName, track, callBack, baseFragment2, isNeedDoAnimation, trackId, albumId, watchAdSource, layerType, data);
                        isHandlingShowDialog  = false;
                    } else {
                        doShowFreeRewardDialog(data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_FREE_REWARD_ALL_DAY ? AddListenTimeBuilder.IType.ALL_DAY : AddListenTimeBuilder.IType.PLAY_PAGE_DIRECT_GIF,
                                data.getDurationLayerType(),
                                data.getGiftDuration(),
                                data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_FREE_REWARD_ALL_DAY ? AddListenTimeBuilder.IRewardDurType.FREE_ALL_DAY : AddListenTimeBuilder.IRewardDurType.FREE_NORMAL,
                                new IShowFreeRewardDialogCallBack() {
                            @Override
                            public void freeDialogClose() {
                                if (callBack != null) {
                                    callBack.onRewardSuccess(data.getGiftDuration() / 60, false);
                                }
                            }

                            @Override
                            public void showDialogFail() {
                                doShowFreeListenDialog(sourceName, track, callBack, baseFragment2, isNeedDoAnimation, trackId, albumId, watchAdSource, layerType, data);
                            }
                        });
                    }
                } else {
                    doShowFreeListenDialog(sourceName, track, callBack, baseFragment2, isNeedDoAnimation, trackId, albumId, watchAdSource, layerType, data);
                    isHandlingShowDialog = false;
                }
            }

            @Override
            public void onError(int code, String message) {
                doShowFreeListenDialog(sourceName, track, callBack, baseFragment2, isNeedDoAnimation, trackId, albumId, watchAdSource, layerType, null);
                isHandlingShowDialog = false;
            }
        });
    }

    public static void checkOutsideStationITing(String data) {
        try {
            Uri itingUri = Uri.parse(data);
            if (itingUri != null) {
                Set<String> queryParameterNames = itingUri.getQueryParameterNames();
                for (String name : queryParameterNames) {
                    if ("free_listen_dialog".equals(name)){
                        String value = itingUri.getQueryParameter(name);
                        if ("1".equals(value)) {
                            setNeedPullUpDialog(true);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setNeedPullUpDialog(boolean enable) {
        needPullUpDialog = enable;
    }

    public static boolean isNeedPullUpDialog() {
        return needPullUpDialog;
    }

    public interface IShowFreeRewardDialogCallBack {
        void freeDialogClose();

        void showDialogFail();
    }

    // 展示直接免费赠送弹窗
    private static void doShowFreeRewardDialog(int type, int durationLayerType, int giftDuration, int rewardDurType, IShowFreeRewardDialogCallBack showFreeRewardDialogCallBack) {
        AddListenTimeBuilder builder = new AddListenTimeBuilder();
        builder.type = type;
        builder.rewardTime = giftDuration;
        builder.sourceName = FreeRewardDialog.SOURCE_NAME;
        builder.rewardDurType = rewardDurType;
        builder.callback = new FreeListenTimeManager.IRewardResultCallback() {
            @Override
            public void onRewardSuccess(FreeListenTimeManager.TimeSyncResult data) {
                hideAllDialog();
                FreeRewardDialog.showFreeRewardDialog(durationLayerType, giftDuration / 60, new FreeRewardDialog.IDialogCallBack() {
                    @Override
                    public void onDialogClose() {
                        if (showFreeRewardDialogCallBack != null) {
                            showFreeRewardDialogCallBack.freeDialogClose();
                        }
                    }
                });
                isHandlingShowDialog = false;
            }

            @Override
            public void onRewardFail(int code, String message) {
                // 赠送失败，展示正常的看视频弹窗
                if (showFreeRewardDialogCallBack != null) {
                    showFreeRewardDialogCallBack.showDialogFail();
                }
                isHandlingShowDialog = false;
            }
        };
        FreeListenTimeManager.addListenTime(builder);
    }

    private static void doShowFreeListenDialog(String sourceName,
                                               Track track,
                                               FreeListenDialogNew.IRewardCallBack callBack,
                                               BaseFragment2 baseFragment2,
                                               boolean isNeedDoAnimation,
                                               long trackId,
                                               long albumId,
                                               String watchAdSource,
                                               int layerType,
                                               PopupDurationInfo data) {
        if (baseFragment2 == null || !baseFragment2.canUpdateUi()) {
            return;
        }
        hideAllDialog();
        WeakReference<BaseFragment2> frgReference = new WeakReference<>(baseFragment2);
        FreeListenDialogNew.showFreeListenDialog(data != null && data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_AUTO_REWARD ? FreeListenConfigManager.PLAY_PAGE_AUTO_DIALOG: sourceName,
                track, new FreeListenDialogNew.IRewardClickCallBack() {
            @Override
            public void onRewardClick(int rewardType, int freeListenType, boolean isFromAuto) {
                doFreeListenDialogClick(isFromAuto, sourceName, track, callBack, frgReference, trackId, albumId, data, rewardType, freeListenType, watchAdSource, layerType);
            }
        }, callBack, new FreeListenDialogNew.IViewProvider() {
            @Override
            public boolean canUpdateUi() {
                return frgReference.get() != null && frgReference.get().canUpdateUi();
            }

            @Override
            public BaseFragment2 getFragment() {
                return frgReference.get();
            }
        }, isNeedDoAnimation, false, trackId, albumId, watchAdSource, layerType, data);
    }

    private static void doFreeListenDialogClick(boolean isFromAuto,
                                                String sourceName,
                                                Track track,
                                                FreeListenDialogNew.IRewardCallBack callBack,
                                                WeakReference<BaseFragment2> frgReference,
                                                long trackId,
                                                long albumId,
                                                PopupDurationInfo data,
                                                int rewardType,
                                                int freeListenType,
                                                String watchAdSource,
                                                int layerType) {
        final Context context = BaseApplication.getMyApplicationContext();
        Runnable watchVideoRunnable = new Runnable() {
            @Override
            public void run() {
                BaseRewardAgainDialog.resetRewardAgainAd();
                if (isSupportRewardAgain(sourceName, freeListenType, false)) {
                    BaseRewardAgainDialog.loadRewardAgainAd();
                }
                canGetReward = false;
                isJumping = false;
                RewardExtraParams rewardExtraParams = new RewardExtraParams();
                rewardExtraParams.setRewardTypeParam(rewardType);
                if (isFromAuto) {
                    rewardExtraParams.setRewardInfoType(AddListenTimeBuilder.IRewardDurType.AUTO_VIDEO);
                } else {
                    rewardExtraParams.setRewardInfoType(FreeListenConfigManager.getInstance().getCurrentRewardInfoType());
                }
                AdUnLockTimeManagerV2.getInstance().unlockTrack(FreeListenConfigManager.getInstance().getCurrentRewardTime(), rewardExtraParams, isFromAuto ? FreeListenConfigManager.PLAY_PAGE_AUTO_DIALOG: sourceName,
                        new AdUnLockTimeManagerV2.IAdUnLockStatusCallBack() {
                    @Override
                    public void onRewardSuccess(AdUnLockVipTrackAdvertis currentAd, int rewardTime) {
                        FreeListenLogManager.writeLog("FreeListenTimeDialog watchRewardVideo reward success time = " + rewardTime);
                        if (FreeListenTimeManager.isIsNeedReplayWhenRewardTime() && FreeListenTimeManager.getListenTime(context) != 0) {
                            XmPlayerManager.getInstance(context).play();
                            FreeListenTimeManager.setIsNeedReplayWhenRewardTime(false);
                        }
                        if (callBack != null) {
                            callBack.onRewardSuccess(rewardTime, false);
                        }
                        FreeListenConfigManager.getInstance().increaseTimeIndex();
                        if (rewardType == 5) {
                            CustomToast. showFailToast("恭喜你获得全天免费时长权益");
                            return;
                        }
                        if (isSupportRewardAgain(sourceName, freeListenType, true)) {
                            BaseRewardAgainDialog.showRewardAgainDialog(rewardTime, frgReference, new BaseRewardAgainDialog.IRewardClickCallBack() {
                                @Override
                                public void onVideoDialogClick(int secondRewardTime, String sourceName) {
                                    doRewardAgainVideoDialogClick(rewardTime, secondRewardTime, sourceName, frgReference, this, data);
                                }

                                @Override
                                public void onClickDialogClick(int secondRewardTime, String sourceName, IAdModel adModel, int jumpStayTime) {
                                    doRewardAgainClickDialogClick(rewardTime, secondRewardTime, sourceName, frgReference, this, adModel, jumpStayTime, data);
                                }
                            }, true);
                        } else {
                            if (!TextUtils.isEmpty(sourceName) && sourceName.contains(FreeListenConfigManager.ALBUM_PAGE_DIALOG)) {
                                CustomToast.showFailToast("恭喜你获得" + rewardTime + "分钟收听时长", Toast.LENGTH_LONG);
                            } else {
                                RewardSuccessDialog.showRewardSuccessDialog(rewardTime, getColorFromData(data), frgReference);
                            }
                        }
                    }

                    @Override
                    public void onRewardFail(int code) {
                        if (callBack != null) {
                            callBack.onRewardFail();
                        }
                        if (isFromAuto && data != null && data.getGiftDuration() != 0) {
                            doShowFreeRewardDialog(AddListenTimeBuilder.IType.PLAY_PAGE_DIRECT_GIF,
                                    data.getDurationLayerType(),
                                    data.getGiftDuration(),
                                    AddListenTimeBuilder.IRewardDurType.AUTO_VIDEO_FAIL_GIF,
                                    new IShowFreeRewardDialogCallBack() {
                                        @Override
                                        public void freeDialogClose() {
                                            if (callBack != null) {
                                                callBack.onRewardSuccess(data.getGiftDuration() / 60, false);
                                            }
                                        }

                                        @Override
                                        public void showDialogFail() {
                                            if (data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_AUTO_REWARD) {
                                                data.setDurationLayerType(PopupDurationInfo.DIALOG_TYPE_NORMAL);
                                            }
                                            // 重新展示弹窗
                                            if (frgReference != null && frgReference.get() != null) {
                                                doShowFreeListenDialog(sourceName, track, callBack, frgReference.get(), false, trackId, albumId, watchAdSource, layerType, data);
                                            }
                                            if (isFromAuto) {
                                                HandlerManager.postOnUIThreadDelay(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        CustomToast.showFailToast("未完成任务，暂未获得免费听时长", Toast.LENGTH_LONG);
                                                    }
                                                }, 500);
                                            }
                                        }
                                    });
                        } else {
                            if (data != null && data.getDurationLayerType() == PopupDurationInfo.DIALOG_TYPE_AUTO_REWARD) {
                                data.setDurationLayerType(PopupDurationInfo.DIALOG_TYPE_NORMAL);
                            }
                            // 重新展示弹窗
                            if (frgReference != null && frgReference.get() != null) {
                                doShowFreeListenDialog(sourceName, track, callBack, frgReference.get(), false, trackId, albumId, watchAdSource, layerType, data);
                            }
                            if (isFromAuto) {
                                HandlerManager.postOnUIThreadDelay(new Runnable() {
                                    @Override
                                    public void run() {
                                        CustomToast.showFailToast("未完成任务，暂未获得免费听时长", Toast.LENGTH_LONG);
                                    }
                                }, 500);
                            }
                        }
                    }
                });
            }
        };
        if (MmkvCommonUtil.getInstance(context).getBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, false)) {
            watchVideoRunnable.run();
            return;
        }
        MmkvCommonUtil.getInstance(context).saveBoolean(PreferenceConstantsInHost.KEY_FREE_LISTEN_PROTOCOL_DIALOG_SHOWN, true);
        Activity topActivity = BaseApplication.getMainActivity();
        if (topActivity == null) {
            return;
        }
        AdFreeListenProtocolDialog protocolDialog = new AdFreeListenProtocolDialog(topActivity, new AdFreeListenProtocolDialog.IDismissCallBack() {
            @Override
            public void onDismiss() {
                watchVideoRunnable.run();
            }
        });
        protocolDialog.show();
    }

    private static void doRewardAgainClickDialogClick(int firstRewardTime, int secondRewardTime, String sourceName, WeakReference<BaseFragment2> frgReference,
                                                      BaseRewardAgainDialog.IRewardClickCallBack rewardClickCallBack,
                                                      IAdModel adModel, int jumpStayTime, PopupDurationInfo data) {
        mFirstRewardTime = firstRewardTime;
        mSecondRewardTime = secondRewardTime;
        mSourceName = sourceName;
        mFrgReference = frgReference;
        mRewardClickCallBack = rewardClickCallBack;
        mAdModel = adModel;
        mPopupDurationInfo = data;

        isJumping = true;
        if (jumpStayTime == 0) {
            canGetReward = true;
        } else {
            canGetReward = false;
            startJumpCountdown(jumpStayTime);
        }
    }

    private static void doRewardAgainVideoDialogClick(int firstRewardTime, int secondRewardTime, String sourceName, WeakReference<BaseFragment2> frgReference,
                                                      BaseRewardAgainDialog.IRewardClickCallBack rewardClickCallBack, PopupDurationInfo data) {
        // 看视频领取奖励
        AdUnLockTimeManagerV2.getInstance().unlockTrack(secondRewardTime, sourceName, new AdUnLockTimeManagerV2.IAdUnLockStatusCallBack() {
            @Override
            public void onRewardSuccess(AdUnLockVipTrackAdvertis currentAd, int rewardTime) {
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        RewardSuccessDialog.showRewardSuccessDialog(rewardTime, getColorFromData(data), frgReference);
                    }
                }, 500);
            }

            @Override
            public void onRewardFail(int code) {
                if (!UserInfoMannage.isVipUser()) {
                    // 重新展示二次新解锁弹窗
                    BaseRewardAgainDialog.showRewardAgainDialog(firstRewardTime, frgReference, rewardClickCallBack, false);
                }
            }
        });
    }

    public static void hideAllDialog() {
        FreeListenDialogNew.hideFreeListenDialog();
        RewardSuccessDialog.hideRewardSuccessDialog();
        BaseRewardAgainDialog.hideRewardAgainDialog();
        FreeRewardDialog.hideFreeRewardDialog();
    }

    private static boolean isSupportRewardAgain(String sourceName, int freeListenType, boolean isNeedAddCount) {
        if (freeListenType == 1 || freeListenType == 2) {
            // 全天权益状态不支持二次解锁
            return false;
        }
        if (!TextUtils.isEmpty(sourceName) && sourceName.contains(FreeListenConfigManager.ALBUM_PAGE_DIALOG)) {
            return false; // 专辑页不支持二次解锁
        }
        FreeListenConfigManager.ListenConfig listenConfig = FreeListenConfigManager.getInstance().getListenConfig();
        if (listenConfig != null
                && (FreeListenTimeManager.getListenTime(ToolUtil.getCtx()) > listenConfig.playDurationLimit)) {
            return false; // 超出时长上限
        }
        int continueCountMax = 0;
        if (listenConfig != null) {
            continueCountMax = listenConfig.continueCount;
        }
        if (continueCountMax == 0) {
            return false;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy_MM_dd");
        String format = simpleDateFormat.format(new Date());

        String showTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getString(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_DATE_NEW);
        int showCount = 0;
        if (TextUtils.equals(showTime, format)) {
            showCount = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getInt(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_TIMES_NEW);
        }
        if (showCount >= continueCountMax) {
            return false;
        } else {
            if (isNeedAddCount) {
                format = simpleDateFormat.format(new Date());
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveString(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_DATE_NEW, format);
                MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveInt(PreferenceConstantsInHost.KEY_REWARD_VIDEO_TWICE_TIMES_NEW, showCount + 1);
            }
            return true;
        }
    }

    /**
     * 拉起全屏二级页面 (https://alidocs.dingtalk.com/i/nodes/93NwLYZXWyglE7edsdYAn9kPJkyEqBQm?dontjump=true&utm_scene=team_space)
     * @param sourceName 专辑标签：albumTag  我的页：myAsset upgradePacket权益升级红包
     */
    public static void showListenTimeFragment(String sourceName) {
        if (FreeListenConfigManager.getInstance().getListenConfig() != null
                && !TextUtils.isEmpty(FreeListenConfigManager.getInstance().getListenConfig().offLineTip)) {
            CustomToast.showFailToast(FreeListenConfigManager.getInstance().getListenConfig().offLineTip);
            return;
        }
        if (!FreeListenConfigManager.isFreeListenV2Open()) {
            FreeListenLogManager.writeLog("ListenTimeDialogManager showListenTimeFragment fail >>> isFreeListenV2Open = false");
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("pageNum", 0 + "");
        params.put("sceneId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, ""));
        params.put("groupId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, ""));
        String freeListenExt = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXT, "");
        if (!TextUtils.isEmpty(freeListenExt)) {
            try {
                params.put("ext", URLEncoder.encode(freeListenExt, "utf-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        CommonRequestM.getFreeListenAlbumData(params, new IDataCallBack<FreeListenAlbumData>() {
            @Override
            public void onSuccess(@Nullable FreeListenAlbumData data) {
                if (data != null) {
                    FreeListenConfigManager.getInstance().initTimeArray(data.getRewardInfos());
                }
                Activity mainActivity = MainApplication.getMainActivity();
                if (mainActivity instanceof MainActivity) {
                    if (data != null && data.getTabType() != 0) {
                        // 进入新的畅听&福利页tab合并页面
                        String url = "iting://open?msg_type=94&bundle=rn_credit_center&embed=1&coin=1";
                        Uri uri = Uri.parse(url);
                        String bundleName = uri.getQueryParameter("bundle");
                        Set<String> keySet = uri.getQueryParameterNames();
                        final Bundle bundle = new Bundle();
                        if (keySet != null && keySet.size() > 0) {
                            Iterator<String> iterator = keySet.iterator();
                            while (iterator.hasNext()) {
                                String key = iterator.next();
                                bundle.putString(key, uri.getQueryParameter(key));
                            }
                        }
                        boolean isDebug = ConstantsOpenSdk.isDebug && ("1".equals(bundle.getString("__debug")));
                        final String fragmentName = isDebug ? "rntest" : "rn";
                        bundle.putString("fragmentName", fragmentName);
                        Router.getActionByCallback(Configure.BUNDLE_RN, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                try {
                                    IRNFunctionRouter functionAction = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).getFunctionAction();
                                    if (functionAction != null && !functionAction.checkBundleSupport(bundleName)) {
                                        onlyStartFreeListenTimeFragment(data, sourceName);
                                    } else {
                                        BaseFragment rnFragment = Router.<RNActionRouter>getActionRouter(Configure.BUNDLE_RN).
                                                getFragmentAction().newRNFragment(fragmentName, bundle);
                                        ((MainActivity) mainActivity).startFragment(new RewardTabFragment(sourceName, data, rnFragment));
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                                onlyStartFreeListenTimeFragment(data, sourceName);
                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                                onlyStartFreeListenTimeFragment(data, sourceName);
                            }

                        });
                    } else {
                        onlyStartFreeListenTimeFragment(data, sourceName);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                onlyStartFreeListenTimeFragment(null, sourceName);
            }
        });
    }

    private static void onlyStartFreeListenTimeFragment(FreeListenAlbumData data, String sourceName) {
        Activity mainActivity = MainApplication.getMainActivity();
        if (mainActivity instanceof MainActivity) {
            ((MainActivity) mainActivity).startFragment(new FreeListenTimeFragment(sourceName, data, false));
        }
    }

    public static void showListenTimeDialogOnResume(BaseFragment2 baseFragment2, Track currentTrack, boolean isFreeListenTrack) {
        if (!FreeListenConfigManager.isFreeListenV2Open()) {
            return;
        }
        if (FreeListenDialogNew.isCurrentDialogShowing()) {
            // 当前已经有弹窗正在展示，不展示二曝弹窗
            return;
        }
        if (FreeRewardDialog.isCurrentDialogShowing()) {
            // 当前已经有赠送弹窗正在展示，不展示二曝弹窗
            return;
        }
        if (baseFragment2 == null) {
            return;
        }
        ViewGroup fragView = (ViewGroup) baseFragment2.getView();
        if (fragView != null && fragView.findViewById(R.id.main_listen_time_float_layout) != null) {
            // 当前已经有气泡浮层正在展示，不展示二曝弹窗
            return;
        }
        Logger.log("showListenTimeDialogOnResume isEnterRewardVideo =" + AdUnLockTimeManagerV2.getInstance().isEnterRewardVideo());
        if (AdUnLockTimeManagerV2.getInstance().isEnterRewardVideo()) {
            // 观看激励视频返回不请求二曝弹窗
            return;
        }
        Context context = BaseApplication.getMyApplicationContext();
        int remainTime = FreeListenTimeManager.getListenTime(context);
        if (remainTime == 0) {
            return;
        }
        WeakReference<BaseFragment2> frgReference = new WeakReference<>(baseFragment2);
        Map<String, String> params = new ArrayMap<>();
        try {
            params.put("sceneId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_SCENE_ID, ""));
            params.put("groupId", MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXP_ID, ""));
            if (currentTrack != null) {
                params.put("trackId", currentTrack.getDataId() + "");
                if (currentTrack.getAlbum() != null) {
                    params.put("albumId", currentTrack.getAlbum().getAlbumId() + "");
                }
            }
            params.put("lastShowTime", MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLong(PreferenceConstantsInHost.KEY_REWARD_VIDEO_LAST_SHOW_TIME, 0) + "");
            params.put("remainingDuration", remainTime + "");
            String freeListenExt = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(PreferenceConstantsInHost.KEY_FREE_LISTEN_EXT, "");
            if (!TextUtils.isEmpty(freeListenExt)) {
                try {
                    params.put("ext", URLEncoder.encode(freeListenExt, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
            params.put("isFreeListenTrack", isFreeListenTrack + "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        CommonRequestM.requestAutoDialog(params, new IDataCallBack<Integer>() {
            @Override
            public void onSuccess(@Nullable Integer data) {
                if (frgReference == null || frgReference.get() == null || !frgReference.get().canUpdateUi()) {
                    return;
                }
                if (data == null || data == 0) {
                    return;
                }
                if (FreeListenTimeManager.getListenTime(context) == 0) {
                    return;
                }
                hideAllDialog();
                ListenTimeDialogManager.showRewardDialogNew(FreeListenConfigManager.AUTO_PLAY_PAGE_POP_DIALOG, currentTrack, frgReference.get(), 4);
            }

            @Override
            public void onError(int code, String message) {
            }
        });
    }

    private static void startJumpCountdown(int actualStayTime) {
        jumpTimer = new CountDownTimer((long) actualStayTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                Logger.i("JumpCountdown", "onTick = " + millisUntilFinished);
            }

            @Override
            public void onFinish() {
                Logger.i("JumpCountdown", "finish");
                canGetReward = true;
            }
        };
        jumpTimer.start();
    }

    private static void cancelJumpCountdown() {
        if (jumpTimer != null) {
            jumpTimer.cancel();
            jumpTimer = null;
        }
    }

    public static void onPlayFragmentResume() {
        if (isJumping) {
            cancelJumpCountdown();
            if (canGetReward) {
                doRewardAgain();
            } else {
                CustomToast.showFailToast("浏览未满足时长要求，请重试");
                // 重新展示二次新解锁弹窗
                BaseRewardAgainDialog.showRewardAgainDialog(mFirstRewardTime, mFrgReference, mRewardClickCallBack, false);
            }            isJumping = false;
        }
    }

    private static void doRewardAgain() {
        AddListenTimeBuilder builder = new AddListenTimeBuilder();
        builder.adId = mAdModel != null ? mAdModel.getAdid() : 0;
        builder.responseId = mAdModel != null ? mAdModel.getResponseId() : 0;
        builder.positionName = mAdModel != null ? mAdModel.getPositionName() : "";
        builder.type = AddListenTimeBuilder.IType.OTHER_AD_CLICK;
        builder.rewardTime = mSecondRewardTime * 60;
        builder.sourceName = mSourceName;
        builder.callback = new FreeListenTimeManager.IRewardResultCallback() {
            @Override
            public void onRewardSuccess(FreeListenTimeManager.TimeSyncResult data) {
                HandlerManager.postOnUIThreadDelay(new Runnable() {
                    @Override
                    public void run() {
                        RewardSuccessDialog.showRewardSuccessDialog(mSecondRewardTime, getColorFromData(mPopupDurationInfo), mFrgReference);
                    }
                }, 500);
            }

            @Override
            public void onRewardFail(int code, String message) {
                CustomToast.showFailToast("获取时长失败，请稍后重试");
            }
        };
        FreeListenTimeManager.addListenTime(builder);
        BaseRewardAgainDialog.resetRewardAgainAd();
    }

    private static int getColorFromData(PopupDurationInfo data) {
        if  (data != null && !TextUtils.isEmpty(data.getButtonColor())) {
            try {
                return Color.parseColor(data.getButtonColor());
            } catch (Exception e) {
               return FreeListenConfigManager.getInstance().getDefaultThemeColor();
            }
        }
        return FreeListenConfigManager.getInstance().getDefaultThemeColor();
    }

    public static void setLastStartFreezeTime(long freezeTime) {
        lastStartFreezeTime = freezeTime;
    }

    public static long getLastStartFreezeTime() {
        return lastStartFreezeTime;
    }
}
