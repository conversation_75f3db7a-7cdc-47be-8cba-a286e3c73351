package com.ximalaya.ting.android.main.fragment.find.child.recommendad;

import com.ximalaya.ting.android.adsdk.BusinessSDK;
import com.ximalaya.ting.android.adsdk.external.XmLoadAdParams;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAd;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAdLoadCallBack;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAdRealTimeProvider;
import com.ximalaya.ting.android.adsdk.external.feedad.IFeedAdHandler;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.SerialInfo;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.ad.AdSDKManager;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew;
import com.ximalaya.ting.android.player.MD5;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * 首页信息流实时化
 */
public class FeedAdManagerStaggeredRealTime implements IFeedAdManagerStaggered{
    private WeakReference <IRecommendFeedAdProviderNew> provider;
    private IExpressFeedAdRealTimeProvider mFeedAdProvider;
    private HashSet<Long> albumAdIds = new HashSet<>();
    private HashSet<Long> trackAdIds = new HashSet<>();
    private String screenId;// 同屏id,用于信息流、焦点图、touch去重

    private int mSlotId;

    public FeedAdManagerStaggeredRealTime(IRecommendFeedAdProviderNew provider) {
        this.provider = new WeakReference<>(provider);
        AdSDKManager.init(ToolUtil.getCtx());
        mFeedAdProvider = BusinessSDK.getInstance().getExpressFeedAdRealTimeProvider();
    }

    public void loadRecommendAd(boolean isNeedRecordAdRefresh, int slotId) {
        mSlotId = slotId;
        if (isNeedRecordAdRefresh) {
            albumAdIds.clear();
            trackAdIds.clear();
        }
        XmLoadAdParams xmLoadAdParams = new XmLoadAdParams(slotId + "");
        Map<String, String> requestParams = new HashMap<>();
        if (slotId == 28) {
            requestParams.put("isModeMix", "1");
            requestParams.put("screenId", screenId);
            requestParams.put("findNativeAdShowStyle", "" + ABTest.getInt(CConstants.Group_ad.ITEM_FIND_NATIVE_AD_UI_STYLE, 0));
        }
        requestParams.put("refreshAd", "true");
        requestParams.put("findNativeRealVersion", "1");
        if (RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment()) {
            requestParams.put("2024NewRecommend", "true");
        } else {
            requestParams.put("2024NewRecommend", "false");
        }
        if (slotId == 293) {
            requestParams.put("isCenterBigFeedAd", "1");
        }
        xmLoadAdParams.setRequestParams(requestParams);
        mFeedAdProvider.loadExpressFeedAd(ToolUtil.getCtx(), xmLoadAdParams, isNeedRecordAdRefresh, new IFeedAdHandler() {
                    @Override
                    public void notifyDataSetChanged() {
                        if (provider == null || provider.get() == null) {
                            return;
                        }
                        AbRecyclerViewAdapter adapter = provider.get().getAdApter(mSlotId);
                        if (adapter != null) {
                            try {
                                adapter.notifyDataSetChanged();
                            } catch (IllegalStateException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void notifyOneDataChanged(int position) {
                        if (provider == null || provider.get() == null) {
                            return;
                        }
                        AbRecyclerViewAdapter adapter = provider.get().getAdApter(mSlotId);
                        if (adapter != null) {
                            try {
                                adapter.notifyItemChanged(position);
                            } catch (IllegalStateException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                },
                new IExpressFeedAdLoadCallBack() {
                    @Override
                    public void onAdLoad(List<IExpressFeedAd> feedAd) {
                        // 中插信息流大图位于非首屏，可以等下滑bind的时候再刷新，不用整体刷新，避免首页加载耗时
                        if (provider == null || provider.get() == null || slotId == 293) {
                            return;
                        }
                        provider.get().setRecommendAdData(mSlotId);
                    }

                    @Override
                    public void onLoadError(int code, String message) {
                        if (provider == null || provider.get() == null || slotId == 293) {
                            return;
                        }
                        provider.get().setRecommendAdData(mSlotId);
                    }
                });
    }

    @Override
    public void onListScroll(boolean scrollUp) {

    }

    // 首页单双混排模式插入广告
    public ItemModel insertFeedAd(int loadedAdIndex, RecommendItemNew recommendItemNew) {
        boolean isCenterBigAd = recommendItemNew != null &&
                RecommendItemNew.RECOMMEND_ITEM_AD_CENTER_BIG_FEED.equals(recommendItemNew.getItemType());
        ItemModel itemModelAd = new ItemModel(recommendItemNew,
                isCenterBigAd ? RecommendFragmentTypeManager.INSTANCE.getVIEW_TYPE_AD_CENTER_BIG_FEED() :
                        RecommendFragmentTypeManager.INSTANCE.getVIEW_TYPE_AD_BANNER_REALTIME());
        if (itemModelAd != null) {
            itemModelAd.setAdIndex(loadedAdIndex);
        }
        return itemModelAd;
    }

    @Override
    public IExpressFeedAd getExpressFeedAd(int loadAdIndex, int position) {
        return mFeedAdProvider.getExpressFeedAd(loadAdIndex, position);
    }

    @Override
    public boolean dislikeAd(int adIndex, boolean isBannerAd) {
        return true;
    }

    @Override
    public boolean isAdNeedRemoveByDislike(int currentAdIndex) {
        return false;
    }

    @Override
    public void addHasShownAd(int adIndex) {

    }

    @Override
    public void updateScreenId() {
        String deviceToken = DeviceUtil.getDeviceToken(MainApplication.getMyApplicationContext());
        String imei = null;
        try {
            imei = SerialInfo.getIMEIAndNotDefual(MainApplication.getMyApplicationContext());
        } catch (Exception e) {
            e.printStackTrace();
        }
        String oaid = DeviceUtil.getOAID();
        String time = System.currentTimeMillis() + "";
        if (!TextUtils.isEmpty(deviceToken)) {
            screenId = MD5.md5(deviceToken + time);
        } else if (!TextUtils.isEmpty(imei)) {
            screenId = MD5.md5(imei + time);
        } else if (!TextUtils.isEmpty(oaid)) {
            screenId = MD5.md5(oaid + time);
        }

        if (provider != null && provider.get() != null) {
            provider.get().updateScreenId(screenId);
        }
    }

    @Override
    public String getScreenId() {
        return screenId;
    }
}
