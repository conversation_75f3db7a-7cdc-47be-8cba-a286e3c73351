package com.ximalaya.ting.android.main.adapter.find.recommendStaggered;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog;
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil;
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager;
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build;
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ad.util.AdUbtReportUtil;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.XmFeedInnerModel;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.IFeedAdManagerStaggered;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager;
import com.ximalaya.ting.android.main.manager.RecommendFragmentAbManager;
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew;
import com.ximalaya.ting.android.main.model.rec.RecommendModuleItem;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;
import java.util.Map;

/**
 * 单双混排--单列中插banner广告,实时化请求
 */
public class RecommendBannerAdRealTimeProvider implements
        IMulitViewTypeViewAndDataStaggered<RecommendBannerAdRealTimeProvider.AdViewHolder, ItemModel<RecommendItemNew>>,
        IMulitViewTypeViewAndDataTraceStaggered<RecommendBannerAdRealTimeProvider.AdViewHolder, ItemModel<RecommendItemNew>> {
    private BaseFragment2 mBaseFragment2;
    private RecommendFragmentStaggeredAdapter.IDataAction mRemover;
    private IFeedAdManagerStaggered mFeedAdManager;

    private INativeAd mShowAdModel;

    public RecommendBannerAdRealTimeProvider(BaseFragment2 fragment2,
                                             RecommendFragmentStaggeredAdapter.IDataAction remover,
                                             IFeedAdManagerStaggered feedAdManager) {
        mBaseFragment2 = fragment2;
        mRemover = remover;
        mFeedAdManager = feedAdManager;
    }

    @Override
    public AdViewHolder createViewHolder(View convertView) {
        return new AdViewHolder(convertView);
    }

    @Override
    public void bindViewHolder(AdViewHolder holder, int position, ItemModel<RecommendItemNew> data, View convertView) {
        if (holder == null || data == null || data.getObject() == null) {
            return;
        }
        RecommendItemNew recommendItemNew = data.getObject();
        IExpressFeedAd feedAd;
        if (data.getTag() instanceof IExpressFeedAd) {
            feedAd = (IExpressFeedAd) data.getTag();
        } else {
            feedAd = mFeedAdManager.getExpressFeedAd(data.getAdIndex(), position);
            data.setTag(feedAd);
        }
        if (feedAd == null) {
            holder.rootContainer.setVisibility(View.GONE);
            if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "bannerAdUiBugFix", false) && mRemover != null) {
                holder.spaceView.setVisibility(View.VISIBLE);
                HandlerManager.postOnUIThread(new Runnable() {
                    @Override
                    public void run() {
                        mRemover.updateAd(position, false);
                    }
                });
            }
            return;
        }
        if (recommendItemNew != null) {
            RecommendStaggeredTraceManager.INSTANCE.checkXmRequestId(data.getObject());
        } else if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException("RecommendItemNew为空");
        }
        convertView.setTag(R.id.main_staggered_default_holder, false);
        holder.rootContainer.setVisibility(View.VISIBLE);
        holder.spaceView.setVisibility(View.GONE);
        feedAd.bindExpressAdToView(holder.rootContainer, RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment() ?
                RecommendFragmentTypeManager.INSTANCE.isNewSceneCard() ? IExpressFeedAd.AD_STYLE_BANNER_CARD_NEW : IExpressFeedAd.AD_STYLE_BANNER_CARD :
                IExpressFeedAd.AD_STYLE_BANNER, new IExpressFeedAd.IExpressAdInteractionListener() {
            @Override
            public void onAdClose(int closeEvent, int i, int listSize) {
                if (!RecommendFragmentAbManager.INSTANCE.is2024NewRecommendFragment() ||
                        !XmAdFeedbackUtil.feedbackEnable(mShowAdModel)) {
                    removeAd();
                    return;
                }
                MoreFuncBuild build = new MoreFuncBuild();
                build.setShowLevel2Dislike(true);
                build.setFragment2(mBaseFragment2);
                DisLikeLeve2Build leve2Build = new DisLikeLeve2Build();
                leve2Build.setFromAd(true);
                leve2Build.setVibratorEnable(false);
                leve2Build.setOnFeedBackListener(new NewXmFeedBackPopDialog.IOnFeedBackListener() {
                    @Override
                    public void onDialogShow(boolean showSuccess) {
                        if (!showSuccess) {
                            removeAd();
                        }
                    }

                    @Override
                    public void onFeedBack(List<XmFeedInnerModel> list) {
                        String name = "";
                        String type = "";
                        if (list != null && !list.isEmpty()) {
                            name = list.get(0).getName();
                            type = list.get(0).getCodeType();
                        }
                        try {
                            feedAd.reportFeedback(name, Integer.parseInt(type));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        removeAd();
                    }
                });
                build.setDisLikeLeve2Build(leve2Build);
                XmMoreFuncManager.checkShowMorePage(build);
            }

            @Override
            public void onAdRenderFail() {
                convertView.setTag(R.id.main_staggered_default_holder, true);
                holder.rootContainer.setVisibility(View.GONE);
            }

            @Override
            public void onAdClicked(@Nullable View view, INativeAd iNativeAd, boolean b) {
                CustomToast.showDebugFailToast("广告点击了-debug环境下显示");
                String positionNew = "";
                Object recommendItem = data.getTag();
                String xmRequestId = "";
                if (recommendItem instanceof RecommendItemNew) {
                    positionNew = ((RecommendItemNew) recommendItem).getNewPos() + "";
                    xmRequestId = ((RecommendItemNew) recommendItem).getXmRequestId();
                }
                Map<String, String> otherInfo = feedAd.getOtherInfo();
                // 新首页-广告（双列）  点击事件
                XMTraceApi.Trace traceBuilder = new XMTraceApi.Trace()
                        .click(38751)
                        .put("adId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                        .put("adType", "商业广告")
                        .put("style", "双列中插大图")
                        .put("positionNew", positionNew)
                        .put("currPage", "newHomePage")
                        .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                        .put(XmRequestIdManager.CONT_ID, otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                        .put("contentType", "图文");

                Logger.d(
                        "msg_ubt",
                        "RecommendBannerAdRealTimeProvider -- 333 -- click --  ");
                AdUbtReportUtil.appendUbtReportMap(traceBuilder, iNativeAd);
                traceBuilder.createTrace();
            }

            @Override
            public void onAdShow(INativeAd iNativeAd) {
                mShowAdModel = iNativeAd;
                CustomToast.showDebugFailToast("广告显示了-debug环境下显示");
                if (mFeedAdManager != null) {
                    mFeedAdManager.addHasShownAd(data.getAdIndex());
                }
            }

            private void removeAd() {
                if (mRemover != null) {
                    if (ConfigureCenter.getInstance().getBool(CConstants.Group_ad.GROUP_NAME, "bannerAdUiBugFix", false)) {
                        mRemover.updateAd(position, true);
                    } else {
                        mRemover.remove(position, false);
                    }
                }
            }
        });

        //待执行完bindExpressAdToView之后获取，子view中的XmNativeAdContainer 设置radio
        if (holder.rootContainer != null && holder.rootContainer.getChildCount() > 0) {
            View view = holder.rootContainer.getChildAt(0);
            if (view != null && view instanceof XmNativeAdContainer) {
                int radio = ConfigureCenter.getInstance().getInt(
                        CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_AD_MAIN_FEED_SHOW_EXPOSE_RADIO, 0);
                if (radio > 0) {
                    ((XmNativeAdContainer) view).setSubPercent(radio);
                }
            }
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.main_item_recommend_ad_banner;
    }

    @Override
    public void traceOnItemShow(ItemModel<RecommendItemNew> data, int position, AdViewHolder holder) {
        if (data == null) {
            return;
        }
        if (holder.rootContainer == null || holder.rootContainer.getChildCount() <= 0 || holder.rootContainer.getVisibility() != View.VISIBLE) {
            return;
        }
        // 新首页-广告（双列）  控件曝光
        IExpressFeedAd feedAd = (IExpressFeedAd) data.getTag();
        if (feedAd == null) {
            return;
        }
        String positionNew = "";
        Object recommendItem = data.getObject();
        if (recommendItem instanceof RecommendItemNew) {
            positionNew = ((RecommendItemNew) recommendItem).getNewPos() + "";
        }
        if (data.getObject() != null) {
            RecommendStaggeredTraceManager.INSTANCE.checkXmRequestId(data.getObject());
        } else if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException("RecommendItemNew为空");
        }
        Map<String, String> otherInfo = feedAd.getOtherInfo();
        XMTraceApi.Trace traceBuilder = new XMTraceApi.Trace()
                .setMetaId(38752)
                .setServiceId("slipPage")
                .put("adId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                .put("adType", "商业广告")
                .put("style", "双列中插大图")
                .put("positionNew", positionNew)
                .put("currPage", "newHomePage")
                .put("contentType", "图文")
                .put("xmContentType", "recommendBannerAd")
                .put(XmRequestIdManager.XM_REQUEST_ID, data.getObject() != null ? data.getObject().getXmRequestId() : "")
                .put(XmRequestIdManager.CONT_ID, otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                .put("exploreType", RecommendFragmentStaggered.Companion.getMExploreType() + "");

        Logger.d(
                "msg_ubt",
                "RecommendBannerAdRealTimeProvider -- 333 -- show --  ");
        AdUbtReportUtil.appendUbtReportMap(traceBuilder, mShowAdModel);
        if (recommendItem instanceof RecommendItemNew &&((RecommendItemNew)recommendItem).isLocalCache()) {
            traceBuilder.isLocalCache();
        }
        traceBuilder.createTrace();
    }

    public static class AdViewHolder extends RecyclerView.ViewHolder {
        ViewGroup rootContainer;
        View spaceView;

        public AdViewHolder(View convertView) {
            super(convertView);
            this.rootContainer = convertView.findViewById(R.id.main_ad_root_container);
            this.spaceView = convertView.findViewById(R.id.main_ad_space);
        }
    }
}
