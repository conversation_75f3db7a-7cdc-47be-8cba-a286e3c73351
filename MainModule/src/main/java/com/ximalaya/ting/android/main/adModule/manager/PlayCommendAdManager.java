package com.ximalaya.ting.android.main.adModule.manager;

import android.view.View;
import android.widget.ListView;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.manager.ad.FeedAdWrapper;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.play.CommentModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.adModule.manager.feedadhandle.IFeedAdHandle;
import com.ximalaya.ting.android.main.adModule.view.CommendAdView;
import com.ximalaya.ting.android.main.adapter.play.CommentListAdapterNew;
import com.ximalaya.ting.android.main.fragment.find.child.FeedAdHelper;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.Iterator;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Created by le.xin on 2020-03-04.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class PlayCommendAdManager {
    private List<Advertis> mCommendAdList;

    private JSONObject commendAdConfig;

    private IRequestCallBack mRequestCallBack;

    private int mMaxHotCommentNum;
    private Track curTrack;

    private FeedAdHelper<Object> mFeedAdHelper;
    private long mLastRequestId;

    public interface IRequestCallBack {

        void onAdRequestSuccess(List<Advertis> advertisList, long trackId);

        void onListDataChange();
    }

    private PlayCommendAdManager() {
    }

    public PlayCommendAdManager(IRequestCallBack requestCallBack, int showHotCommentNum,
                                IHandleOk notifyDataSetChanged) {
        mRequestCallBack = requestCallBack;
        mMaxHotCommentNum = showHotCommentNum;

        mFeedAdHelper = new FeedAdHelper(new FeedAdHelper.IFeedProvider() {
            @Override
            public boolean compareShowStyle(int showStyle1, int showStyle2) {
                if((showStyle1 == Advertis.IMG_SHOW_TYPE_COMMEND_STATIC_IMG
                        || showStyle1 == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO)
                        && (showStyle2 == Advertis.IMG_SHOW_TYPE_COMMEND_STATIC_IMG
                        || showStyle2 == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO)) {
                    return true;
                }
                return false;
            }

            @Override
            public void notifyDataSetChanged() {
                if(notifyDataSetChanged != null) {
                    notifyDataSetChanged.onReady();
                }
            }
        }, new CommendAdHandleImpl(), AppConstants.AD_POSITION_NAME_PLAY_COMMENT);
    }

    private int minCommentCount;
    private int startIndex;
    private int interval;

    private void updateConfiger() {
        commendAdConfig = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                    CConstants.Group_ad.ITEM_COMMENTADCONFIG);
        if(commendAdConfig == null) {
            minCommentCount = 5;
            startIndex = 3;
            interval = 20;
        } else {
            try {
                minCommentCount = commendAdConfig.getInt("minCommentCount");
                startIndex = commendAdConfig.getInt("startIndex");
                interval = commendAdConfig.getInt("interval");

                if(interval <= 0) {
                    interval = 1;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Logger.log("PlayCommendAdManager : minCommentCount=" + minCommentCount +
                "; startIndex=" + startIndex + "; interval=" + interval);

    }

    private boolean canInsertAdvertis(int commentCount) {
        return minCommentCount <= commentCount;
    }

    private void resetData() {
        mCommendAdList = null;
        updateConfiger();
        curTrack = null;
    }

    public void loadCommendAd(final Track track ,List<CommentModel> commentModelList) {
        resetData();

        curTrack = track;

        if(track == null ||
                !canInsertAdvertis(track.getCommentCount())) {
            mFeedAdHelper.updateAdvertis(null, false);
            removeInsertedAds(commentModelList, true);
            return;
        }

        mLastRequestId = System.currentTimeMillis();

        final long tempRequestId = mLastRequestId;

        AdRequest.playCommentAd(false, track.getCommentCount(),new IDataCallBack<List<Advertis>>() {
            @Override
            public void onSuccess(@Nullable List<Advertis> object) {
                if(tempRequestId != mLastRequestId) {
                    return;
                }

                mFeedAdHelper.updateAdvertis(object, false);

                mCommendAdList = object;
                if(mRequestCallBack != null) {
                    mRequestCallBack.onAdRequestSuccess(mCommendAdList,track.getDataId());
                }
            }

            @Override
            public void onError(int code, String message) {
                if(tempRequestId != mLastRequestId) {
                    return;
                }

                mFeedAdHelper.updateAdvertis(null, false);
                removeInsertedAds(commentModelList, true);
            }
        });
    }

    public void removeInsertedAds(List<CommentModel> commentModelList, boolean notification) {
        if(ToolUtil.isEmptyCollects(commentModelList)) {
            return;
        }

        boolean isRemoved = false;
        Iterator<CommentModel> iterator = commentModelList.iterator();
        while (iterator.hasNext()) {
            if(iterator.next().id == CommentListAdapterNew.ITEM_AD) {
                iterator.remove();
                isRemoved = true;
            }
        }

        if(isRemoved && mRequestCallBack != null && notification) {
            mRequestCallBack.onListDataChange();
        }
    }

    public void insertAd(List<CommentModel> commentModelList) {
        if(ToolUtil.isEmptyCollects(mCommendAdList) ||
                ToolUtil.isEmptyCollects(commentModelList) ||
                commentModelList.size() < 2) {
            return;
        }

        removeInsertedAds(commentModelList, false);

        int curAdvertisIndex = 0;
        CommentModel commentModel;
        int intervalNum = 0;
        boolean firstInsert = true;    // 是否是第一次插入

//        Set<Long> hotCommentIds = new HashSet<>();

        boolean isLast = false;

        int allCommentCount = 0;

        for (int i = 0; i < commentModelList.size(); i++) {
            if(curAdvertisIndex >= mCommendAdList.size()) {
                return;
            }

            if(i == commentModelList.size() - 1) {
                CommentModel commentModelTemp = commentModelList.get(commentModelList.size() - 1);
                if(!(commentModelTemp != null && commentModelTemp.id == CommentListAdapterNew.ITEM_AD)) {
                    isLast = true;
                } else {
                    isLast = false;
                }
            }

            commentModel = commentModelList.get(i);

            if(commentModel.groupType == CommentModel.GROUP_TYPE_HOT && commentModel.id > 0) {
//                hotCommentIds.add(commentModel.id);
//                if(hotCommentIds.size() > mMaxHotCommentNum) {
                    continue;
//                }
            }

            if(commentModel.groupType == CommentModel.GROUP_TYPE_ALL && commentModel.id > 0) {
                allCommentCount++;
            }

            Logger.log("PlayCommendAdManager : curIndex " + i + "  " + intervalNum);

            if(firstInsert &&
                    (intervalNum == startIndex || (isLast && intervalNum + 1 == startIndex)) &&
                    commentModel.id > 0) {
                firstInsert = false;
                CommentModel commentAdModel = createCommentAdModel(curAdvertisIndex);
                if(commentAdModel != null) {

                    if(intervalNum == startIndex) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i, commentAdModel);
                    } else if(i + 1 <= commentModelList.size()) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i + 1, commentAdModel);
                    }

                    curAdvertisIndex ++;
                    intervalNum = 0;
                    continue;
                }
            }

            if(!firstInsert && (intervalNum == interval || (isLast && intervalNum + 1 == interval))) {
                CommentModel commentAdModel = createCommentAdModel(curAdvertisIndex);
                if(commentAdModel != null) {

                    if(intervalNum == interval) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i , commentAdModel);
                    } else if(i + 1 <= commentModelList.size()) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i + 1, commentAdModel);
                    }

                    curAdvertisIndex ++;
                    intervalNum = 0;
                    continue;
                }
            }

            if(commentModel.id > 0) {
                intervalNum ++;
            }
        }

    }

    @Nullable
    private CommentModel createCommentAdModel(int adIndex) {
        if(!ToolUtil.isEmptyCollects(mCommendAdList) && mCommendAdList.size() > adIndex) {
            CommentModel adComment = new CommentModel();
            adComment.id = CommentListAdapterNew.ITEM_AD;
            adComment.mFeedAdWrapper = mFeedAdHelper.insertFeedAd(adIndex, null);
            adComment.adSequence = adIndex;
            return adComment;
        }
        return null;
    }

    public void checkHasAdAndPlay(ListView listView) {
        if(listView == null) {
            return;
        }

        for (int i = 0; i < listView.getChildCount(); i++) {
            View childAt = listView.getChildAt(i);
            if(childAt instanceof CommendAdView) {
//                // 调用这个函数使其自动播放
//                ((CommendAdView) childAt).onScrollStateChanged(listView ,
//                        AbsListView.OnScrollListener.SCROLL_STATE_IDLE);
            }
        }
    }

    public void onDestroy() {
    }

    @Nullable
    public FeedAdHelper.IFeedAdShowedCallBack getFeedAdShowCallBack() {
        if(mFeedAdHelper != null) {
            return mFeedAdHelper.getFeedAdShowCallBack();
        }
        return null;
    }

    public void onListScroll(boolean scrollUp) {
        if(mFeedAdHelper != null) {
            mFeedAdHelper.onListScroll(scrollUp);
        }
    }

    public static class CommendAdHandleImpl implements IFeedAdHandle<Object> {
        @Override
        public List<Advertis> updateAdvertis(List<Advertis> advertis) {
            return advertis;
        }

        @Override
        public boolean insertFeedAd(FeedAdWrapper feedAdWrapper, int index, Object otherData) {
            return true;
        }
    }

}
