package com.ximalaya.ting.android.main.adModule.manager;

import static com.ximalaya.ting.android.host.model.play.CommentModel.ITEM_TYPE_NORMAL;

import android.view.View;
import android.widget.ListView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.host.manager.ad.FeedAdWrapper;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.play.CommentModel;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.main.adModule.manager.feedadhandle.IFeedAdHandle;
import com.ximalaya.ting.android.main.adModule.view.CommendAdViewX;
import com.ximalaya.ting.android.main.fragment.find.child.FeedAdHelper;
import com.ximalaya.ting.android.main.view.comment.util.CommentListUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.util.Iterator;
import java.util.List;

/**
 * Created by zhao.peng.
 * describe 评论广告管理类，insert 与revomeInsert 与老版本不同
 * Date: 2023/5/26
 */
public class XPlayCommendAdManager {
    private List<Advertis> mCommendAdList;

    private JSONObject commendAdConfig;

    private IRequestCallBack mRequestCallBack;

    private Track curTrack;

    private FeedAdHelper<Object> mFeedAdHelper;
    private long mLastRequestId;

    public interface IRequestCallBack {

        void onAdRequestSuccess(List<Advertis> advertisList, long trackId);

        void onListDataChange();
    }

    private XPlayCommendAdManager() {
    }

    public XPlayCommendAdManager(IRequestCallBack requestCallBack, IHandleOk notifyDataSetChanged) {
        mRequestCallBack = requestCallBack;

        mFeedAdHelper = new FeedAdHelper(new FeedAdHelper.IFeedProvider() {
            @Override
            public boolean compareShowStyle(int showStyle1, int showStyle2) {
                if((showStyle1 == Advertis.IMG_SHOW_TYPE_COMMEND_STATIC_IMG
                        || showStyle1 == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO)
                        && (showStyle2 == Advertis.IMG_SHOW_TYPE_COMMEND_STATIC_IMG
                        || showStyle2 == Advertis.IMG_SHOW_TYPE_COMMEND_VIDEO)) {
                    return true;
                }
                return false;
            }

            @Override
            public void notifyDataSetChanged() {
                if(notifyDataSetChanged != null) {
                    notifyDataSetChanged.onReady();
                }
            }
        }, new CommendAdHandleImpl(), AppConstants.AD_POSITION_NAME_PLAY_COMMENT);
    }

    private int minCommentCount;
    private int startIndex;
    private int interval;

    private void updateConfiger() {
        commendAdConfig = ConfigureCenter.getInstance().getJson(CConstants.Group_ad.GROUP_NAME,
                CConstants.Group_ad.ITEM_COMMENTADCONFIG);
        if(commendAdConfig == null) {
            minCommentCount = 5;
            startIndex = 3;
            interval = 20;
        } else {
            try {
                minCommentCount = commendAdConfig.getInt("minCommentCount");
                startIndex = commendAdConfig.getInt("startIndex");
                interval = commendAdConfig.getInt("interval");

                if(interval <= 0) {
                    interval = 1;
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Logger.log("XPlayCommendAdManager : minCommentCount=" + minCommentCount +
                "; startIndex=" + startIndex + "; interval=" + interval);

    }

    private boolean canInsertAdvertis(int commentCount) {
        return minCommentCount <= commentCount;
    }

    private void resetData() {
        mCommendAdList = null;
        updateConfiger();
        curTrack = null;
    }

    public void loadCommendAd(final Track track, List<CommentModel> commentModelList, boolean isNoTabStyle) {
        resetData();

        curTrack = track;

        if(track == null ||
                !canInsertAdvertis(track.getCommentCount())) {
            mFeedAdHelper.updateAdvertis(null, false);
            removeInsertedAds(commentModelList, true);
            return;
        }

        mLastRequestId = System.currentTimeMillis();

        final long tempRequestId = mLastRequestId;

        AdRequest.playCommentAd(isNoTabStyle, track.getCommentCount(), new IDataCallBack<List<Advertis>>() {
            @Override
            public void onSuccess(@Nullable List<Advertis> object) {
                if(tempRequestId != mLastRequestId) {
                    return;
                }

                mFeedAdHelper.updateAdvertis(object, false);

                mCommendAdList = object;
                if(mRequestCallBack != null) {
                    mRequestCallBack.onAdRequestSuccess(mCommendAdList,track.getDataId());
                }
            }

            @Override
            public void onError(int code, String message) {
                if(tempRequestId != mLastRequestId) {
                    return;
                }

                mFeedAdHelper.updateAdvertis(null, false);
                removeInsertedAds(commentModelList, true);
            }
        });
    }

    public void removeInsertedAds(List<CommentModel> commentModelList, boolean notification) {
        if(ToolUtil.isEmptyCollects(commentModelList)) {
            return;
        }

        boolean isRemoved = false;
        Iterator<CommentModel> iterator = commentModelList.iterator();
        while (iterator.hasNext()) {
            if(iterator.next().id == CommentListUtil.ITEM_AD) {
                iterator.remove();
                isRemoved = true;
            }
        }

        if(isRemoved && mRequestCallBack != null && notification) {
            mRequestCallBack.onListDataChange();
        }
    }

    public void insertAd(List<CommentModel> commentModelList) {
        if(ToolUtil.isEmptyCollects(mCommendAdList) ||
                ToolUtil.isEmptyCollects(commentModelList) ||
                commentModelList.size() < 2) {
            return;
        }

        removeInsertedAds(commentModelList, false);

        int curAdvertisIndex = 0;
        CommentModel commentModel;
        int intervalNum = 0;
        boolean firstInsert = true;    // 是否是第一次插入

//        Set<Long> hotCommentIds = new HashSet<>();

        boolean isLast = false;

        int allCommentCount = 0;

        int lastIndex = commentModelList.size() - 1;
        if (lastIndex >= 0) {
            CommentModel lastModel = commentModelList.get(lastIndex);
            if (lastModel != null && (lastModel.getItemType() == CommentModel.ITEM_TYPE_FOLD || lastModel.getItemType() == CommentModel.ITEM_TYPE_EMPTY_FOLD)) {
                lastIndex--;
            }
        }
        for (int i = 0; i <= lastIndex; i++) {
            if(curAdvertisIndex >= mCommendAdList.size()) {
                return;
            }

            if (i == lastIndex) {
                CommentModel commentModelTemp = commentModelList.get(lastIndex);
                if(!(commentModelTemp != null && commentModelTemp.id == CommentListUtil.ITEM_AD)) {
                    isLast = true;
                } else {
                    isLast = false;
                }
            }

            commentModel = commentModelList.get(i);

            if(commentModel.id > 0 && commentModel.getItemType()== ITEM_TYPE_NORMAL) {
                allCommentCount++;
            }

            Logger.log("XPlayCommendAdManager : curIndex " + i + "  " + intervalNum);

            if(firstInsert &&
                    (intervalNum == startIndex || (isLast && intervalNum + 1 == startIndex)) &&
                    (commentModel.id > 0 && commentModel.getItemType()== ITEM_TYPE_NORMAL)) {
                firstInsert = false;
                CommentModel commentAdModel = createCommentAdModel(curAdvertisIndex);
                if(commentAdModel != null) {

                    if(intervalNum == startIndex) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i, commentAdModel);
                    } else {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i + 1, commentAdModel);
                    }
                    lastIndex++;
                    curAdvertisIndex ++;
                    intervalNum = 0;
                    continue;
                }
            }

            if (!firstInsert && (intervalNum == interval || (isLast && intervalNum + 1 == interval)) && (commentModel.id > 0 && commentModel.getItemType() == ITEM_TYPE_NORMAL)) {
                CommentModel commentAdModel = createCommentAdModel(curAdvertisIndex);
                if(commentAdModel != null) {

                    if(intervalNum == interval) {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i , commentAdModel);
                    } else {
                        commentAdModel.adRank = allCommentCount - 1;
                        commentModelList.add(i + 1, commentAdModel);
                    }
                    lastIndex++;
                    curAdvertisIndex ++;
                    intervalNum = 0;
                    continue;
                }
            }

            if(commentModel.id > 0 && commentModel.getItemType()== ITEM_TYPE_NORMAL) {
                intervalNum ++;
            }
        }

    }

    @Nullable
    private CommentModel createCommentAdModel(int adIndex) {
        if(!ToolUtil.isEmptyCollects(mCommendAdList) && mCommendAdList.size() > adIndex) {
            CommentModel adComment = new CommentModel();
            adComment.id = CommentListUtil.ITEM_AD;
            adComment.setItemType(CommentModel.ITEM_TYPE_AD);
            adComment.mFeedAdWrapper = mFeedAdHelper.insertFeedAd(adIndex, null);
            adComment.adSequence = adIndex;
            return adComment;
        }
        return null;
    }

    public void checkHasAdAndPlay(ListView listView) {
        if(listView == null) {
            return;
        }

        for (int i = 0; i < listView.getChildCount(); i++) {
            View childAt = listView.getChildAt(i);
            if(childAt instanceof CommendAdViewX) {
//                // 调用这个函数使其自动播放
//                ((CommendAdView) childAt).onScrollStateChanged(listView ,
//                        AbsListView.OnScrollListener.SCROLL_STATE_IDLE);
            }
        }
    }

    public void onDestroy() {
    }

    @Nullable
    public FeedAdHelper.IFeedAdShowedCallBack getFeedAdShowCallBack() {
        if(mFeedAdHelper != null) {
            return mFeedAdHelper.getFeedAdShowCallBack();
        }
        return null;
    }

    public void onListScroll(boolean scrollUp) {
        if(mFeedAdHelper != null) {
            mFeedAdHelper.onListScroll(scrollUp);
        }
    }

    public static class CommendAdHandleImpl implements IFeedAdHandle<Object> {
        @Override
        public List<Advertis> updateAdvertis(List<Advertis> advertis) {
            return advertis;
        }

        @Override
        public boolean insertFeedAd(FeedAdWrapper feedAdWrapper, int index, Object otherData) {
            return true;
        }
    }

}

